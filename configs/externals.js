const { createMomentExternals, createLodashExternals } = require('whispered-build/configs/commonExternal/external')

module.exports = config => {
  if (config && config.patch && config.patch.vendors) {
    config.patch.vendors(['dist/*/eui-icons.js', ['@hose/eui-icons']])

    // config.patch.vendors(['dist/*/eui-icons-menu.js', ['@hose/eui-icons']])
  }

  config.externals({
    '@hose/eui-icons': 'lib_eui_icons',
    // 排除 antd-mobile，防止打包进 PC 端项目
    'antd-mobile': 'undefined',
    ...createMomentExternals(),
    ...createLodashExternals({
      prefix: 'lib_lodash.'
    }),
    'lodash-es': 'lib_lodash'
  })
}
