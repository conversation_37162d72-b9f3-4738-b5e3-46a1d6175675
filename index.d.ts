declare module 'ekbc-template'
declare const Fetch: any
declare const alertMessage: Function
declare const __PLANTFORM__: string
declare module 'react-highlight-words'

interface Window {
  CURRENCY_SYMBOL: string
  DingTalkPC: string
  masdk: any
  CURRENCY_SYMBOL: string
  alertMessage: Function
  isNewHome: boolean
  TRACK: Function
  __PLANTFORM__: string
  IS_LDAP: boolean
  HUAWEI_LOGIN: string
  PLATFORMINFO: { platform: string; name: string }
  dtCorpId: string //钉钉企业id
  __SUB_PLANNTFORM__: string
  __showMessage_error: (message: string) => void
  showLoading: (message: string, duration?: number | (() => void) | undefined) => void
  hideLoading: () => void
  CURRENT_AICHAT_SESSIONID: string
  /**
   * 企微ISV服务域名
   * */
  _WECOM_SERVER_HOSTNAME_: string
}

declare module '*.png' {
  const value: string
  export default value
}

declare module '*.svg' {
  const value: string
  export default value
}

declare module '*.svgx' {
  import React from 'react'
  const value: React.ComponentType<any>
  export default value
}

declare module '*.module.less' {
  const value: any
  export default value
}

declare module '*.module.less' {
  const value: any
  export default value
}

declare module '*.worker' {
  const value: Worker
  export default value
}

declare module '*.png' {
  const value: string
  export default value
}

declare module '*.less'

declare module '*.js'

declare module 'ekbc-enhance-stacker-manager' {
  const EnhanceStackerManager: any
  export { EnhanceStackerManager }
}
