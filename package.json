{"name": "@ekuaibao/entry-web", "version": "9.147.20", "description": "whispered-web", "author": "ekuaibao@team", "license": "MIT", "private": true, "monacoBuildVersion": "**********", "devDependencies": {"@babel/compat-data": "7.24.7", "@babel/helper-create-class-features-plugin": "7.24.7", "@babel/helper-remap-async-to-generator": "7.24.7", "@babel/helper-replace-supers": "7.24.7", "@babel/plugin-transform-async-generator-functions": "7.24.7", "@babel/plugin-transform-classes": "7.24.7", "@babel/preset-env": "7.24.7", "@ekuaibao/lib": "2.15.17-qa.5", "@ekuaibao/fetch": "0.7.1", "@ekuaibao/mobx-remotedev": "0.5.0", "@ekuaibao/webpack-retry-chunk-load-plugin": "1.5.4", "@ekuaibao/whispered": "5.7.12", "@host/plugin-testid-checker": "^1.1.5", "@types/big.js": "6.2.2", "@types/classnames": "2.2.10", "@types/jest": "23.3.14", "@types/lodash": "4.14.150", "@types/minimatch": "3.0.3", "@types/react": "16.9.34", "@types/react-color": "3.0.5", "@types/react-dom": "16.9.7", "@types/react-grid-layout": "0.16.7", "@types/react-infinite-scroller": "1.2.1", "@types/react-virtualized": "9.21.9", "acorn": "6.4.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "chokidar": "3.5.3", "coa": "2.0.2", "duplicate-package-checker-webpack-plugin": "^3.0.0", "eslint": "5.6.0", "eslint-config-prettier": "3.6.0", "eslint-plugin-prettier": "3.1.3", "expose-loader": "^0.7.5", "glob": "7.1.6", "html-replace-webpack-plugin": "^2.6.0", "jest": "23.6.0", "linebyline": "1.3.0", "node-ipc": "9.2.1", "prettier": "1.19.1", "request": "2.88.2", "rimraf": "3.0.2", "script-ext-html-webpack-plugin": "2.1.5", "start-server-and-test": "1.14.0", "ts-jest": "23.10.5", "tslint": "5.20.1", "tslint-config-prettier": "1.18.0", "webpack-bundle-analyzer": "^4.10.2", "whispered-build": "3.3.13-beta.5"}, "dependencies": {"@ant-design/icons": "4.7.0", "@ekuaibao/3rd_resources": "1.1.0", "@ekuaibao/async-component": "1.0.8", "@ekuaibao/auth": "1.3.11", "@ekuaibao/charts": "1.0.0-beta.0", "@ekuaibao/collection-definition": "0.0.33-release", "@ekuaibao/datagrid": "1.11.0-beta.4", "@ekuaibao/ekuaibao_404": "0.0.2", "@ekuaibao/ekuaibao_types": "1.0.68", "@ekuaibao/enhance-layer-manager": "5.6.6", "@ekuaibao/enhance-stacker-manager": "3.1.3", "@ekuaibao/eui": "1.6.1-qa.1", "@ekuaibao/eui-isomorphic": "1.2.8", "@ekuaibao/eui-styles": "2.1.0", "@ekuaibao/eui-web": "0.0.53", "@ekuaibao/formula": "1.0.30", "@ekuaibao/formula-patch": "1.0.3", "@ekuaibao/helpers": "1.1.10", "@ekuaibao/i18n": "3.1.5", "@ekuaibao/image-player-manager": "0.1.12", "@ekuaibao/keel": "1.1.0", "@ekuaibao/loading": "4.0.1", "@ekuaibao/markup-image": "0.0.4", "@ekuaibao/messagecenter": "5.1.0", "@ekuaibao/mfe-dock": "1.0.6", "@ekuaibao/mobx-store": "1.3.7", "@ekuaibao/monaco-editor": "0.17.3000", "@ekuaibao/money-math": "4.1.9", "@ekuaibao/navigator": "1.0.21", "@ekuaibao/noekb": "1.0.2", "@ekuaibao/painter": "0.0.75", "@ekuaibao/platform.is": "1.0.19", "@ekuaibao/polyfill": "1.0.2", "@ekuaibao/rpc": "1.1.13", "@ekuaibao/sdk-bridge": "1.17.8-beta.12", "@ekuaibao/session-info": "1.0.2", "@ekuaibao/show-util": "1.0.1", "@ekuaibao/signature": "0.0.8", "@ekuaibao/springboard": "2.1.0", "@ekuaibao/store": "1.0.7", "@ekuaibao/template": "5.0.10", "@ekuaibao/template-next": "6.0.2", "@ekuaibao/theme-variables": "1.3.13", "@ekuaibao/uploader": "3.5.1", "@ekuaibao/vendor-antd": "3.9.9", "@ekuaibao/vendor-common": "1.4.4-beta.0", "@ekuaibao/vendor-lodash": "4.17.13", "@ekuaibao/vendor-polyfill": "1.0.3", "@ekuaibao/vendor-whispered": "3.1.2-release.0", "@ekuaibao/web-theme-variables": "1.1.3", "@featbit/js-client-sdk": "3.0.12", "@hose/eui": "3.9.5", "@hose/eui-icons": "3.0.94", "@hose/eui-theme": "3.0.6", "@hose/idp-jslib": "0.0.13", "@hose/invoice-components": "0.4.4-beta.44", "@hose/pro-eui-pc-components": "1.0.40", "@loadable/component": "^5.16.4", "@mikecousins/react-pdf": "5.5.1", "@types/crypto-js": "^4.2.2", "@webcomponents/webcomponentsjs": "2.7.0", "antd": "3.8.4", "big.js": "5.2.2", "braft-editor": "2.3.9", "children-dirs": "2.0.0", "classnames": "2.2.6", "colors": "1.4.0", "cropperjs": "1.6.2", "cross-env": "7.0.2", "crypto-js": "^4.2.0", "driver.js": "^1.3.1", "echarts": "3.8.5", "ekbc-datagrid": "5.3.11", "ekbc-list-layout": "3.0.0", "ekbc-pro-ui": "3.0.4", "ekbc-query-builder": "2.0.3", "ekbc-thirdParty-card": "3.0.4", "github-markdown-css": "^5.8.1", "html2canvas": "1.1.4", "image-promise": "5.0.1", "isarray-polyfill-for-mobx4-observablearray": "1.0.1", "jsencrypt": "^3.3.2", "jspdf": "2.3.1", "jszip": "3.10.1", "localforage": "1.10.0", "lodash": "4.17.15", "md5": "2.2.1", "minimatch": "3.0.4", "mobx": "4.15.4", "mobx-cobweb": "0.0.58", "mobx-react-lite": "2.2.2", "moment": "2.24.0", "nanobar": "0.4.2", "postmate": "^1.5.2", "prop-types": "15.7.2", "qrcode.react": "0.9.3", "rc-editor-mention": "1.1.13", "rc-motion": "2.6.0", "rc-progress": "2.5.2", "rc-select": "9.2.3", "rc-tree-select": "5.4.0", "rc-util": "5.21.5", "rc-virtual-list": "3.4.8", "re-resizable": "6.10.1", "react": "17.0.2", "react-color": "2.19.3", "react-copy-to-clipboard": "5.0.2", "react-dom": "17.0.2", "react-grid-layout": "0.16.6", "react-highlight-words": "0.14.0", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "1.2.4", "react-intl-universal": "1.16.2", "react-lottie": "1.2.3", "react-markdown": "7.1.2", "react-mentions": "4.0.2", "react-sortable-hoc": "0.8.4", "remark-gfm": "2.0.0", "simple-i18n-cli": "1.1.1", "string-width": "4.2.0", "systemjs": "0.21.6", "tslib": "1.11.1", "upload_to_cdn": "1.3.1", "victory": "36.2.0", "web-vitals": "4.2.1"}, "config": {"testserver": "", "testcypresskey": ""}, "scripts": {"fix-jszip-issue": "node ./scripts/fix-jszip-issue.js", "build:pre:dev": "rimraf .dist && npm run fix-jszip-issue && node --max_old_space_size=40240 scripts/builds-dev", "build:pre:pro": "rimraf .dist && npm run fix-jszip-issue && node --max_old_space_size=40240 scripts/builds-pro", "start": "npm run build:pre:dev && npm run dev", "dev": "npm run fix-jszip-issue && npm run dev:server", "dev:server": "node --max_old_space_size=10240 node_modules/.bin/webpack-dev-server", "serve": "webpack-dev-server", "clean": "cross-env rm -rf ./build", "build:source": "node --max_old_space_size=10240 node_modules/webpack/bin/webpack.js --config ./webpack.build.js --hash --compress --progress", "build:nosource": "node --max_old_space_size=10240 node_modules/webpack/bin/webpack.js --config ./webpack.build.nosource.js --hash --compress --progress", "build": "npm run clean && npm run build:pre:pro && npm run build:source", "build:dev": "npm run clean && npm run build:pre:pro && npm run build:source", "icons": "node compile-feetype-icon.js", "lint:es": "eslint --ext .jsx,.js src", "lint:ts": "tslint -p tsconfig.json", "publish2npm": "npm run build && npm publish dist", "test:watch": "jest -o --watch", "test": "jest --coverage", "cy:verify": "cypress verify", "emit:local:config": "cp ./configs/webpack.local.js webpack.local.js", "e2e:run": "cypress run --record --key=$npm_package_config_testcypresskey --parallel", "e2e:test": "start-test test:server 9999 e2e:run", "local:open": "cypress open", "local:run": "cypress run", "bus:ast": "node ./busanalyzer/bus-ast.js", "bus:analyzer": "npm run bus:ast && http-server ./busanalyzer -p 3001 -o -c-1", "patch:i18n": "find ./src/i18n/locales -type f -name '*.all.json' | sed 's/.all.json//' | xargs -I % mv %.all.json %.json", "build:i18n": "i18n-cli wrap --dry-run false './src/**/*' && i18n-cli parse --output-dir ./src/i18n/locales --locales en-US,zh-CN './src/**/*' && npm run patch:i18n", "clear": "cross-env rm -rf ./packge-lock.json  && cross-env rm -rf ./node_modules && cross-env rm -rf ./dll && sudo npm i", "mfe:build": "/bin/bash build-mfe.sh", "fix-memory-limit": "cross-env LIMIT=20240 increase-memory-limit", "mfe:restore": "/bin/bash restore_mfe.sh", "build:analyze": "rimraf build && npm run fix-jszip-issue && NODE_ENV=production  NODE_OPTIONS='--max_old_space_size=10240' ANALYZE=true npx webpack --mode production --progress --config ./webpack.build.analyze.js", "stagewise": "npx stagewise -b -s", "analyze:iconfont": "node configs/scripts/analyze-iconfont.js"}, "whispered": {"plugins": {}, "entryPlugins": {"app": ["account5"], "application": ["account5"], "group": ["account5"], "ldap": ["account5"], "browser": ["account"], "huawei": ["account5"], "kdcloud": ["new-feature-kdcloud"], "thirdparty": ["account5"], "billentry": ["account5"], "nbbank": ["account"], "hwly": ["account5"]}, "ignorePlugins": ["bi", "bi-manage", "changjiepayment", "custom-project", "home", "chanpay-payment", "cgbpayment"]}, "optionalDependencies": {"@ekuaibao/plugin-web-account": "1.0.5-release.42", "@ekuaibao/plugin-web-account5": "1.10.4-release.qa.2", "@ekuaibao/plugin-web-common-domain": "1.13.1-release.qa.8", "@ekuaibao/plugin-web-bills-domain": "1.25.0-release.qa.28", "@ekuaibao/plugin-web-pay-domain": "1.28.5-release-qa.41", "@ekuaibao/plugin-web-specification-domain": "1.39.1-release.qa.3", "@ekuaibao/plugin-web-mc-all": "1.0.15-beta.0"}}