# MarkdownRenderer 功能测试

## 基础格式测试

这是一个**粗体文本**和*斜体文本*的例子。

这是一个***粗体斜体***的例子。

这是一个~~删除线~~的例子。

这是一个`内联代码`的例子。

## 引用块测试

> 这是一个简单的引用块。
> 
> 引用块可以包含**粗体**和*斜体*文本。
> 
> > 这是嵌套的引用块。
> > 非常有用！

## 列表测试

### 无序列表

- 第一项
- 第二项
  - 嵌套项目1
  - 嵌套项目2
- 第三项

### 有序列表  

1. 第一步
2. 第二步
   1. 子步骤A
   2. 子步骤B
3. 第三步

## 代码块测试

```javascript
function hello() {
  console.log("Hello, World!");
  return "success";
}
```

```python
def greet(name):
    print(f"Hello, {name}!")
    return True
```

## 水平分割线测试

这是一段文字。

---

这是分割线后的文字。

***

另一个分割线。

## 混合格式测试

这段文字包含**粗体**、*斜体*、`代码`和~~删除线~~。

> 引用块中的**粗体**和*斜体*
> 
> ```
> 引用块中的代码
> ```

1. 列表中的**粗体项目**
2. 列表中的*斜体项目*  
3. 列表中的`代码项目`

## 链接测试

这里有一个指向百度的[链接](https://www.baidu.com)。

这里有一个指向GitHub的[链接](https://github.com)。

这段文字中包含一个[示例链接](https://www.example.com)和其他文本。 