---
description:
globs: *.tsx,*.jsx,*js,*.ts
alwaysApply: false
---
# Single Responsibility Principle for Functions

## Overview
Each function should have only one reason to change, meaning it should perform a single, well-defined task. This principle helps maintain code that is easier to understand, test, and maintain.

## Guidelines

### ✅ Good Examples
```typescript
// Single responsibility: Only formats a date
function formatDate(date: Date): string {
    return date.toLocaleDateString();
}

// Single responsibility: Only validates an email
function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
```

### ❌ Bad Examples
```typescript
// Multiple responsibilities: Validates, formats, and sends email
function processEmail(email: string): void {
    // Validates email
    if (!email.includes('@')) {
        throw new Error('Invalid email');
    }

    // Formats email
    const formattedEmail = email.toLowerCase().trim();

    // Sends email
    sendEmail(formattedEmail);
}
```

## Best Practices

1. **Function Names**: Choose clear, specific names that describe exactly what the function does
2. **Function Size**: Keep functions small and focused
3. **Parameters**: Limit the number of parameters to reduce complexity
4. **Return Values**: Functions should return a single type of value
5. **Side Effects**: Minimize side effects and make them explicit in the function name

## When to Split Functions

Split a function when:
- It performs multiple distinct operations
- It has multiple return types
- It's longer than 20-30 lines
- It has multiple levels of nesting
- It's difficult to describe its purpose in a single sentence

## Benefits

- Improved code readability
- Easier testing
- Better maintainability
- Reduced complexity
- Better reusability
