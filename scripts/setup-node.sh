#!/bin/bash

# 设置 Node.js 版本为 14.19.3
# 此脚本帮助团队成员在这个项目中使用正确的 Node.js 版本

echo "🔧 正在设置项目 Node.js 版本..."

# 检查是否安装了 nvm
if ! command -v nvm &> /dev/null; then
    echo "❌ 未找到 nvm，请先安装 nvm (Node Version Manager)"
    echo "安装命令: curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
    echo "或者访问: https://github.com/nvm-sh/nvm#installing-and-updating"
    exit 1
fi

# 安装并使用 Node.js 14.19.3
echo "📦 安装 Node.js 14.19.3..."
nvm install 14.19.3

echo "✅ 切换到 Node.js 14.19.3..."
nvm use 14.19.3

# 验证版本
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)

echo "✅ 当前 Node.js 版本: $NODE_VERSION"
echo "✅ 当前 npm 版本: $NPM_VERSION"

# 检查版本是否正确
if [[ $NODE_VERSION == "v14.19.3" ]]; then
    echo "🎉 Node.js 版本设置成功！"
    echo "💡 提示: 在这个项目目录下，你可以随时运行 'nvm use' 来切换到正确的版本"
else
    echo "⚠️  警告: Node.js 版本不匹配，期望 v14.19.3，实际 $NODE_VERSION"
fi

echo ""
echo "📚 使用说明:"
echo "- 在其他项目中使用最新版本: nvm use node"
echo "- 回到这个项目使用 14.19.3: nvm use 14.19.3 或者 nvm use"
echo "- 查看所有安装的版本: nvm list" 