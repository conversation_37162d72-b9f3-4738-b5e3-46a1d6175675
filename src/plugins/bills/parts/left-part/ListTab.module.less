@import '~@ekuaibao/eui-styles/less/token';
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/web-theme-variables/styles/default';

.tab-list-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.tab-list-part {
  position: relative;
  flex: 1;
  width: 100%;
  background: @white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding-top: 16px;

  :global {
    .tab-list {
      position: absolute;
      top: 0;
      bottom: 35px;
      width: 100%;
      overflow: hidden;
      overflow-y: auto;
    }
    .ant-tabs {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    .ant-tabs-tabpane {
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
    }
    .ant-tabs-bar {
      margin-bottom: 0;
    }
    .ant-tabs-tab {
      margin: 0 32px;
      display: inline-flex;
      align-items: center;
      width: 85px;
      height: 50px;
    }
    .ant-tabs-tab-inner {
      padding: 0 20px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      font-size: 14px;
    }
    .ant-tabs-nav-container {
      font-size: 14px;
    }
    .ant-tabs-content {
      position: absolute;
      top: 49px;
      bottom: 0;
      width: 100%;
      & > div {
        height: 100%;
        .ant-tabs-tabpane {
          height: 100%;
        }
      }
    }

    .skeleton-modal{
      padding: 16px;
        .head{
          height: 24px;
          width: 114px;
          margin-bottom: 24px;
          .eui-skeleton{
            width: 100%;
            height: 100%;
          }
        }
        .body{
          width: 100%;
          height: 16px;
          margin-bottom: 16px;
          .eui-skeleton{
            width: 100%;
            height: 100%;
          }
        }
    }
  }
}
.show-msg {
  padding-top: 106px !important;
  padding-bottom: 54px;
}
.tab-list-part-layout5 {
  width: 280px;
  background: @color-white-1;
  border-radius: 8px;
  flex: none;
  overflow: hidden;
}
