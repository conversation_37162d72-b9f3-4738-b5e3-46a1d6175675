import React, { useMemo } from 'react'
import { Segmented, Tooltip } from '@hose/eui'
import { OutlinedEditDisorderList, OutlinedDataGrid } from '@hose/eui-icons'
import styles from './Title.module.less'
import classNames from 'classnames'

export type ViewType = 'table' | 'list'

interface ListViewTitleProps {
  /** 标题文本 */
  title?: string
  /** 当前视图类型 */
  currentViewType?: ViewType
  /** 视图类型变更回调 */
  onViewTypeChange?: (viewType: ViewType) => void
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
}

const ListViewTitle: React.FC<ListViewTitleProps> = ({
  title = i18n.get('我的单据'),
  currentViewType = 'list',
  onViewTypeChange,
  className,
  style
}) => {
  const handleViewTypeChange = (value: string) => {
    onViewTypeChange?.(value as ViewType)
  }

  const options = useMemo(() => {
    return [
      {
        label: (
          <Tooltip title={i18n.get('列表视图')}>
            <OutlinedEditDisorderList />
          </Tooltip>
        ),
        value: 'list'
      },
      {
        label: (
          <Tooltip title={i18n.get('表格视图')}>
            <OutlinedDataGrid />
          </Tooltip>
        ),
        value: 'table'
      }
    ]
  }, [])

  return (
    <div className={classNames(styles['title-wrapper'], className)} style={style}>
      <div className={styles.title}>{title}</div>
      <Segmented
        value={currentViewType}
        onChange={handleViewTypeChange}
        size="middle"
        options={options}
        data-platform-wx2-hidden={window.isInWeComISV}
      />
    </div>
  )
}

export default ListViewTitle
