/**
 * 获取我的单据列表由于需要根据单据的状态排序，后台的接口不支持这样的分页请求，所以之前是一次性获取尽量多的数据，当获取特别多的单据的时候会导致后端服务挂掉
 * 目前1500条的时候出现问题，现在的方案是让前端处理，根据单据的不同状态再去分页请求，过程如下：
 * 单据类型：
 * rejected: 1, //驳回
 * paid: 2, //已支付
 * draft: 3, //草稿
 * sending: 4, //待寄送
 * receiving: 5, //待收单
 * approving: 6 //审批中
 * 实现逻辑：
 * 1、分页（1～100）请求一种类型的单据，判断是否大于100条，如果小于100条进入2，否则进入3
 * 2、分页（1～100）请求另一类型的单据，判断1和2总共获取的条数是否大于100条，如果小于100条循环1和2，否则进入3
 * 3、返回所有数据，退出循环
 * 点击加载更多：进入1，分页开始的位置要在对应类型的单据已请求的个数
 */
import { searchMyBillList } from '../../bills.action'
import { orderBy, cloneDeep } from 'lodash'

type BillType = 'rejected' | 'draft' | 'sending' | 'receiving' | 'receivingExcep' | 'approving' | 'pending' | 'paying'
type BillTypes = { type: BillType; weight: number }

interface BillProps {}

interface BillKindProps {
  bills: BillProps[]
  count: number
}

interface BillListMap {
  [propName: string]: BillKindProps
}

const initValue = (): BillKindProps => {
  const result: BillKindProps = { bills: [], count: 0 }
  return result
}

const loadMore = {
  type: 'loadMore'
}

export default class MyBillListData {
  private billTypes: BillTypes[] = orderBy(
    [
      { type: 'rejected', weight: 1 },
      { type: 'draft', weight: 2 },
      { type: 'sending', weight: 3 },
      { type: 'receiving', weight: 4 },
      { type: 'receivingExcep', weight: 4 },
      { type: 'approving', weight: 5 },
      { type: 'pending', weight: 6 },
      { type: 'paying', weight: 7 },
      { type: 'paid', weight: 8 },
    ],
    ['weight'],
    ['asc']
  )
  isLoading: boolean = false
  requestParams: any = undefined
  private billListMap: BillKindProps = cloneDeep(initValue())

  private start = async (params: any, type: string) => {
    const p = { ...params }
    p.stateFilter = `state.in(${type})`
    return searchMyBillList(p)
  }

  private isNeedGet = (billInfo: BillKindProps) => {
    return billInfo.count === 0 || billInfo.bills.length < billInfo.count
  }

  /**
   * 获取整理后的数据，已经判断是否有加载更多按钮
   */
  private getBillList = () => {
    let result: BillProps[] = []
    result = result.concat(this.billListMap.bills)
    if (this.billListMap.bills.length < this.billListMap.count) {
      result.push(loadMore)
    }
    return result
  }

  init = () => {
    this.billListMap = cloneDeep(initValue())
  }

  /**
   * 分页获取单据逻辑
   * @param params
   */
  getBills = async (params: any) => {
    if (!this.isLoading) {
      let result = []
      try {
        result = await this.getList(params)
      } finally {
        if (!!this.requestParams) {
          const { isLoadMore, ...others } = this.requestParams
          if (!isLoadMore) {
            this.init()
          }
          this.requestParams = undefined
          result = await this.getList({ ...others })
        }
      }
      return result
    } else {
      this.requestParams = params
      return []
    }
  }

  getList = async (params: any) => {
    try {
      this.isLoading = true
      let billTypes = this.billTypes
      if (!!params?.billTypes?.length) {
        billTypes = billTypes.filter(el => params.billTypes.includes(el.type))
      }
      params.start = this.billListMap.bills.length
      const data = await this.start(params, billTypes.map(item=>`"${item.type}"`).join(","))
      this.billListMap.count = data.count
      this.billListMap.bills = this.billListMap.bills.concat(data.items)
      const result = this.getBillList()
      return result
    } catch (e) {
      return Promise.reject(e)
    } finally {
      this.isLoading = false
    }
  }
}
