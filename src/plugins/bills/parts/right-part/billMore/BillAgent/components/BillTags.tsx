/**
 * @file 包含单据业务场景下所有自定义标签的 React 组件实现。
 * 这些组件被 createBillTagRenderers 工厂函数使用。
 */

import React, { useState, useEffect, useRef } from 'react';
import { app as api } from '@ekuaibao/whispered';
import PopoverWrapper from '../../../../../../../elements/namePopover/PopoverWrapper';
// 注意：这里的路径可能需要根据您的项目结构微调
// 为了解耦，理想情况下 UserTag, DetailTag, FieldTag 的具体实现应该在 BillAgent 模块内部，
// 而不是直接依赖顶层的 elements 或其他模块。
// 但作为示例，我们暂时沿用现有组件。

// 组件级用户数据缓存
const userCache = new Map<string, any>();

// 用户标签组件
export const UserTag: React.FC<{ userId: string; staffMap?: any, fallbackText: string }> = ({ userId, staffMap, fallbackText }) => {
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // 处理不完整的 userID 格式的容错函数
  const normalizeUserId = (inputUserId: string): string => {
    if (!inputUserId) return inputUserId;
    
    // 检查是否已经是完整格式（包含冒号）
    if (inputUserId.includes(':')) {
      return inputUserId;
    }
    
    // 如果不包含冒号，尝试拼接当前 corpID
    try {
      // 尝试多种方式获取当前 corpID
      let corpId: string | undefined;
      
      // 方式1: 从 api 实例获取
      if ((api as any).corpId) {
        corpId = (api as any).corpId;
      }
      
      // 方式2: 从全局配置获取
      if (!corpId && (window as any).APP_CONFIG?.corpId) {
        corpId = (window as any).APP_CONFIG.corpId;
      }
      
      // 方式3: 从 URL 或其他地方尝试获取
      if (!corpId && window.location) {
        const urlMatch = window.location.href.match(/corpId[=\/]([^&\/\?]+)/);
        if (urlMatch) {
          corpId = urlMatch[1];
        }
      }
      
      if (corpId) {
        return `${corpId}:${inputUserId}`;
      }
    } catch (err) {
      console.warn('Failed to get corpId for user normalization:', err);
    }
    
    // 如果无法获取 corpID，返回原始值
    return inputUserId;
  };

  useEffect(() => {
    if (!userId) return;

    // 规范化 userId 格式
    const normalizedUserId = normalizeUserId(userId);

    // 1. 优先从传入的staffMap中获取（如果有的话）
    // 同时检查原始userId和规范化后的userId
    if (staffMap?.[userId] || staffMap?.[normalizedUserId]) {
      setUserData(staffMap[userId] || staffMap[normalizedUserId]);
      return;
    }

    // 2. 检查组件级缓存
    // 同时检查原始userId和规范化后的userId
    if (userCache.has(userId) || userCache.has(normalizedUserId)) {
      setUserData(userCache.get(userId) || userCache.get(normalizedUserId));
      return;
    }

    // 3. 从服务器获取数据
    const fetchUserData = async () => {
      setLoading(true);
      setError(false);
      
      try {
        // 优先使用规范化后的userId进行API调用
        const staff = await api.invokeService('@bills:get:StaffById', normalizedUserId);
        
        if (!isMountedRef.current) return;
        
        if (staff) {
          // 缓存数据（同时缓存原始ID和规范化ID）
          userCache.set(userId, staff);
          userCache.set(normalizedUserId, staff);
          setUserData(staff);
        } else {
          setError(true);
        }
      } catch (err) {
        console.error('Failed to fetch staff data:', err);
        if (!isMountedRef.current) return;
        setError(true);
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };

    fetchUserData();
  }, [userId, staffMap]);

  // 渲染loading状态
  if (loading) {
    return (
      <span style={{ 
        display: 'inline-flex', 
        alignItems: 'center', 
        color: 'var(--eui-neutral-neu-400, #999)',
        fontWeight: 'normal' as const,
        cursor: 'default',
        margin: '0 1px',
        fontStyle: 'italic',
        fontSize: '13px',
        lineHeight: '13px'
      }}>
        @加载中...
      </span>
    );
  }

  // 渲染错误状态
  if (error || !userData) {
    return (
      <span style={{ 
        display: 'inline-flex', 
        alignItems: 'center', 
        fontWeight: 'normal' as const,
        cursor: 'default',
        margin: '0 1px',
        fontSize: '13px',
        lineHeight: '13px'
      }}>
        {fallbackText || `@${userId}`}
      </span>
    );
  }

  // 渲染正常状态，包含悬浮卡片
  const name = userData.name || fallbackText;
  
  return (
    <PopoverWrapper
      info={userData}
      trigger="hover"
      placement="bottomLeft"
      name={
        <span style={{ 
          display: 'inline-flex', 
          alignItems: 'center', 
          color: 'var(--eui-primary-pri-500, #2555FF)',
          fontWeight: 'bold' as const,
          cursor: 'pointer',
          margin: '0 1px',
          textDecoration: 'none' as const,
          transition: 'opacity 0.2s ease',
          fontSize: '13px',
          lineHeight: '13px'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.opacity = '0.8';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.opacity = '1';
        }}
        >
          @{name}
        </span>
      }
    />
  );
};

// 费用明细标签组件
export const DetailTag: React.FC<{ detailId: string; billDetails?: any; bus?: any, fallbackText: string }> = ({ detailId, billDetails, bus, fallbackText }) => {
  let detailIndex = -1;
  let detailInfo = null;
  
  if (billDetails?.form?.details && Array.isArray(billDetails.form.details)) {
    if (!isNaN(Number(detailId))) { 
      // 如果是数字索引格式
      detailIndex = Number(detailId);
      detailInfo = billDetails.form.details[detailIndex];
    } else { 
      // 如果是ID匹配格式
      detailIndex = billDetails.form.details.findIndex(d => {
        return d && (
          d.id === detailId || 
          d.feeTypeForm?.detailId === detailId ||
          d.detailId === detailId
        );
      });
      if (detailIndex !== -1) {
        detailInfo = billDetails.form.details[detailIndex];
      }
    }
  }
  
  let displayText = fallbackText || `费用`;
  if (detailIndex !== -1 && detailInfo) {
    displayText = `费用#${detailIndex + 1}`;
    
    // 尝试多种可能的金额字段结构
    let amount = '';
    if (detailInfo.money) {
      const currency = detailInfo.money.currency?.standardCode || 
                      detailInfo.money.currency?.code || 
                      detailInfo.money.currencyCode || 
                      'CNY';
      const value = detailInfo.money.standard || 
                   detailInfo.money.amount || 
                   detailInfo.money.value || 
                   detailInfo.money;
      if (value) {
        amount = ` ${currency}${value}`;
      }
    } else if (detailInfo.feeTypeForm?.amount?.standard) {
      // 根据实际数据结构，金额在feeTypeForm.amount中
      const currency = detailInfo.feeTypeForm.amount.standardStrCode || 'CNY';
      const value = detailInfo.feeTypeForm.amount.standard;
      amount = ` ${currency}${value}`;
    } else if (detailInfo.amount) {
      amount = ` ¥${detailInfo.amount}`;
    } else if (detailInfo.cost) {
      amount = ` ¥${detailInfo.cost}`;
    }
    
    if (amount) {
      displayText += amount;
    }
  }

  const handleDetailClick = (e: React.MouseEvent) => {
    if (bus && detailInfo) {
      e.stopPropagation();
      e.preventDefault();
      
      // 获取正确的ID用于跳转
      const targetId = detailInfo.feeTypeForm?.detailId || detailInfo.detailId || detailInfo.id;
      
      bus.emit('risk:scroll:view', {
        path: 'FEE_DETAIL',
        controlField: targetId,
        pathValueId: targetId
      });
    }
  };

  const baseStyle = {
    display: 'inline' as const,
    color: 'var(--eui-primary-pri-500, #2555FF)',
    fontWeight: 'bold' as const,
    textDecoration: 'none' as const,
    margin: '0 1px',
    cursor: (bus && detailInfo) ? 'pointer' : 'default',
    transition: 'opacity 0.2s ease'
  };
  
  return (
    <span 
      style={baseStyle}
      onClick={(bus && detailInfo) ? handleDetailClick : undefined}
      onMouseEnter={(e) => {
        if (bus && detailInfo) {
          e.currentTarget.style.opacity = '0.7';
          e.currentTarget.style.textDecoration = 'underline';
        }
      }}
      onMouseLeave={(e) => {
        if (bus && detailInfo) {
          e.currentTarget.style.opacity = '1';
          e.currentTarget.style.textDecoration = 'none';
        }
      }}
    >
      {displayText}
    </span>
  );
};

 