import { Agent, createServerApiClient, AgentConfig } from '../AIAgent';
import { createBillTools } from './BillTools';
import type { BillToolsDependencies } from './BillTools';
import { createBillTagRenderers } from './createBillTagRenderers';

// The full set of data properties provided by the host component.
export interface BillAgentDependencies extends BillToolsDependencies {
  riskData?: any;
  billRiskWaringStore?: any;
}

/**
 * Creates and configures a new Agent instance specifically for handling bills.
 * This acts as an adapter, taking raw data from the host and setting up the agent.
 *
 * @param dependencies - The raw data and event bus from the host component.
 * @returns A fully configured Agent instance or null if creation fails.
 */
export function createBillAgent(dependencies: BillAgentDependencies) {
  const { billDetails, bus, userInfo, staffMap } = dependencies;
  
  if (!billDetails) {
    console.error('❌ Cannot create Bill Agent without billDetails.');
    return null;
  }

  try {
    // 1. Create the API Client
    const apiClient = createServerApiClient('https://genai.hosecloud.com/v1/gen');

    // 2. Create the bill-specific tools.
    // The dependencies here are primarily for context, as tools will
    // mainly use the runtime context provided by updateBillAgentContext.
    const tools = createBillTools({ billDetails, bus, userInfo, staffMap });

    // 3. Create the custom tag renderers
    const tagRenderers = createBillTagRenderers(dependencies);

    // 4. Create the Agent instance with bill-specific configuration
    const agentConfig: AgentConfig = {
      apiClient,
      tools,
      tagRenderers,
          name: i18n.get('智能单据助手'),
    description: i18n.get('我可以帮你处理单据，例如分析风险、填写数据、进行计算，并能回答财务制度相关问题。'),
    recommendedQuestions: [
      i18n.get('这张单据有哪些风险？'),
      i18n.get('审核建议是什么？'),
      i18n.get('总结这张单据'),
    ],
    quickActions: [i18n.get('总结单据'), i18n.get('分析风险'), i18n.get('审核建议')],
      systemInstruction: {
        role: '你是一个专业的单据助手。你可以帮助用户查看和编辑单据信息，包括获取字段值、设置字段值、管理明细行等操作。',
        workflow: '你可以根据用户的需求，合理使用可用的工具来帮助用户完成操作。如果需要执行某个操作但缺少必要信息，请向用户询问详细信息。',
        toolRules: '1. 如果你需要调用工具修改单据，请**务必先调用**get-bill-details工具获取可以修改的字段，确保修改操作的合法性。\n2. 当你调用一个工具后，你会收到一个JSON格式的返回结果，其中包含一个 `isSuccess` 字段。\n    * 如果 `isSuccess` 为 `true`，`data` 字段中是成功的结果，请基于此结果继续你的任务。\n    * 如果 `isSuccess` 为 `false`，`error` 字段中是工具执行失败的原因。**请不要直接向用户展示这个技术性的错误信息。** 你应该：\n      1. 分析失败的原因。\n      2. 如果是因为缺少信息，你应该礼貌地向用户提问，索要所需信息。\n      3. 如果是因为其他原因，你可以尝试调用另一个相关工具，或者向用户解释你为什么无法完成任务。\n      4. 你的目标是尽可能地解决问题，保持对话的流畅性。\n3. 当你调用工具并**收到function Response**后，请基于你要执行的任务以及工具调用结果思考是否需要进一步的工具调用。如果需要，那么直接发起工具调用，而不是回复用户消息。',
        globalContext: `当前单据类型: ${billDetails?.formType || '未知'}\n当前单据状态: ${billDetails?.state || '未知'}\n当前时间: ${new Date().toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }).replace(/(\d{4})\/(\d{2})\/(\d{2})/, '$1-$2-$3')}`,
      },
    };

    const agent = new Agent(agentConfig);

    // 5. Set the initial context for the agent
    updateBillAgentContext(agent, dependencies);

    return agent;

  } catch (error) {
    console.error('❌ Failed to create Bill Agent:', error);
    if (error instanceof Error) {
        console.error('📍 Error stack:', error.stack);
    }
    return null; // Return null on failure
  }
}

/**
 * Updates the context of an existing Bill Agent instance.
 * This is useful when props change in the host component.
 * 
 * @param agent - The agent instance to update.
 * @param dependencies - The new set of data dependencies.
 */
export function updateBillAgentContext(agent: Agent, dependencies: BillAgentDependencies) {
    const { billDetails, userInfo, staffMap, riskData, billRiskWaringStore, bus } = dependencies;

    if (agent) {
        agent.updateContext({
            billDetails,
            userInfo,
            staffMap,
            riskData: riskData || billRiskWaringStore,
            bus,
        });
    }
} 