/**
 * @file 工厂函数，用于创建单据业务场景下所有自定义标签的渲染器。
 */

import React from 'react';
import type { TagRendererMap } from '../AIAgent';
import { UserTag, DetailTag } from './components/BillTags';
import type { BillAgentDependencies } from './BillAgent';

/**
 * 创建一个用于单据场景的 TagRendererMap。
 * @param dependencies - 包含单据详情、员工信息等业务数据的依赖对象。
 * @returns 配置好的 TagRendererMap 对象。
 */
export function createBillTagRenderers(dependencies: BillAgentDependencies): TagRendererMap {
  const { billDetails, staffMap, bus } = dependencies;

  return {
    user: {
      description: i18n.get('用户'),
      render: (id, fallbackText) => (
        <UserTag userId={id} staffMap={staffMap} fallbackText={fallbackText} />
      ),
    },
    details: {
      description: i18n.get('费用明细'),
      render: (id, fallbackText) => (
        <DetailTag detailId={id} billDetails={billDetails} bus={bus} fallbackText={fallbackText} />
      ),
    },
  };
} 