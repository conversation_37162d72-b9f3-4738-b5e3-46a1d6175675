/**
 * 单据工具通用辅助函数
 * 提供字段值解析、类型获取、验证等功能
 */

/**
 * 智能解析字段值，根据不同字段类型进行适当转换
 */
export async function parseFieldValue(value: any, fieldName: string, bus: any): Promise<any> {
  try {
    // 如果已经是对象或数组，直接返回
    if (typeof value === 'object' && value !== null) {
      return value
    }

    // 获取字段类型信息
    const fieldType = await getFieldType(fieldName, bus)
    
    // 优先按日期类型处理字符串值（常见的日期字段名）
    const commonDateFields = ['expenseDate', 'requisitionDate', 'submitDate', 'feeDate', 'repaymentDate', 'loanDate']
    const isLikelyDateField = commonDateFields.some(name => fieldName.toLowerCase().includes(name.toLowerCase()))
    
    // 根据字段类型进行智能转换
    switch (fieldType?.toLowerCase()) {
      case 'date':
        // 日期类型：支持字符串格式，但内部仍转换为时间戳
        if (typeof value === 'string') {
          console.log(`[parseFieldValue] 解析日期字符串: ${value}`)
          
          // 支持多种日期格式
          const dateFormats = [
            value, // 原始格式
            value.replace(/[年月]/g, '-').replace(/[日]/g, ''), // 中文格式转换
            value.replace(/\//g, '-'), // 斜杠转横杠
          ]
          
          for (const format of dateFormats) {
            const date = new Date(format)
            console.log(`[parseFieldValue] 尝试格式 ${format}，解析结果:`, date, date.getTime())
            if (!isNaN(date.getTime())) {
              const timestamp = date.getTime()
              console.log(`[parseFieldValue] 成功解析日期: ${format} -> ${timestamp} (${new Date(timestamp).toISOString()})`)
              return timestamp
            }
          }
          
          // 尝试解析相对时间（今天、明天、昨天等）
          const relativeDate = parseRelativeDate(value)
          if (relativeDate) {
            const timestamp = relativeDate.getTime()
            console.log(`[parseFieldValue] 解析相对日期: ${value} -> ${timestamp} (${relativeDate.toISOString()})`)
            return timestamp
          }
          
          console.warn(`[parseFieldValue] 无法解析日期字符串: ${value}，返回原值`)
          return value // 无法解析时返回原值
        }
        return typeof value === 'number' ? value : Date.now()
      
      case 'daterange':
        // 日期范围类型：支持字符串格式，但内部仍转换为时间戳
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value)
            if (parsed.start && parsed.end) {
              return {
                start: new Date(parsed.start).getTime(),
                end: new Date(parsed.end).getTime()
              }
            }
          } catch (e) {
            // 尝试解析形如 "2024-01-01 to 2024-01-31" 的格式
            const match = value.match(/(\d{4}-\d{2}-\d{2}).*?(\d{4}-\d{2}-\d{2})/)
            if (match) {
              return {
                start: new Date(match[1]).getTime(),
                end: new Date(match[2]).getTime()
              }
            }
          }
        }
        return value
      
      case 'money':
        // 金额类型：智能构建金额对象
        if (typeof value === 'string' || typeof value === 'number') {
          const amount = String(value)
          return {
            foreign: amount,
            foreignUnit: 'CNY',
            rate: '1',
            standard: amount,
            standardUnit: 'CNY'
          }
        }
        return value
      
      case 'number':
        // 数字类型：转换为数字
        if (typeof value === 'string') {
          const num = parseFloat(value)
          return isNaN(num) ? value : num
        }
        return value
      
      case 'switcher':
      case 'boolean':
        // 开关/布尔类型：转换为布尔值
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true' || value === '1' || value === 'yes' || value === '是'
        }
        return Boolean(value)
      
      default:
        // 如果字段类型未知，但字段名看起来像日期字段，则尝试按日期处理
        if (isLikelyDateField && typeof value === 'string') {
          console.log(`[parseFieldValue] 字段${fieldName}看起来像日期字段，尝试解析: ${value}`)
          
          const dateFormats = [
            value, // 原始格式
            value.replace(/[年月]/g, '-').replace(/[日]/g, ''), // 中文格式转换
            value.replace(/\//g, '-'), // 斜杠转横杠
          ]
          
          for (const format of dateFormats) {
            const date = new Date(format)
            if (!isNaN(date.getTime())) {
              const timestamp = date.getTime()
              console.log(`[parseFieldValue] 成功解析可能的日期字段: ${format} -> ${timestamp} (${new Date(timestamp).toISOString()})`)
              return timestamp
            }
          }
          
          // 尝试解析相对时间
          const relativeDate = parseRelativeDate(value)
          if (relativeDate) {
            const timestamp = relativeDate.getTime()
            console.log(`[parseFieldValue] 解析可能的相对日期: ${value} -> ${timestamp} (${relativeDate.toISOString()})`)
            return timestamp
          }
        }
        
        // 默认情况下，尝试解析JSON字符串
        if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
          try {
            return JSON.parse(value)
          } catch (e) {
            return value
          }
        }
        return value
    }
  } catch (error) {
    // 解析失败时返回原始值
    console.warn(`解析字段值失败: ${fieldName}`, error)
    return value
  }
}

/**
 * 获取字段类型信息
 */
export async function getFieldType(fieldName: string, bus: any): Promise<string | null> {
  try {
    console.log(`[getFieldType] 获取字段类型: ${fieldName}`)
    
    // 尝试从表单信息获取字段类型
    const formInfo = await bus.invoke('get:form:info')
    console.log(`[getFieldType] formInfo:`, formInfo)
    if (formInfo?.template) {
      const field = formInfo.template.find((f: any) => f.name === fieldName || f.field === fieldName)
      console.log(`[getFieldType] 从formInfo找到字段:`, field)
      if (field) return field.type || null
    }
    
    // 尝试从其他来源获取
    const dataSource = await bus.invoke('get:data:source')
    console.log(`[getFieldType] dataSource:`, dataSource)
    if (dataSource?.currentSpecification?.components) {
      const field = dataSource.currentSpecification.components.find((f: any) => f.name === fieldName || f.field === fieldName)
      console.log(`[getFieldType] 从dataSource找到字段:`, field)
      if (field) return field.type || null
    }
    
    // 尝试直接从bus获取单据信息
    const billValue = await bus.getValue()
    console.log(`[getFieldType] billValue:`, billValue)
    if (billValue?.form?.specificationId?.components) {
      const field = billValue.form.specificationId.components.find((f: any) => f.name === fieldName || f.field === fieldName)
      console.log(`[getFieldType] 从billValue找到字段:`, field)
      if (field) return field.type || null
    }
    
    console.warn(`[getFieldType] 无法找到字段类型: ${fieldName}`)
    return null
  } catch (error) {
    console.error(`[getFieldType] 获取字段类型失败:`, error)
    return null
  }
}

/**
 * 解析相对日期（今天、明天、昨天等）
 */
export function parseRelativeDate(dateStr: string): Date | null {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  const yesterday = new Date(today)
  yesterday.setDate(today.getDate() - 1)
  
  const lowerStr = dateStr.toLowerCase().trim()
  
  if (lowerStr.includes('今天') || lowerStr.includes('today')) {
    return today
  } else if (lowerStr.includes('明天') || lowerStr.includes('tomorrow')) {
    return tomorrow
  } else if (lowerStr.includes('昨天') || lowerStr.includes('yesterday')) {
    return yesterday
  } else if (lowerStr.includes('下周') || lowerStr.includes('next week')) {
    const nextWeek = new Date(today)
    nextWeek.setDate(today.getDate() + 7)
    return nextWeek
  } else if (lowerStr.includes('上周') || lowerStr.includes('last week')) {
    const lastWeek = new Date(today)
    lastWeek.setDate(today.getDate() - 7)
    return lastWeek
  }
  
  return null
}

/**
 * 验证字段值的有效性
 */
export function validateFieldValue(value: any, fieldType: string, fieldName: string): { valid: boolean; error?: string } {
  try {
    switch (fieldType?.toLowerCase()) {
      case 'date':
        if (typeof value === 'number') {
          const date = new Date(value)
          if (isNaN(date.getTime())) {
            return { valid: false, error: '无效的日期时间戳' }
          }
        }
        break

      case 'daterange':
        if (typeof value === 'object' && value) {
          if (!value.start || !value.end) {
            return { valid: false, error: '日期范围必须包含开始和结束时间' }
          }
          if (typeof value.start !== 'number' || typeof value.end !== 'number') {
            return { valid: false, error: '日期范围的开始和结束时间必须是时间戳' }
          }
          if (value.start >= value.end) {
            return { valid: false, error: '开始时间必须早于结束时间' }
          }
        }
        break

      case 'money':
        if (typeof value === 'object' && value) {
          if (!value.standard || !value.standardUnit) {
            return { valid: false, error: '金额对象必须包含本位币金额和币种' }
          }
          if (isNaN(parseFloat(value.standard))) {
            return { valid: false, error: '本位币金额必须是有效数字' }
          }
        }
        break

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return { valid: false, error: '字段值必须是有效数字' }
        }
        break

      case 'switcher':
      case 'boolean':
        if (typeof value !== 'boolean') {
          return { valid: false, error: '字段值必须是布尔类型' }
        }
        break
    }

    return { valid: true }
  } catch (error) {
    return { valid: false, error: `验证失败: ${error instanceof Error ? error.message : '未知错误'}` }
  }
}

/**
 * 生成明细行ID
 */
export function generateDetailId(): string {
  return `detail_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 根据字段值和字段名智能推断字段类型
 * @param value 字段值
 * @param fieldName 字段名
 * @returns 推断的字段类型
 */
function inferFieldType(value: any, fieldName: string): string {
  // 先根据字段名模式推断
  const lowerFieldName = fieldName.toLowerCase();
  
  if (lowerFieldName.includes('date') || lowerFieldName.includes('日期') || lowerFieldName.includes('时间')) {
    return 'date';
  }
  
  if (lowerFieldName.includes('money') || lowerFieldName.includes('金额') || lowerFieldName.includes('费用') || lowerFieldName.includes('报价')) {
    return 'money';
  }
  
  // 再根据值的类型推断
  if (typeof value === 'number') {
    // 如果是13位数字，很可能是时间戳
    if (value > 1000000000000) {
      return 'date';
    }
    return 'number';
  }
  
  if (typeof value === 'boolean') {
    return 'switcher';
  }
  
  if (typeof value === 'object' && value) {
    if ('start' in value && 'end' in value) {
      return 'dateRange';
    }
    if ('standard' in value || 'foreign' in value) {
      return 'money';
    }
  }
  
  // 默认为文本类型
  return 'text';
}

/**
 * 从字段名中提取显示标签
 * @param fieldName 字段名，如 "E_88080aea9796fb433000_出发地"
 * @returns 提取的标签，如 "出发地"
 */
function extractLabelFromFieldName(fieldName: string): string {
  // 如果字段名包含下划线，取最后一部分作为标签
  const parts = fieldName.split('_');
  if (parts.length > 1) {
    // 取最后一个非空部分
    const label = parts[parts.length - 1];
    if (label && label.trim()) {
      return label.trim();
    }
  }
  
  // 如果没有下划线或解析失败，返回原字段名
  return fieldName;
}

/**
 * 处理dataLinkEdits类型字段的值，构建AI友好的数据结构
 * @param fieldValue 字段的原始值（dataLinkEdits数组）
 * @param fieldComponent 字段配置信息
 * @returns AI友好的数据结构
 */
export function processDataLinkEditsValue(fieldValue: any, fieldComponent: any): any {
  console.log('[processDataLinkEditsValue] 开始处理dataLinkEdits字段:', { fieldValue, fieldComponent });
  
  if (!fieldValue || !Array.isArray(fieldValue)) {
    console.log('[processDataLinkEditsValue] 字段值为空或非数组，返回空数组');
    return [];
  }

  // 处理每个dataLinkEdits项
  return fieldValue.map((item: any, index: number) => {
    const dataLinkForm = item?.dataLinkForm || {};
    const processedItem: any = {};

    // 如果有referenceData.fields配置，则使用配置进行处理
    if (fieldComponent?.referenceData?.fields) {
      console.log('[processDataLinkEditsValue] 使用referenceData.fields配置处理');
      const supportedTypes = ['text', 'date', 'dateRange', 'number', 'money', 'textarea', 'switcher'];
      const subFields = fieldComponent.referenceData.fields.filter((field: any) => 
        !field.formula && 
        supportedTypes.includes(field.type) &&
        field.name // 确保有name字段
      );

      subFields.forEach((subField: any) => {
        const subFieldName = subField.name;
        const subFieldValue = dataLinkForm[subFieldName];

        processedItem[subFieldName] = {
          label: subField.label || subFieldName,
          type: subField.type,
          value: formatSubFieldValue(subFieldValue, subField.type)
        };
      });
    } else {
      // 如果没有字段配置，则智能推断字段类型
      console.log('[processDataLinkEditsValue] 没有referenceData.fields配置，智能推断字段类型');
      Object.keys(dataLinkForm).forEach((fieldName) => {
        const fieldValue = dataLinkForm[fieldName];
        const inferredType = inferFieldType(fieldValue, fieldName);
        const label = extractLabelFromFieldName(fieldName);

        processedItem[fieldName] = {
          label: label,
          type: inferredType,
          value: formatSubFieldValue(fieldValue, inferredType)
        };
      });
    }

    return processedItem;
  });
}

/**
 * 格式化子字段的值，使其对AI友好
 * @param value 原始值
 * @param fieldType 字段类型
 * @returns 格式化后的值
 */
function formatSubFieldValue(value: any, fieldType: string): any {
  if (value === null || value === undefined || value === '') {
    return null;
  }

  // 尝试解析JSON字符串
  if (typeof value === 'string' && (value.startsWith('[') || value.startsWith('{'))) {
    try {
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        // 如果是数组，提取其中的label或name字段
        return parsed.map(item => item.label || item.name || item).join(', ');
      } else if (typeof parsed === 'object') {
        return parsed.label || parsed.name || parsed;
      }
    } catch (e) {
      // 解析失败，继续使用原值
    }
  }

  switch (fieldType) {
    case 'date':
      if (typeof value === 'number') {
        return formatTimestampToDateString(value, 'YEAR_MONTH_DAY');
      }
      return value;

    case 'dateRange':
      if (value && typeof value === 'object' && 'start' in value && 'end' in value) {
        return {
          start: typeof value.start === 'number' ? 
            formatTimestampToDateString(value.start, 'YEAR_MONTH_DAY') : value.start,
          end: typeof value.end === 'number' ? 
            formatTimestampToDateString(value.end, 'YEAR_MONTH_DAY') : value.end
        };
      }
      return value;

    case 'money':
      return processMoneyValue(value);

    case 'switcher':
      return Boolean(value);

    default:
      return value;
  }
}

/**
 * 从formatTimestampToDateString移植的日期格式化函数
 */
function formatTimestampToDateString(timestamp: number, dateTimeType: string): string {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  switch (dateTimeType) {
    case 'YEAR_MONTH_DAY_TIME':
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    case 'YEAR_MONTH_DAY':
      return `${year}-${month}-${day}`;
    case 'YEAR_MONTH':
      return `${year}-${month}`;
    default:
      return `${year}-${month}-${day}`;
  }
}

/**
 * 从get-bill-details.tool.ts移植的金额处理函数
 */
function processMoneyValue(moneyValue: any): any {
  if (!moneyValue || typeof moneyValue !== 'object') {
    return moneyValue;
  }

  // 检查是否有foreign系列属性，表示存储了外币
  const hasForeign = moneyValue.foreign !== undefined && 
                     moneyValue.foreignStrCode !== undefined && 
                     moneyValue.foreignSymbol !== undefined;

  if (hasForeign) {
    // 外币格式：返回对象
    return {
      '原币': `${moneyValue.foreignStrCode} ${moneyValue.foreign}`,
      '汇率': moneyValue.rate || moneyValue.sysRate || '',
      '本位币': `${moneyValue.standardStrCode} ${moneyValue.standard}`
    };
  } else if (moneyValue.standardStrCode && moneyValue.standard !== undefined) {
    // 纯本位币格式：返回纯文本
    return `${moneyValue.standardStrCode} ${moneyValue.standard}`;
  }

  return moneyValue;
}

/**
 * 检查字段是否为dataLinkEdits类型
 * @param component 字段配置
 * @returns 是否为dataLinkEdits类型
 */
export function isDataLinkEditsField(component: any): boolean {
  const isTypeMatch = component?.type === 'dataLinkEdits';
  const isDataTypeListMatch = component?.dataType?.type === 'list';
  const result = isTypeMatch || isDataTypeListMatch;
  
  // 输出详细的调试信息来分析问题
  console.log(`🔍 [isDataLinkEditsField] 详细检查 ${component?.field || component?.name}:`, {
    type: component?.type,
    dataType: component?.dataType,
    dataType_type: component?.dataType?.type,
    isTypeMatch,
    isDataTypeListMatch,
    result,
    // 输出完整的component结构用于调试
    fullComponent: JSON.stringify(component, null, 2)
  });
  
  return result;
}

/**
 * 构建dataLinkEdits字段的datalinkFieldMap结构（类似AIAttachment中的逻辑）
 * @param component 字段配置
 * @returns datalinkFieldMap结构
 */
export function buildDataLinkFieldMap(component: any): { [key: string]: { label: string, type: string }[] } {
  if (!isDataLinkEditsField(component)) {
    return {};
  }

  // 过滤支持的字段类型（参考AIAttachment中的逻辑）
  const supportedTypes = ['text', 'date', 'dateRange', 'number', 'money', 'textarea', 'switcher'];
  const subFields = component.referenceData.fields
    .filter((field: any) => 
      !field.formula && 
      supportedTypes.includes(field.type)
    )
    .map((field: any) => ({ 
      label: field.label, 
      type: field.type 
    }));

  return {
    [component.label.trim()]: subFields
  };
} 