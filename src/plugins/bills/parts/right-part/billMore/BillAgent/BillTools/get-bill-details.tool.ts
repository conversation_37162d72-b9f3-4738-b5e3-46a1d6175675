/**
 * @fileoverview
 * Tool for querying details of the current bill.
 * Returns structured JSON data including field configurations and current values.
 */

import { Tool, ToolContext, ToolOutput, FunctionDeclaration, ToolType } from '../../AIAgent';
import { isDataLinkEditsField, processDataLinkEditsValue } from './utils';

/**
 * Gets a description for a given dateTimeType.
 */
const getDateFormatDescription = (dateTimeType: string): string => {
  switch (dateTimeType) {
    case 'YEAR_MONTH_DAY_TIME':
      return 'yyyy-MM-dd HH:mm';
    case 'YEAR_MONTH_DAY':
      return 'yyyy-MM-dd';
    case 'YEAR_MONTH':
      return 'yyyy-MM';
    default:
      return 'yyyy-MM-dd';
  }
};

/**
 * Formats a timestamp into a date string based on the specified format.
 */
const formatTimestampToDateString = (timestamp: number, dateTimeType: string): string => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  switch (dateTimeType) {
    case 'YEAR_MONTH_DAY_TIME':
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    case 'YEAR_MONTH_DAY':
      return `${year}-${month}-${day}`;
    case 'YEAR_MONTH':
      return `${year}-${month}`;
    default:
      return `${year}-${month}-${day}`;
  }
};

/**
 * 处理money类型的金额数据
 */
const processMoneyValue = (moneyValue: any): any => {
  if (!moneyValue || typeof moneyValue !== 'object') {
    return moneyValue;
  }

  // 检查是否有foreign系列属性，表示存储了外币
  const hasForeign = moneyValue.foreign !== undefined && 
                     moneyValue.foreignStrCode !== undefined && 
                     moneyValue.foreignSymbol !== undefined;

  if (hasForeign) {
    // 外币格式：返回对象
    return {
      '原币': `${moneyValue.foreignStrCode} ${moneyValue.foreign}`,
      '汇率': moneyValue.rate || moneyValue.sysRate || '',
      '本位币': `${moneyValue.standardStrCode} ${moneyValue.standard}`
    };
  } else if (moneyValue.standardStrCode && moneyValue.standard !== undefined) {
    // 纯本位币格式：返回纯文本
    return `${moneyValue.standardStrCode} ${moneyValue.standard}`;
  }

  return moneyValue;
};

/**
 * 处理payeeInfo类型的数据，只保留指定字段
 */
const processPayeeInfo = (payeeInfo: any): any => {
  if (!payeeInfo || typeof payeeInfo !== 'object') {
    return payeeInfo;
  }

  // 只保留指定的字段
  const {
    id,
    bank,
    city,
    branch,
    name,
    cardNo,
    type
  } = payeeInfo;

  return {
    id,
    bank,
    city,
    branch,
    name,
    cardNo,
    type
  };
};

/**
 * 通用的表单数据处理函数
 * @param form 表单数据
 * @param components 字段配置
 * @param editableOnly 是否只返回可编辑字段
 * @param isDetail 是否是费用明细（影响返回格式）
 */
const processFormData = (form: any, components: any[], editableOnly: boolean = false, isDetail: boolean = false): any => {
  if (!components || !Array.isArray(components)) {
    return {};
  }

  const relevantComponents = editableOnly
    ? components.filter((c: any) => c.readonly === false && c.editable !== false && c.type !== 'separator')
    : components;

  const result: any = {};

  relevantComponents.forEach((component: any) => {
    if (component.type === 'separator' || !component.field) {
      return;
    }
    if (component.type === 'annotation') {
      return; // 跳过注释类型
    }

    // 对于费用明细，返回简单的key-value格式
    if (isDetail) {
      let fieldValue = form?.[component.field];
      
      if (fieldValue !== undefined && fieldValue !== null) {
        // 根据字段类型处理值
        let fieldType = component.type;
        if (component.type === 'dataLink') fieldType = 'select';
        if (component.type === 'select' && component.multiple === true) fieldType = 'multiple-select';
        if (component.type === 'payeeInfo') fieldType = 'select';
        
        if (isDataLinkEditsField(component)) {
          // 特殊处理dataLinkEdits类型字段（明细中）
          console.log('[get-bill-details] 明细中检测到dataLinkEdits字段:', component.field, component.type, component.dataType);
          result[component.field] = processDataLinkEditsValue(fieldValue, component);
          console.log('[get-bill-details] 明细中处理后的dataLinkEdits值:', result[component.field]);
        } else if ((fieldType === 'select' || fieldType === 'multiple-select') && fieldValue) {
          if (Array.isArray(fieldValue)) {
            result[component.field] = fieldValue.map((item: any) => {
              if (component.type === 'payeeInfo') {
                return processPayeeInfo(item);
              }
              return { id: item.id, code: item.code, name: item.name };
            }).filter(Boolean);
          } else if (typeof fieldValue === 'object') {
            if (component.type === 'payeeInfo') {
              result[component.field] = processPayeeInfo(fieldValue);
            } else {
              result[component.field] = { id: fieldValue.id, code: fieldValue.code, name: fieldValue.name };
            }
          }
        } else if (fieldType === 'date' && typeof fieldValue === 'number') {
          result[component.field] = formatTimestampToDateString(fieldValue, component.dateTimeType || 'YEAR_MONTH_DAY');
        } else if (fieldType === 'dateRange' && fieldValue && typeof fieldValue === 'object' && 'start' in fieldValue && 'end' in fieldValue) {
          result[component.field] = {
            start: typeof fieldValue.start === 'number' ? formatTimestampToDateString(fieldValue.start, component.dateTimeType || 'YEAR_MONTH_DAY') : fieldValue.start,
            end: typeof fieldValue.end === 'number' ? formatTimestampToDateString(fieldValue.end, component.dateTimeType || 'YEAR_MONTH_DAY') : fieldValue.end
          };
        } else if (component.type === 'money' && fieldValue && typeof fieldValue === 'object') {
          result[component.field] = processMoneyValue(fieldValue);
        } else {
          result[component.field] = fieldValue;
        }
      } else {
        result[component.field] = null;
      }
    } else {
      // 对于单据表头，返回包含type和label的详细格式
      const fieldInfo: any = {
        type: component.type,
        label: component.label,
      };

      // 添加字段约束信息
      if (component.placeholder) {
        fieldInfo.placeholder = component.placeholder;
      }

      const constraints = [];
      if (component.minLength !== undefined) constraints.push(`最少${component.minLength}字符`);
      if (component.maxLength !== undefined) constraints.push(`最多${component.maxLength}字符`);
      if (component.min !== undefined) constraints.push(`最小值${component.min}`);
      if (component.max !== undefined) constraints.push(`最大值${component.max}`);
      if (component.dateTimeType) constraints.push(`格式: ${getDateFormatDescription(component.dateTimeType)}`);
      if (constraints.length > 0) {
        fieldInfo.constraints = constraints.join('，');
      }

      // 处理字段值
      let fieldValue = form?.[component.field];
      
      if (component.field === 'details' && component.type === 'details') {
        // 特殊处理费用明细
        const details = form?.details || [];
        fieldInfo.value = processDetailItems(details);
      } else if (isDataLinkEditsField(component)) {
        // 特殊处理dataLinkEdits类型字段
        console.log('[get-bill-details] 检测到dataLinkEdits字段:', component.field, component.type, component.dataType);
        fieldInfo.type = 'dataLinkEdits';
        fieldInfo.value = processDataLinkEditsValue(fieldValue, component);
        console.log('[get-bill-details] 处理后的dataLinkEdits值:', fieldInfo.value);
      } else if (fieldValue !== undefined && fieldValue !== null) {
        // 处理其他字段类型
        let fieldType = component.type;
        if (component.type === 'dataLink') fieldType = 'select';
        if (component.type === 'select' && component.multiple === true) fieldType = 'multiple-select';
        if (component.type === 'payeeInfo') fieldType = 'select';
        
        if ((fieldType === 'select' || fieldType === 'multiple-select') && fieldValue) {
          if (Array.isArray(fieldValue)) {
            fieldInfo.value = fieldValue.map((item: any) => {
              if (component.type === 'payeeInfo') {
                return processPayeeInfo(item);
              }
              return { id: item.id, code: item.code, name: item.name };
            }).filter(Boolean);
          } else if (typeof fieldValue === 'object') {
            if (component.type === 'payeeInfo') {
              fieldInfo.value = processPayeeInfo(fieldValue);
            } else {
              fieldInfo.value = { id: fieldValue.id, code: fieldValue.code, name: fieldValue.name };
            }
          }
        } else if (fieldType === 'date' && typeof fieldValue === 'number') {
          fieldInfo.value = formatTimestampToDateString(fieldValue, component.dateTimeType || 'YEAR_MONTH_DAY');
        } else if (fieldType === 'dateRange' && fieldValue && typeof fieldValue === 'object' && 'start' in fieldValue && 'end' in fieldValue) {
          fieldInfo.value = {
            start: typeof fieldValue.start === 'number' ? formatTimestampToDateString(fieldValue.start, component.dateTimeType || 'YEAR_MONTH_DAY') : fieldValue.start,
            end: typeof fieldValue.end === 'number' ? formatTimestampToDateString(fieldValue.end, component.dateTimeType || 'YEAR_MONTH_DAY') : fieldValue.end
          };
        } else if (component.type === 'money' && fieldValue && typeof fieldValue === 'object') {
          fieldInfo.value = processMoneyValue(fieldValue);
        } else {
          fieldInfo.value = fieldValue;
        }
      } else {
        fieldInfo.value = null;
      }

      result[component.field] = fieldInfo;
    }
  });

  return result;
};

/**
 * 处理费用明细数据
 */
const processDetailItems = (details: any[]): any[] => {
  if (!Array.isArray(details)) return [];
  
  return details.map((item, index) => {
    const detailResult: any = {
      detailNo: index + 1,
      detailId: item.id || item.detailId || item.feeTypeForm?.detailId || `detail_${index}`,
    };

    // 处理费用类型信息
    if (item.feeTypeForm?.feeTypeId) {
      const feeTypeId = item.feeTypeForm.feeTypeId;
      detailResult.feeType = {
        id: feeTypeId.id,
        name: feeTypeId.name,
        fullname: feeTypeId.fullname,
        description: feeTypeId.description
      };
    }

    // 使用通用函数处理费用明细的字段
    if (item.specificationId?.components) {
      const detailFields = processFormData(item.feeTypeForm, item.specificationId.components, false, true);
      Object.assign(detailResult, detailFields);
    }

    return detailResult;
  });
};

export const getBillDetailsTool: Tool = {
  type: ToolType.VIEW,
  definition: {
    name: 'get_bill_details',
    description: '查询当前单据的详细信息，返回结构化的字段配置和当前值。可以指定是否只返回可编辑的字段。',
    parameters: {
      type: 'OBJECT',
      properties: {
        description: {
          type: 'STRING',
          description: '用简要语言描述这次工具调用的目的，不超过25个字'
        },
        editableOnly: {
          type: 'BOOLEAN',
          description: '如果为true，则只返回可编辑的字段信息；否则返回所有字段信息。默认为false。'
        }
      },
      required: ['description']
    }
  },
  execute: async (args: { description: string; editableOnly?: boolean }, context: ToolContext): Promise<ToolOutput> => {
    console.log('🔧 [get-bill-details] 工具开始执行，参数:', args);
    
    const { billDetails } = context;
    const { editableOnly = false } = args;
    console.log(billDetails);
    if (!billDetails) {
      console.log('❌ [get-bill-details] 未找到单据信息');
      return { isSuccess: false, error: '上下文中未找到单据信息，无法查询。' };
    }

    try {
      let components = billDetails?.form?.specificationId?.components;
      if (!components) {
        components = billDetails?.currentSpecification?.components;
      }
      if (!components || !Array.isArray(components)) {
        console.log('❌ [get-bill-details] 无法获取字段配置信息');
        return { isSuccess: false, error: '无法获取单据的字段配置信息。' };
      }

      console.log('📋 [get-bill-details] 开始处理单据表头数据，字段数量:', components.length);
      
      // 检查是否有dataLinkEdits字段
      const dataLinkEditsFields = components.filter(c => isDataLinkEditsField(c));
      if (dataLinkEditsFields.length > 0) {
        console.log('🔗 [get-bill-details] 发现dataLinkEdits字段:', dataLinkEditsFields.map(f => f.field));
      } else {
        console.log('ℹ️ [get-bill-details] 未发现dataLinkEdits字段');
      }

      // 使用通用函数处理单据表头数据
      const result = processFormData(billDetails.form, components, editableOnly, false);

      console.log('✅ [get-bill-details] 工具执行成功，返回字段数量:', Object.keys(result).length);
      return { isSuccess: true, data: result };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('❌ [get-bill-details] 执行失败:', errorMessage);
      return { isSuccess: false, error: `生成单据详情时发生错误: ${errorMessage}` };
    }
  }
}; 

/**
 * dataLinkEdits字段处理说明：
 * 
 * 对于dataLinkEdits类型的字段，系统会：
 * 1. 识别字段类型为 dataLinkEdits 且 dataType.type 为 'list'
 * 2. 从 referenceData.fields 中获取子字段配置
 * 3. 构建AI友好的数据结构，格式如下：
 * 
 * "fieldName": {
 *   "type": "dataLinkEdits",
 *   "label": "字段标签",
 *   "value": [
 *     {
 *       "subFieldName1": { "label": "子字段1", "type": "text", "value": "实际值1" },
 *       "subFieldName2": { "label": "子字段2", "type": "money", "value": "CNY 100.00" }
 *     }
 *   ]
 * }
 * 
 * 这种结构使AI能够：
 * - 理解dataLinkEdits字段的子字段结构
 * - 获取每个子字段的当前值和类型信息
 * - 在set-field-value工具中正确处理字段更新
 */ 