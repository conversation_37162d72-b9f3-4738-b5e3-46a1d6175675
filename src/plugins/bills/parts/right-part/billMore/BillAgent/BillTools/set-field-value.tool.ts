/**
 * @fileoverview
 * 基于BillInfoEditable组件的bus.setFieldsValue机制
 * Tool for setting the value of one or more fields in the current bill form.
 */

import { Tool, ToolContext, ToolOutput, validateArgs, safeExecute, createSuccessResult, createErrorResult, FunctionDeclaration, ToolType } from '../../AIAgent';
import { parseFieldValue, getFieldType, validateFieldValue } from './utils';
import type { BillToolsDependencies } from './index';

// 工具依赖接口
interface SetFieldValueDependencies {
  // 目前不需要额外的静态依赖
}

/**
 * 比较两个值是否相等（支持简单的深度比较）
 */
function isValueEqual(actual: any, expected: any): boolean {
  // 严格相等
  if (actual === expected) {
    return true;
  }
  
  // null 和 undefined 处理
  if (actual == null || expected == null) {
    return actual == expected;
  }
  
  // 类型不同
  if (typeof actual !== typeof expected) {
    return false;
  }
  
  // 对象比较（简单的深度比较）
  if (typeof actual === 'object' && typeof expected === 'object') {
    // 数组比较
    if (Array.isArray(actual) && Array.isArray(expected)) {
      if (actual.length !== expected.length) {
        return false;
      }
      return actual.every((item, index) => isValueEqual(item, expected[index]));
    }
    
    // 普通对象比较
    if (!Array.isArray(actual) && !Array.isArray(expected)) {
      const actualKeys = Object.keys(actual).sort();
      const expectedKeys = Object.keys(expected).sort();
      
      if (actualKeys.length !== expectedKeys.length) {
        return false;
      }
      
      if (!actualKeys.every(key => expectedKeys.includes(key))) {
        return false;
      }
      
      return actualKeys.every(key => isValueEqual(actual[key], expected[key]));
    }
    
    return false;
  }
  
  // 字符串形式的数字比较
  if (typeof actual === 'string' && typeof expected === 'number') {
    return parseFloat(actual) === expected;
  }
  if (typeof actual === 'number' && typeof expected === 'string') {
    return actual === parseFloat(expected);
  }
  
  return false;
}

export function createSetFieldValueTool(dependencies: SetFieldValueDependencies): Tool {
  const definition: FunctionDeclaration = {
    name: 'set_field_value',
    description: '根据用户的指令，批量更新单据中一个或多个字段的值。此工具是修改字段值的唯一方式，支持同时设置多个字段以提高效率。',
    parameters: {
      type: 'OBJECT',
      properties: {
        description: {
          type: 'STRING',
          description: "根据用户的意图，用一句话总结本次操作的目的。例如：'修改标题和费用承担部门' 或 '批量更新单据基础信息'。这个描述应该简洁地说明操作范围。"
        },
        fields: {
          type: 'ARRAY',
          description: '要更新的字段列表。每个字段包含字段名、值和相关配置。',
          items: {
            type: 'OBJECT',
            properties: {
              fieldName: {
                type: 'STRING',
                description: '需要修改的字段的唯一标识符（英文名）。请务必确保要更新的字段在当前单据中是可编辑的，你可以通过get_bill_details工具获取当前单据的可编辑字段列表。'
              },
              value: {
                description: "要为字段设置的具体值。它的格式严格依赖于该字段的类型（在系统指令的元数据中已定义）。请根据字段类型选择下面 `oneOf` 中最合适的格式来构建 `value`。",
                oneOf: [
                  // 1. 文本/多行文本 (text/textarea)
                  {
                    title: "文本值",
                    type: "STRING",
                    description: "用于 text 或 textarea 类型的字段。这是一个纯文本字符串。"
                  },
                  // 2. 日期 (date)
                  {
                    title: "日期字符串",
                    type: "STRING",
                    description: "用于 date 类型的字段。使用易读的日期格式，如：'2024-04-06'（年月日）或 '2024-06-07 18:24'（年月日时分）。具体格式请参考字段说明中的要求。"
                  },
                  // 3. 日期范围 (dateRange)
                  {
                    title: "日期范围对象",
                    type: "OBJECT",
                    description: "用于 dateRange 类型的字段。",
                    properties: {
                      start: { type: "STRING", description: "范围开始日期，使用易读格式如 '2024-04-06' 或 '2024-06-07 18:24'。" },
                      end: { type: "STRING", description: "范围结束日期，使用易读格式如 '2024-04-06' 或 '2024-06-07 18:24'。" }
                    },
                    required: ["start", "end"]
                  },
                  // 4. 开关 (switcher)
                  {
                    title: "布尔开关",
                    type: "BOOLEAN",
                    description: "用于 switcher 类型的字段，值为 true 或 false。"
                  },
                  // 5. 数字 (number)
                  {
                    title: "数字值",
                    type: "NUMBER",
                    description: "用于 number 类型的字段。注意，即使系统指令中显示为字符串，这里也应提供一个纯数字。"
                  },
                  // 6. 金额 (money)
                  {
                    title: "金额对象",
                    type: "OBJECT",
                    description: "用于 money 类型的字段。模型应根据用户输入智能填充。如果用户只提供了单一数额，则将其作为原币金额。如果原币与本位币相同，则可省略原币相关字段。",
                    properties: {
                      foreign: { type: "STRING", description: "原币金额，以字符串格式表示。例如 '100.50'。" },
                      foreignUnit: { type: "STRING", description: "原币币种代码, 例如 'USD', 'JPY'。" },
                      rate: { type: "STRING", description: "汇率，以字符串格式表示。" },
                      standard: { type: "STRING", description: "本位币金额，以字符串格式表示。" },
                      standardUnit: { type: "STRING", description: "本位币币种代码, 总是 'CNY'。" }
                    },
                    required: ["standard", "standardUnit"] // 本位币是必须的
                  }
                ]
              },
              isDetail: {
                type: 'BOOLEAN',
                description: '是否为明细行字段。对于当前列表中的所有字段，此值都应为 false。'
              },
              detailIndex: {
                type: 'NUMBER',
                description: '仅在 isDetail 为 true 时使用，指定要更新的明细行索引。'
              }
            },
            required: ['fieldName', 'value']
          }
        }
      },
      required: ['description', 'fields']
    }
  };

  const execute = async (args: any, context: ToolContext) => {
    return await safeExecute(async () => {
      // 验证参数
      const validation = validateArgs(args, ['description', 'fields']);
      if (validation) {
        return createErrorResult(validation);
      }

      const { description, fields } = args;
      const { bus } = context;

      if (!bus) {
        return createErrorResult('缺少bus对象，无法设置字段值');
      }

      if (!Array.isArray(fields) || fields.length === 0) {
        return createErrorResult('fields 参数必须是非空数组');
      }

      // 分离表头字段和明细字段
      const headerFields: any = {};
      const detailUpdates: { [detailIndex: number]: any } = {};
      const results: any[] = [];

      // 验证和解析所有字段值
      for (const field of fields) {
        const { fieldName, value, isDetail = false, detailIndex } = field;

        if (!fieldName || value === undefined) {
          return createErrorResult(`字段 ${fieldName || '(未知)'} 缺少必要参数`);
        }

        if (isDetail && typeof detailIndex !== 'number') {
          return createErrorResult(`明细字段 ${fieldName} 必须指定 detailIndex`);
        }

        // 智能解析字段值
        const parsedValue = await parseFieldValue(value, fieldName, bus);
        
        // 验证解析后的值
        const fieldType = await getFieldType(fieldName, bus);
        if (fieldType) {
          const validation = validateFieldValue(parsedValue, fieldType, fieldName);
          if (!validation.valid) {
            return createErrorResult(`字段 ${fieldName} 值验证失败: ${validation.error}`);
          }
        }

        if (isDetail) {
          // 收集明细字段更新
          if (!detailUpdates[detailIndex]) {
            detailUpdates[detailIndex] = {};
          }
          detailUpdates[detailIndex][fieldName] = parsedValue;
        } else {
          // 收集表头字段更新
          headerFields[fieldName] = parsedValue;
        }

        results.push({
          fieldName,
          value: parsedValue,
          originalValue: value,
          isDetail,
          detailIndex
        });
      }

      // 批量执行表头字段更新
      if (Object.keys(headerFields).length > 0) {
        try {
          bus.setFieldsValue(headerFields);
          
          // 验证更新是否成功：获取最新值并比较
          const updatedValue = await bus.getValue();
          const failedFields = [];
          
          for (const [fieldName, expectedValue] of Object.entries(headerFields)) {
            const actualValue = updatedValue?.[fieldName];
            
            // 简单的值比较（对于复杂对象，可能需要深度比较）
            if (!isValueEqual(actualValue, expectedValue)) {
              failedFields.push({
                fieldName,
                expectedValue,
                actualValue,
                reason: '设置后的值与期望值不一致'
              });
            }
          }
          
          if (failedFields.length > 0) {
            console.warn('部分表头字段更新失败:', failedFields);
            return createErrorResult(`表头字段更新失败: ${failedFields.map(f => f.fieldName).join(', ')}`);
          }
        } catch (error) {
          console.error('表头字段更新异常:', error);
          return createErrorResult(`表头字段更新异常: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }

      // 批量执行明细字段更新
      if (Object.keys(detailUpdates).length > 0) {
        try {
          const currentValue = await bus.getValue();
          if (currentValue?.details && Array.isArray(currentValue.details)) {
            const details = currentValue.details.slice();
            const failedUpdates = [];
            
            for (const [detailIndexStr, updates] of Object.entries(detailUpdates)) {
              const detailIndex = parseInt(detailIndexStr, 10);
              if (details[detailIndex]) {
                if (!details[detailIndex].feeTypeForm) {
                  details[detailIndex].feeTypeForm = {};
                }
                Object.assign(details[detailIndex].feeTypeForm, updates);
              } else {
                failedUpdates.push(`明细行索引 ${detailIndex} 不存在`);
              }
            }
            
            if (failedUpdates.length > 0) {
              return createErrorResult(failedUpdates.join('; '));
            }
            
            // 触发明细变化事件
            bus.emit('details:change', details);
            
            // 验证明细字段更新是否成功
            const verificationValue = await bus.getValue();
            const detailFailures = [];
            
            for (const [detailIndexStr, updates] of Object.entries(detailUpdates)) {
              const detailIndex = parseInt(detailIndexStr, 10);
              const actualDetail = verificationValue?.details?.[detailIndex];
              
              if (actualDetail?.feeTypeForm) {
                for (const [fieldName, expectedValue] of Object.entries(updates)) {
                  const actualValue = actualDetail.feeTypeForm[fieldName];
                  if (!isValueEqual(actualValue, expectedValue)) {
                    detailFailures.push({
                      detailIndex,
                      fieldName,
                      expectedValue,
                      actualValue
                    });
                  }
                }
              } else {
                detailFailures.push({
                  detailIndex,
                  reason: '明细行数据结构异常'
                });
              }
            }
            
            if (detailFailures.length > 0) {
              console.warn('部分明细字段更新失败:', detailFailures);
              return createErrorResult(`明细字段更新失败: ${detailFailures.length} 个字段验证失败`);
            }
          } else {
            return createErrorResult('单据明细数据不存在');
          }
        } catch (error) {
          console.error('明细字段更新异常:', error);
          return createErrorResult(`明细字段更新异常: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }

      return createSuccessResult({ 
        description,
        updatedFields: results,
        headerFieldsCount: Object.keys(headerFields).length,
        detailFieldsCount: Object.keys(detailUpdates).length,
        totalFieldsCount: fields.length
      });
    }, '批量设置字段值失败');
  };

  return { type: ToolType.EDIT, definition, execute };
} 