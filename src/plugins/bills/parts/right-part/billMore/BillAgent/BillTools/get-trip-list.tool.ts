/**
 * @fileoverview
 * WIP: 此工具文件暂时不提供，开发中...
 * 
 * Tool for querying current bill's trip list (行程规划).
 * Returns structured JSON data including trip details with human-readable format.
 */

import { Tool, ToolContext, ToolOutput, FunctionDeclaration, ToolType } from '../../AIAgent';
import { isDataLinkEditsField, processDataLinkEditsValue } from './utils';

/**
 * 行程类型映射表 - 英文到中文
 */
const TRIP_TYPE_MAP: Record<string, string> = {
  FLIGHT: '飞机',
  TRAIN: '火车', 
  HOTEL: '酒店',
  TAXI: '用车',
  FOOD: '餐饮',
  COMMON: '通用'
};

/**
 * 格式化时间戳为易读的日期字符串
 */
const formatTimestamp = (timestamp: number): string => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 解析城市信息
 */
const parseCityInfo = (cityData: string): string => {
  if (!cityData) return '';
  try {
    const cities = JSON.parse(cityData);
    if (Array.isArray(cities) && cities.length > 0) {
      if (cities.length === 1) {
        return cities[0].label || cities[0].name || '';
      } else {
        const mainCity = cities[0].label || cities[0].name || '';
        return `${mainCity}(+${cities.length - 1})`;
      }
    }
  } catch (e) {
    console.warn('解析城市信息失败:', e);
  }
  return cityData;
};

/**
 * 提取字段名的最后部分（去除前缀）
 */
const getFieldSuffix = (fieldName: string): string => {
  const lastUnderscoreIndex = fieldName.lastIndexOf('_');
  return lastUnderscoreIndex !== -1 ? fieldName.substring(lastUnderscoreIndex + 1) : fieldName;
};

/**
 * 处理单个行程数据
 */
const processTripData = (trip: any, index: number, templates: any[] = []): any => {
  const { dataLinkId, dataLinkTemplateId, dataLinkForm } = trip;
  
  // 通过模板ID查找行程类型
  let tripType = '';
  const template = templates.find(t => t.templateId === dataLinkTemplateId);
  if (template?.entity?.type) {
    tripType = TRIP_TYPE_MAP[template.entity.type] || template.entity.type;
  }

  // 解析dataLinkForm中的字段
  const formData: any = {};
  if (dataLinkForm && typeof dataLinkForm === 'object') {
    Object.keys(dataLinkForm).forEach(key => {
      const suffix = getFieldSuffix(key);
      formData[suffix] = dataLinkForm[key];
    });
  }

  // 提取核心信息
  const startCity = parseCityInfo(formData['出发地'] || formData['住宿地'] || '');
  const endCity = parseCityInfo(formData['目的地'] || '');
  const startDate = formatTimestamp(formData['行程日期'] || formData['入住日期'] || 0);
  const endDate = formatTimestamp(formData['离店日期'] || 0);
  const referencePrice = formData['参考报价'] || 0;
  const tripName = formData['name'] || '';
  
  // 解析场景信息
  let scenes: string[] = [];
  try {
    const sceneData = formData['场景'];
    if (sceneData && typeof sceneData === 'string') {
      const parsedScenes = JSON.parse(sceneData);
      if (Array.isArray(parsedScenes)) {
        scenes = parsedScenes.map(s => s.sceneName || s.name || '').filter(Boolean);
      }
    }
  } catch (e) {
    console.warn('解析场景信息失败:', e);
  }

  // 构建人类可读的描述
  let description = '';
  if (tripType) {
    description = `${tripType}行程`;
    if (startCity && endCity) {
      description += `：${startCity} → ${endCity}`;
    } else if (startCity) {
      description += `：${startCity}`;
    }
    
    if (startDate && endDate && startDate !== endDate) {
      description += `，${startDate} 至 ${endDate}`;
    } else if (startDate) {
      description += `，${startDate}`;
    }
    
    if (referencePrice > 0) {
      const unit = tripType === '酒店' ? '晚' : '张';
      description += `，参考报价${referencePrice}元/${unit}`;
    }
  }

  return {
    index: index + 1,
    id: dataLinkId,
    templateId: dataLinkTemplateId,
    type: tripType,
    name: tripName,
    description,
    details: {
      startCity,
      endCity,
      startDate,
      endDate,
      referencePrice,
      scenes,
      raw: formData
    }
  };
};

/**
 * 查询行程列表工具的定义
 */
const definition: FunctionDeclaration = {
  name: 'get_trip_list',
  description: '查询当前单据的行程规划列表，返回所有行程的详细信息，包括行程类型、城市、日期、价格等。',
  parameters: {
    type: 'OBJECT',
    properties: {
      includeDetails: {
        type: 'BOOLEAN',
        description: '是否包含详细信息。true返回完整信息，false只返回基本摘要。默认为true。'
      }
    }
  }
};

/**
 * 执行查询行程列表的逻辑
 */
const execute = async (args: any, context: ToolContext): Promise<ToolOutput> => {
  try {
    const { includeDetails = true } = args;
    const { bus } = context;

    if (!bus) {
      return {
        type: ToolType.FUNCTION,
        result: {
          success: false,
          error: '缺少bus对象，无法查询行程信息'
        }
      };
    }

    console.log('[get-trip-list] 开始查询行程列表');

    // 获取当前单据数据
    let billValue: any;
    try {
      billValue = await bus.getValue();
      console.log('[get-trip-list] 获取到单据数据:', billValue);
    } catch (error) {
      console.error('[get-trip-list] 获取单据数据失败:', error);
      return {
        type: ToolType.FUNCTION,
        result: {
          success: false,
          error: '无法获取当前单据数据'
        }
      };
    }

    // 查找行程规划字段
    const tripFieldName = 'u_行程规划';
    let tripList: any[] = [];
    
    // 从表头查找
    if (billValue?.form && billValue.form[tripFieldName]) {
      tripList = billValue.form[tripFieldName];
      console.log('[get-trip-list] 从表头找到行程:', tripList);
    } 
    // 从明细查找
    else if (billValue?.form?.details && Array.isArray(billValue.form.details)) {
      for (const detail of billValue.form.details) {
        if (detail.feeTypeForm && detail.feeTypeForm[tripFieldName]) {
          tripList = detail.feeTypeForm[tripFieldName];
          console.log('[get-trip-list] 从明细找到行程:', tripList);
          break;
        }
      }
    }

    if (!Array.isArray(tripList)) {
      console.log('[get-trip-list] 行程数据不是数组:', tripList);
      return {
        type: ToolType.FUNCTION,
        result: {
          success: true,
          tripCount: 0,
          trips: [],
          summary: '当前单据暂无行程规划'
        }
      };
    }

    if (tripList.length === 0) {
      return {
        type: ToolType.FUNCTION,
        result: {
          success: true,
          tripCount: 0,
          trips: [],
          summary: '当前单据暂无行程规划'
        }
      };
    }

    // 获取模板信息用于类型映射
    let templates: any[] = [];
    try {
      // 尝试从单据规格中获取模板信息
      const components = billValue?.form?.specificationId?.components || [];
      const tripField = components.find((c: any) => c.field === tripFieldName);
      if (tripField?.referenceData?.id) {
        // 这里可以调用相关接口获取模板信息，暂时使用空数组
        console.log('[get-trip-list] 找到行程字段配置:', tripField);
      }
    } catch (error) {
      console.warn('[get-trip-list] 获取模板信息失败:', error);
    }

    // 处理行程数据
    const processedTrips = tripList.map((trip, index) => 
      processTripData(trip, index, templates)
    ).filter(Boolean);

    // 生成摘要
    const typeCounts: Record<string, number> = {};
    processedTrips.forEach(trip => {
      if (trip.type) {
        typeCounts[trip.type] = (typeCounts[trip.type] || 0) + 1;
      }
    });

    const typeDescriptions = Object.entries(typeCounts)
      .map(([type, count]) => `${type}${count}项`)
      .join('、');
    
    const summary = `共${processedTrips.length}项行程${typeDescriptions ? `，包含${typeDescriptions}` : ''}`;

    console.log('[get-trip-list] 查询完成，共找到', processedTrips.length, '项行程');

    return {
      type: ToolType.FUNCTION,
      result: {
        success: true,
        tripCount: processedTrips.length,
        summary,
        trips: includeDetails ? processedTrips : processedTrips.map(trip => ({
          index: trip.index,
          type: trip.type,
          description: trip.description
        }))
      }
    };

  } catch (error) {
    console.error('[get-trip-list] 执行失败:', error);
    return {
      type: ToolType.FUNCTION,
      result: {
        success: false,
        error: `查询行程列表失败: ${error instanceof Error ? error.message : String(error)}`
      }
    };
  }
};

/**
 * 导出行程查询工具
 */
export const getTripListTool: Tool = {
  definition,
  execute
}; 