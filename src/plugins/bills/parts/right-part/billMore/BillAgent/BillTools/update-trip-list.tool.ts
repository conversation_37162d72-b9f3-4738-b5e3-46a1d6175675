/**
 * @fileoverview
 * WIP: 此工具文件暂时不提供，开发中...
 * 
 * Tool for updating bill's trip list (行程规划).
 * Supports adding, editing, and deleting trips in the current bill.
 */

import { Tool, ToolContext, ToolOutput, FunctionDeclaration, ToolType, validateArgs, safeExecute, createSuccessResult, createErrorResult } from '../../AIAgent';
import { generateDetailId } from './utils';
import type { BillToolsDependencies } from './index';
import { app as api } from '@ekuaibao/whispered';

/**
 * 行程类型映射表 - 中文到英文
 */
const TRIP_TYPE_REVERSE_MAP: Record<string, string> = {
  '飞机': 'FLIGHT',
  '火车': 'TRAIN', 
  '酒店': 'HOTEL',
  '用车': 'TAXI',
  '餐饮': 'FOOD',
  '通用': 'COMMON'
};

/**
 * 获取行程模板配置
 */
const getTripTemplates = async (specificationReferenceId: string): Promise<any[]> => {
  console.log('getTripTemplates', specificationReferenceId);
  try {
    const result = await api.invokeService('@bills:get:getDataLinkEditTemplate', {
      id: specificationReferenceId,
      type: true, // INSERT 行为对应 true
    });
    console.log('getTripTemplates result', result);
    if (result.items && Array.isArray(result.items)) {
      return result.items;
    }
    console.log('getTripTemplates result2', result);
    
    return [];
  } catch (error) {
    console.error('[update-trip-list] 获取模板配置失败:', error);
    return [];
  }
};

/**
 * 根据行程类型查找匹配的模板ID
 */
const findTemplateIdByTripType = (templates: any[], tripType: string): string | null => {
  console.log('findTemplateIdByTripType', templates, tripType);
  const template = templates.find(t => 
    t.entity && t.entity.type === tripType
  );
  
  return template ? template.templateId : null;
};

/**
 * 解析日期字符串为时间戳
 */
const parseDate = (dateStr: string): number => {
  if (!dateStr) return 0;
  
  // 尝试多种日期格式
  const formats = [
    dateStr,
    dateStr.replace(/[年月]/g, '-').replace(/[日]/g, ''),
    dateStr.replace(/\//g, '-'),
  ];
  
  for (const format of formats) {
    const date = new Date(format);
    if (!isNaN(date.getTime())) {
      return date.getTime();
    }
  }
  
  console.warn('无法解析日期:', dateStr);
  return 0;
};

/**
 * 构建城市数据
 */
const buildCityData = (cityData: any): string => {
  if (!cityData) return '[]';
  
  // 如果是字符串（兼容旧格式），转换为对象数组
  if (typeof cityData === 'string') {
    return JSON.stringify([{
      key: '0',
      label: cityData,
      enLabel: cityData
    }]);
  }
  
  // 如果是单个城市对象
  if (cityData.key && cityData.label) {
    return JSON.stringify([{
      key: cityData.key,
      label: cityData.label,
      enLabel: cityData.label
    }]);
  }
  
  // 如果是城市对象数组
  if (Array.isArray(cityData)) {
    const cityObjects = cityData.map((city, index) => {
      // 如果是城市对象，直接使用
      if (city.key && city.label) {
        return {
          key: city.key,
          label: city.label,
          enLabel: city.label
        };
      }
      // 如果是字符串，转换为对象（兼容旧格式）
      if (typeof city === 'string') {
        return {
          key: String(index + 1),
          label: city,
          enLabel: city
        };
      }
      return city;
    });
    return JSON.stringify(cityObjects);
  }
  
  return '[]';
};

/**
 * 生成行程名称
 */
const generateTripName = (startCity: any, endCity: any): string => {
  // 获取城市名称，去掉"市区"字眼
  const getCityName = (cityData: any): string => {
    if (!cityData) return '';
    
    let cityName = '';
    if (typeof cityData === 'string') {
      cityName = cityData;
    } else if (cityData.label) {
      cityName = cityData.label;
    } else if (Array.isArray(cityData) && cityData.length > 0 && cityData[0].label) {
      cityName = cityData[0].label;
    }
    
    // 去掉"市区"字眼
    return cityName.replace(/市区$/g, '');
  };
  
  const startCityName = getCityName(startCity);
  const endCityName = getCityName(endCity);
  
  // 如果两个城市都有值且不同，显示为 "startCity - endCity"
  if (startCityName && endCityName && startCityName !== endCityName) {
    return `${startCityName} - ${endCityName}`;
  }
  
  // 如果相同或者只有一个城市有值，显示城市名
  return startCityName || endCityName || '';
};

/**
 * 构建行程表单数据
 */
const buildTripFormData = (trip: any, fieldPrefix: string): any => {
  const formData: any = {};
  
  // 自动生成行程名称
  const tripName = generateTripName(trip.startCity, trip.endCity);
  if (tripName) formData[`${fieldPrefix}_name`] = tripName;
  
  // 基础信息
  if (trip.scene) formData[`${fieldPrefix}_场景`] = trip.scene;
  if (trip.referencePrice !== undefined) formData[`${fieldPrefix}_参考报价`] = Number(trip.referencePrice);
  
  // 固定字段：场景字段设置为空字符串
  formData[`${fieldPrefix}场景`] = "";
  
  // 城市信息
  if (trip.startCity) {
    formData[`${fieldPrefix}_出发地`] = buildCityData(trip.startCity);
    formData[`${fieldPrefix}_住宿地`] = buildCityData(trip.startCity);
  }
  if (trip.endCity) {
    formData[`${fieldPrefix}_目的地`] = buildCityData(trip.endCity);
  }
  
  // 日期信息
  if (trip.startDate) {
    const startTimestamp = parseDate(trip.startDate);
    formData[`${fieldPrefix}_行程日期`] = startTimestamp;
    formData[`${fieldPrefix}_入住日期`] = startTimestamp;
  }
  if (trip.endDate) {
    formData[`${fieldPrefix}_离店日期`] = parseDate(trip.endDate);
  }
  
  return formData;
};

/**
 * 创建新行程数据
 */
const createNewTrip = (trip: any, templateId: string, fieldPrefix: string): any => {
  return {
    dataLinkId: null,
    dataLinkTemplateId: templateId,
    dataLinkForm: buildTripFormData(trip, fieldPrefix)
  };
};

export function createUpdateTripListTool(dependencies: BillToolsDependencies): Tool {
  const definition: FunctionDeclaration = {
    name: 'update_trip_list',
    description: '修改当前单据的行程规划列表，支持同时进行新增、修改、删除操作。操作完成后会自动保存到单据中。城市信息（key和label）可以通过search_city工具获取。行程名称会根据城市信息自动生成。',
    parameters: {
      type: 'OBJECT',
      properties: {
        description: {
          type: 'STRING',
          description: '操作描述，简要说明本次修改的内容，如"新增北京到上海的飞机行程"、"删除第2项酒店行程"等。'
        },
        operations: {
          type: 'ARRAY',
          description: '要执行的操作列表，可以包含多个增删改操作。',
          items: {
            type: 'OBJECT',
            properties: {
              type: {
                type: 'STRING',
                enum: ['add', 'update', 'delete'],
                description: '操作类型：add-新增行程，update-修改行程，delete-删除行程'
              },
              index: {
                type: 'NUMBER',
                description: '行程索引（从1开始）。update和delete操作必须指定，add操作可选（指定插入位置）。'
              },
              trip: {
                type: 'OBJECT',
                description: '行程数据，add和update操作需要提供。',
                properties: {
                  type: {
                    type: 'STRING',
                    enum: ['飞机', '火车', '酒店', '用车', '餐饮', '通用'],
                    description: '行程类型'
                  },

                  startCity: {
                    oneOf: [
                      { 
                        type: 'OBJECT',
                        description: '出发城市信息（单个城市）；如果是酒店/餐饮/用车，则表示消费城市',
                        properties: {
                          key: { type: 'STRING', description: '城市ID，可通过search_city工具获取' },
                          label: { type: 'STRING', description: '城市显示名称（name），可通过search_city工具获取' }
                        },
                        required: ['key', 'label']
                      },
                      { 
                        type: 'ARRAY', 
                        description: '多个出发城市；如果是酒店/餐饮/用车，则表示消费城市',
                        items: {
                          type: 'OBJECT',
                          properties: {
                            key: { type: 'STRING', description: '城市ID，可通过search_city工具获取' },
                            label: { type: 'STRING', description: '城市显示名称（name），可通过search_city工具获取' }
                          },
                          required: ['key', 'label']
                        }
                      },
                      { type: 'STRING', description: '城市名称（兼容格式，建议使用对象格式）' }
                    ]
                  },
                  endCity: {
                    oneOf: [
                      { 
                        type: 'OBJECT',
                        description: '目的地城市信息（单个城市）；如果是酒店/餐饮/用车，此项可为空',
                        properties: {
                          key: { type: 'STRING', description: '城市ID，可通过search_city工具获取' },
                          label: { type: 'STRING', description: '城市显示名称（name），可通过search_city工具获取' }
                        },
                        required: ['key', 'label']
                      },
                      { 
                        type: 'ARRAY', 
                        description: '多个目的地城市；如果是酒店/餐饮/用车，此项可为空',
                        items: {
                          type: 'OBJECT',
                          properties: {
                            key: { type: 'STRING', description: '城市ID，可通过search_city工具获取' },
                            label: { type: 'STRING', description: '城市显示名称（name），可通过search_city工具获取' }
                          },
                          required: ['key', 'label']
                        }
                      },
                      { type: 'STRING', description: '城市名称（兼容格式，建议使用对象格式）' }
                    ]
                  },
                  startDate: {
                    type: 'STRING',
                    description: '开始日期/入住日期，格式如"2024-01-15"'
                  },
                  endDate: {
                    type: 'STRING',
                    description: '结束日期/离店日期，格式如"2024-01-17"'
                  },
                  referencePrice: {
                    type: 'NUMBER',
                    description: '参考报价（元）'
                  }
                }
              }
            },
            required: ['type']
          }
        }
      },
      required: ['description', 'operations']
    }
  };

  const execute = async (args: any, context: ToolContext): Promise<ToolOutput> => {
    return await safeExecute(async () => {
      const validation = validateArgs(args, ['description', 'operations']);
      if (validation) {
        return createErrorResult(validation);
      }

      const { description, operations } = args;
      const { bus, billDetails } = context;

      if (!bus) {
        return createErrorResult('缺少bus对象，无法修改行程');
      }

      if (!Array.isArray(operations) || operations.length === 0) {
        return createErrorResult('operations必须是非空数组');
      }

      console.log('[update-trip-list] 开始修改行程:', description);

      // 获取当前单据数据
      let billValue: any;
      try {
        billValue = await bus.getValue();
      } catch (error) {
        return createErrorResult('无法获取当前单据数据');
      }

      // 查找行程规划字段
      const tripFieldName = 'u_行程规划';
      let currentTripList: any[] = [];
      let isHeaderField = false;
      
      // 从表头查找
      if (billValue?.form && billValue.form[tripFieldName]) {
        currentTripList = Array.isArray(billValue.form[tripFieldName]) ? 
          billValue.form[tripFieldName].slice() : [];
        isHeaderField = true;
      }
      // 从明细查找（暂时不支持明细中的行程修改）
      else if (billValue?.form?.details && Array.isArray(billValue.form.details)) {
        for (const detail of billValue.form.details) {
          if (detail.feeTypeForm && detail.feeTypeForm[tripFieldName]) {
            currentTripList = Array.isArray(detail.feeTypeForm[tripFieldName]) ? 
              detail.feeTypeForm[tripFieldName].slice() : [];
            break;
          }
        }
      }

      console.log('[update-trip-list] 当前行程数量:', currentTripList.length);

      // 获取模板配置和字段前缀
      let templates: any[] = [];
      let fieldPrefix = '';
      let specificationReferenceId = '';
      
      try {
        // 从单据规格配置中获取行程规划字段的referenceData.id
        if (billDetails?.form?.specificationId?.components) {
          const tripComponent = billDetails.form.specificationId.components.find((item: any) => 
            item.field === 'u_行程规划'
          );
          console.log('tripComponent', tripComponent);
          if (tripComponent?.referenceData?.id) {
            specificationReferenceId = tripComponent.referenceData.id;
            fieldPrefix = `E_${specificationReferenceId}_`;
            templates = await getTripTemplates(specificationReferenceId);
            console.log('[update-trip-list] 获取到模板配置:', templates.length, '个');
            console.log('[update-trip-list] 字段前缀:', fieldPrefix);
          }
        }
        
        if (templates.length === 0) {
          console.warn('[update-trip-list] 未能获取到模板配置');
        }
      } catch (error) {
        console.error('[update-trip-list] 获取模板配置时出错:', error);
      }

      // 处理每个操作
      const results: any[] = [];
      let modifiedTripList = currentTripList.slice();

      for (const operation of operations) {
        const { type, index, trip } = operation;

        try {
          switch (type) {
            case 'add':
              if (!trip) {
                results.push({ operation, success: false, error: '新增操作缺少trip数据' });
                continue;
              }
              
              // 构建模板ID（根据行程类型选择）
              const tripType = TRIP_TYPE_REVERSE_MAP[trip.type];
              let templateId = '';
              
              // 从模板配置中查找匹配的模板ID
              if (templates.length > 0) {
                const foundTemplateId = findTemplateIdByTripType(templates, tripType);
                if (foundTemplateId) {
                  templateId = foundTemplateId;
                  console.log(`[update-trip-list] 从模板配置中找到${trip.type}(${tripType})的模板ID: ${templateId}`);
                } else {
                  console.error(`[update-trip-list] 未找到${trip.type}(${tripType})的模板配置，无法创建行程`);
                  results.push({ 
                    operation, 
                    success: false, 
                    error: `未找到${trip.type}行程的模板配置，请检查单据规格设置` 
                  });
                  continue;
                }
              } else {
                console.error(`[update-trip-list] 未能获取模板配置，无法创建行程`);
                results.push({ 
                  operation, 
                  success: false, 
                  error: '未能获取行程模板配置，请检查单据规格设置' 
                });
                continue;
              }
              
              // 检查字段前缀是否获取成功
              if (!fieldPrefix) {
                console.error(`[update-trip-list] 未能获取字段前缀，无法创建行程`);
                results.push({ 
                  operation, 
                  success: false, 
                  error: '未能获取字段前缀，请检查单据规格设置' 
                });
                continue;
              }
              
              const newTrip = createNewTrip(trip, templateId, fieldPrefix);
              
              if (typeof index === 'number' && index > 0 && index <= modifiedTripList.length + 1) {
                modifiedTripList.splice(index - 1, 0, newTrip);
              } else {
                modifiedTripList.push(newTrip);
              }
              
              results.push({ 
                operation, 
                success: true, 
                message: `成功添加${trip.type}行程`,
                newIndex: typeof index === 'number' ? index : modifiedTripList.length
              });
              break;

            case 'update':
              if (typeof index !== 'number' || index < 1 || index > modifiedTripList.length) {
                results.push({ operation, success: false, error: `无效的行程索引: ${index}` });
                continue;
              }
              
              if (!trip) {
                results.push({ operation, success: false, error: '修改操作缺少trip数据' });
                continue;
              }
              
              const existingTrip = modifiedTripList[index - 1];
              const updatedFormData = {
                ...existingTrip.dataLinkForm,
                ...buildTripFormData(trip, fieldPrefix)
              };
              
              modifiedTripList[index - 1] = {
                ...existingTrip,
                dataLinkForm: updatedFormData
              };
              
              results.push({ 
                operation, 
                success: true, 
                message: `成功修改第${index}项行程`
              });
              break;

            case 'delete':
              if (typeof index !== 'number' || index < 1 || index > modifiedTripList.length) {
                results.push({ operation, success: false, error: `无效的行程索引: ${index}` });
                continue;
              }
              
              const deletedTrip = modifiedTripList.splice(index - 1, 1)[0];
              results.push({ 
                operation, 
                success: true, 
                message: `成功删除第${index}项行程`
              });
              break;

            default:
              results.push({ operation, success: false, error: `不支持的操作类型: ${type}` });
          }
        } catch (error) {
          results.push({ 
            operation, 
            success: false, 
            error: `操作失败: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      }

      // 保存修改后的行程列表
      try {
        const updateData: any = {};
        updateData[tripFieldName] = modifiedTripList;
        
        console.log('[update-trip-list] 准备保存的行程数据:', JSON.stringify(modifiedTripList, null, 2));
        
        await bus.setFieldsValue(updateData);
        
        console.log('[update-trip-list] setFieldsValue调用完成');
        
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;
        
        console.log('[update-trip-list] 操作完成:', { successCount, failCount, totalTrips: modifiedTripList.length });
        
        // 如果有任何操作失败，整个工具执行都标记为失败
        if (failCount > 0) {
          return createErrorResult(`行程修改失败：成功${successCount}项，失败${failCount}项。失败原因：${results.filter(r => !r.success).map(r => r.error).join('；')}`);
        }
        
        return createSuccessResult({
          message: `行程修改完成：成功修改${successCount}项行程`,
          totalTrips: modifiedTripList.length,
          operationResults: results,
          summary: description
        });
        
      } catch (error) {
        return createErrorResult(`保存行程数据失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    });
  };

  return {
    type: ToolType.EDIT,
    definition,
    execute
  };
} 