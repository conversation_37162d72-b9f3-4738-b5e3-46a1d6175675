/**
 * @fileoverview
 * Tool for deleting a detail row from a bill's detail list by its ID.
 */

import { Tool, ToolContext, ToolOutput, validateArgs, safeExecute, createSuccessResult, createErrorResult, FunctionDeclaration, ToolType } from '../../AIAgent';
import type { BillToolsDependencies } from './index';

// 工具依赖接口
interface DeleteDetailRowDependencies {
  // 目前不需要额外的静态依赖
}

export function createDeleteDetailRowTool(dependencies: BillToolsDependencies): Tool {
  const definition: FunctionDeclaration = {
    name: 'delete_detail_row',
    description: '删除指定索引的单据明细行',
    parameters: {
      type: 'OBJECT',
      properties: {
        description: {
          type: 'STRING',
          description: '用简要语言描述这次工具调用的目的，不超过25个字'
        },
        index: {
          type: 'NUMBER',
          description: '要删除的明细行索引（从0开始）'
        }
      },
      required: ['description', 'index']
    }
  };

  const execute = async (args: any, context: ToolContext) => {
    return await safeExecute(async () => {
      // 验证参数
      const validation = validateArgs(args, ['description', 'index']);
      if (validation) {
        return createErrorResult(validation);
      }

      const { index } = args;
      const { bus } = context;

      if (!bus) {
        return createErrorResult('缺少bus对象，无法删除明细行');
      }

      const currentValue = await bus.getValue();
      if (!currentValue?.details || !Array.isArray(currentValue.details)) {
        return createErrorResult('单据明细数据不存在');
      }

      const details = currentValue.details.slice();
      if (index < 0 || index >= details.length) {
        return createErrorResult(`无效的明细行索引: ${index}`);
      }

      const deletedDetail = details[index];
      details.splice(index, 1);

      // 触发明细变化事件
      bus.emit('details:change', details);

      return createSuccessResult({ 
        deleted: true, 
        index,
        deletedDetailId: deletedDetail?.feeTypeForm?.detailId,
        remainingRows: details.length
      });
    }, '删除明细行失败');
  };

  return { type: ToolType.EDIT, definition, execute };
} 