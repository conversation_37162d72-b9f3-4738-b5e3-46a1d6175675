/**
 * @fileoverview
 * Tool for adding a new detail row to a bill's detail list.
 */

import { Tool, ToolContext, ToolOutput, validateArgs, safeExecute, createSuccessResult, createErrorResult, FunctionDeclaration, ToolType } from '../../AIAgent';
import type { BillToolsDependencies } from './index';
import { generateDetailId } from './utils';

// 工具依赖接口
interface AddDetailRowDependencies {
  // 目前不需要额外的静态依赖
}

export function createAddDetailRowTool(dependencies: AddDetailRowDependencies): Tool {
  const definition: FunctionDeclaration = {
    name: 'add_detail_row',
    description: '添加单据明细行到指定位置',
    parameters: {
      type: 'OBJECT',
      properties: {
        description: {
          type: 'STRING',
          description: '用简要语言描述这次工具调用的目的，不超过25个字'
        },
        feeTypeForm: {
          type: 'OBJECT',
          description: '明细行的费用表单数据，包含amount、feeTypeId等字段',
          properties: {
            amount: {
              type: 'OBJECT',
              description: '费用金额，必须是金额对象格式',
              properties: {
                foreign: { type: 'STRING', description: '原币金额' },
                foreignUnit: { type: 'STRING', description: '原币币种代码' },
                rate: { type: 'STRING', description: '汇率' },
                standard: { type: 'STRING', description: '本位币金额' },
                standardUnit: { type: 'STRING', description: '本位币币种代码' }
              },
              required: ['standard', 'standardUnit']
            },
            feeTypeId: {
              type: 'STRING',
              description: '费用类型ID'
            },
            description: {
              type: 'STRING',
              description: '费用描述'
            },
            consumptionDate: {
              type: 'STRING',
              description: '消费日期，格式如：2024-04-06'
            }
          },
          required: ['amount']
        },
        index: {
          type: 'NUMBER',
          description: '插入位置索引，不指定时添加到末尾'
        }
      },
      required: ['description', 'feeTypeForm']
    }
  };

  const execute = async (args: any, context: ToolContext) => {
    return await safeExecute(async () => {
      // 验证参数
      const validation = validateArgs(args, ['description', 'feeTypeForm']);
      if (validation) {
        return createErrorResult(validation);
      }

      const { feeTypeForm, index } = args;
      const { bus } = context;

      if (!bus) {
        return createErrorResult('缺少bus对象，无法添加明细行');
      }

      const currentValue = await bus.getValue();
      const details = currentValue?.details ? currentValue.details.slice() : [];

      // 创建新的明细行
      const newDetail = {
        feeTypeForm: {
          detailId: generateDetailId(), // 生成唯一ID
          ...feeTypeForm
        }
      };

      // 插入明细行
      if (typeof index === 'number' && index >= 0 && index <= details.length) {
        details.splice(index, 0, newDetail);
      } else {
        details.push(newDetail);
      }

      // 触发明细变化事件
      bus.emit('details:change', details);

      return createSuccessResult({ 
        added: true, 
        detailId: newDetail.feeTypeForm.detailId,
        index: typeof index === 'number' ? index : details.length - 1,
        totalRows: details.length
      });
    }, '添加明细行失败');
  };

  return { type: ToolType.EDIT, definition, execute };
} 