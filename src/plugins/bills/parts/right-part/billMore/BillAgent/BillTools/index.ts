/**
 * 单据工具统一导出和工厂函数
 * 实现工具的依赖注入和批量创建
 */

import { Tool, ToolType } from '../../AIAgent';
import { createSetFieldValueTool } from './set-field-value.tool';
import { createDeleteDetailRowTool } from './delete-detail-row.tool';
import { createAddDetailRowTool } from './add-detail-row.tool';
import { getBillDetailsTool } from './get-bill-details.tool';
// WIP: 行程相关工具暂时不提供
// import { getTripListTool } from './get-trip-list.tool';
// import { createUpdateTripListTool } from './update-trip-list.tool';
import { searchCityTool } from './search-city.tool';

// 导出工厂函数
export {
  createSetFieldValueTool,
  createDeleteDetailRowTool,
  createAddDetailRowTool,
  // createUpdateTripListTool // WIP: 暂时不提供
};

// 导出辅助函数
export * from './utils';

/**
 * Defines the data context provided by the host component (e.g., MoreInfo.tsx).
 * These are raw data objects and the event bus, which the tools will use
 * at runtime via the Agent's context.
 */
export interface BillToolsDependencies {
  billDetails: any;
  bus: any; 
  userInfo: any;
  staffMap: any;
}

type ToolFactory = (dependencies: BillToolsDependencies) => Tool;

const billToolFactories: Record<string, ToolFactory> = {
  'set_field_value': createSetFieldValueTool,
  'delete_detail_row': createDeleteDetailRowTool,
  'add_detail_row': createAddDetailRowTool,
  // 'update_trip_list': createUpdateTripListTool // WIP: 暂时不提供
};

/**
 * A factory function to create a configured set of bill tools.
 * It takes a `dependencies` object which provides the necessary context
 * (like the current bill details) for the tools to function.
 *
 * @param dependencies - The external dependencies needed by the tools.
 * @returns An array of configured Tool objects.
 */
export function createBillTools(dependencies: BillToolsDependencies): Tool[] {
  const tools = Object.values(billToolFactories).map(factory => factory(dependencies));
  
  // Add static tools that don't require dependencies
  tools.push(getBillDetailsTool);
  // tools.push(getTripListTool); // WIP: 暂时不提供
  tools.push(searchCityTool);
  
  return tools;
}

/**
 * Returns a list of all available bill tool names.
 * This can be used to dynamically select which tools to enable.
 * @returns An array of tool name strings.
 */
export function getBillToolNames(): string[] {
  return Object.keys(billToolFactories).concat([
    getBillDetailsTool.definition.name,
    // getTripListTool.definition.name, // WIP: 暂时不提供
    searchCityTool.definition.name
  ]);
}

/**
 * A collection of all standard tools for bill manipulation.
 * These tools are designed to be general-purpose and reusable.
 */
export const allBillTools: Tool[] = [
  createSetFieldValueTool({} as BillToolsDependencies),
  createAddDetailRowTool({} as BillToolsDependencies),
  createDeleteDetailRowTool({} as BillToolsDependencies),
  // createUpdateTripListTool({} as BillToolsDependencies), // WIP: 暂时不提供
  getBillDetailsTool,
  // getTripListTool, // WIP: 暂时不提供
  searchCityTool,
]; 