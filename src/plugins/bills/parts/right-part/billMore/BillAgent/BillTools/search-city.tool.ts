/**
 * @fileoverview
 * Tool for searching cities based on keywords.
 * Supports multiple keywords and returns formatted city information.
 */

import { Tool, ToolContext, ToolOutput, FunctionDeclaration, ToolType } from '../../AIAgent';
import { Resource } from '@ekuaibao/fetch';
import { QuerySelect } from 'ekbc-query-builder';

const v3CityBaseData = new Resource('/api/v3/basedata/city');

/**
 * 格式化城市数据为工具返回格式
 */
const formatCityData = (cityItems: any[]): any[] => {
  return cityItems
    .filter(item => item.active === true && item.isLast === true)
    .map(item => ({
      key: item.id,
      name: item.name,
      fullName: item.fullName,
      extendInfo: {
        trainCode: item.extendInfo?.trainCode || [],
        airportCode: item.extendInfo?.airportCode || [],
        hoseMallTrain: item.extendInfo?.hoseMallTrain || "",
        cityExtendCode: item.extendInfo?.cityExtendCode || "",
        hoseMallFlight: item.extendInfo?.hoseMallFlight || "",
        hoseMallCityCode: item.extendInfo?.hoseMallCityCode || ""
      }
    }));
};

/**
 * 搜索单个关键词的城市
 */
const searchSingleCity = async (keyword: string): Promise<any[]> => {
  const trimSearchValue = keyword.trim();
  if (!trimSearchValue) {
    return [];
  }

  try {
    const options = {
      keyword: trimSearchValue,
      query: new QuerySelect()
        .filterBy(`(nameSpell.startsWithIgnoreCase("${trimSearchValue}") || name.startsWithIgnoreCase("${trimSearchValue}") || enName.startsWithIgnoreCase("${trimSearchValue}") || enFullName.containsIgnoreCase(", ${trimSearchValue}") || fullName.containsIgnoreCase("${trimSearchValue}"))`)
        .filterBy('treeLevel>0')
        .orderBy('treeLevel', 'ASC')
        .orderBy('code', 'ASC')
        .limit(0, 50)
        .value()
    };

    const result = await v3CityBaseData.POST('/search', options);
    return formatCityData(result.items || []);
  } catch (error) {
    console.error(`搜索城市关键词 "${keyword}" 失败:`, error);
    return [];
  }
};

/**
 * 城市搜索工具的定义
 */
const definition: FunctionDeclaration = {
  name: 'search_city',
  description: '根据关键词搜索城市以及城市的机场/火车站信息。可以同时搜索多个城市，返回每个关键词对应的城市列表。支持中文名、英文名、拼音等多种搜索方式。',
  parameters: {
    type: 'OBJECT',
    properties: {
      description: {
        type: 'STRING',
        description: '用简要语言描述这次工具调用的目的，不超过25个字'
      },
      keywords: {
        type: 'ARRAY',
        description: '要搜索的城市关键词列表，例如["北京", "上海", "广州"]。每个关键词可以是中文名、英文名或拼音',
        items: {
          type: 'STRING'
        }
      }
    },
    required: ['keywords']
  }
};

/**
 * 执行城市搜索的逻辑
 */
const execute = async (args: any, context: ToolContext): Promise<ToolOutput> => {
  try {
    const { keywords } = args;

    if (!Array.isArray(keywords) || keywords.length === 0) {
      return {
        isSuccess: false,
        error: '关键词参数必须是非空数组'
      };
    }

    console.log('[search-city] 开始搜索城市，关键词:', keywords);

    // 并行搜索所有关键词
    const searchPromises = keywords.map(async (keyword: string) => {
      const cities = await searchSingleCity(keyword);
      return { keyword, cities };
    });

    const searchResults = await Promise.all(searchPromises);
    
    // 构建返回结果格式 { "北京": [...], "上海": [...] }
    const result: Record<string, any[]> = {};
    searchResults.forEach(({ keyword, cities }) => {
      result[keyword] = cities;
    });

    console.log('[search-city] 搜索完成，结果:', result);

    return {
      isSuccess: true,
      data: {
        result,
        summary: `成功搜索了 ${keywords.length} 个关键词，共找到 ${Object.values(result).reduce((total, cities) => total + cities.length, 0)} 个城市`
      }
    };

  } catch (error) {
    console.error('[search-city] 城市搜索失败:', error);
    return {
      isSuccess: false,
      error: `城市搜索失败: ${error instanceof Error ? error.message : '未知错误'}`
    };
  }
};

/**
 * 城市搜索工具
 */
export const searchCityTool: Tool = {
  type: ToolType.VIEW,
  definition,
  execute
}; 