/**
 * React状态连接器Hook
 * 将Agent的状态暴露给React，并确保状态变化时UI会重新渲染
 */

import { useReducer, useEffect } from 'react';
import { Agent, AgentStatus } from '../core/Agent';
import { ChatMessage } from '../types/ChatMessage';

// Hook返回的状态接口
export interface AgentState {
  history: ChatMessage[];
  status: AgentStatus;
  isLoading: boolean;
  hasError: boolean;
}

/**
 * 将Agent实例的状态连接到React组件
 * @param agent Agent实例，可以为null（加载中）
 * @returns Agent的当前状态
 */
export const useAgentState = (agent: Agent | null): AgentState => {
  // 使用useReducer强制重新渲染
  const [, forceUpdate] = useReducer((x: number) => x + 1, 0);

  useEffect(() => {
    if (!agent) return undefined;

    // 订阅Agent状态变化
    const unsubscribe = agent.subscribe(() => {
      forceUpdate();
    });

    // 清理订阅
    return unsubscribe;
  }, [agent]);

  // 如果Agent未创建，返回初始状态
  if (!agent) {
    return {
      history: [],
      status: 'idle',
      isLoading: true,
      hasError: false
    };
  }

  // 返回Agent的当前状态
  return {
    history: agent.history,
    status: agent.status,
    isLoading: agent.status === 'thinking' || agent.status === 'tool_calling',
    hasError: agent.status === 'error'
  };
}; 