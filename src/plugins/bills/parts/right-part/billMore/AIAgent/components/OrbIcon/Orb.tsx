import React from 'react';
import './Orb.less';

interface OrbProps {
  size?: number;
}

const Orb: React.FC<OrbProps> = ({ size = 200 }) => {
  // 计算基于size的动画参数
  const scaleFactor = size / 200; // 以200px为基准
  const blobSize = 120 * scaleFactor;
  const moveDistance = 70 * scaleFactor;
  const verticalDistance = 75 * scaleFactor;
  const diagonalDistance = 60 * scaleFactor;
  const blurAmount = 20 * scaleFactor;

  const containerStyle: React.CSSProperties = {
    '--orb-size': `${size}px`,
    '--blob-size': `${blobSize}px`,
    '--move-distance': `${moveDistance}px`,
    '--vertical-distance': `${verticalDistance}px`,
    '--diagonal-distance': `${diagonalDistance}px`,
    '--blur-amount': `${blurAmount}px`,
  };

  return (
    <div className="siri-orb-container" style={containerStyle}>
      <div className="orb-wrapper">
        <div className="siri-orb">
          <div className="blob">
            <div className="blob-inner"></div>
          </div>
          <div className="blob">
            <div className="blob-inner"></div>
          </div>
          <div className="blob">
            <div className="blob-inner"></div>
          </div>
          <div className="blob">
            <div className="blob-inner"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Orb; 