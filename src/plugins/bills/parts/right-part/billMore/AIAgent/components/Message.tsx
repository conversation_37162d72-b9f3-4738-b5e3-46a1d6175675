/**
 * 消息组件 - 单条消息展示
 * 简化版本：将所有消息类型都展示为纯文本
 * 后续会增强支持Markdown、工具调用等
 */

import React, { useState } from 'react'
import { Spin, Modal, Button } from '@hose/eui'
import { OutlinedGeneralMore, OutlinedTipsDone, OutlinedTipsClose, TwoToneGeneralAutofillStatic, TwoToneGeneralAiSummarize, TwoToneGeneralAiSummary, TwoToneGeneralApproval, OutlinedGeneralComplaint } from '@hose/eui-icons'
import type { ChatMessage } from '../types/ChatMessage'

import MarkdownRenderer from './MarkdownRenderer'
import { TagRendererMap } from '../types/TagRenderer'
import { ToolType } from '../tools/Tool'

interface MessageProps {
  message: ChatMessage,
  tagRenderers?: TagRendererMap;
  debugMode?: boolean;
}

// 获取工具消息的最终执行状态
const getToolMessageFinalStatus = (msg: any): 'running' | 'success' | 'failed' => {
  // 如果status不是success，直接返回原状态
  if (msg.status === 'running' || msg.status === 'pending' || msg.status === 'executing') {
    return 'running'
  }
  
  if (msg.status === 'error' || msg.status === 'failed') {
    return 'failed'
  }
  
  // 如果status是success，还需要检查result.isSuccess
  if (msg.status === 'success') {
    // 检查result.isSuccess来判断业务逻辑是否成功
    if (msg.result && typeof msg.result.isSuccess === 'boolean') {
      return msg.result.isSuccess ? 'success' : 'failed'
    }
    // 如果没有result.isSuccess字段，认为成功
    return 'success'
  }
  
  // 其他情况默认为失败
  return 'failed'
}

const Message: React.FC<MessageProps> = ({ message, tagRenderers, debugMode = false }) => {
  const [showJsonModal, setShowJsonModal] = useState(false)

  // 根据工具类型获取对应的图标
  const getToolTypeIcon = (toolType: ToolType) => {
    switch (toolType) {
      case ToolType.EDIT:
        return <TwoToneGeneralAutofillStatic style={{ fontSize: '16px', color: '#8c8c8c' }} />
      case ToolType.VIEW:
        return <TwoToneGeneralAiSummarize style={{ fontSize: '16px', color: '#8c8c8c' }} />
      case ToolType.ANALYZE:
        return <TwoToneGeneralAiSummary style={{ fontSize: '16px', color: '#8c8c8c' }} />
      case ToolType.REVIEW:
        return <TwoToneGeneralApproval style={{ fontSize: '16px', color: '#8c8c8c' }} />
      case ToolType.OTHER:
      default:
        return <TwoToneGeneralAiSummary style={{ fontSize: '16px', color: '#8c8c8c' }} />
    }
  }

  // 根据状态获取对应的图标和颜色（参考 ChatArea.tsx 的 getToolCallIcon）
  const getToolCallIcon = (status: string, toolType?: ToolType) => {
    switch (status) {
      case 'pending':
      case 'executing':
      case 'running':
        return {
          icon: <OutlinedGeneralMore style={{ fontSize: '16px', color: '#1677ff' }} />,
        }
      case 'success':
        return {
          icon: toolType ? getToolTypeIcon(toolType) : <OutlinedTipsDone style={{ fontSize: '16px', color: '#8c8c8c' }} />,
        }
      case 'error':
      case 'failed':
        return {
          icon: <OutlinedTipsClose style={{ fontSize: '16px', color: 'var(--eui-function-danger-500)' }} />,
        }
      default:
        return {
          icon: <OutlinedGeneralMore style={{ fontSize: '16px', color: '#666' }} />,
        }
    }
  }

  // 根据消息类型提取文本内容
  const getMessageText = (msg: ChatMessage): string => {
    switch (msg.role) {
      case 'user':
      case 'model':
        // UserMessage和ModelMessage都有parts字段
        if (!msg.parts || msg.parts.length === 0) {
          return ''
        }
        return msg.parts
          .filter(part => 'text' in part)
          .map(part => part.text)
          .join('\n')
      
      case 'tool':
        // ToolStatusMessage显示工具执行状态，使用新的状态判断逻辑
        const finalStatus = getToolMessageFinalStatus(msg)
        const statusText = finalStatus === 'running' ? i18n.get('执行中') : 
                          finalStatus === 'success' ? i18n.get('执行成功') : i18n.get('执行失败')
        return `${i18n.get('工具')} ${msg.toolName} ${statusText}`
      
      case 'system':
        // SystemErrorMessage显示系统错误
        return msg.content
      
      default:
        return ''
    }
  }

  // 渲染工具消息的专门UI（参考 ChatArea.tsx 的 renderToolCalls）
  const renderToolMessage = (msg: any) => {
    const finalStatus = getToolMessageFinalStatus(msg)
    // 从消息中获取工具类型
    const toolType = msg.toolType
    const { icon } = getToolCallIcon(finalStatus, toolType)
    const statusText = finalStatus === 'running' ? i18n.get('执行中') : 
                      finalStatus === 'success' ? i18n.get('执行成功') : 
                      finalStatus === 'failed' ? i18n.get('执行失败') : i18n.get('未知状态')
    
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        fontSize: '12px'
      }}>
        <span style={{ display: 'flex', alignItems: 'center' }}>
          {icon}
        </span>
        <span style={{ color: '#818783', fontSize: '11px', lineHeight: '18px' }}>
          {msg.toolArgs.description || i18n.get('调用工具')} - {statusText}
        </span>
      </div>
    )
  }

  // 渲染系统消息的专门UI，样式与工具消息保持一致
  const renderSystemMessage = (msg: any) => {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        fontSize: '12px'
      }}>
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <OutlinedGeneralComplaint style={{ fontSize: '13px', color: '#8c8c8c' }} />
        </span>
        <span style={{ color: '#818783', fontSize: '11px', lineHeight: '18px' }}>
          {msg.content}
        </span>
      </div>
    )
  }

  const renderContent = () => {
    // 根据消息类型和状态渲染不同内容
    if (message.role === 'tool') {
      // 为工具消息提供专门的UI渲染
      return renderToolMessage(message)
    }
    
    if (message.role === 'system') {
      // 为系统消息提供专门的UI渲染
      return renderSystemMessage(message)
    }
    
    const messageText = getMessageText(message);

    if (message.role === 'model') {
      // ModelMessage有status字段
      const modelMsg = message as any; // 临时类型断言
      
      // 加载状态（初始状态，还没有任何文本）
      if (modelMsg.status === 'streaming' && !messageText.trim()) {
        return (
          <div style={inlineStyles.loadingContent}>
            <Spin size="small" />
            {i18n.get('思考中...')}
          </div>
        )
      }
      
      // 流式输出状态（有文本内容且正在streaming）
      if (modelMsg.status === 'streaming' && messageText.trim()) {
        return (
          <MarkdownRenderer
            content={messageText}
            tagRenderers={tagRenderers}
          />
        )
      }
    }
    
    // 渲染已完成的模型消息
    if (message.role === 'model') {
      return (
        <MarkdownRenderer
          content={messageText}
          tagRenderers={tagRenderers}
        />
      );
    }
    
    // 默认返回文本内容
    return messageText
  }

  const handleShowJson = () => {
    setShowJsonModal(true)
  }

  const handleCloseJson = () => {
    setShowJsonModal(false)
  }

  // 格式化JSON数据
  const formatJsonData = (data: any): string => {
    try {
      return JSON.stringify(data, null, 2)
    } catch (error) {
      return i18n.get('无法序列化消息数据')
    }
  }

  // 样式定义 - 与旧UI保持一致
  const inlineStyles = {
    messageContainer: {
      marginTop: (message.role === 'tool' || message.role === 'system') ? '3px' : '6px',
      marginBottom: '6px',
      width: 'auto',
      maxWidth: '100%'
    } as React.CSSProperties,
    messageBubble: {
      padding: message.role === 'user' ? '10px 12px' : '0px 6px',
      borderRadius: '8px',
      maxWidth: '100%',
      minWidth: message.role === 'user' ? '50px' : '120px',
      whiteSpace: 'pre-wrap',
      lineHeight: 1.6,
      fontSize: '13px',
      background: message.role === 'user' ? '#f2f2f2' : '#ffffff',
      color: 'var(--eui-text-title)',
      position: 'relative',
      border: 'none',
      paddingRight: debugMode ? (message.role === 'user' ? '50px' : '44px') : (message.role === 'user' ? '12px' : '6px'), // 根据调试模式调整空间
    } as React.CSSProperties,
    loadingContent: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    } as React.CSSProperties,
    debugButton: {
      position: 'absolute',
      top: '4px',
      right: '4px',
      fontSize: '8px',
      padding: '2px 6px',
      height: '16px',
      minWidth: '36px',
      opacity: 0.6,
      zIndex: 10
    } as React.CSSProperties,
    jsonContent: {
      backgroundColor: '#f6f8fa',
      border: '1px solid #d0d7de',
      borderRadius: '6px',
      padding: '16px',
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      fontSize: '12px',
      lineHeight: '1.45',
      overflow: 'auto',
      whiteSpace: 'pre',
      maxHeight: '400px'
    } as React.CSSProperties
  }

  return (
    <div style={inlineStyles.messageContainer}>
      <div style={inlineStyles.messageBubble}>
        {renderContent()}
        
        {/* 调试按钮 - 移动到气泡内部右侧 */}
        {debugMode && (
          <Button 
            category="text" 
            size="small" 
            style={inlineStyles.debugButton}
            onClick={handleShowJson}
          >
            JSON
          </Button>
        )}
      </div>

      {/* JSON模态窗 */}
      <Modal
        title={i18n.get('消息JSON数据')}
        open={showJsonModal}
        onCancel={handleCloseJson}
        footer={[
          <Button key="close" onClick={handleCloseJson}>
            {i18n.get('关闭')}
          </Button>
        ]}
        width={600}
      >
        <div style={inlineStyles.jsonContent}>
          {formatJsonData(message)}
        </div>
      </Modal>
    </div>
  )
}

export default Message 