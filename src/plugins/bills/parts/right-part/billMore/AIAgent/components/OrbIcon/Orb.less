// Siri Orb 动画效果样式
// 颜色变量
@blob-color-1: #ff80c0;
@blob-color-2: #8a7dff;
@blob-color-3: #7ddaff;
@blob-color-4: #7dffae;

.siri-orb-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.orb-wrapper {
  width: var(--orb-size);
  height: var(--orb-size);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  overflow: hidden;
}

.siri-orb {
  position: relative;
  width: 100%;
  height: 100%;
  filter: blur(var(--blur-amount));
}

// 外部容器：只负责移动
.blob {
  position: absolute;
  top: 30%;
  left: 30%;
  width: var(--blob-size);
  height: var(--blob-size);

  // 内部元素：负责外观和变形
  &-inner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  // 为每个光斑分配独特的动画和颜色
  &:nth-child(1) {
    animation: move-1 12s ease-in-out infinite;

    .blob-inner {
      background-color: @blob-color-1;
      animation: 
        scale-1 6s ease-in-out infinite,
        squish-1 8s ease-in-out infinite;
      animation-delay: -2s, -2s;
    }
  }

  &:nth-child(2) {
    animation: move-2 8s ease-in-out infinite;
    animation-delay: -4s;

    .blob-inner {
      background-color: @blob-color-2;
      animation:
        scale-2 4s ease-in-out infinite,
        squish-2 6s ease-in-out infinite;
    }
  }

  &:nth-child(3) {
    animation: move-3 15s linear infinite;
    animation-delay: -6s;

    .blob-inner {
      background-color: @blob-color-3;
      animation:
        scale-1 8s ease-in-out infinite,
        squish-2 10s ease-in-out infinite;
      animation-delay: -3s, -5s;
    }
  }

  &:nth-child(4) {
    animation: move-1 10s ease-in-out infinite reverse;
    animation-delay: -8s;

    .blob-inner {
      background-color: @blob-color-4;
      animation:
        scale-2 5s ease-in-out infinite,
        squish-1 7s ease-in-out infinite reverse;
      animation-delay: -1s, -3s;
    }
  }
}

// 动画关键帧定义
// 移动路径模板
@keyframes move-1 {
  0%, 100% { 
    transform: rotate(0deg) translateX(var(--move-distance)) rotate(0deg); 
  }
  50% { 
    transform: rotate(180deg) translateX(var(--move-distance)) rotate(-180deg); 
  }
}

@keyframes move-2 {
  0%, 100% { 
    transform: translateY(var(--vertical-distance)); 
  }
  50% { 
    transform: translateY(calc(-1 * var(--vertical-distance))); 
  }
}

@keyframes move-3 {
  0%, 100% { 
    transform: translate(
      calc(-1 * var(--diagonal-distance)), 
      calc(-1 * var(--diagonal-distance))
    ); 
  }
  25% { 
    transform: translate(
      var(--diagonal-distance), 
      calc(-1 * var(--diagonal-distance))
    ); 
  }
  50% { 
    transform: translate(
      var(--diagonal-distance), 
      var(--diagonal-distance)
    ); 
  }
  75% { 
    transform: translate(
      calc(-1 * var(--diagonal-distance)), 
      var(--diagonal-distance)
    ); 
  }
}

// 缩放动画模板
@keyframes scale-1 {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.3); }
}

@keyframes scale-2 {
  0%, 100% { transform: scale(0.8); }
  50% { transform: scale(1.1); }
}

// 形状变幻模板 (Amoeba-like)
@keyframes squish-1 {
  0%, 100% { border-radius: 50% 50% 50% 50%; }
  50% { border-radius: 30% 70% 70% 30%; }
}

@keyframes squish-2 {
  0%, 100% { border-radius: 50% 50% 50% 50%; }
  50% { border-radius: 70% 30% 30% 70% / 60% 40% 60% 40%; }
} 