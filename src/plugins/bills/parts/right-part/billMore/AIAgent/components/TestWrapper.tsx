/**
 * 测试包装组件
 * 用于逐步调试导入问题
 */

import React from 'react'

interface TestWrapperProps {
  billDetails: any
  bus: any
  userInfo: any
  isActiveInfo: boolean
  riskData: any
  staffMap: any
}

const TestWrapper: React.FC<TestWrapperProps> = (props) => {
  return (
    <div style={{ 
      padding: '20px',
      textAlign: 'center',
      color: '#666'
    }}>
      <div style={{ fontSize: '16px', marginBottom: '10px' }}>🤖</div>
      <div>AI助手测试组件</div>
      <div style={{ fontSize: '12px', marginTop: '10px' }}>
        {i18n.get('单据ID')}: {props.billDetails?.id || i18n.get('未知')}
      </div>
    </div>
  )
}

export default TestWrapper 