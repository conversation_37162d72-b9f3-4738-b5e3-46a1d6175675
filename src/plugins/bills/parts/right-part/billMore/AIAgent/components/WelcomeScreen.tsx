/**
 * 欢迎屏幕组件
 * 显示AI助手介绍和快速问题按钮
 */

import React, { useMemo } from 'react'
import { Agent } from '../core/Agent'
import Orb from './OrbIcon/Orb'

interface WelcomeScreenProps {
  agent: Agent;
  onQuestionClick: (question: string) => void
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ agent, onQuestionClick }) => {
  const { name, description, recommendedQuestions } = agent.config;

  // 如果推荐问题超过3个，随机选择3个展示
  const questionsToShow = useMemo(() => {
    const questions = recommendedQuestions || [];
    if (questions.length > 3) {
      return questions.slice().sort(() => 0.5 - Math.random()).slice(0, 3);
    }
    return questions;
  }, [recommendedQuestions]);

  // 样式定义
  const styles = {
    welcomeContainer: {
      textAlign: 'center',
      padding: '30px 20px',
      color: '#595959',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      overflowY: 'auto',
      minHeight: 0
    } as React.CSSProperties,
    orbContainer: {
      marginBottom: '20px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    } as React.CSSProperties,
    welcomeTitle: { 
      fontSize: '22px', 
      marginBottom: '8px', 
      fontWeight: 600, 
      color: '#262626' 
    } as React.CSSProperties,
    welcomeDesc: { 
      fontSize: '14px', 
      marginBottom: '24px', 
      color: '#8c8c8c', 
      lineHeight: 1.6, 
      maxWidth: '320px' 
    } as React.CSSProperties,
    welcomeHelpTitle: { 
      fontSize: '14px', 
      marginBottom: '12px', 
      color: '#595959' 
    } as React.CSSProperties,
    quickQuestionButton: {
      padding: '10px 16px',
      backgroundColor: '#f5f5f5',
      borderRadius: '8px',
      cursor: 'pointer',
      fontSize: '13px',
      textAlign: 'left',
      width: '100%',
      border: '1px solid #f0f0f0',
      marginBottom: '8px',
      transition: 'background-color 0.2s, border-color 0.2s'
    } as React.CSSProperties,
    questionsContainer: {
      width: '100%',
      maxWidth: '340px'
    } as React.CSSProperties
  }

  return (
    <div style={styles.welcomeContainer}>
      {/* AI图标 */}
      <div style={styles.orbContainer}>
        <Orb size={100} />
      </div>
      
      {/* 标题和描述 */}
      <div style={styles.welcomeTitle}>{name || i18n.get('AI智能助手')}</div>
      <div style={styles.welcomeDesc}>
        {description || i18n.get('我可以帮你解答问题、提供建议。')}
      </div>
      
      {/* 快速问题 */}
      {questionsToShow.length > 0 && (
        <>
          <div style={styles.welcomeHelpTitle}>{i18n.get('我可以帮你回答:')}</div>
          <div style={styles.questionsContainer}>
            {questionsToShow.map((question) => (
              <button 
                key={question}
                style={styles.quickQuestionButton}
                onClick={() => onQuestionClick(question)}
                onMouseEnter={(e) => { 
                  e.currentTarget.style.backgroundColor = 'rgb(237 241 255)'
                  e.currentTarget.style.borderColor = 'rgb(37 85 255)'
                }}
                onMouseLeave={(e) => { 
                  e.currentTarget.style.backgroundColor = '#f5f5f5' 
                  e.currentTarget.style.borderColor = '#f0f0f0' 
                }}
              >
                {question}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  )
}

export default WelcomeScreen 