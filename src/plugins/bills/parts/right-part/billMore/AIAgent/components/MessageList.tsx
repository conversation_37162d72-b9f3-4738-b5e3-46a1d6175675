/**
 * 消息列表组件
 * 展示所有聊天消息，支持自动滚动
 */

import React, { useRef, useEffect } from 'react'
import { Button } from '@hose/eui'
import Message from './Message'
import type { ChatMessage } from '../types/ChatMessage'
import type { TagRendererMap } from '../types/TagRenderer'

interface MessageListProps {
  messages: ChatMessage[]
  onNewChat?: () => void
  tagRenderers?: TagRendererMap
  debugMode?: boolean
}

const MessageList: React.FC<MessageListProps> = ({ messages, onNewChat, tagRenderers, debugMode = false }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const chatAreaRef = useRef<HTMLDivElement>(null)

  // 添加调试日志
  // console.log('🔍 MessageList 渲染开始');
  // console.log('💬 messages数量:', messages.length);
  // console.log('🏷️ tagRenderers:', tagRenderers);

  // 自动滚动到底部
  useEffect(() => {
    if (chatAreaRef.current) {
      chatAreaRef.current.scrollTop = chatAreaRef.current.scrollHeight
    }
  }, [messages])

  // 样式定义 - 与旧UI保持一致
  const styles = {
    chatArea: {
      flex: 1,
      overflowY: 'auto',
      padding: '16px',
      display: 'flex',
      flexDirection: 'column'
    } as React.CSSProperties,
    newChatContainer: {
      display: 'flex',
      justifyContent: 'center',
      marginBottom: '16px'
    } as React.CSSProperties,
    newChatButton: {
      padding: '8px 16px',
      fontSize: '13px',
      borderRadius: '6px',
      border: '1px solid #F0F0F0',
      background: '#ffffff',
      color: 'rgba(0, 0, 0, 0.88)',
      cursor: 'pointer',
      transition: 'all 0.2s',
      fontWeight: 400,
    } as React.CSSProperties
  }

  // 过滤掉不应该显示的消息（如纯工具调用和工具执行结果）
  const visibleMessages = messages.filter(msg => {
    // 检查消息的parts属性
    if (msg.role === 'user' || msg.role === 'model') {
      if (Array.isArray(msg.parts)) {
        // 如果parts为空，应该显示（loading状态）
        if (msg.parts.length === 0) {
          return true;
        }
        // 如果消息只包含工具调用或工具结果，不显示
        const isOnlyToolMessages = msg.parts.every(part => 
          'functionCall' in part || 'functionResponse' in part
        );
        return !isOnlyToolMessages;
      }
    }
    return true
  })

  return (
    <div style={styles.chatArea} ref={chatAreaRef}>
      {/* 消息列表 */}
      {visibleMessages.map(message => (
        <Message key={message.id} message={message} tagRenderers={tagRenderers} debugMode={debugMode} />
      ))}
      
      {/* 滚动锚点 */}
      <div ref={messagesEndRef} />
    </div>
  )
}

export default MessageList 