/**
 * 聊天输入组件
 * 包含输入框和快捷操作按钮
 */

import React, { useState } from 'react'
import { Button, Input } from '@hose/eui'

interface ChatInputProps {
  onSubmit: (message: string) => void
  disabled?: boolean
  loading?: boolean
  showQuickActions?: boolean
  quickActions?: string[]
}

const ChatInput: React.FC<ChatInputProps> = ({ 
  onSubmit, 
  disabled = false, 
  loading = false,
  showQuickActions = true,
  quickActions = []
}) => {
  const [inputValue, setInputValue] = useState('')

  const handleSubmit = () => {
    console.log('📝 ChatInput.handleSubmit 触发')
    console.log('📊 状态检查:', {
      inputValue: inputValue,
      trimmed: inputValue.trim(),
      disabled,
      loading,
      canSubmit: inputValue.trim() && !disabled && !loading
    })
    
    if (inputValue.trim() && !disabled && !loading) {
      console.log('✅ 条件满足，开始提交消息:', inputValue.trim())
      try {
        onSubmit(inputValue.trim())
        console.log('🔄 onSubmit 调用完成，清空输入框')
        setInputValue('')
      } catch (error) {
        console.error('❌ onSubmit 调用失败:', error)
      }
    } else {
      console.log('⚠️ 提交条件不满足，跳过提交')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !disabled && !loading) {
      handleSubmit()
    }
  }

  const handleQuickAction = (action: string) => {
    if (!disabled && !loading) {
      try {
        onSubmit(action)
      } catch (error) {
        console.error('❌ 快捷操作 onSubmit 调用失败:', error)
      }
    } else {
      console.log('⚠️ 快捷操作条件不满足，跳过执行')
    }
  }

  // 样式定义 - 与旧UI保持一致
  const styles = {
    senderContainer: {
      padding: '16px',
      background: '#fff',
      flexShrink: 0
    } as React.CSSProperties,
    senderTopActions: {
      display: showQuickActions ? 'flex' : 'none',
      gap: '8px',
      marginBottom: '12px',
      flexWrap: 'wrap'
    } as React.CSSProperties,
    quickActionButton: {
      padding: '8px 12px',
      fontSize: '13px',
      borderRadius: '6px',
      border: '1px solid #F0F0F0',
      background: '#ffffff',
      color: 'rgba(0, 0, 0, 0.88)',
      cursor: 'pointer',
      transition: 'all 0.2s',
      fontWeight: 400,
      lineHeight: 1,
    } as React.CSSProperties,
    inputGroup: { 
      display: 'flex', 
      gap: '8px', 
      alignItems: 'center',
      border: '1px solid',
      borderRadius: '10px',
      borderColor: '#dcdcdc',
      padding: '0px',
      background: (disabled||loading) ? '#f2f3f5' : '#fff',
    } as React.CSSProperties,
    sendButton: {
      borderRadius: '50%', 
      width: '30px', 
      height: '30px', 
      padding: 0, 
      margin: '8px',
      minWidth: '30px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    } as React.CSSProperties,
  }

  // 快捷操作按钮 - 移除硬编码，改为使用传入的quickActions
  const quickActionItems = quickActions.map(action => ({
    label: action,
    action: action
  }))

  return (
    <div style={styles.senderContainer}>
      {/* 快捷操作按钮 */}
      <div style={styles.senderTopActions}>
        {quickActionItems.map((item) => (
          <button 
            key={item.label}
            style={styles.quickActionButton} 
            onClick={() => handleQuickAction(item.action)}
            disabled={disabled || loading}
            onMouseEnter={(e) => { 
              if (!disabled && !loading) {
                e.currentTarget.style.backgroundColor = '#f5f5f5'
                e.currentTarget.style.borderColor = '#F0F0F0'
              }
            }}
            onMouseLeave={(e) => { 
              e.currentTarget.style.backgroundColor = '#ffffff' 
              e.currentTarget.style.borderColor = '#F0F0F0' 
            }}
          >
            {item.label}
          </button>
        ))}
      </div>
      
      {/* 输入框和发送按钮 */}
      <div style={styles.inputGroup}>
        <Input
          style={{ 
            flex: 1, 
            padding: 12, 
            borderRadius: 16, 
            border: 'none', 
            backgroundColor: 'transparent' 
          }}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onPressEnter={handleKeyPress}
          placeholder={i18n.get('输入您的问题或想法')}
          disabled={disabled || loading}
        />
        <Button
          theme="highlight"
          loading={loading}
          onClick={handleSubmit}
          disabled={!inputValue.trim() || disabled || loading}
          style={styles.sendButton}
        >
          {loading ? '' : '↑'}
        </Button>
      </div>
    </div>
  )
}

export default ChatInput 