// 定义一个 class，比如 .customListItem
.customListItem {
    // 关键：为伪元素的绝对定位提供参考
    position: relative;
    
    // 关键：为左侧的"•"留出空间
    padding-left: 20px; 
    
    margin-bottom: 0;
    line-height: 1.5; // 设定一个明确的行高，保证行间距一致
  
    // 使用 Less 的嵌套语法定义伪元素，& 代表父选择器 (.customListItem)
    &::before {
      // 关键：生成项目符号
      content: '•';
      
      // 关键：使用绝对定位，使其脱离文档流，不影响父元素高度
      position: absolute;
      left: 4px; // 控制"•"的水平位置
      top: 0;    // 控制"•"的垂直位置，可微调
  
      color: var(--eui-primary-pri-500, #2555FF);
      user-select: none;
  
      // 推荐：用 font-size 代替 transform，更容易控制对齐
      font-size: 24px;
      line-height: 1; // 让这个伪元素的高度和它的字号接近
    }
  }

// Markdown渲染器的主容器
.markdownRenderer {
  line-height: 1.6;
  color: var(--eui-text-title);
  font-size: inherit;
  word-wrap: break-word;
}

// 标题样式
.heading1, .heading2, .heading3, .heading4, .heading5, .heading6 {
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
  line-height: 1.3;
  color: var(--eui-text-title);
}

// 段落样式
.paragraph {
  margin-bottom: 1em;
  line-height: inherit;
}

// 空行样式
.emptyLine {
  height: 0.8em;
  line-height: 0.8em;
}

// 内联代码样式
.inlineCode {
  background-color: rgba(175, 184, 193, 0.2);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
  color: #e01e5a;
}

// 代码块样式
.codeBlock {
  background-color: #f6f8fa;
  border: 1px solid #d1d9e0;
  border-radius: 6px;
  margin: 1em 0;
  overflow-x: auto;
}

.codeBlockLanguage {
  background-color: #e1e7ed;
  padding: 0.5em 1em;
  font-size: 0.875em;
  font-weight: 600;
  color: #656d76;
  border-bottom: 1px solid #d1d9e0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.codeBlockContent {
  padding: 1em;
  margin: 0;
  overflow-x: auto;
  
  code {
    background: none;
    padding: 0;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.875em;
    line-height: 1.45;
    color: var(--eui-text-title);
  }
}

// 引用块样式
.blockquote {
  margin: 1em 0;
  padding: 1em;
  border-left: 4px solid var(--eui-primary-pri-500, #2555FF);
  background-color: rgba(37, 85, 255, 0.05);
  color: #656d76;
}

// 水平分割线样式
.horizontalRule {
  border: none;
  height: 1px;
  background-color: #d1d9e0;
  margin: 1.5em 0;
}

// 有序列表项样式
.orderedListItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.25em;
  line-height: 1.5;
}

.orderedListNumber {
  color: var(--eui-primary-pri-500, #2555FF);
  font-weight: 600;
  margin-right: -0.2em;
  min-width: 1.5em;
  user-select: none;
}

// 无序列表项样式
.unorderedListItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.25em;
  line-height: 1.5;
}

.listBullet {
  color: var(--eui-primary-pri-500, #2555FF);
  font-weight: bold;
  margin-left: 0.3em;
  margin-right: -0.2em;
  min-width: 1em;
  user-select: none;
  transform: scale(1.5);
  transform-origin: 50% 25%;
  line-height: 1;
}

.listItemContent {
  flex: 1;
  word-wrap: break-word;
}

// 链接样式
.link {
  color: var(--eui-primary-pri-500, #2555FF);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
  
  &:hover {
    border-bottom-color: var(--eui-primary-pri-500, #2555FF);
    text-decoration: none;
  }
  
  &:visited {
    color: var(--eui-primary-pri-400, #4169E1);
  }
}