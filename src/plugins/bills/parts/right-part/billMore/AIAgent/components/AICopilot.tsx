/**
 * AI助手主组件
 * 纯UI组件，接收Agent实例作为props
 * 负责渲染聊天界面和处理用户交互
 */

import React, { useState } from 'react'
import WelcomeScreen from './WelcomeScreen'
import MessageList from './MessageList'
import ChatInput from './ChatInput'
import { useAgentState } from '../hooks/useAgentState'
import { Agent } from '../core/Agent'
import styles from './AICopilot.module.less'

interface AICopilotProps {
  agent: Agent
}

const AICopilot: React.FC<AICopilotProps> = ({ agent }) => {
  // 调试模式状态
  const [debugMode, setDebugMode] = useState(false)
  
  // 使用Agent状态管理Hook
  const { isLoading, hasError, history } = useAgentState(agent)

  // 添加调试日志
  // console.log('🔍 AICopilot 渲染开始');
  // console.log('🤖 agent:', agent);
  // console.log('🏷️ agent.tagRenderers:', agent?.tagRenderers);
  // console.log('🐛 debugMode:', debugMode);

  // 处理消息提交
  const handleSubmit = async (message: string) => {
    if (!agent || isLoading) {
      return
    }

    // 检查是否是调试指令
    if (message.trim() === '/debug') {
      const newDebugMode = !debugMode
      setDebugMode(newDebugMode)
      
      // 发送系统消息通知调试模式变化
      const systemMessage = {
        id: `debug_${Date.now()}`,
        timestamp: new Date(),
        role: 'system' as const,
        content: newDebugMode 
          ? i18n.get('🐛 调试模式已开启 - 消息中将显示调试按钮') 
          : i18n.get('🔧 调试模式已关闭 - 调试按钮已隐藏')
      }
      
      // 直接添加到Agent的消息历史中  
      agent.history.push(systemMessage)
      
      // 手动触发Agent更新通知
      ;(agent as any)._notifyUpdate?.()
      
      console.log(`🐛 调试模式${newDebugMode ? '开启' : '关闭'}:`, newDebugMode)
      return
    }

    try {
      // 使用Agent的sendMessage方法，它会自动管理消息历史
      await agent.sendMessage(message)
    } catch (error) {
      console.error('❌ AICopilot 发送消息失败:', error)
      console.error('📍 错误堆栈:', error instanceof Error ? error.stack : error)
    }
  }

  // 错误状态
  if (hasError) {
    return (
      <div className={styles.container} style={{ justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ textAlign: 'center', color: '#ff4d4f' }}>
          <div style={{ fontSize: '16px', marginBottom: '8px' }}>⚠️</div>
          <div>{i18n.get('AI助手遇到错误')}</div>
          <div style={{ fontSize: '12px', marginTop: '8px', color: '#8c8c8c' }}>
            {i18n.get('请刷新页面重试')}
          </div>
        </div>
      </div>
    )
  }

  const isWelcomeScreen = history.length === 0

  return (
    <div className={styles.container}>
      {/* 主要内容区域 */}
      {isWelcomeScreen && agent ? (
        <WelcomeScreen agent={agent} onQuestionClick={handleSubmit} />
      ) : (
        <MessageList 
          messages={history} 
          tagRenderers={agent?.tagRenderers}
          debugMode={debugMode}
        />
      )}
      
      {/* 输入区域 */}
      <ChatInput
        onSubmit={handleSubmit}
        disabled={false}
        loading={isLoading}
        showQuickActions={!isWelcomeScreen}
        quickActions={agent?.config.quickActions || []}
      />
    </div>
  )
}

export default AICopilot 