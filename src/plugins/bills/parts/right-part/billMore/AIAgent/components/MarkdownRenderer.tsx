import React, { useMemo } from 'react';
import styles from './MarkdownRenderer.module.less';

// -- 主组件 --

interface MarkdownRendererProps {
  content: string;
  tagRenderers?: any; // 将使用从Agent传递过来的TagRendererMap
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  tagRenderers = {},
}) => {

  // 处理内联格式的通用函数
  const processInlineFormats = (text: string): React.ReactNode[] => {
    if (!text) return [text];
    
    let result: React.ReactNode[] = [text];
    let keyCounter = 0;
    
    // 按优先级处理各种格式
    const formatters = [
      // 处理tag链接
      {
        regex: /\[([^\]]+)\]\((tag:\/\/([^\/]+)\/([^)]+))\)/g,
        process: (match: RegExpMatchArray) => {
          const [, fallbackText, , tagType, id] = match;
          const descriptor = tagRenderers[tagType];
          if (descriptor && typeof descriptor.render === 'function') {
            return descriptor.render(id, fallbackText);
          }
          return fallbackText;
        }
      },
      // 处理普通链接
      {
        regex: /\[([^\]]+)\]\(([^)]+)\)/g,
        process: (match: RegExpMatchArray) => (
          <a 
            key={keyCounter++} 
            href={match[2]} 
            target="_blank" 
            rel="noopener noreferrer"
            className={styles.link}
          >
            {match[1]}
          </a>
        )
      },
      // 处理内联代码
      {
        regex: /`([^`]+)`/g,
        process: (match: RegExpMatchArray) => (
          <code key={keyCounter++} className={styles.inlineCode}>
            {match[1]}
          </code>
        )
      },
      // 处理粗体+斜体
      {
        regex: /\*\*\*([^*]+)\*\*\*/g,
        process: (match: RegExpMatchArray) => (
          <strong key={keyCounter++}>
            <em>{match[1]}</em>
          </strong>
        )
      },
      // 处理粗体
      {
        regex: /\*\*([^*]+)\*\*/g,
        process: (match: RegExpMatchArray) => (
          <strong key={keyCounter++}>{match[1]}</strong>
        )
      },
      // 处理斜体
      {
        regex: /\*([^*]+)\*/g,
        process: (match: RegExpMatchArray) => (
          <em key={keyCounter++}>{match[1]}</em>
        )
      },
      // 处理删除线
      {
        regex: /~~([^~]+)~~/g,
        process: (match: RegExpMatchArray) => (
          <del key={keyCounter++}>{match[1]}</del>
        )
      }
    ];

    formatters.forEach(formatter => {
      result = result.flatMap(part => {
        if (typeof part !== 'string') return [part];
        
        const segments: React.ReactNode[] = [];
        let lastIndex = 0;
        let match;
        
        while ((match = formatter.regex.exec(part)) !== null) {
          // 添加匹配前的文本
          if (match.index && match.index > lastIndex) {
            segments.push(part.substring(lastIndex, match.index));
          }
          
          // 添加处理后的匹配内容
          segments.push(formatter.process(match));
          
          lastIndex = (match.index || 0) + match[0].length;
        }
        
        // 重置正则表达式
        formatter.regex.lastIndex = 0;
        
        // 添加最后一段文本
        if (lastIndex < part.length) {
          segments.push(part.substring(lastIndex));
        }
        
        return segments.length > 0 ? segments : [part];
      });
    });

    return result.filter(part => part !== '' && part !== null && part !== undefined);
  };

  // 渲染标题
  const renderHeading = (line: string, index: number): React.ReactNode | null => {
    const headingMatch = line.trim().match(/^(#{1,6})\s+(.+)$/);
    if (!headingMatch) return null;
    
    const level = headingMatch[1].length as 1 | 2 | 3 | 4 | 5 | 6;
    const headingText = headingMatch[2];
    
    const headingStyles = {
      1: { fontSize: '22px', fontWeight: 'bold', marginBottom: '8px', marginTop: '16px' },
      2: { fontSize: '20px', fontWeight: 'bold', marginBottom: '6px', marginTop: '14px' },
      3: { fontSize: '18px', fontWeight: 'bold', marginBottom: '5px', marginTop: '12px' },
      4: { fontSize: '16px', fontWeight: 'bold', marginBottom: '4px', marginTop: '10px' },
      5: { fontSize: '14px', fontWeight: 'bold', marginBottom: '3px', marginTop: '8px' },
      6: { fontSize: '13px', fontWeight: 'bold', marginBottom: '2px', marginTop: '6px' }
    };
    
    return React.createElement(
      `h${level}`,
      {
        key: index,
        style: { ...headingStyles[level], lineHeight: '1.3' },
        className: styles[`heading${level}`]
      },
      processInlineFormats(headingText)
    );
  };

  // 渲染引用块
  const renderBlockquote = (lines: string[], startIndex: number): { element: React.ReactNode; consumedLines: number } | null => {
    if (!lines[startIndex]?.trim().startsWith('>')) return null;
    
    const quoteLines: string[] = [];
    let currentIndex = startIndex;
    
    // 收集连续的引用行
    while (currentIndex < lines.length) {
      const line = lines[currentIndex];
      if (line.trim().startsWith('>')) {
        // 移除 '>' 和可选的空格
        const quoteLine = line.replace(/^\s*>\s?/, '');
        quoteLines.push(quoteLine);
        currentIndex++;
      } else if (line.trim() === '') {
        // 空行也包含在引用中
        quoteLines.push('');
        currentIndex++;
      } else {
        break;
      }
    }
    
    const quoteContent = quoteLines.join('\n');
    
    return {
      element: (
        <blockquote key={startIndex} className={styles.blockquote}>
          <MarkdownRenderer content={quoteContent} tagRenderers={tagRenderers} />
        </blockquote>
      ),
      consumedLines: currentIndex - startIndex
    };
  };

  // 渲染代码块
  const renderCodeBlock = (lines: string[], startIndex: number): { element: React.ReactNode; consumedLines: number } | null => {
    const line = lines[startIndex];
    const codeBlockMatch = line.match(/^```(\w+)?$/);
    if (!codeBlockMatch) return null;
    
    const language = codeBlockMatch[1] || '';
    const codeLines: string[] = [];
    let currentIndex = startIndex + 1;
    
    // 查找结束的 ```
    while (currentIndex < lines.length) {
      const currentLine = lines[currentIndex];
      if (currentLine.trim() === '```') {
        break;
      }
      codeLines.push(currentLine);
      currentIndex++;
    }
    
    return {
      element: (
        <div key={startIndex} className={styles.codeBlock}>
          {language && (
            <div className={styles.codeBlockLanguage}>{language}</div>
          )}
          <pre className={styles.codeBlockContent}>
            <code>{codeLines.join('\n')}</code>
          </pre>
        </div>
      ),
      consumedLines: currentIndex - startIndex + 1
    };
  };

  // 渲染有序列表项
  const renderOrderedListItem = (line: string, index: number): React.ReactNode | null => {
    const match = line.match(/^(\s*)(\d+)\.\s+(.+)$/);
    if (!match) return null;
    
    const [, indent, number, listText] = match;
    const indentLevel = Math.floor(indent.length / 2); // 每2个空格为一个缩进级别
    
    return (
      <div key={index} className={styles.orderedListItem} style={{
        marginLeft: `${indentLevel * 20}px`,
        marginBottom: '4px',
      }}>
        <span className={styles.orderedListNumber}>{number}.</span>
        <span className={styles.listItemContent}>
          {processInlineFormats(listText)}
        </span>
      </div>
    );
  };

  // 渲染无序列表项
  const renderUnorderedListItem = (line: string, index: number): React.ReactNode | null => {
    const match = line.match(/^(\s*)[-*]\s+(.+)$/);
    if (!match) return null;
    
    const [, indent, listText] = match;
    const indentLevel = Math.floor(indent.length / 2); // 每2个空格为一个缩进级别
    
    return (
      <div key={index} className={styles.unorderedListItem} style={{
        marginLeft: `${indentLevel * 20}px`,
        marginBottom: '4px',
      }}>
        <span className={styles.listBullet}>•</span>
        <span className={styles.listItemContent}>
          {processInlineFormats(listText)}
        </span>
      </div>
    );
  };

  // 渲染水平分割线
  const renderHorizontalRule = (line: string, index: number): React.ReactNode | null => {
    if (!/^(---+|\*\*\*+|___+)$/.test(line.trim())) return null;
    
    return <hr key={index} className={styles.horizontalRule} />;
  };

  // 主渲染函数
  const renderMarkdown = (markdown: string) => {
    const lines = markdown.split('\n');
    const result: React.ReactNode[] = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      
      // 处理代码块（优先级最高）
      const codeBlock = renderCodeBlock(lines, i);
      if (codeBlock) {
        result.push(codeBlock.element);
        i += codeBlock.consumedLines;
        continue;
      }
      
      // 处理引用块
      const blockquote = renderBlockquote(lines, i);
      if (blockquote) {
        result.push(blockquote.element);
        i += blockquote.consumedLines;
        continue;
      }
      
      // 处理标题
      if (line.trim().startsWith('#')) {
        const heading = renderHeading(line, i);
        if (heading) {
          result.push(heading);
          i++;
          continue;
        }
      }
      
      // 处理水平分割线
      const hr = renderHorizontalRule(line, i);
      if (hr) {
        result.push(hr);
        i++;
        continue;
      }
      
      // 处理有序列表
      const orderedItem = renderOrderedListItem(line, i);
      if (orderedItem) {
        result.push(orderedItem);
        i++;
        continue;
      }
      
      // 处理无序列表
      const unorderedItem = renderUnorderedListItem(line, i);
      if (unorderedItem) {
        result.push(unorderedItem);
        i++;
        continue;
      }
      
      // 处理空行
      if (!line.trim()) {
        result.push(
          <div key={i} className={styles.emptyLine}>
            &nbsp;
          </div>
        );
        i++;
        continue;
      }
      
      // 处理普通段落
      result.push(
        <div key={i} className={styles.paragraph}>
          {processInlineFormats(line)}
        </div>
      );
      i++;
    }

    return result;
  };

  const renderedContent = useMemo(() => renderMarkdown(content), [content, tagRenderers]);
  
  return (
    <div className={styles.markdownRenderer}>
      {renderedContent}
    </div>
  );
};

export default MarkdownRenderer; 