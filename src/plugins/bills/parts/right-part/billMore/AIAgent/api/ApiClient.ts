/**
 * API客户端抽象接口
 * 定义Agent与后端通信的契约
 */

import { FunctionDeclaration, UserMessage } from '../types/ChatMessage';

// 流式聊天请求载荷
export interface StreamChatPayload {
  message: UserMessage;
  history?: any[];
  chatContext?: string;
  functions?: FunctionDeclaration[];
}

// SSE事件类型
export type SSEEventType = 'text_delta' | 'function_call' | 'done' | 'error';

// SSE事件数据结构
export interface SSEEvent {
  type: SSEEventType;
  data: any;
}

// 文本增量事件
export interface TextDeltaEvent extends SSEEvent {
  type: 'text_delta';
  data: {
    text: string;
  };
}

// 函数调用事件
export interface FunctionCallEvent extends SSEEvent {
  type: 'function_call';
  data: {
    name: string;
    args: { [key: string]: any };
  };
}

// 完成事件
export interface DoneEvent extends SSEEvent {
  type: 'done';
  data: {
    price?: number;
    usage?: {
      promptTokenCount?: number;
      candidatesTokenCount?: number;
      [key: string]: any;
    };
  };
}

// 错误事件
export interface ErrorEvent extends SSEEvent {
  type: 'error';
  data: {
    message: string;
    code?: string;
  };
}

// 统一的SSE事件类型
export type ParsedSSEEvent = TextDeltaEvent | FunctionCallEvent | DoneEvent | ErrorEvent;

// API客户端接口
export interface ApiClient {
  /**
   * 发起流式聊天请求
   * @param payload 请求载荷
   * @returns 异步可迭代的SSE事件流
   */
  streamChat(payload: StreamChatPayload): AsyncIterable<ParsedSSEEvent>;
  
  /**
   * 关闭连接（如果有的话）
   */
  close?(): void;
} 