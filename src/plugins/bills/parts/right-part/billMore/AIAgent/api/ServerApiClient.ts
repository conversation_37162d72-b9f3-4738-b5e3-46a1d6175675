/**
 * 服务器API客户端实现
 * 使用SSE进行流式通信
 */

import { ApiClient, StreamChatPayload, ParsedSSEEvent } from './ApiClient';
import { session } from '@ekuaibao/session-info';
import { Fetch } from '@ekuaibao/fetch';
import { JSEncrypt } from 'jsencrypt';
import CryptoJS from 'crypto-js';

/**
 * Public key for request encryption.
 * In a real-world scenario, this should be fetched from a secure configuration endpoint
 * or embedded during the build process.
 * IMPORTANT: Replace this with the actual public key provided by the server.
 */
const SERVER_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnqPIKIAzb4byFNbk0G26
Z23yLXm8ZLH6J9qqKx+AM1QK04sbC7uRRo8QC/5gGlP4wXuWgC9k56u0aTPxw7FJ
5/Ym1SHFM2YdFaJ458tMHHxsGTXgdNiZmyo5KjNax5e7Do94dLJHAW8Co82LNb+2
83PA1oKp/yjClPEjHXy+lpUIvOnF7Mn6qp78yypJE5SXxEpLxdGhvuimxhkh+Pnn
MMrJbhNP/XUL8kkEo7hdSgr9YXBQMcxrL2BZSa8oJ9sAuXkajaBf8tXVd9HpjO0u
z6Ixf2v+3bxLzNa0VC6YBEc23myAyXwGQz2o6rCUguvw0ByoyzMbB6X0q+ouZow8
VQIDAQAB
-----END PUBLIC KEY-----`;

/**
 * 获取当前用户的完整会话信息
 * 整合session和Fetch中的信息
 */
function getCurrentUserSession() {
  try {
    const sessionUser = session.get('user') || {};
    return {
      accessToken: Fetch.accessToken || sessionUser.accessToken || '',
      corpId: sessionUser.corpId || Fetch.ekbCorpId || '',
      userId: sessionUser.userId || sessionUser.id || '',
      userName: sessionUser.userName || sessionUser.name || '',
      ...sessionUser
    };
  } catch (error) {
    console.error('获取用户会话信息失败:', error);
    return {
      accessToken: Fetch.accessToken || '',
      corpId: Fetch.ekbCorpId || '',
      userId: '',
      userName: ''
    };
  }
}

export class ServerApiClient implements ApiClient {
  private serverUrl: string;
  private currentEventSource?: EventSource;
  private currentEventType?: string; // 用于保存当前SSE事件类型

  constructor(serverUrl: string = 'https://genai.hosecloud.com/v1/gen') {
    this.serverUrl = serverUrl;
  }
  async *streamChat(payload: StreamChatPayload): AsyncIterable<ParsedSSEEvent> {
    // 关闭之前的连接
    this.close();
    // 重置事件类型状态
    this.currentEventType = undefined;
    console.log("streamChat", payload)
    try {
      // 获取认证token和企业ID
      const userSession = getCurrentUserSession();
      if (!userSession.accessToken) {
        throw new Error('用户未登录或token已过期');
      }
      
      if (!userSession.corpId) {
        throw new Error('无法获取企业ID');
      }

      // 构建完整的请求载荷，自动添加corporationId
      // 对 message 和 history 进行字段过滤，只保留 role 和 parts 属性
      const cleanedMessage = payload.message ? {
        role: payload.message.role,
        parts: payload.message.parts
      } : payload.message;
      
      const cleanedHistory = payload.history?.map(item => ({
        role: item.role,
        parts: item.parts
      })) || payload.history;
      
      const fullPayload = {
        ...payload,
        message: cleanedMessage,
        history: cleanedHistory,
        corporationId: userSession.corpId
      };

      // --- Hybrid Encryption Implementation ---

      // 1. Generate a random, one-time AES key and Initialization Vector (IV).
      // AES is a symmetric algorithm, fast and suitable for large data.
      const aesKey = CryptoJS.lib.WordArray.random(128 / 8); // 128-bit key
      const aesIv = CryptoJS.lib.WordArray.random(128 / 8);  // 128-bit IV for CBC mode

      // 2. Encrypt the large fullPayload with the AES key.
      const encryptedPayload = CryptoJS.AES.encrypt(
        JSON.stringify(fullPayload),
        aesKey,
        { 
          iv: aesIv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        }
      ).toString();

      // 3. Encrypt the short AES key and IV using the server's public RSA key.
      // This protects the AES key during transit.
      const rsaEncrypt = new JSEncrypt();
      rsaEncrypt.setPublicKey(SERVER_PUBLIC_KEY);

      const aesKeyData = {
        key: CryptoJS.enc.Base64.stringify(aesKey),
        iv: CryptoJS.enc.Base64.stringify(aesIv)
      };
      
      const encryptedAesKey = rsaEncrypt.encrypt(JSON.stringify(aesKeyData));
      if (encryptedAesKey === false) {
        throw new Error('AES key encryption failed. The public key might be invalid.');
      }
      
      // 4. Prepare the final request body with both parts.
      const finalBody = JSON.stringify({
        encryptedKey: encryptedAesKey,  // RSA-encrypted AES key and IV
        encryptedData: encryptedPayload // AES-encrypted main payload
      });

      // --- End of Hybrid Encryption ---

      // 构建EventSource URL（GET方式）
      const url = new URL(this.serverUrl);
      
      // 将载荷转换为查询参数或使用POST方式
      const fetchUrl = this.serverUrl;
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userSession.accessToken}`,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      };

      // 由于EventSource不支持POST，我们使用fetch + ReadableStream
      const response = await fetch(fetchUrl, {
        method: 'POST',
        headers,
        body: finalBody,
      });
      console.warn('body', fullPayload);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('响应体为空');
      }

      // 解析SSE流
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || '';

          // 处理完整的行
          for (const line of lines) {
            const event = this.parseSSELine(line);
            if (event) {
              yield event;
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      // 将网络错误转换为错误事件
      yield {
        type: 'error',
        data: {
          message: error instanceof Error ? error.message : '未知网络错误',
          code: 'NETWORK_ERROR'
        }
      };
    }
  }

  close(): void {
    if (this.currentEventSource) {
      this.currentEventSource.close();
      this.currentEventSource = undefined;
    }
  }

  /**
   * 解析SSE行数据
   */
  private parseSSELine(line: string): ParsedSSEEvent | null {
    if (!line.trim()) {
      return null;
    }

    try {
      // 解析事件类型行: "event: event_name"
      if (line.startsWith('event:')) {
        this.currentEventType = line.substring(6).trim();
        return null; // 等待对应的data行
      }

      // 解析数据行: "data: json_data"
      if (line.startsWith('data:')) {
        const jsonStr = line.substring(5).trim();
        
        if (jsonStr === '[DONE]') {
          // 某些实现使用 [DONE] 标记结束
          return {
            type: 'done',
            data: {}
          };
        }

        const data = JSON.parse(jsonStr);
        const eventType = this.currentEventType;
        
        // 重置当前事件类型
        this.currentEventType = undefined;
        
        // 根据事件类型返回对应的ParsedSSEEvent
        switch (eventType) {
          case 'text_delta':
            return {
              type: 'text_delta',
              data
            };
          
          case 'function_call':
            return {
              type: 'function_call',
              data
            };
          
          case 'done':
            return {
              type: 'done',
              data
            };
          
          case 'error':
            return {
              type: 'error',
              data
            };
          
          default:
            console.warn('未知的SSE事件类型:', eventType, '数据:', data);
            return null;
        }
      }

      return null;
    } catch (error) {
      console.error('解析SSE数据失败:', line, error);
      return {
        type: 'error',
        data: {
          message: '数据解析失败',
          code: 'PARSE_ERROR'
        }
      };
    }
  }
} 