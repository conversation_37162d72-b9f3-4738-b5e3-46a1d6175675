/**
 * AIAgent 智能助手模块
 * 
 * 这是 AIAgent 模块的统一入口，提供完整的智能助手解决方案
 * 包括对话管理、工具系统、API客户端和UI组件等核心功能
 * 
 * ============================================================================
 * 架构说明：
 * - Agent：核心对话引擎，负责与AI API通信和工具调用编排  
 * - ApiClient：API抽象层，支持不同的AI服务提供商（Server、Mock等）
 * - Tools：工具系统，通过依赖注入方式为AI提供业务操作能力
 * - Components：React UI组件，提供开箱即用的聊天界面
 * - Hooks：React状态管理，将Agent状态同步到UI组件
 * - Types：TypeScript类型定义，确保类型安全
 * ============================================================================
 */

// ============================= UI 组件导出 =============================
/**
 * AI副驾驶主界面组件
 * 提供完整的对话界面，包括消息展示、输入框、推荐问题等
 */
export { default as AICopilot } from './components/AICopilot'

/**
 * AI悬浮球图标组件  
 * 用于触发AI助手界面的入口按钮
 */
export { default as AIOrbIcon } from './components/OrbIcon/Orb'

// ============================= 核心类型导出 =============================
/**
 * 聊天消息类型定义
 * 包含用户消息、AI回复、系统消息等所有消息类型
 */
export type { ChatMessage, FunctionDeclaration } from './types/ChatMessage'

/**
 * 工具定义接口
 * 定义了AI可以调用的工具的结构和输出格式
 */
export type { Tool, ToolOutput } from './tools/Tool'

/**
 * API客户端抽象接口
 * 定义了与AI服务通信的标准接口，支持多种实现
 */
export type { ApiClient } from './api/ApiClient'

/**
 * 自定义标签渲染器类型定义
 * 允许业务端注入自定义的UI渲染逻辑
 */
export type { TagRendererDescriptor, TagRendererMap } from './types/TagRenderer';

// ============================= 核心引擎导出 =============================
/**
 * Agent 核心对话引擎
 * 负责管理对话状态、调用AI API、执行工具等核心功能
 */
export { Agent } from './core/Agent'

/**
 * Agent 配置和状态类型
 * - AgentConfig: Agent初始化配置，包括API客户端、工具列表、系统指令等
 * - AgentStatus: Agent运行状态，包括空闲、思考中、执行工具等状态
 * - SystemInstruction: 系统指令类型定义
 */
export type { AgentConfig, AgentStatus, SystemInstruction } from './core/Agent'

// ============================= React Hooks 导出 =============================
/**
 * Agent状态管理Hook
 * 将Agent的状态同步到React组件，提供响应式的状态更新
 */
export { useAgentState } from './hooks/useAgentState'

/**
 * Agent状态类型定义
 * 包含消息列表、加载状态、错误信息等UI所需的所有状态
 */
export type { AgentState } from './hooks/useAgentState'

// ============================= API 客户端导出 =============================
/**
 * 服务端API客户端实现
 * 连接到实际的AI服务提供商，处理HTTP请求和响应
 * 支持流式响应、错误处理、重试机制等
 */
export { ServerApiClient } from './api/ServerApiClient'

// ============================= 工具系统 (Function Calling) =============================
/**
 * 导出 Agent 的工具（Function Calling）相关定义。
 * "Tool" 是 Agent 执行具体操作的接口，每个 Tool 都代表一个可供 AI 调用的函数。
 *
 * 这里导出创建自定义工具所需的基础设施，包括：
 * - `Tool`: 定义工具结构（名称、描述、输入参数、执行函数）的基础接口。
 * - `ToolOutput`: 工具执行后返回结果的统一格式。
 * - `ToolContext`: 工具执行时可以访问的上下文信息。
 * - 辅助函数: 如 `validateArgs`, `safeExecute`, `createSuccessResult`, `createErrorResult` 等，用于简化工具的创建和执行。
 *
 * 开发者可以基于这些导出来实现自定义工具，从而扩展 Agent 的能力。
 */
export * from './tools/Tool'

// ============================= 工具函数导出 =============================
/**
 * 创建Agent实例的工厂函数
 * 简化Agent的创建过程，提供合理的默认配置
 */
export function createAgent(config: import('./core/Agent').AgentConfig): import('./core/Agent').Agent {
  const { Agent } = require('./core/Agent');
  return new Agent(config);
}

/**
 * 创建默认的服务端API客户端
 * 使用默认配置创建ServerApiClient实例
 */
export function createServerApiClient(baseUrl?: string): import('./api/ServerApiClient').ServerApiClient {
  const { ServerApiClient } = require('./api/ServerApiClient');
  return new ServerApiClient(baseUrl || 'https://genai.hosecloud.com/v1/gen');
} 