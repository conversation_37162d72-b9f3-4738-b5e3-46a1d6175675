/**
 * @file 定义了用于 Markdown 渲染器中自定义标签的类型。
 *
 * 这个系统允许通过注入自定义渲染器来扩展 Markdown 的功能，
 * 使得 Agent 的响应能够包含丰富的、与业务数据联动的 UI 组件。
 */

import type React from 'react';

/**
 * 描述一个自定义标签渲染器的对象。
 * 它包含了生成系统提示词所需的信息以及实际的渲染逻辑。
 */
export interface TagRendererDescriptor {
  /**
   * 标签的简短描述，用于向 LLM 解释这个标签的用途。
   * 例如："一个用户"、"一条费用明细"、"一个产品"。
   */
  description: string;

  /**
   * 渲染函数，负责将标签的值（如 USER_ID）转换成一个 React 组件。
   * @param id - 从标签 `(tag://key/id)` 中解析出的 `id` 部分。
   * @param fallbackText - 从 Markdown 链接 `[fallbackText](...)` 中解析出的默认显示文本。
   * @returns 一个可供渲染的 React 节点。
   */
  render: (id: string, fallbackText: string) => React.ReactNode;
}

/**
 * 标签渲染器的映射表。
 * Key: 标签的唯一标识符（即类型），例如 'user', 'details'。
 * Value: 该标签的描述符对象。
 */
export type TagRendererMap = Record<string, TagRendererDescriptor>; 