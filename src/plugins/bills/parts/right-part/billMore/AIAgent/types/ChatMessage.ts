/**
 * 聊天消息类型定义
 * 与后端API和Gemini规范保持一致
 */

import { ToolType } from '../tools/Tool';

// 定义与 Gemini API 一致的 Part 结构
export interface FunctionCall { 
  name: string; 
  args: { [key: string]: any }; 
}

export interface FunctionResponse { 
  name: string; 
  response: { [key: string]: any }; 
}

export type Part =
  | { text: string; }
  | { inlineData: { mimeType: string; data: string; }; }
  | { functionCall: FunctionCall; }
  | { functionResponse: FunctionResponse; };

// 基础消息接口
export interface BaseMessage {
  id: string;
  timestamp: Date;
}

// 用户消息
export interface UserMessage extends BaseMessage {
  role: 'user';
  parts: Part[];
}

// 模型消息 (可以是文本或工具调用)
export interface ModelMessage extends BaseMessage {
  role: 'model';
  parts: Part[];
  status: 'streaming' | 'complete' | 'error';
}

// UI 专用：用于展示工具执行状态
export interface ToolStatusMessage extends BaseMessage {
  role: 'tool';
  toolName: string;
  toolType: ToolType; // 添加工具类型字段
  toolArgs: { [key: string]: any };
  status: 'running' | 'success' | 'error';
  result?: any;
  error?: string;
}

// UI 专用：用于显示系统级错误
export interface SystemErrorMessage extends BaseMessage {
  role: 'system';
  content: string;
}

// 最终的聊天历史记录中使用的消息类型
export type ChatMessage = UserMessage | ModelMessage | ToolStatusMessage | SystemErrorMessage;

// 工具函数声明结构（用于发送给后端）
export interface FunctionDeclaration {
  name: string;
  description: string;
  parameters: {
    type: 'OBJECT';
    properties: { [key: string]: any };
    required?: string[];
  };
}

// 消息工具函数
export function createUserMessage(input: string | Part | Part[]): UserMessage {
  let parts: Part[];
  
  if (typeof input === 'string') {
    parts = [{ text: input }];
  } else if (Array.isArray(input)) {
    parts = input;
  } else {
    parts = [input];
  }
  
  return {
    id: generateMessageId(),
    timestamp: new Date(),
    role: 'user',
    parts
  };
}

export function createModelMessage(parts: Part[] = []): ModelMessage {
  return {
    id: generateMessageId(),
    timestamp: new Date(),
    role: 'model',
    parts,
    status: 'streaming'
  };
}

export function createToolStatusMessage(
  toolName: string, 
  toolType: ToolType,
  toolArgs: { [key: string]: any }, 
  status: 'running' | 'success' | 'error' = 'running'
): ToolStatusMessage {
  return {
    id: generateMessageId(),
    timestamp: new Date(),
    role: 'tool',
    toolName,
    toolType,
    toolArgs,
    status
  };
}

export function createSystemErrorMessage(content: string): SystemErrorMessage {
  return {
    id: generateMessageId(),
    timestamp: new Date(),
    role: 'system',
    content
  };
}

// 生成消息ID
function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
} 