/**
 * 核心Agent类
 * 管理对话状态、编排工具调用和API通信
 * 纯业务逻辑，不包含任何UI依赖
 */

import { ApiClient, StreamChatPayload } from '../api/ApiClient';
import { Tool, ToolContext, ToolOutput, ToolError, ToolType } from '../tools/Tool';
import { 
  ChatMessage, 
  Part, 
  UserMessage,
  ModelMessage,
  ToolStatusMessage,
  createUserMessage, 
  createModelMessage, 
  createToolStatusMessage,
  createSystemErrorMessage,
  FunctionDeclaration,
} from '../types/ChatMessage';
import type { TagRendererMap } from '../types/TagRenderer';

// 新增：系统指令结构
export interface SystemInstruction {
  role?: string; // 核心身份与角色 (Core Identity & Role)
  principles?: string; // 指导原则与行为准则 (Guiding Principles & Behavior)
  workflow?: string; // 核心工作流程与思考链(Core Workflow & Chain of Thought)
  toolRules?: string; // 工具调用规则 (Tool Usage Rules)
  output?: string; // 输出格式规范 (Output Format Specification)
  globalContext?: string; // 全局通用上下文信息，例如今天的日期
}

// Agent配置
export interface AgentConfig {
  apiClient: ApiClient;
  tools?: Tool[];
  systemInstruction?: SystemInstruction;
  tagRenderers?: TagRendererMap; // 新增：自定义标签渲染器
  name: string; // Agent名称，用于UI展示
  description: string; // Agent介绍，用于UI展示
  recommendedQuestions: string[]; // 推荐问题，可为空数组
  quickActions: string[]; // 快捷操作，可为空数组
  maxConsecutiveToolCalls?: number; // 新增：最大连续工具调用次数
}

// Agent状态
export type AgentStatus = 'idle' | 'thinking' | 'tool_calling' | 'error';

// 流式处理内部状态
interface StreamState {
  isDoneReceived: boolean;
  pendingFunctionCalls: Array<{
    name: string;
    args: any;
    toolStatusMessageId: string;
  }>;
}

export class Agent {
  // 公开状态（供React订阅）
  public history: ChatMessage[] = [];
  public status: AgentStatus = 'idle';
  public readonly config: AgentConfig;
  public readonly tagRenderers: TagRendererMap; // 新增：公开的渲染器属性
  
  // 私有状态
  private context: ToolContext = {};
  private tools: Map<string, Tool> = new Map();
  private onUpdate: () => void = () => {};
  private currentModelMessage?: ModelMessage;
  private currentStream: StreamState | null = null;
  private readonly _systemInstructionString: string;
  
  // 循环控制
  private consecutiveToolCallCount: number = 0;
  private readonly maxConsecutiveToolCalls: number;

  constructor(config: AgentConfig) {
    this.config = config;
    this.tagRenderers = config.tagRenderers || {}; // 初始化渲染器
    this.maxConsecutiveToolCalls = config.maxConsecutiveToolCalls ?? 10;
    this._systemInstructionString = this._buildSystemInstructionString({
      ...config.systemInstruction,
      // 动态生成标签指令并注入到 output 部分
      output: [
        config.systemInstruction?.output,
        this._generateTagInstructions()
      ].filter(Boolean).join('\n\n')
    });
    
    // 注册工具
    config.tools?.forEach(tool => {
      this.tools.set(tool.definition.name, tool);
    });
  }

  // --- 公开方法（供React使用） ---

  /**
   * 订阅状态变化
   * @param callback 状态变化回调
   * @returns 取消订阅函数
   */
  public subscribe(callback: () => void): () => void {
    this.onUpdate = callback;
    return () => { 
      this.onUpdate = () => {}; 
    };
  }

  /**
   * 更新工具执行上下文
   * @param newContext 新的上下文数据
   */
  public updateContext(newContext: Partial<ToolContext>): void {
    this.context = { ...this.context, ...newContext };
  }

  /**
   * 发送用户消息
   * @param text 用户输入的文本
   */
  public async sendMessage(text: string): Promise<void> {
    if (this.status !== 'idle') {
      console.warn('Agent正忙，无法处理新消息');
      return;
    }
    
    // 用户主动发送消息时，重置连续工具调用计数
    this.consecutiveToolCallCount = 0;
    await this._sendUserParts([{ text }]);
  }

  /**
   * 清空对话历史
   */
  public clearHistory(): void {
    this.history = [];
    this.currentStream = null;
    this.consecutiveToolCallCount = 0;
    this._setStatus('idle');
    this._notifyUpdate();
  }

  /**
   * 获取工具列表（用于发送给API）
   */
  public getToolDefinitions(): FunctionDeclaration[] {
    return Array.from(this.tools.values()).map(tool => tool.definition);
  }

  /**
   * 获取可用工具列表
   */
  public getAvailableTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  /**
   * 获取当前状态
   */
  public getStatus(): AgentStatus {
    return this.status;
  }

  // --- 私有方法 ---

  private _notifyUpdate(): void {
    this.onUpdate();
  }

  private _setStatus(status: AgentStatus): void {
    this.status = status;
    this._notifyUpdate();
  }

  /**
   * 新增：根据 tagRenderers 动态生成给 LLM 的指令
   */
  private _generateTagInstructions(): string {
    const availableTags = Object.keys(this.tagRenderers);
    if (availableTags.length === 0) return '';

    const instructions = availableTags.map(key => {
      const descriptor = this.tagRenderers[key];
      // -> 引用一个【用户】，请使用格式 `[@用户名](tag://user/USER_ID)`。
      return `- 引用一个【${descriptor.description}】，请使用格式 \`[相关文本](tag://${key}/ID)\`。`;
    }).join('\n');

    return `你可以使用以下特殊格式来丰富你的回答，以便嵌入动态的UI元素：\n${instructions}`;
  }

  private _buildSystemInstructionString(instruction?: SystemInstruction): string {
    if (!instruction) {
      return '';
    }

    const parts: { title: string; content?: string }[] = [
      { title: '核心身份与角色 (Core Identity & Role)', content: instruction.role },
      { title: '指导原则与行为准则 (Guiding Principles & Behavior)', content: instruction.principles },
      { title: '核心工作流程与思考链 (Core Workflow & Chain of Thought)', content: instruction.workflow },
      { title: '工具调用规则 (Tool Usage Rules)', content: instruction.toolRules },
      { title: '输出格式规范 (Output Format Specification)', content: instruction.output },
      { title: '全局通用上下文信息 (Global Context)', content: instruction.globalContext },
    ];

    let instructionCount = 0;
    return parts
      .filter(part => part.content && part.content.trim())
      .map(part => {
        instructionCount++;
        return `# ${instructionCount}. ${part.title}\n${part.content}`;
      })
      .join('\n\n');
  }

  private async _callModelAndStream(message: UserMessage, history: any[]): Promise<void> {
    const payload: StreamChatPayload = {
      message,
      history,
      chatContext: this._systemInstructionString,
      functions: this.getToolDefinitions(),
    };

    // 初始化流状态
    this.currentStream = {
      isDoneReceived: false,
      pendingFunctionCalls: [],
    };

    try {
      for await (const event of this.config.apiClient.streamChat(payload)) {
        switch (event.type) {
          case 'text_delta':
            this._handleTextDelta(event.data.text);
            break;
          case 'function_call':
            this._handleFunctionCallEvent(event.data);
            break;
          case 'done':
            await this._handleDoneEvent(event.data);
            break;
          case 'error':
            this._handleError(event.data.message);
            break;
        }
      }
    } catch (error) {
      console.error('API调用失败:', error);
      this._handleError(error instanceof Error ? error.message : 'API调用失败');
    }
  }

  private _handleTextDelta(text: string): void {
    if (!this.currentModelMessage || this.currentModelMessage.role !== 'model') return;

    // 更新当前模型消息的文本内容
    const textPart = this.currentModelMessage.parts.find(part => 'text' in part);
    if (textPart && 'text' in textPart) {
      textPart.text += text;
    } else {
      this.currentModelMessage.parts.push({ text });
    }

    this.currentModelMessage.status = 'streaming';
    
    this._notifyUpdate();
  }

  private _handleFunctionCallEvent(callData: { name: string; args: any }): void {
    if (!this.currentStream) return;
  
    this._setStatus('tool_calling');
  
    if (this.currentModelMessage) {
      this.currentModelMessage.parts.push({
        functionCall: { name: callData.name, args: callData.args }
      });
      this._notifyUpdate();
    }
  
    // 从工具注册表中获取工具类型
    const tool = this.tools.get(callData.name);
    const toolType = tool?.type || ToolType.OTHER; // 如果找不到工具，默认为其他类型
    
    const toolStatusMessage = createToolStatusMessage(callData.name, toolType, callData.args, 'running');
    this.history.push(toolStatusMessage);
    
    this.currentStream.pendingFunctionCalls.push({
      ...callData,
      toolStatusMessageId: toolStatusMessage.id
    });
    
    this._notifyUpdate();
  }

  private async _handleDoneEvent(data: any): Promise<void> {
    if (!this.currentStream) return;

    this.currentStream.isDoneReceived = true;

    if (this.currentModelMessage) {
      this.currentModelMessage.status = 'complete';
      this._notifyUpdate();
    }

    if (data.usage || data.price) {
      // 处理用量统计
    }

    await this._processPendingFunctionCalls();
  }

  private async _processPendingFunctionCalls(): Promise<void> {
    if (!this.currentStream || !this.currentStream.isDoneReceived) return;

    const { pendingFunctionCalls } = this.currentStream;
    this.currentStream = null; // Mark current stream as processed

    if (pendingFunctionCalls.length === 0) {
      this._finishCurrentTurn();
      return;
    }

    if (this.consecutiveToolCallCount >= this.maxConsecutiveToolCalls) {
      this._handleMaxToolCallsReached();
      return;
    }
    
    this.consecutiveToolCallCount++;

    const toolExecutionPromises = pendingFunctionCalls.map(async (callInfo) => {
      let result: ToolOutput;
      try {
        result = await this._executeTool(callInfo.name, callInfo.args);
      } catch (error) {
        console.error(`工具执行异常 [${callInfo.name}]:`, error);
        result = { isSuccess: false, error: error instanceof Error ? error.message : '工具执行时发生未知异常' };
      }

      const toolStatusMessage = this.history.find(
        (msg): msg is ToolStatusMessage => msg.role === 'tool' && msg.id === callInfo.toolStatusMessageId
      );
      
      if (toolStatusMessage) {
        if (result.isSuccess) {
          toolStatusMessage.status = 'success';
          toolStatusMessage.result = result.data;
        } else {
          toolStatusMessage.status = 'error';
          toolStatusMessage.error = (result as ToolError).error;
        }
        this._notifyUpdate();
      }

      const responseContent = result.isSuccess 
        ? result.data 
        : { success: false, reason: (result as ToolError).error };

      return {
        functionResponse: {
          name: callInfo.name,
          response: {
            content: responseContent
          }
        }
      } as Part;
    });

    const functionResponses = await Promise.all(toolExecutionPromises);
    await this._sendUserParts(functionResponses);
  }


  private async _executeTool(toolName: string, args: any): Promise<ToolOutput> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return { isSuccess: false, error: `工具不存在: ${toolName}` };
    }

    return tool.execute(args, this.context);
  }

  /**
   * 核心私有方法：发送一个包含指定parts的用户消息，并触发模型调用
   * @param parts 要发送的消息parts
   */
  private async _sendUserParts(parts: Part[]): Promise<void> {
    try {
      // --- 1. 为API准备数据 ---
      const historyForApi = this.history
        .filter((msg): msg is UserMessage | ModelMessage => 
          msg.role === 'user' || (msg.role === 'model' && msg.parts.length > 0)
        )
        .map(msg => ({ role: msg.role, parts: msg.parts }));

      // --- 2. 更新UI ---
      const userMessage = createUserMessage(parts);
      this.history.push(userMessage);

      this._setStatus('thinking');
      this.currentModelMessage = createModelMessage();
      this.history.push(this.currentModelMessage);
      this._notifyUpdate();

      // --- 3. 调用API ---
      await this._callModelAndStream(userMessage, historyForApi);

    } catch (error) {
      console.error('❌ 发送用户parts失败:', error);
      console.error('📍 错误堆栈:', error instanceof Error ? error.stack : error);
      this._handleError(error instanceof Error ? error.message : '发送用户parts失败');
    }
  }

  private _finishCurrentTurn(): void {
    this._setStatus('idle');
  }

  private _handleMaxToolCallsReached(): void {
    console.warn(`已达到连续工具调用的最大次数: ${this.maxConsecutiveToolCalls}`);
    
    const systemMessage = createSystemErrorMessage(
      `已达到连续调用工具的最大次数 (${this.maxConsecutiveToolCalls})，请重新发送消息继续对话。`
    );
    this.history.push(systemMessage);
    
    this.consecutiveToolCallCount = 0;
    this.currentStream = null;
    this._setStatus('idle');
    this._notifyUpdate();
  }

  private _handleError(message: string): void {
    if (this.currentModelMessage) {
      this.currentModelMessage.status = 'error';
    }

    const errorMessage = createSystemErrorMessage(message);
    this.history.push(errorMessage);
    
    this._setStatus('error');
    this._notifyUpdate();
  }
} 