# AIAgent 模块完整开发指南

## 🚀 简介

`AIAgent` 是一个通用的智能助手前端模块，基于现代前端架构设计，支持与大语言模型进行流式对话、函数调用（Function Calling）和动态内容渲染。它为开发者提供了一套完整的工具来快速构建和集成具备智能交互能力的AI助手到任何React应用中。

### 核心特性

- 🎯 **场景化Agent预设** - 为不同业务场景提供专门优化的Agent实例
- 🛠️ **丰富的工具系统** - 支持自定义工具，实现AI与业务系统的深度集成
- 🎨 **动态内容渲染** - 通过TagRenderer系统支持富文本和交互式UI组件
- 📡 **完整流式支持** - 支持文本流式输出和多工具并行调用
- 🔒 **调试模式彩蛋** - 内置调试功能，输入`/debug`开启开发者模式
- 🧩 **高度可扩展** - 模块化设计，支持插件式扩展

---

## 🏗️ 架构设计

### 核心设计原则

- **关注点分离 (SoC)**: UI组件与业务逻辑严格分离
- **依赖注入 (DI)**: Agent的能力通过外部注入，保持核心通用性
- **面向接口编程**: 基于接口而非实现编程，便于测试和替换
- **响应式状态管理**: 通过React Hook实现状态的响应式更新
- **健壮的错误处理**: 工具错误作为数据返回，赋予AI自主纠错能力

### 目录结构

```
AIAgent/
├── api/                          # API通信层
│   ├── ApiClient.ts              # API客户端接口
│   └── ServerApiClient.ts        # 服务端API实现
│
├── components/                   # UI组件层
│   ├── AICopilot.tsx            # 主入口组件
│   ├── AICopilot.module.less    # 主组件样式
│   ├── Message.tsx              # 单条消息组件
│   ├── MessageList.tsx          # 消息列表组件
│   ├── ChatInput.tsx            # 聊天输入框
│   ├── WelcomeScreen.tsx        # 欢迎界面
│   ├── MarkdownRenderer.tsx     # Markdown渲染器
│   └── TestWrapper.tsx          # 测试包装组件
│
├── core/                        # 核心业务层
│   └── Agent.ts                 # Agent核心类
│
├── hooks/                       # React状态管理
│   └── useAgentState.ts         # Agent状态Hook
│
├── tools/                       # 工具系统
│   ├── Tool.ts                  # 工具接口定义
│   └── bill/                    # 单据工具集
│       ├── index.ts             # 工具导出和工厂
│       ├── utils.ts             # 工具辅助函数
│       ├── get-field-value.tool.ts    # 获取字段值
│       ├── set-field-value.tool.ts    # 设置字段值
│       ├── get-editable-fields.tool.ts # 获取可编辑字段
│       ├── add-detail-row.tool.ts     # 添加明细行
│       └── delete-detail-row.tool.ts  # 删除明细行
│
├── types/                       # 类型定义
│   ├── ChatMessage.ts          # 消息类型定义
│   └── TagRenderer.ts          # 标签渲染器类型
│
└── index.ts                     # 模块统一出口
```

### 核心概念

#### 1. Agent
核心对话引擎，负责：
- 管理对话历史和状态
- 编排工具调用序列
- 处理流式API通信
- 控制对话流程

#### 2. Tool（工具）
Agent的操作能力，分为5种类型：
- **编辑 (EDIT)**: 修改数据的工具，如设置字段值
- **查看 (VIEW)**: 查询信息的工具，如获取单据详情  
- **分析 (ANALYZE)**: 数据分析工具，如统计计算
- **审核 (REVIEW)**: 审核验证工具，如风险检查
- **其他 (OTHER)**: 其他功能工具

#### 3. TagRenderer（标签渲染器）
Agent的展示能力，支持在AI回复中嵌入富文本和交互组件：
- 格式：`[@显示文本](tag://类型/ID)`
- 支持自定义React组件渲染
- 可实现点击交互、数据联动等

#### 4. ApiClient
API通信抽象层，支持：
- 流式数据传输
- 函数调用协议
- 错误处理和重试

---

## 🎯 从零开始：创建你的第一个Agent

### 第一步：确定业务场景

假设我们要为订单管理页面创建一个`OrderAgent`，它需要能够：
- 查询订单详情
- 修改订单状态
- 展示产品信息卡片
- 展示客户信息卡片

### 第二步：创建目录结构

```bash
mkdir -p src/plugins/orders/OrderAgent/{components,tools,types}
```

```
src/plugins/orders/OrderAgent/
├── components/
│   └── OrderTags.tsx           # 自定义标签组件
├── tools/
│   ├── index.ts               # 工具工厂
│   ├── get-order-info.tool.ts # 获取订单信息
│   └── update-order-status.tool.ts # 更新订单状态
├── types/
│   └── index.ts               # 类型定义
├── createOrderTagRenderers.ts  # 标签渲染器工厂
└── OrderAgent.ts              # Agent主入口
```

### 第三步：定义类型和依赖

```typescript
// src/plugins/orders/OrderAgent/types/index.ts
export interface OrderAgentDependencies {
  orderId: string;
  orderDetails: any;
  apiClient: any;
  userInfo: any;
  onOrderUpdate?: (orderId: string) => void;
}

export interface OrderInfo {
  id: string;
  status: string;
  customerId: string;
  products: Product[];
  totalAmount: number;
}

export interface Product {
  id: string;
  name: string;
  price: number;
  quantity: number;
}
```

### 第四步：实现工具

```typescript
// src/plugins/orders/OrderAgent/tools/get-order-info.tool.ts
import { Tool, ToolContext, ToolOutput, ToolType } from '../../../bills/parts/right-part/billMore/AIAgent/tools/Tool';

export const getOrderInfoTool: Tool = {
  type: ToolType.VIEW,
  definition: {
    name: 'get_order_info',
    description: '获取指定订单的详细信息',
    parameters: {
      type: 'object',
      properties: {
        orderId: {
          type: 'string',
          description: '订单ID'
        }
      },
      required: ['orderId']
    }
  },
  execute: async (args: { orderId: string }, context: ToolContext): Promise<ToolOutput> => {
    const { orderDetails, apiClient } = context;
    
    try {
      // 如果是查询当前订单
      if (args.orderId === orderDetails?.id) {
        const markdown = `## 📋 订单信息

**订单号**: [@${orderDetails.id}](tag://order/${orderDetails.id})
**状态**: ${orderDetails.status}
**客户**: [@${orderDetails.customerName}](tag://customer/${orderDetails.customerId})
**总金额**: ¥${orderDetails.totalAmount}

### 商品清单
${orderDetails.products?.map(p => 
  `- [@${p.name}](tag://product/${p.id}) × ${p.quantity} = ¥${p.price * p.quantity}`
).join('\n')}`;

        return { isSuccess: true, data: { markdown } };
      }
      
      // 查询其他订单
      const orderInfo = await apiClient.getOrder(args.orderId);
      
      if (!orderInfo) {
        return { isSuccess: false, error: `未找到订单 ${args.orderId}` };
      }
      
      // 构造带标签的markdown响应
      const markdown = `## 📋 订单 ${orderInfo.id}
      
**状态**: ${orderInfo.status}
**客户**: [@${orderInfo.customerName}](tag://customer/${orderInfo.customerId})
**总金额**: ¥${orderInfo.totalAmount}`;

      return { isSuccess: true, data: { markdown } };
      
    } catch (error) {
      return { 
        isSuccess: false, 
        error: `查询订单信息失败: ${error instanceof Error ? error.message : '未知错误'}` 
      };
    }
  }
};
```

```typescript
// src/plugins/orders/OrderAgent/tools/update-order-status.tool.ts
import { Tool, ToolContext, ToolOutput, ToolType } from '../../../bills/parts/right-part/billMore/AIAgent/tools/Tool';
import { OrderAgentDependencies } from '../types';

export function createUpdateOrderStatusTool(dependencies: OrderAgentDependencies): Tool {
  const { onOrderUpdate } = dependencies;
  
  return {
    type: ToolType.EDIT,
    definition: {
      name: 'update_order_status',
      description: '更新订单状态',
      parameters: {
        type: 'object',
        properties: {
          orderId: {
            type: 'string',
            description: '订单ID'
          },
          status: {
            type: 'string',
            description: '新状态',
            enum: ['pending', 'confirmed', 'shipping', 'delivered', 'cancelled']
          },
          reason: {
            type: 'string',
            description: '状态变更原因'
          }
        },
        required: ['orderId', 'status']
      }
    },
    execute: async (args: { orderId: string; status: string; reason?: string }, context: ToolContext): Promise<ToolOutput> => {
      const { apiClient } = context;
      
      try {
        await apiClient.updateOrderStatus(args.orderId, args.status, args.reason);
        
        // 触发页面更新回调
        onOrderUpdate?.(args.orderId);
        
        return { 
          isSuccess: true, 
          data: { 
            message: `订单 ${args.orderId} 状态已更新为 ${args.status}`,
            markdown: `✅ 订单状态更新成功\n\n订单 [@${args.orderId}](tag://order/${args.orderId}) 已更新为 **${args.status}**${args.reason ? `\n\n更新原因: ${args.reason}` : ''}`
          } 
        };
      } catch (error) {
        return { 
          isSuccess: false, 
          error: `更新订单状态失败: ${error instanceof Error ? error.message : '未知错误'}` 
        };
      }
    }
  };
}
```

```typescript
// src/plugins/orders/OrderAgent/tools/index.ts
import { Tool } from '../../../bills/parts/right-part/billMore/AIAgent/tools/Tool';
import { OrderAgentDependencies } from '../types';
import { getOrderInfoTool } from './get-order-info.tool';
import { createUpdateOrderStatusTool } from './update-order-status.tool';

export function createOrderTools(dependencies: OrderAgentDependencies): Tool[] {
  return [
    getOrderInfoTool,
    createUpdateOrderStatusTool(dependencies)
  ];
}
```

### 第五步：实现TagRenderer

```typescript
// src/plugins/orders/OrderAgent/components/OrderTags.tsx
import React from 'react';
import { Button } from '@hose/eui';

export const OrderTag: React.FC<{ orderId: string, fallbackText: string }> = ({ orderId, fallbackText }) => {
  const handleClick = () => {
    // 跳转到订单详情页
    window.open(`/orders/${orderId}`, '_blank');
  };

  return (
    <Button 
      category="text" 
      size="small" 
      style={{ 
        padding: '2px 6px', 
        height: 'auto',
        color: '#1677ff',
        textDecoration: 'underline'
      }}
      onClick={handleClick}
    >
      {fallbackText}
    </Button>
  );
};

export const ProductTag: React.FC<{ productId: string, fallbackText: string }> = ({ productId, fallbackText }) => {
  return (
    <span style={{
      backgroundColor: '#f0f8ff',
      border: '1px solid #d0e7ff',
      borderRadius: '4px',
      padding: '2px 6px',
      fontSize: '12px',
      color: '#1677ff',
      cursor: 'pointer'
    }}>
      🛍️ {fallbackText}
    </span>
  );
};

export const CustomerTag: React.FC<{ customerId: string, fallbackText: string }> = ({ customerId, fallbackText }) => {
  return (
    <span style={{
      backgroundColor: '#f6ffed',
      border: '1px solid #b7eb8f',
      borderRadius: '4px',
      padding: '2px 6px',
      fontSize: '12px',
      color: '#52c41a',
      cursor: 'pointer'
    }}>
      👤 {fallbackText}
    </span>
  );
};
```

```typescript
// src/plugins/orders/OrderAgent/createOrderTagRenderers.ts
import { TagRendererMap } from '../../bills/parts/right-part/billMore/AIAgent/types/TagRenderer';
import { OrderTag, ProductTag, CustomerTag } from './components/OrderTags';

export function createOrderTagRenderers(): TagRendererMap {
  return {
    order: {
      description: '订单信息',
      render: (id: string, text: string) => <OrderTag orderId={id} fallbackText={text} />
    },
    product: {
      description: '产品信息',
      render: (id: string, text: string) => <ProductTag productId={id} fallbackText={text} />
    },
    customer: {
      description: '客户信息',
      render: (id: string, text: string) => <CustomerTag customerId={id} fallbackText={text} />
    }
  };
}
```

### 第六步：创建Agent工厂

```typescript
// src/plugins/orders/OrderAgent/OrderAgent.ts
import { Agent, AgentConfig } from '../../bills/parts/right-part/billMore/AIAgent/core/Agent';
import { ServerApiClient } from '../../bills/parts/right-part/billMore/AIAgent/api/ServerApiClient';
import { createOrderTools } from './tools';
import { createOrderTagRenderers } from './createOrderTagRenderers';
import { OrderAgentDependencies } from './types';

export function createOrderAgent(dependencies: OrderAgentDependencies): Agent {
  const { orderId, orderDetails } = dependencies;
  
  // 1. 创建工具集和渲染器
  const tools = createOrderTools(dependencies);
  const tagRenderers = createOrderTagRenderers();
  
  // 2. 配置Agent
  const agentConfig: AgentConfig = {
    apiClient: new ServerApiClient('https://genai.hosecloud.com/v1/gen'),
    tools,
    tagRenderers,
    name: '智能订单助手',
    description: '专业的订单管理助手，帮助您查询和管理订单信息',
    recommendedQuestions: [
      '查看当前订单详情',
      '更新订单状态',
      '查看订单商品清单',
      '联系客户信息'
    ],
    quickActions: [
      '查看订单',
      '确认订单',
      '发货',
      '完成订单'
    ],
    systemInstruction: {
      role: `你是一个专业的订单管理助手。当前正在处理订单 ${orderId}。`,
      principles: `
        - 始终以客户服务为中心，提供准确、及时的订单信息
        - 在执行任何订单状态变更前，要明确说明变更的影响
        - 主动提供相关的后续操作建议
        - 使用专业但友好的语气与用户交流
      `,
      workflow: `
        1. 理解用户的订单管理需求
        2. 使用可用工具查询或更新订单信息
        3. 以结构化的方式展示信息，使用标签增强可读性
        4. 提供实用的操作建议
      `,
      toolRules: `
        - 使用 get_order_info 查询订单详细信息
        - 使用 update_order_status 更新订单状态，务必谨慎操作
        - 在展示订单、产品、客户信息时，使用对应的标签格式
      `,
      globalContext: `当前时间: ${new Date().toLocaleString('zh-CN')}`
    }
  };
  
  // 3. 创建Agent实例并设置上下文
  const agent = new Agent(agentConfig);
  agent.updateContext(dependencies);
  
  return agent;
}
```

### 第七步：在页面中使用

```typescript
// src/pages/OrderDetailPage.tsx
import React, { useState, useEffect, useMemo } from 'react';
import { AICopilot } from '../plugins/bills/parts/right-part/billMore/AIAgent';
import { createOrderAgent } from '../plugins/orders/OrderAgent/OrderAgent';
import { OrderAgentDependencies } from '../plugins/orders/OrderAgent/types';

interface OrderDetailPageProps {
  orderId: string;
}

const OrderDetailPage: React.FC<OrderDetailPageProps> = ({ orderId }) => {
  const [orderDetails, setOrderDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  // 获取订单数据
  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        // 模拟API调用
        const details = await fetch(`/api/orders/${orderId}`).then(r => r.json());
        setOrderDetails(details);
      } catch (error) {
        console.error('获取订单详情失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId]);

  // 订单更新回调
  const handleOrderUpdate = (updatedOrderId: string) => {
    console.log('订单已更新:', updatedOrderId);
    // 重新获取订单数据
    // fetchOrderDetails();
  };

  // 创建Agent实例
  const agent = useMemo(() => {
    if (!orderDetails) return null;

    const dependencies: OrderAgentDependencies = {
      orderId,
      orderDetails,
      apiClient: {
        getOrder: (id: string) => fetch(`/api/orders/${id}`).then(r => r.json()),
        updateOrderStatus: (id: string, status: string, reason?: string) => 
          fetch(`/api/orders/${id}/status`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status, reason })
          })
      },
      userInfo: {}, // 从全局状态获取
      onOrderUpdate: handleOrderUpdate
    };

    return createOrderAgent(dependencies);
  }, [orderId, orderDetails]);

  if (loading) {
    return <div>加载中...</div>;
  }

  if (!agent) {
    return <div>无法加载订单助手</div>;
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 页面头部 */}
      <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
        <h1>订单详情 - {orderId}</h1>
      </div>
      
      {/* AI助手 */}
      <div style={{ flex: 1, padding: '16px' }}>
        <AICopilot agent={agent} />
      </div>
    </div>
  );
};

export default OrderDetailPage;
```

---

## 🛠️ 工具开发指南

### 工具类型分类

每个工具必须指定一个类型，用于UI展示和功能分类：

```typescript
export enum ToolType {
  EDIT = 'EDIT',       // 修改数据 - 显示 ✏️ 图标
  VIEW = 'VIEW',       // 查询信息 - 显示 🔍 图标  
  ANALYZE = 'ANALYZE', // 数据分析 - 显示 📊 图标
  REVIEW = 'REVIEW',   // 审核验证 - 显示 👀 图标
  OTHER = 'OTHER'      // 其他功能 - 显示 ⚙️ 图标
}
```

### 静态工具 vs 动态工具

#### 静态工具（无外部依赖）

```typescript
// tools/analytics/calculate-total.tool.ts
export const calculateTotalTool: Tool = {
  type: ToolType.ANALYZE,
  definition: {
    name: 'calculate_total',
    description: '计算订单总金额',
    parameters: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          description: '订单项目列表',
          items: {
            type: 'object',
            properties: {
              price: { type: 'number' },
              quantity: { type: 'number' }
            }
          }
        }
      },
      required: ['items']
    }
  },
  execute: async (args, context) => {
    const total = args.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    return { 
      isSuccess: true, 
      data: { 
        total,
        markdown: `📊 **计算结果**\n\n总金额: ¥${total.toFixed(2)}`
      } 
    };
  }
};
```

#### 动态工具（需要依赖注入）

```typescript
// tools/order/send-notification.tool.ts
export function createSendNotificationTool(dependencies: { notificationService: any }): Tool {
  return {
    type: ToolType.OTHER,
    definition: {
      name: 'send_notification',
      description: '发送通知给客户',
      parameters: {
        type: 'object',
        properties: {
          customerId: { type: 'string', description: '客户ID' },
          message: { type: 'string', description: '通知内容' }
        },
        required: ['customerId', 'message']
      }
    },
    execute: async (args, context) => {
      const { notificationService } = dependencies;
      
      try {
        await notificationService.send(args.customerId, args.message);
        return { 
          isSuccess: true, 
          data: { message: '通知发送成功' } 
        };
      } catch (error) {
        return { 
          isSuccess: false, 
          error: `发送通知失败: ${error.message}` 
        };
      }
    }
  };
}
```

### 工具注册

```typescript
// tools/index.ts
import { Tool } from '../Tool';
import { calculateTotalTool } from './analytics/calculate-total.tool';
import { createSendNotificationTool } from './order/send-notification.tool';

export interface MyToolsDependencies {
  notificationService: any;
  // 其他依赖...
}

export function createMyTools(dependencies: MyToolsDependencies): Tool[] {
  return [
    // 静态工具直接添加
    calculateTotalTool,
    
    // 动态工具通过工厂创建
    createSendNotificationTool(dependencies)
  ];
}
```

---

## 🎨 TagRenderer开发指南

### 基础TagRenderer

```typescript
// components/BasicTags.tsx
import React from 'react';

export const LinkTag: React.FC<{ url: string, fallbackText: string }> = ({ url, fallbackText }) => {
  return (
    <a 
      href={url} 
      target="_blank" 
      rel="noopener noreferrer"
      style={{ 
        color: '#1677ff', 
        textDecoration: 'underline',
        cursor: 'pointer'
      }}
    >
      {fallbackText}
    </a>
  );
};

export const StatusTag: React.FC<{ status: string, fallbackText: string }> = ({ status, fallbackText }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return { bg: '#f6ffed', border: '#b7eb8f', color: '#52c41a' };
      case 'warning': return { bg: '#fffbe6', border: '#ffe58f', color: '#faad14' };
      case 'error': return { bg: '#fff2f0', border: '#ffccc7', color: '#ff4d4f' };
      default: return { bg: '#f0f0f0', border: '#d9d9d9', color: '#666' };
    }
  };

  const colors = getStatusColor(status);

  return (
    <span style={{
      backgroundColor: colors.bg,
      border: `1px solid ${colors.border}`,
      borderRadius: '4px',
      padding: '2px 6px',
      fontSize: '12px',
      color: colors.color
    }}>
      {fallbackText}
    </span>
  );
};
```

### 交互式TagRenderer

```typescript
// components/InteractiveTags.tsx
import React, { useState } from 'react';
import { Button, Modal, Card } from '@hose/eui';

export const UserTag: React.FC<{ userId: string, fallbackText: string }> = ({ userId, fallbackText }) => {
  const [showModal, setShowModal] = useState(false);
  const [userInfo, setUserInfo] = useState(null);

  const handleClick = async () => {
    // 异步加载用户信息
    try {
      const info = await fetch(`/api/users/${userId}`).then(r => r.json());
      setUserInfo(info);
      setShowModal(true);
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  };

  return (
    <>
      <Button 
        category="text" 
        size="small"
        style={{ 
          padding: '2px 6px', 
          height: 'auto',
          color: '#1677ff' 
        }}
        onClick={handleClick}
      >
        👤 {fallbackText}
      </Button>
      
      <Modal
        title="用户信息"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={null}
      >
        {userInfo && (
          <Card>
            <p><strong>姓名:</strong> {userInfo.name}</p>
            <p><strong>邮箱:</strong> {userInfo.email}</p>
            <p><strong>部门:</strong> {userInfo.department}</p>
          </Card>
        )}
      </Modal>
    </>
  );
};
```

### TagRenderer工厂

```typescript
// createTagRenderers.ts
import { TagRendererMap } from '../types/TagRenderer';
import { LinkTag, StatusTag } from './components/BasicTags';
import { UserTag } from './components/InteractiveTags';

export function createMyTagRenderers(dependencies?: any): TagRendererMap {
  return {
    link: {
      description: '外部链接',
      render: (url: string, text: string) => <LinkTag url={url} fallbackText={text} />
    },
    status: {
      description: '状态标签',
      render: (status: string, text: string) => <StatusTag status={status} fallbackText={text} />
    },
    user: {
      description: '用户信息',
      render: (userId: string, text: string) => <UserTag userId={userId} fallbackText={text} />
    }
  };
}
```

---

## 🔧 调试功能

### 调试模式彩蛋

在聊天输入框中输入 `/debug` 可以开启调试模式：

- **开启调试模式**：每条消息右上角会显示 `JSON` 按钮
- **关闭调试模式**：再次输入 `/debug` 关闭
- **JSON查看**：点击 `JSON` 按钮可以查看消息的完整数据结构
