/**
 * 工具接口定义
 * 所有AI工具必须遵循的标准化接口
 */

import { FunctionDeclaration } from '../types/ChatMessage';

// 工具类型枚举
export enum ToolType {
  EDIT = 'edit',       // 编辑：修改单据数据
  VIEW = 'view',       // 查看：读取单据信息
  ANALYZE = 'analyze', // 分析：分析单据数据
  REVIEW = 'review',   // 审核：审核单据内容
  OTHER = 'other'      // 其他：其他功能
}

// 工具类型描述映射
export const ToolTypeDescription = {
  [ToolType.EDIT]: i18n.get('编辑'),
  [ToolType.VIEW]: i18n.get('查看'),
  [ToolType.ANALYZE]: i18n.get('分析'),
  [ToolType.REVIEW]: i18n.get('审核'),
  [ToolType.OTHER]: i18n.get('其他')
} as const;

// 工具执行上下文
export interface ToolContext {
  [key: string]: any;
}

export type ToolSuccess = { isSuccess: true; data: any; };
export type ToolError = { isSuccess: false; error: string; };

export type ToolOutput = ToolSuccess | ToolError;

// 工具接口
export interface Tool {
  // 工具的函数声明（用于发送给AI模型）
  definition: FunctionDeclaration;
  
  // 工具类型，用于UI渲染和分类
  type: ToolType;
  
  // 执行工具的具体逻辑
  execute: (args: any, context: ToolContext) => Promise<ToolOutput>;
}

// 工具工厂函数类型（用于依赖注入）
export type ToolFactory<T extends Tool = Tool> = (dependencies: any) => T;

// 工具工厂映射（用于批量创建工具）
export interface ToolFactoryMap {
  [toolName: string]: ToolFactory;
}

// 工具帮助函数：创建成功结果
export function createSuccessResult(data: any): ToolOutput {
  return { isSuccess: true, data };
}

// 工具帮助函数：创建失败结果
export function createErrorResult(error: string): ToolOutput {
  return { isSuccess: false, error };
}

// 工具帮助函数：验证参数
export function validateArgs(args: any, requiredFields: string[]): string | null {
  if (!args || typeof args !== 'object') {
    return i18n.get('参数必须是对象类型');
  }

  for (const field of requiredFields) {
    if (!(field in args) || args[field] === undefined || args[field] === null) {
      return `缺少必需参数: ${field}`;
    }
  }

  return null;
}

// 工具帮助函数：安全执行（捕获开发者错误）
export async function safeExecute<T>(
  fn: () => Promise<T>,
  fallbackError: string = i18n.get('工具执行失败')
): Promise<ToolOutput> {
  try {
    const result = await fn();
    return createSuccessResult(result);
  } catch (error) {
    // 区分开发者错误和业务逻辑错误
    if (error instanceof Error) {
      // 开发者错误：空指针、类型错误等，应该重新抛出
      if (error.name === 'TypeError' || 
          error.name === 'ReferenceError' || 
          error.message.includes('Cannot read prop') ||
          error.message.includes('is not a function')) {
        throw error; // 重新抛出开发者错误
      }
    }
    
    // 业务逻辑错误：返回错误结果让模型处理
    const errorMessage = error instanceof Error ? error.message : fallbackError;
    return createErrorResult(errorMessage);
  }
} 