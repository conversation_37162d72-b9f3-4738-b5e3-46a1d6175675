/**
 * 单据右侧信息展示，目前只展示风险&审查结果
 */

import React, { Component } from 'react'
import { app } from '@ekuaibao/whispered'
import { Tabs, SkeletonParagraph, Button } from '@hose/eui'
import { OutlinedDirectionPullRight, OutlinedDirectionPullLeft, OutlinedTipsReport, OutlinedDataConnectionPoint, TwoToneGeneralBill1, TwoToneLogoAi } from '@hose/eui-icons'
import RiskWarning from '../../../../../plugins/bills/riskWarning'
import styles from './MoreInfo.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
import { BillStateEnum } from '../../../enum'
import { logEvent } from '../../../../../lib/logs'
import { debounce } from 'lodash'
import FlowPlanHistory from './FlowPlanHistory'
import { Resizable, ResizeCallback } from 're-resizable'
import { flowPartDrawerConfig, BILL_CONTENT_PART_MIN_WIDTH, billDrawerConfig } from '../../../../../elements/configure/bill-drawer'
import classNames from 'classnames'
import LocalStore from '../../../../../lib/local-store'
import AIChat from './AIChat'
import { parseAsMeta } from '../../../util/parse'

const localStore = new LocalStore('billMoreInfoWidthExpand')


import AccountingVoucherPreview from './AccountingVoucherPreview/AccountingVoucherPreview'
import { app as api } from '@ekuaibao/whispered'
import { AICopilot, AIOrbIcon } from './AIAgent'
import { createBillAgent, updateBillAgentContext, BillAgentDependencies } from './BillAgent/BillAgent'
import { getBoolVariation } from '../../../../../lib/featbit'
import { enableAIChatCharge } from '../../../../../lib/charge-util'

interface RightInfoProps {
  billDetails: any //  单据详情
  bus: any // bus
  riskData: any // 风险数据
  userInfo: any
  billRiskWaringStore: any
  showBillMore: Boolean
  isActiveInfo: Boolean
  staffDisplayConfigField: any
  dynamicChannelMap: any
  isEditConfig: boolean
  versionItemAction: (item: any) => void
  showBillApproveResult?: boolean // 是否显示审查结果
  mode?: string // 页面进入模式，table为表格，list为列表，默认为表格
  canDeleteComment?: boolean
  privilegeId?: string
  className?: string
  baseDataProperties?: any
}

interface RightInfoState {
  tabData: []
  isExpand: boolean
  isLoading: boolean
  openGuide: boolean
  activeTab: string
  filterType: string
  drawerWidth?: number
  moreInfoWidth?: number
  isDragging: boolean
  lockIsExpand: boolean
}

const TabKeys = {
  Flow: 'flowPlan',
  Result: 'result',
  AccountingVoucherPreview: 'accountingVoucherPreview',
  AICopilot: 'aiCopilot',
  AIChat: 'AIChat'
}

const hasAIChat = (state) => {
  const stateAllowed = [BillStateEnum.New, BillStateEnum.Draft, BillStateEnum.Rejected].includes(state)
  return enableAIChatCharge() && stateAllowed
}

const isEmptyObject = obj => {
  return Object.keys(obj).length === 0 && obj.constructor === Object
}
@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
  billRiskWaringStore: state['@bills'].billRiskWaring,
  staffMap: state['@common'].authStaffStaffMap || {},
  baseDataProperties: state['@common'].globalFields.data,
}))
export default class MoreInfo extends Component<RightInfoProps, RightInfoState> {
  static defaultProps = {
    RiskWarningData: []
  }
  state: RightInfoState = {
    tabData: null,
    isExpand: localStore.get() ?? true,
    isLoading: true,
    openGuide: false,
    activeTab: TabKeys.AICopilot,
    filterType: '',
    drawerWidth: undefined,
    isDragging: false,
    lockIsExpand: false
  }

  ifShowingApprovalFlowByDefault = false

  componentDidMount() {
    const { bus } = this.props
    bus.watch('open:wait:guide', this.openGuideCallback)
    bus.on('open:expand:by:tabKey', this.handleOpenExpandByTabKey)

    window.addEventListener('drawerWidthResize', this.handleDrawerWidthResize)

    // 获取凭证预览权限
    this.getVoucherPreviewPower()

    // 创建或更新Agent实例
    this.createOrUpdateAgent()

  }


  // 创建或更新Agent
  createOrUpdateAgent = (props: RightInfoProps = this.props) => {
    // Assemble the raw data dependencies for the agent
    const dependencies: BillAgentDependencies = {
      billDetails: props.billDetails,
      bus: props.bus,
      userInfo: props.userInfo,
      staffMap: props.staffMap,
      riskData: props.riskData,
      billRiskWaringStore: props.billRiskWaringStore,
    };

    if (this.state.agent) {
      updateBillAgentContext(this.state.agent, dependencies);
    } else {
      const agent = createBillAgent(dependencies);
      if (agent) {
        this.setState({ agent });
      }
    }
  }

  handleDrawerWidthResize = (e: CustomEvent) => {
    this.setState({
      drawerWidth: e?.detail?.width
    })
  }


  componentWillUnmount() {
    const { bus } = this.props
    bus.un('open:wait:guide', this.openGuideCallback)
    bus.un('open:expand:by:tabKey', this.handleOpenExpandByTabKey)

    window.removeEventListener('drawerWidthResize', this.handleDrawerWidthResize)
  }

  openGuideCallback = () => {
    this.setState({
      openGuide: this.state.openGuide
    })
  }

  handleTabChange = (key: string) => {
    this.setState({ activeTab: key })
  }

  handleOpenExpandByTabKey = (expand: boolean, key: string, options: Record<string, any>) => {
    const { isExpand } = this.state
    this.setState({
      activeTab: key,
      filterType: options?.filterType
    })
    if (!isExpand && expand) {
      this.setExpandStatus({})
    }
  }

  componentWillReceiveProps(nextProps: Readonly<RightInfoProps>): void {
    if (nextProps.riskData && !isEmptyObject(nextProps.riskData)) {
      this.setState({ isLoading: false })
    }
    // 更新Agent上下文（如果Agent存在且相关props发生变化）
    const contextChanged =
      nextProps.billDetails !== this.props.billDetails ||
      nextProps.userInfo !== this.props.userInfo ||
      nextProps.staffMap !== this.props.staffMap ||
      nextProps.riskData !== this.props.riskData ||
      nextProps.billRiskWaringStore !== this.props.billRiskWaringStore ||
      nextProps.bus !== this.props.bus

    if (contextChanged) {
      this.createOrUpdateAgent(nextProps);
    }
  }

  // 根据单据状态是否展示当前组件
  isShowByBillStatus() {
    const { billDetails, showBillMore = true } = this.props
    if (([BillStateEnum.New].includes(billDetails.state) || !showBillMore) && !hasAIChat(billDetails.state)) {
      return false
    }
    return true
  }


  // 编辑费用明细时锚点的状态更新
  setModifyFeeDetail = isExpand => {
    const details = document.getElementById('details-container')
    const rightWarpWidthNotPadding = 260
    const detailsAffix: HTMLDivElement = details?.querySelector('.ant-affix') // 展开和明细锚点有兼容性问题
    if (detailsAffix) {
      // 展开和
      const width = isExpand
        ? detailsAffix?.offsetWidth + rightWarpWidthNotPadding
        : detailsAffix?.offsetWidth - rightWarpWidthNotPadding
      detailsAffix.style.width = `${width}px`
    }
  }

  setExpandStatus = debounce(event => {
    const { isExpand } = this.state
    // 向导弹框阻止点击事件
    if (event?.currentTarget?.classList?.contains('driver-active-element')) {
      return
    }
    const newIsExpand = !isExpand
    this.setModifyFeeDetail(newIsExpand)
    this.setState({ isExpand: newIsExpand }, () => {
      localStore.set(newIsExpand)
    })
    setTimeout(() => {
      // 1.5秒的展开动画
      if (this.props?.bus?.has('details:table:list:width:change')) {
        this.props?.bus?.emit('details:table:list:width:change', !isExpand)
      }
    }, 1000)
    logEvent('open_risk_right_status', { title: '点击审查结果侧边栏', openStatus: isExpand })
  }, 500)

  // 停止拖动
  forceStopResize = () => {
    const mouseUpEvent = new MouseEvent('mouseup', {
      bubbles: true,
      cancelable: true,
    })
    document.dispatchEvent(mouseUpEvent)
  }

  handleResize = ((e, direction, ref) => {
    const { isExpand, lockIsExpand } = this.state
    const moreInfoWidth = Number(ref.style.width.replace('px', ''))

    if (lockIsExpand) {
      // 锁定状态，不进行展开/收起
      if (isExpand) {
        this.setState({ moreInfoWidth: Math.max(moreInfoWidth, flowPartDrawerConfig.minWidth) })
      }
      return
    }

    if (isExpand) {
      if (moreInfoWidth < flowPartDrawerConfig.minWidth) {
        // 收起MoreInfo
        this.setState({ isExpand: false })
        this.forceStopResize()
      } else {
        this.setState({ moreInfoWidth })
      }
    } else {
      if (moreInfoWidth > flowPartDrawerConfig.collapseWidth) {
        // 展开MoreInfo
        this.setState({ moreInfoWidth: flowPartDrawerConfig.minWidth, isExpand: true, lockIsExpand: true })
      }
    }
  }) as ResizeCallback

  getVoucherPreviewPower = () => {
    api
      .dataLoader('@common.powers')
      .load()
      .then(powers => powers.VoucherPreview)
      .then(value => {
        this.setState({ voucherPower: value })
      })
  }


  // 判断是否应该显示凭证预览
  shouldShowAccountingVoucherPreview = () => {
    const { billDetails } = this.props
    const { voucherPower } = this.state
    const { formType, state } = billDetails

    // 支持的单据类型
    const supportedFormTypes = ['expense', 'loan', 'reimbursement', 'payment', 'corpPayment']

    // 草稿状态的单据不提供凭证预览
    const isDraftState = state === BillStateEnum.Draft || state === BillStateEnum.New

    // 需要同时满足权限、单据类型条件，并且不是草稿状态
    return voucherPower && supportedFormTypes.includes(formType) && !isDraftState
  }

  ifShowFlowPlan = () => {
    const { billDetails } = this.props
    const billState = billDetails.state === 'draft' ? (billDetails.plan ? 'retract' : 'draft') : billDetails.state
    return ![BillStateEnum.New, BillStateEnum.Draft].includes(billState)
  }

  renderTabs() {
    const {
      riskData,
      billRiskWaringStore,
      billDetails,
      bus,
      isActiveInfo,
      dynamicChannelMap,
      userInfo,
      staffDisplayConfigField,
      isEditConfig,
      versionItemAction,
      showBillApproveResult = true,
      canDeleteComment,
      privilegeId,
      staffMap,
    } = this.props
    const { isLoading, activeTab, isExpand, filterType, agent } = this.state
    const templates = parseAsMeta(billDetails?.form?.specificationId || billDetails?.currentSpecification, this.props.baseDataProperties)
    if (!this.isShowByBillStatus()) {
      return <></>
    }
    const tabs = []
    const showAccountingVoucherPreview = this.shouldShowAccountingVoucherPreview()
    if (getBoolVariation('ai-colipot') && (billDetails.state !== 'new' && billDetails.type !== 'permit')) {
      tabs.push({
        label: i18n.get('AI助手'),
        key: TabKeys.AICopilot,
        children: agent ? (
          <AICopilot agent={agent} />
        ) : (
          <div className="ai-copilot-loading">正在初始化AI助手...</div>
        )
      })
    }


    if (getBoolVariation('ai-colipot') && showAccountingVoucherPreview) {
      tabs.push({
        label: i18n.get('凭证预览'),
        key: TabKeys.AccountingVoucherPreview,
        children: (
          <AccountingVoucherPreview
            billDetails={billDetails}
            bus={bus}
            userInfo={userInfo}
            isActiveInfo={isActiveInfo}
          />
        )
      })
    }
    if (hasAIChat(billDetails.state)) {
      tabs.push({
        label: i18n.get('AI 助理'),
        key: TabKeys.AIChat,
        children: (<AIChat
          chatId={billDetails.state}
          isActiveInfo={isActiveInfo}
          RiskWarningData={riskData || billRiskWaringStore}
          billDetails={billDetails}
          bus={bus}
          isLoading={isLoading}
          templates={templates}
        />
        )
      })
    }
    const billState = billDetails.state === 'draft' ? (billDetails.plan ? 'retract' : 'draft') : billDetails.state
    if (showBillApproveResult && ![BillStateEnum.New].includes(billState)) {
      tabs.push({
        label: i18n.get('审查结果'),
        key: TabKeys.Result,
        children: (
          <RiskWarning
            isActiveInfo={isActiveInfo}
            RiskWarningData={riskData || billRiskWaringStore}
            billDetails={billDetails}
            bus={bus}
            isLoading={isLoading}
          />
        )
      })
    }
    if (this.ifShowFlowPlan()) {
      tabs.push({
        label: i18n.get('审批流程'),
        key: 'flowPlan',
        children: (
          <FlowPlanHistory
            flowInfo={billDetails}
            dynamicChannelMap={dynamicChannelMap}
            staffDisplayConfigField={staffDisplayConfigField}
            userInfo={userInfo}
            staffMap={staffMap}
            isEditConfig={isEditConfig}
            bus={bus}
            versionItemAction={versionItemAction}
            filterType={filterType}
            canDeleteComment={canDeleteComment}
            privilegeId={privilegeId}
          />
        )
      })
    }
    // 默认选中flow，如果没有flow则选中result
    if (!tabs.some(tab => tab.key === activeTab)) {
      this.setState({ activeTab: tabs[0]?.key || '' })
    }

    return <Tabs className={'bill-more-tabs'} activeKey={activeTab} items={tabs} onTabClick={this.handleTabChange} />
  }

  renderExpandContent() {
    const { isLoading, activeTab } = this.state
    const { riskData, billRiskWaringStore } = this.props
    if (activeTab === TabKeys.Result && isLoading && isEmptyObject(riskData || billRiskWaringStore)) {
      return (
        <>
          <SkeletonParagraph className="bill-more-info-skeleton" lineCount={13} />
        </>
      )
    }
    return this.renderTabs()
  }

  handleTabIconClick = (key: string) => {
    this.handleTabChange(key)
    this.setExpandStatus({})
  }

  renderCollapseContent() {
    const { billDetails, showBillApproveResult = true } = this.props
    if (billDetails.state === BillStateEnum.New) {
      return <div className={styles['collapse-content']}>
        {hasAIChat(billDetails.state) && <Button category='text' className={styles['collapse-content-item']} onClick={() => this.handleTabIconClick(TabKeys.AIChat)}>
          <span className={styles['collapse-inner']}>
            <span className={styles.icon}>
              <TwoToneLogoAi fontSize={18} />
            </span>
            <span className={styles.text}>
              {i18n.get('AI助理')}
            </span>
          </span>
        </Button>}
      </div>
    }
    return <div className={styles['collapse-content']}>
      {getBoolVariation('ai-colipot') && <Button category='text' className={styles['collapse-content-item']} onClick={() => this.handleTabIconClick(TabKeys.AICopilot)}>
        <span className={styles['collapse-inner']}>
          <span className={styles.icon}>
            <AIOrbIcon size={18} />
          </span>
          <span className={styles.text}>
            {i18n.get('AI助手')}
          </span>
        </span>
      </Button>}
      {hasAIChat(billDetails.state) && <Button category='text' className={styles['collapse-content-item']} onClick={() => this.handleTabIconClick(TabKeys.AIChat)}>
        <span className={styles['collapse-inner']}>
          <span className={styles.icon}>
            <TwoToneLogoAi fontSize={18} />
          </span>
          <span className={styles.text}>
            {i18n.get('AI助理')}
          </span>
        </span>
      </Button>}
      {getBoolVariation('ai-colipot') && this.shouldShowAccountingVoucherPreview() && <Button category='text' className={styles['collapse-content-item']} onClick={() => this.handleTabIconClick(TabKeys.AccountingVoucherPreview)}>
        <span className={styles['collapse-inner']}>
          <span className={styles.icon}>
            <TwoToneGeneralBill1 />
          </span>
          <span className={styles.text}>
            {i18n.get('凭证预览')}
          </span>
        </span>
      </Button>}
      {showBillApproveResult && <Button category='text' className={styles['collapse-content-item']} onClick={() => this.handleTabIconClick(TabKeys.Result)}>
        <span className={styles['collapse-inner']}>
          <span className={styles.icon}>
            <OutlinedTipsReport />
          </span>
          <span className={styles.text}>
            {i18n.get('审查')}
          </span>
        </span>
      </Button>}
      {this.ifShowFlowPlan() && <Button category='text' className={styles['collapse-content-item']} onClick={() => this.handleTabIconClick(TabKeys.Flow)}>
        <span className={styles['collapse-inner']}>
          <span className={styles.icon}>
            <OutlinedDataConnectionPoint />
          </span>
          <span className={styles.text}>
            {i18n.get('流程')}
          </span>
        </span>
      </Button>}
    </div>
  }

  renderToggleButton() {
    const { isExpand } = this.state
    return <div className="show-btn" onClick={this.setExpandStatus} id="BillMoreInfoNext">
      {isExpand ? <OutlinedDirectionPullRight /> : <OutlinedDirectionPullLeft />}
    </div>
  }

  render() {
    const { mode, className, billDetails } = this.props
    const { isExpand, activeTab } = this.state
    if (!this.isShowByBillStatus()) {
      return <></>
    }

    const isZh = i18n.currentLocale === 'zh-CN'
    let defaultWidth:number =
      (isExpand && mode === 'list') || billDrawerConfig?.width <= 400 + 320
        ? flowPartDrawerConfig?.listModeDefaultWidth
        : flowPartDrawerConfig?.tableModeDefaultWidth
    if([BillStateEnum.New].includes(billDetails.state) && hasAIChat(billDetails.state)){
      defaultWidth = flowPartDrawerConfig.createStateDefaultWidth
    }
    return (
      <div className={classNames('dis-f', isZh ? 'bill-more-info-lang-zh' : 'bill-more-info-lang-others', className, activeTab === TabKeys.AIChat && styles['bill-more-info-container'])}>
        <Resizable
          defaultSize={isExpand && { width: defaultWidth }}
          size={
            !isExpand
              ? { width: flowPartDrawerConfig.collapseWidth }
              : { width: this.state.moreInfoWidth || defaultWidth }
          }
          minWidth={isExpand ? flowPartDrawerConfig.minWidth - 1 : flowPartDrawerConfig.collapseWidth}
          maxWidth={
            isExpand
              ? this.state.drawerWidth
                ? Math.min(this.state.drawerWidth - BILL_CONTENT_PART_MIN_WIDTH, flowPartDrawerConfig.maxWidth)
                : flowPartDrawerConfig.maxWidth
              : flowPartDrawerConfig.collapseWidth + 1
          }
          enable={{ left: true, right: false }}
          handleClasses={{ left: styles['resizable-custom-handle'] }}
          onResize={this.handleResize}
          onResizeStart={() => this.setState({ isDragging: true })}
          onResizeStop={() => this.setState({ isDragging: false, lockIsExpand: false })}
        >
          <div id="BillMoreInfoGuide" style={{ width: '100%' }} className={classNames(isExpand ? styles['bill-more-info'] : styles['bill-more-hide-info'], { [styles['is-dragging']]: this.state.isDragging })}>
            {this.renderToggleButton()}
            <div className={classNames(styles.content, isExpand ? styles['bill-more-info-expanded'] : styles['bill-more-info-collapsed'])}>
              {this.renderCollapseContent()}
              {this.renderExpandContent()}
            </div>
          </div>
        </Resizable>
      </div>
    )
  }
}


