// 凭证数据服务层

/**
 * 辅助核算维度项
 * @description 代表一个具体的辅助核算条目，例如"部门：销售部"或"项目：P001_新产品研发"。
 */
export interface AuxiliaryDimension {
  /**
   * 辅助核算维度类型ID
   * @example 'department', 'project', 'customer'
   */
  dimensionType?: string;

  /**
   * 辅助核算维度类型名称 (用于显示)
   * @example '部门', '项目', '客户'
   */
  dimensionTypeName?: string;

  /**
   * 辅助核算维度值编码
   * @example 'DEPT001', 'PROJ123', 'CUST-A'
   */
  dimensionValueCode?: string;

  /**
   * 辅助核算维度值名称 (用于显示)
   * @example '销售一部', '核心系统升级项目', '阿里巴巴'
   */
  dimensionValueName?: string;
}

/**
 * 凭证状态枚举
 * @description 定义了凭证在其生命周期中可能存在的各种状态。
 */
export enum VoucherStatus {
  /**
   * 暂存 (未提交)
   * @description The voucher is saved as a draft and has not been submitted for review.
   */
  Draft = 'DRAFT',

  /**
   * 已提交 (待审核)
   * @description The voucher has been submitted and is awaiting approval.
   */
  Submitted = 'SUBMITTED',

  /**
   * 已审核
   * @description The voucher has been reviewed and approved. It is ready for posting.
   */
  Approved = 'APPROVED',

  /**
   * 已过账
   * @description The voucher has been posted to the general ledger, and its values are reflected in financial reports.
   * This status is final and usually cannot be reversed.
   */
  Posted = 'POSTED',

  /**
   * 已拒绝/驳回
   * @description The voucher was rejected during the review process.
   */
  Rejected = 'REJECTED',

  /**
   * 已作废
   * @description The voucher has been voided and is no longer valid.
   */
  Voided = 'VOIDED',
}

/**
 * 借贷方向枚举
 * @description 用于明确分录是借方还是贷方。
 */
export enum DebitCreditDirection {
  Debit = 'DEBIT',
  Credit = 'CREDIT',
}

/**
 * 凭证分录 (Voucher Entry / Body)
 * @description 定义了一张凭证中的单条分录信息。
 */
export interface VoucherEntry {
  /**
   * 分录的唯一标识符 (UUID 或自增ID)
   */
  id?: string;

  /**
   * 在凭证内的顺序号，从1开始
   */
  sequence?: number;

  /**
   * 分录摘要
   * @description 对该笔经济业务的简要说明。
   */
  summary?: string;

  /**
   * 会计科目代码
   * @example '1002.01' (银行存款-工商银行)
   */
  accountCode?: string;

  /**
   * 会计科目名称 (冗余字段，用于前端显示)
   * @example '银行存款-工商银行'
   */
  accountName?: string;

  /**
   * 借贷方向
   */
  direction?: DebitCreditDirection;

  /**
   * 币别代码
   * @default 'CNY'
   * @example 'USD', 'EUR', 'CNY'
   */
  currency?: string;

  /**
   * 汇率
   * @description 相对于本位币的汇率。如果币别是本位币，则汇率为1。
   * @default 1
   */
  exchangeRate?: number;

  /**
   * 原币金额
   * @description 发生业务的原始货币金额，总是为正数。
   */
  originalAmount?: number;

  /**
   * 本位币借方金额
   * @description 如果是借方分录，此字段为金额；否则为0。
   * @description `debitAmount = (direction === 'DEBIT') ? originalAmount * exchangeRate : 0`
   */
  debitAmount?: number;

  /**
   * 本位币贷方金额
   * @description 如果是贷方分录，此字段为金额；否则为0。
   * @description `creditAmount = (direction === 'CREDIT') ? originalAmount * exchangeRate : 0`
   */
  creditAmount?: number;

  /**
   * 辅助核算信息列表
   * @description 用于对该分录进行多维度分析，如部门、项目等。
   * @optional
   */
  auxiliaryDimensions?: AuxiliaryDimension[];
}

/**
 * 凭证头 (Voucher Header)
 * @description 定义了一张凭证的公共信息。
 */
export interface VoucherHeader {
  /**
   * 凭证的唯一标识符 (UUID 或自增ID)
   */
  id?: string;

  /**
   * 公司代码
   * @description 公司标识代码
   * @example 'COMP001'
   */
  companyCode?: string;

  /**
   * 会计年份
   * @example 2024
   */
  year?: number;

  /**
   * 会计期间 (月份, 1-12, 或包含13作为调整期)
   * @example 3
   */
  period?: number;

  /**
   * 凭证字
   * @example '记', '收', '付', '转'
   */
  voucherGroup?: string;

  /**
   * 凭证号 (在`年份-期间-凭证字`范围内唯一且连续)
   * @example '1', '2', '3'...
   */
  voucherNumber?: string;

  /**
   * 凭证日期 (业务发生的实际日期)
   * @example '2024-03-15'
   */
  date?: string; // ISO 8601 format: YYYY-MM-DD

  /**
   * 凭证类型
   * @description 业务类型分类，用于凭证归类和检索
   * @example '费用报销凭证', '采购付款凭证', '支付凭证', '核销凭证'
   */
  voucherType?: string;

  /**
   * 凭证摘要 (可选)
   * @description 对整张凭证业务的总体描述
   * @example '销售收款业务', '采购付款业务'
   * @optional
   */
  voucherSummary?: string;

  /**
   * 附件数
   * @default 0
   */
  attachmentCount?: number;

  /**
   * 制单人ID
   */
  preparerId?: string;
  
  /**
   * 制单人姓名 (冗余字段，用于显示)
   */
  preparerName?: string;

  /**
   * 审核人ID (可选，审核后才有值)
   * @optional
   */
  auditorId?: string;

  /**
   * 审核人姓名 (冗余字段，用于显示)
   * @optional
   */
  auditorName?: string;

  /**
   * 过账人ID (可选，过账后才有值)
   * @optional
   */
  posterId?: string;

  /**
   * 过账人姓名 (冗余字段，用于显示)
   * @optional
   */
  posterName?: string;

  /**
   * 过账日期 (可选，过账后才有值)
   * @description 凭证过账到总账的日期
   * @example '2024-03-20'  
   * @optional
   */
  postDate?: string; // ISO 8601 format: YYYY-MM-DD

  /**
   * 凭证状态
   */
  status?: VoucherStatus;

  /**
   * 创建时间
   */
  createdAt?: Date;
  
  /**
   * 最后更新时间
   */
  updatedAt?: Date;
}

/**
 * 完整凭证 (Voucher)
 * @description 组合了凭证头和凭证分录，代表一张完整的总账凭证。
 * 这是一个典型的 "一对多" 关系。
 */
export interface Voucher extends VoucherHeader {
  /**
   * 凭证分录列表
   * @description 一张凭证至少包含两条分录（一借一贷）。
   */
  entries?: VoucherEntry[];
}

// 保持向后兼容性，保留原有接口名称
export type VoucherData = Voucher;

// 模拟凭证数据生成
/**
 * 单据数据接口定义
 */
interface BillData {
  corporationId: string;  // 企业ID
  formData: any;          // 单据数据对象
  formState: any;         // 单据状态
  formId: string;         // 单据id
  formCode: string;       // 单号
  formVersion: string;    // 单据数据版本
}

/**
 * 安全地转换API数据为凭证数据
 * @param apiData API返回的原始数据
 * @returns 转换后的凭证数据
 */
const safeTransformVoucherData = (apiData: any): VoucherData => {
  // 安全地转换凭证头数据
  const voucherHeader: VoucherHeader = {
    id: apiData?.id || undefined,
    companyCode: apiData?.companyCode || undefined,
    year: typeof apiData?.year === 'number' ? apiData.year : undefined,
    period: typeof apiData?.period === 'number' ? apiData.period : undefined,
    voucherGroup: apiData?.voucherGroup || undefined,
    voucherNumber: apiData?.voucherNumber || undefined,
    date: apiData?.date || undefined,
    voucherType: apiData?.voucherType || undefined,
    voucherSummary: apiData?.voucherSummary || undefined,
    attachmentCount: typeof apiData?.attachmentCount === 'number' ? apiData.attachmentCount : undefined,
    preparerId: apiData?.preparerId || undefined,
    preparerName: apiData?.preparerName || undefined,
    auditorId: apiData?.auditorId || undefined,
    auditorName: apiData?.auditorName || undefined,
    posterId: apiData?.posterId || undefined,
    posterName: apiData?.posterName || undefined,
    postDate: apiData?.postDate || undefined,
    status: apiData?.status && Object.values(VoucherStatus).includes(apiData.status) 
      ? apiData.status as VoucherStatus 
      : undefined,
    createdAt: apiData?.createdAt ? new Date(apiData.createdAt) : undefined,
    updatedAt: apiData?.updatedAt ? new Date(apiData.updatedAt) : undefined,
  };

  // 安全地转换凭证分录数据
  const entries: VoucherEntry[] = Array.isArray(apiData?.entries) 
    ? apiData.entries.map((entry: any, index: number): VoucherEntry => ({
        id: entry?.id || undefined,
        sequence: typeof entry?.sequence === 'number' ? entry.sequence : index + 1,
        summary: entry?.summary || undefined,
        accountCode: entry?.accountCode || undefined,
        accountName: entry?.accountName || undefined,
        direction: entry?.direction && Object.values(DebitCreditDirection).includes(entry.direction)
          ? entry.direction as DebitCreditDirection
          : undefined,
        currency: entry?.currency || undefined,
        exchangeRate: typeof entry?.exchangeRate === 'number' ? entry.exchangeRate : undefined,
        originalAmount: typeof entry?.originalAmount === 'number' ? entry.originalAmount : undefined,
        debitAmount: typeof entry?.debitAmount === 'number' ? entry.debitAmount : undefined,
        creditAmount: typeof entry?.creditAmount === 'number' ? entry.creditAmount : undefined,
        auxiliaryDimensions: Array.isArray(entry?.auxiliaryDimensions)
          ? entry.auxiliaryDimensions.map((dim: any): AuxiliaryDimension => ({
              dimensionType: dim?.dimensionType || undefined,
              dimensionTypeName: dim?.dimensionTypeName || undefined,
              dimensionValueCode: dim?.dimensionValueCode || undefined,
              dimensionValueName: dim?.dimensionValueName || undefined,
            }))
          : undefined,
      }))
    : [];

  return {
    ...voucherHeader,
    entries,
  };
};

/**
 * 根据billDetails生成凭证数据
 * @param billDetails 单据详情
 * @returns Promise<VoucherData[]> 凭证数据数组
 */
const generateMockVoucherData = async (billDetails: any): Promise<VoucherData[]> => {
  // 根据billDetails转换为billData
  const billData: BillData = {
    corporationId: billDetails.corporationId,  // 企业ID
    formData: billDetails.form,                // 单据数据对象
    formState: billDetails.state,              // 单据状态
    formId: billDetails.id,                    // 单据id
    formCode: billDetails.form?.code,          // 单号
    formVersion: billDetails.version           // 单据数据版本
  };

  console.log("📋 单据数据转换:", billData);

  try {
    // 调用API接口获取凭证数据
    const response = await fetch('https://qoe540tu84.gzg.sealos.run/get-voucher', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(billData)
    });

    if (!response.ok) {
      // 403错误表示功能未开通
      if (response.status === 403) {
        throw new Error('FEATURE_NOT_ENABLED');
      }
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log("✅ 获取到凭证数据:", responseData);
    
    // 检查响应数据结构，提取voucher数据
    if (responseData && responseData.voucher) {
      if (Array.isArray(responseData.voucher)) {
        // voucher是数组格式：{ voucher: [...] }
        const transformedVouchers = responseData.voucher.map((voucherData: any) => 
          safeTransformVoucherData(voucherData)
        );
        console.log("✅ 转换后的凭证数组数据:", transformedVouchers);
        return transformedVouchers;
      } else if (typeof responseData.voucher === 'object') {
        // voucher是单个对象格式：{ voucher: {...} }
        const transformedVoucher = safeTransformVoucherData(responseData.voucher);
        console.log("✅ 转换后的单个凭证数据:", transformedVoucher);
        return [transformedVoucher];
      }
    }
    
    // 如果不是标准的 { voucher: ... } 格式，尝试其他格式
    console.warn("⚠️ API返回的数据结构不符合预期:", responseData);
    if (Array.isArray(responseData)) {
      // 直接是凭证数组格式：[...]
      const transformedVouchers = responseData.map((voucherData: any) => 
        safeTransformVoucherData(voucherData)
      );
      console.log("✅ 直接转换的凭证数组数据:", transformedVouchers);
      return transformedVouchers;
    } else if (responseData && typeof responseData === 'object') {
      // 直接是单个凭证对象格式：{...}
      const transformedVoucher = safeTransformVoucherData(responseData);
      console.log("✅ 直接转换的单个凭证数据:", transformedVoucher);
      return [transformedVoucher];
    } else {
      throw new Error("API返回的数据结构不正确：无法识别的数据格式");
    }
  } catch (error) {
    console.error("❌ 获取凭证数据失败:", error);
    
    // 如果API调用失败，返回空数组或者抛出错误
    // 这里选择抛出错误，让调用方处理
    throw new Error(`获取凭证数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// API接口服务
export class VoucherService {
  // 获取凭证预览数据
  static async getVoucherPreview(billDetails: any): Promise<VoucherData[]> {
    // 根据单据详情生成凭证数据（现在是调用真实API）
    return await generateMockVoucherData(billDetails);
  }

  // 验证凭证平衡性
  static validateVoucherBalance(voucher: VoucherData): boolean {
    if (!voucher.entries || !Array.isArray(voucher.entries)) {
      return false;
    }
    
    const totalDebit = voucher.entries.reduce((sum, entry) => {
      return sum + (typeof entry?.debitAmount === 'number' ? entry.debitAmount : 0);
    }, 0);
    
    const totalCredit = voucher.entries.reduce((sum, entry) => {
      return sum + (typeof entry?.creditAmount === 'number' ? entry.creditAmount : 0);
    }, 0);
    
    return Math.abs(totalDebit - totalCredit) < 0.01;
  }

  // 计算凭证合计
  static calculateVoucherTotals(voucher: VoucherData) {
    if (!voucher.entries || !Array.isArray(voucher.entries)) {
      return {
        totalDebit: 0,
        totalCredit: 0,
        isBalanced: false
      };
    }
    
    const totalDebit = voucher.entries.reduce((sum, entry) => {
      return sum + (typeof entry?.debitAmount === 'number' ? entry.debitAmount : 0);
    }, 0);
    
    const totalCredit = voucher.entries.reduce((sum, entry) => {
      return sum + (typeof entry?.creditAmount === 'number' ? entry.creditAmount : 0);
    }, 0);
    
    const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;
    
    return {
      totalDebit,
      totalCredit,
      isBalanced
    };
  }
} 