import React from 'react';
import { Tooltip } from '@hose/eui';
import { VoucherEntry as VoucherEntryType } from '../services/voucherService';
import styles from '../AccountingVoucherPreview.module.less';

interface VoucherEntryProps {
  entry: VoucherEntryType;
  index: number;
  showAuxiliaryCode: boolean;
  onAuxiliaryTagClick: () => void;
}

export const VoucherEntry: React.FC<VoucherEntryProps> = ({ entry, index, showAuxiliaryCode, onAuxiliaryTagClick }) => {
  // 判断是否显示原币金额（非CNY且汇率不为1）
  const shouldShowOriginalAmount = entry.currency !== 'CNY' && entry.exchangeRate !== 1;

  return (
    <div className={styles['entry-item']}>
      <div className={styles['entry-content']}>
        <div className={styles['entry-main']}>
          <div className={styles['entry-header']}>
            <span className={styles['entry-index']}>
              {index + 1}
            </span>
            <span className={styles['entry-account']}>
              {entry.accountCode+' '+entry.accountName}
            </span>
          </div>
          <p className={styles['entry-summary']}>
            {i18n.get('摘要')+'：'+entry.summary}
          </p>

          {/* 金额显示 */}
          <div className={styles['entry-amounts']}>
            <div className={styles['amount-item']}>
              <span className={styles['amount-label']}>
                {i18n.get('借方')}
              </span>
              {entry.debitAmount > 0 ? (
                <div className={styles['amount-container']}>
                <span className={`${styles['amount-value']} ${styles.debit}`}>
                  {entry.debitAmount.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                  })}
                </span>
                  {shouldShowOriginalAmount && (
                    <span className={styles['original-amount']}>
                      {entry.currency} {entry.originalAmount.toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                      })}
                    </span>
                  )}
                </div>
              ) : (
                <span className={`${styles['amount-value']} ${styles.empty}`}>
                  -
                </span>
              )}
            </div>
            <div className={styles['amount-item']}>
              <span className={styles['amount-label']}>
                {i18n.get('贷方')}
              </span>
              {entry.creditAmount > 0 ? (
                <div className={styles['amount-container']}>
                <span className={`${styles['amount-value']} ${styles.credit}`}>
                  {entry.creditAmount.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                  })}
                </span>
                  {shouldShowOriginalAmount && (
                    <span className={styles['original-amount']}>
                      {entry.currency} {entry.originalAmount.toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                      })}
                    </span>
                  )}
                </div>
              ) : (
                <span className={`${styles['amount-value']} ${styles.empty}`}>
                  -
                </span>
              )}
            </div>
          </div>

          {/* 辅助核算 */}
          {entry.auxiliaryDimensions && entry.auxiliaryDimensions.length > 0 && (
            <div className={styles['entry-auxiliary']}>
              {entry.auxiliaryDimensions.map((aux, auxIndex) => (
                <Tooltip
                  key={auxIndex}
                  title={`${aux.dimensionTypeName}: ${aux.dimensionValueName} (${aux.dimensionValueCode})`}
                  placement="top"
                >
                  <span 
                    className={styles['auxiliary-tag']}
                    onClick={onAuxiliaryTagClick}
                  >
                    {showAuxiliaryCode 
                      ? `${aux.dimensionTypeName}: ${aux.dimensionValueCode}`
                      : `${aux.dimensionTypeName}: ${aux.dimensionValueName}`
                    }
                  </span>
                </Tooltip>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 