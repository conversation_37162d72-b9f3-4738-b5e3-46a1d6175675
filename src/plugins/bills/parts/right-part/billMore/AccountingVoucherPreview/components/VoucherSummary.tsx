import React from 'react';
import styles from '../AccountingVoucherPreview.module.less';
import { FilledTipsCheck, OutlinedTipsClose } from '@hose/eui-icons';

interface VoucherSummaryProps {
  totalDebit: number;
  totalCredit: number;
  isBalanced: boolean;
}

export const VoucherSummary: React.FC<VoucherSummaryProps> = ({
  totalDebit,
  totalCredit,
  isBalanced
}) => {
  return (
    <div className={styles['voucher-summary']}>
      <div className={styles['summary-content']}>
        <div className={styles['summary-item']}>
          <span className={styles['summary-label']}>
            {i18n.get('借方合计')}
          </span>
          <span className={`${styles['summary-value']} ${styles.debit}`}>
            {totalDebit.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
          </span>
        </div>
        <div className={styles['summary-item']}>
          <span className={styles['summary-label']}>
            {i18n.get('贷方合计')}
          </span>
          <span className={`${styles['summary-value']} ${styles.credit}`}>
            {totalCredit.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
          </span>
        </div>
        <div className={styles['balance-indicator']}>
          <div
            className={`${styles['balance-icon']} ${
              isBalanced ? styles.balanced : styles.unbalanced
            }`}
          >
            {isBalanced ? (
              <FilledTipsCheck style={{ fontSize: '12px', color: 'white' }} />
            ) : (
              <OutlinedTipsClose style={{ fontSize: '12px', color: 'white' }} />
            )}
          </div>
          <span className={styles['balance-text']}>
            {isBalanced ? i18n.get('平衡') : i18n.get('不平衡')}
          </span>
        </div>
      </div>
    </div>
  );
}; 