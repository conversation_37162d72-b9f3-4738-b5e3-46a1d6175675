import React, { useState } from 'react';
import { VoucherData } from '../services/voucherService';
import styles from '../AccountingVoucherPreview.module.less';

interface VoucherInfoProps {
  voucher: VoucherData;
}

// 凭证状态映射
const STATUS_MAP = {
  'DRAFT': '暂存',
  'SUBMITTED': '已提交',
  'APPROVED': '已审核', 
  'POSTED': '已过账',
  'REJECTED': '已拒绝',
  'VOIDED': '已作废'
};

export const VoucherInfo: React.FC<VoucherInfoProps> = ({ voucher }) => {
  const [showMore, setShowMore] = useState(false);
  
  const handleToggleMore = () => {
    setShowMore(!showMore);
  };

  // 格式化日期显示
  const formatDate = (date: string | Date) => {
    if (!date) return '--';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <div className={styles['voucher-info']}>
      {/* 基本信息 - 默认显示 */}
      <div className={styles['info-grid']}>
        <div className={styles['info-row']}>
          <div className={styles['info-field']}>
            <span className={styles['field-label']}>
              {i18n.get('会计年份')}
            </span>
            <span className={styles['field-value']}>
              {voucher.year || '--'}
            </span>
          </div>
          <div className={styles['info-field']}>
            <span className={styles['field-label']}>
              {i18n.get('会计区间')}
            </span>
            <span className={styles['field-value']}>
              {voucher.period || '--'}
            </span>
          </div>
          <div className={styles['info-field']}>
            <span className={styles['field-label']}>
              {i18n.get('凭证日期')}
            </span>
            <span className={styles['field-value']}>
              {formatDate(voucher.date)}
            </span>
          </div>
        </div>
        
        <div className={styles['info-row']}>
          <div className={styles['info-field']}>
            <span className={styles['field-label']}>
              {i18n.get('凭证字')}
            </span>
            <span className={styles['field-value']}>
              {voucher.voucherGroup || '--'}
            </span>
          </div>
          <div className={styles['info-field']}>
            <span className={styles['field-label']}>
              {i18n.get('凭证号')}
            </span>
            <span className={styles['field-value']}>
              {voucher.voucherNumber || '--'}
            </span>
          </div>
          <div className={styles['info-field']}>
            <span className={styles['field-label']}>
              {i18n.get('凭证类型')}
            </span>
            <span className={styles['field-value']}>
              {voucher.voucherType || '--'}
            </span>
          </div>
        </div>
      </div>


      {/* 更多信息 - 可折叠 */}
      {showMore && (
        <div className={styles['info-grid']}>
          <div className={styles['info-row']}>
            <div className={styles['info-field']}>
              <span className={styles['field-label']}>
                {i18n.get('制单人')}
              </span>
              <span className={styles['field-value']}>
                {voucher.preparerName || '--'}
              </span>
            </div>
            <div className={styles['info-field']}>
              <span className={styles['field-label']}>
                {i18n.get('审核人')}
              </span>
              <span className={styles['field-value']}>
                {voucher.auditorName || '--'}
              </span>
            </div>
            <div className={styles['info-field']}>
              <span className={styles['field-label']}>
                {i18n.get('过账人')}
              </span>
              <span className={styles['field-value']}>
                {voucher.posterName || '--'}
              </span>
            </div>
          </div>
          
          <div className={styles['info-row']}>
            <div className={styles['info-field']}>
              <span className={styles['field-label']}>
                {i18n.get('凭证状态')}
              </span>
              <span className={styles['field-value']}>
                {STATUS_MAP[voucher.status] || voucher.status || '--'}
              </span>
            </div>
            <div className={styles['info-field']}>
              <span className={styles['field-label']}>
                {i18n.get('过账日期')}
              </span>
              <span className={styles['field-value']}>
                {formatDate(voucher.postDate)}
              </span>
            </div>
            <div className={styles['info-field']}>
              <span className={styles['field-label']}>
                {i18n.get('更新日期')}
              </span>
              <span className={styles['field-value']}>
                {formatDate(voucher.updatedAt)}
              </span>
            </div>
          </div>
          
          {/* 凭证摘要 - 单独一行 */}
          {voucher.voucherSummary && (
            <div className={styles['info-row']}>
              <div className={styles['info-field-full']}>
                <span className={styles['field-label']}>
                  {i18n.get('凭证摘要')}
                </span>
                <span className={styles['field-value']}>
                  {voucher.voucherSummary || '--'}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 展开/收起按钮 */}
      <div className={styles['toggle-button-container']}>
        <button 
          className={styles['toggle-button']}
          onClick={handleToggleMore}
        >
          {showMore ? i18n.get('收起') : i18n.get('显示更多')}
        </button>
        <div className={styles['toggle-button-separator']}></div>
      </div>
    </div>
  );
}; 