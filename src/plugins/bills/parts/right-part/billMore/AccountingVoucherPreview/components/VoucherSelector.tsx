import React from 'react';
import { VoucherData } from '../services/voucherService';
import styles from '../AccountingVoucherPreview.module.less';

interface VoucherSelectorProps {
  vouchers: VoucherData[];
  selectedIndex: number;
  onSelectVoucher: (index: number) => void;
}

export const VoucherSelector: React.FC<VoucherSelectorProps> = ({
  vouchers,
  selectedIndex,
  onSelectVoucher
}) => {
  if (vouchers.length === 0) return null;

  return (
    <div className={styles['voucher-selector']}>
      <div className={styles['selector-buttons']}>
        {vouchers.map((voucher, index) => (
          <button
            key={voucher.id}
            onClick={() => onSelectVoucher(index)}
            className={`${styles['selector-button']} ${
              selectedIndex === index ? styles.active : ''
            }`}
          >
            {voucher.voucherType}
          </button>
        ))}
      </div>
    </div>
  );
}; 