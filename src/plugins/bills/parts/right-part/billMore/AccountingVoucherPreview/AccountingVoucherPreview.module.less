@import '~@eku<PERSON>bao/eui-styles/less/token-mobile.less';

.voucher-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.loading-placeholder,
.empty-state,
.error-state,
.feature-not-enabled {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--eui-text-caption);
  font-size: 14px;
  
  .loading-content,
  .empty-content,
  .error-content,
  .feature-not-enabled-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px;
  }
  
  .loading-text,
  .empty-text {
    font-size: 14px;
    color: #8c8c8c;
  }
}

.error-state {
  .error-content {
    flex-direction: column;
    text-align: center;
  }
  
  .error-message {
    color: #ff4d4f;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .retry-button {
    padding: 4px 12px;
    font-size: 12px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background-color: #40a9ff;
    }
  }
}

// 功能未开通状态样式
.feature-not-enabled {
  .feature-not-enabled-content {
    flex-direction: column;
    text-align: center;
  }
  
  .feature-not-enabled-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }
  
  .feature-not-enabled-title {
    color: #1f2937;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .feature-not-enabled-description {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 16px;
    max-width: 300px;
    line-height: 1.4;
  }
  
  .retry-button {
    padding: 4px 12px;
    font-size: 12px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background-color: #40a9ff;
    }
  }
}

// 凭证预览主体样式
.voucher-container {
  background-color: white;
  max-width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 凭证选择器样式
.voucher-selector {
  padding: 12px;
  border-bottom: 1px solid #e5e5e5;
  background-color: #ffffff;
  
  .selector-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
  
  .selector-button {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    min-width: 60px;
    cursor: pointer;
    background-color: white;
    color: #111827;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.active {
      background-color: var(--eui-primary-pri-500, #2555FF);
      color: white;
      border-color: var(--eui-primary-pri-500, #2555FF);
    }
  }
}

// 凭证信息样式
.voucher-info {
  padding: 0px;
  margin: 0px 24px;
  
  .info-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    font-size: 12px;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    
    .info-label {
      color: #848a93;
    }
    
    .info-value {
      font-weight: 500;
    }
  }

  // 新的网格布局样式
  .info-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 12px;
  }

  .info-row {
    display: flex;
    gap: 16px;
  }

  .info-field {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .field-label {
      font-size: 10px;
      color: #848a93;
      display: block;
    }
    
    .field-value {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
    }
  }

  .info-field-full {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .field-label {
      font-size: 10px;
      color: #848a93;
      display: block;
    }
    
    .field-value {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
      word-break: break-all;
    }
  }

  // 展开/收起按钮样式
  .toggle-button-container {
    margin-top: 4px;
    display: flex;
    justify-content: center;
    position: relative;
  }

  .toggle-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    font-size: 10px;
    color: #848a93;
    background: white;
    border: none;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    position: relative;
    z-index: 1;
    
    &:hover {
      background-color: #f0f8ff;
      color: var(--eui-primary-pri-500, #2555FF);
    }
  }

  .toggle-button-separator {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #f0f0f0;
    transform: translateY(-50%);
  }

  .toggle-icon {
    font-size: 8px;
    transition: transform 0.2s ease;
    
    &.rotate {
      transform: rotate(180deg);
    }
  }
}

// 凭证分录样式
.voucher-entries {
  flex: 1;
  overflow-y: auto;
  
  .entry-item {
    &:hover {
      background-color: #fafafa;
    }
  }
  
  .entry-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 12px 0px;
    margin: 0px 24px;
    border-bottom: 1px solid #f0f0f0;
    
    .entry-main {
      flex: 1;
      min-width: 0;
    }
  }
  
  .entry-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    
    .entry-index {
      font-size: 10px;
      color: #6b7280;
      background-color: #f2f4f6;
      padding: 2px 6px;
      border-radius: 4px;
    }
    
    .entry-account {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  
  .entry-summary {
    font-size: 12px;
    color: #4b5563;
    margin-bottom: 8px;
  }
  
  .entry-amounts {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    
    .amount-item {
      flex: 1;
      
      .amount-label {
        font-size: 10px;
        color: #848a93;
        display: block;
      }
      
      .amount-container {
        display: flex;
        flex-direction: column;
        gap: 2px;
      }
      
      .amount-value {
        font-size: 13px;
        font-weight: 500;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        
        &.debit {
          color: #2563eb;
        }
        
        &.credit {
          color: #dc2626;
        }
        
        &.empty {
          color: #bfbfbf;
        }
      }
      
      .original-amount {
        font-size: 8px;
        color: #8c8c8c;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      }
    }
  }
  
  .entry-auxiliary {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    
    .auxiliary-tag {
      display: inline-flex;
      align-items: center;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      background-color: #dbeafe;
      color: #1e40af;
      max-width: 120px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #bfdbfe;
      }
    }
  }
}

// 凭证合计样式
.voucher-summary {
  background-color: #ffffff;
  padding: 12px 24px;
  border-top: 1px solid #ededed;
  
  .summary-content {
    display: flex;
    gap: 16px;
    position: relative;
    
    .summary-item {
      flex: 1;
      
      .summary-label {
        font-size: 10px;
        color: #848a93;
        display: block;
      }
      
      .summary-value {
        font-size: 14px;
        font-weight: 700;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        
        &.debit {
          color: #2563eb;
        }
        
        &.credit {
          color: #dc2626;
        }
      }
    }
    
    .balance-indicator {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .balance-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.balanced {
          background-color: #22c55e;
        }
        
        &.unbalanced {
          background-color: #ff4d4f;
        }
        
        svg {
          width: 12px;
          height: 12px;
          color: white;
        }
      }
      
      .balance-text {
        color: #4b5563;
        margin-top: 4px;
        font-size: 10px;
      }
    }
  }
} 