import React, { Component } from 'react'
import styles from './AccountingVoucherPreview.module.less'
import { VoucherSelector } from './components/VoucherSelector'
import { VoucherInfo } from './components/VoucherInfo'
import { VoucherEntry } from './components/VoucherEntry'
import { VoucherSummary } from './components/VoucherSummary'
import { useVoucherData } from './hooks/useVoucherData'

interface AccountingVoucherPreviewProps {
  billDetails: any
  bus: any
  userInfo: any
  isActiveInfo: boolean
}

// 函数式组件包装器，使用Hook
const VoucherPreviewContent: React.FC<{ billDetails: any }> = ({ billDetails }) => {
  const {
    vouchers,
    selectedVoucherIndex,
    currentVoucher,
    loading,
    error,
    totalDebit,
    totalCredit,
    isBalanced,
    selectVoucher,
    refreshData,
  } = useVoucherData(billDetails)

  // 控制辅助核算标签是否显示编码（而非名称）
  const [showAuxiliaryCode, setShowAuxiliaryCode] = React.useState(false)

  // 处理辅助核算标签点击事件
  const handleAuxiliaryTagClick = () => {
    setShowAuxiliaryCode(!showAuxiliaryCode)
  }

  if (loading) {
    return (
      <div className={styles['loading-placeholder']}>
        <div className={styles['loading-content']}>
          <div className={styles['loading-text']}>
            {i18n.get('加载中')}...
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    // 功能未开通的特殊处理
    if (error === 'FEATURE_NOT_ENABLED') {
      return (
        <div className={styles['feature-not-enabled']}>
          <div className={styles['feature-not-enabled-content']}>
            <div className={styles['feature-not-enabled-icon']}>
              🔒
            </div>
            <div className={styles['feature-not-enabled-title']}>
              {i18n.get('功能未开通')}
            </div>
            <div className={styles['feature-not-enabled-description']}>
              {i18n.get('凭证预览功能暂未开通，请联系管理员开通相关权限')}
            </div>
            <button
              onClick={refreshData}
              className={styles['retry-button']}
            >
              {i18n.get('重试')}
            </button>
          </div>
        </div>
      )
    }

    // 其他错误的处理
    return (
      <div className={styles['error-state']}>
        <div className={styles['error-content']}>
          <div className={styles['error-message']}>
            {error}
          </div>
          <button
            onClick={refreshData}
            className={styles['retry-button']}
          >
            {i18n.get('重试')}
          </button>
        </div>
      </div>
    )
  }

  if (!currentVoucher) {
    return (
      <div className={styles['empty-state']}>
        <div className={styles['empty-content']}>
          <div className={styles['empty-text']}>
            {i18n.get('暂无凭证数据')}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles['voucher-container']}>
      {/* 凭证选择器 - 只有多条凭证时才显示 */}
      {vouchers.length > 1 && (
        <VoucherSelector
          vouchers={vouchers}
          selectedIndex={selectedVoucherIndex}
          onSelectVoucher={selectVoucher}
        />
      )}

      <div className={styles['voucher-entries']}>
        {/* 凭证基本信息 */}
        <VoucherInfo voucher={currentVoucher} />
        {/* 分录列表 */}
        {currentVoucher.entries.map((entry, index) => (
          <VoucherEntry 
            key={entry.id} 
            entry={entry} 
            index={index}
            showAuxiliaryCode={showAuxiliaryCode}
            onAuxiliaryTagClick={handleAuxiliaryTagClick}
          />
        ))}
      </div>

      {/* 合计行 */}
      <VoucherSummary
        totalDebit={totalDebit}
        totalCredit={totalCredit}
        isBalanced={isBalanced}
      />
    </div>
  )
}

// 主组件类
export default class AccountingVoucherPreview extends Component<AccountingVoucherPreviewProps> {
  render() {
    const { billDetails } = this.props
    
    return (
      <div className={styles['voucher-preview-container']}>
        <VoucherPreviewContent billDetails={billDetails} />
      </div>
    )
  }
} 