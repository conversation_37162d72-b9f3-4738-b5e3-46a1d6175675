# 凭证预览组件 (AccountingVoucherPreview)

## 概述

凭证预览组件用于在单据详情页面展示对应的会计凭证预览信息。该组件采用模块化架构，将数据处理与视图展示分离，支持国际化，提供良好的用户体验。

## 功能特性

- 📊 **凭证数据展示**: 显示完整的会计凭证信息，包括分录明细
- 🔄 **多凭证切换**: 支持在多个相关凭证之间切换，单凭证时自动隐藏选择器
- ⚖️ **平衡验证**: 自动验证借贷双方金额平衡性
- 🌍 **国际化支持**: 使用 i18n.get() 处理所有文案
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🔄 **状态管理**: 支持加载、错误、空状态、功能未开通等多种状态
- 🔒 **权限控制**: 403错误时显示功能未开通引导页面
- 🧩 **模块化架构**: 组件拆分合理，易于维护和扩展
- 🏷️ **辅助核算**: 支持辅助核算标签显示，可切换编码/名称显示

## 架构设计

### 目录结构

```
AccountingVoucherPreview/
├── AccountingVoucherPreview.tsx      # 主组件
├── AccountingVoucherPreview.module.less # 样式文件
├── index.ts                         # 导出文件
├── services/
│   └── voucherService.ts            # 数据服务层
├── components/
│   ├── VoucherSelector.tsx          # 凭证选择器
│   ├── VoucherInfo.tsx              # 凭证基本信息
│   ├── VoucherEntry.tsx             # 单个分录展示
│   └── VoucherSummary.tsx           # 凭证合计
├── hooks/
│   └── useVoucherData.ts            # 数据管理Hook
└── README.md                        # 文档
```

### 数据流

```
单据数据 (billDetails) 
    ↓
API调用 (VoucherService.getVoucherPreview)
    ↓
数据转换 (safeTransformVoucherData)
    ↓
Hook管理 (useVoucherData)
    ↓
子组件渲染
```

## 核心组件

### 1. 主组件 (AccountingVoucherPreview)

负责整体布局和状态控制，采用类组件包装函数式组件的架构。

```tsx
interface AccountingVoucherPreviewProps {
  billDetails: any;     // 单据详情数据
  bus: any;            // 事件总线
  userInfo: any;       // 用户信息
  isActiveInfo: boolean; // 是否激活状态
}
```

**特殊状态处理**：
- 加载状态：显示加载中提示
- 错误状态：显示错误信息和重试按钮
- 功能未开通：403错误时显示专门的引导页面
- 空状态：无凭证数据时的友好提示

### 2. 数据服务层 (VoucherService)

提供凭证数据的获取和处理功能：

```tsx
class VoucherService {
  // 获取凭证预览数据（调用真实API）
  static async getVoucherPreview(billDetails: any): Promise<VoucherData[]>
  
  // 验证凭证平衡性
  static validateVoucherBalance(voucher: VoucherData): boolean
  
  // 计算凭证合计
  static calculateVoucherTotals(voucher: VoucherData)
}
```

**API集成**：
- 调用真实的凭证生成接口 `https://qoe540tu84.gzg.sealos.run/get-voucher`
- 支持403错误的特殊处理（功能未开通）
- 包含完善的错误处理和数据安全转换

### 3. 数据管理Hook (useVoucherData)

统一管理凭证数据状态和操作：

```tsx
const {
  vouchers,           // 凭证列表
  selectedVoucherIndex, // 当前选中索引
  currentVoucher,     // 当前选中凭证
  loading,            // 加载状态
  error,              // 错误信息（包含特殊错误标识）
  totalDebit,         // 借方合计
  totalCredit,        // 贷方合计
  isBalanced,         // 是否平衡
  selectVoucher,      // 选择凭证
  refreshData,        // 刷新数据
} = useVoucherData(billDetails);
```

### 4. 子组件详解

#### VoucherSelector - 凭证选择器
- 只有多个凭证时才显示（单凭证自动隐藏）
- 支持凭证间快速切换
- 显示凭证编号和基本信息

#### VoucherInfo - 凭证基本信息
- 采用网格布局展示凭证头信息
- 支持展开/收起更多信息
- 统一空值处理（显示"--"）
- 包含凭证状态、制单人、审核人等完整信息

#### VoucherEntry - 分录展示
- 显示会计科目、摘要、借贷金额
- 支持辅助核算标签（可切换编码/名称显示）
- 点击辅助核算标签可切换显示模式
- 借贷方向和金额的清晰展示

#### VoucherSummary - 凭证合计
- 显示借方合计、贷方合计
- 平衡状态指示（平衡/不平衡）
- 不平衡时的警告提示

## 数据接口

### VoucherData 完整接口

```tsx
interface VoucherData extends VoucherHeader {
  entries?: VoucherEntry[];
}

interface VoucherHeader {
  id?: string;                    // 凭证ID
  companyCode?: string;           // 公司代码
  year?: number;                  // 会计年份
  period?: number;                // 会计期间
  voucherGroup?: string;          // 凭证字
  voucherNumber?: string;         // 凭证号
  date?: string;                  // 凭证日期
  voucherType?: string;           // 凭证类型
  voucherSummary?: string;        // 凭证摘要
  attachmentCount?: number;       // 附件数
  preparerId?: string;            // 制单人ID
  preparerName?: string;          // 制单人姓名
  auditorId?: string;             // 审核人ID
  auditorName?: string;           // 审核人姓名
  posterId?: string;              // 过账人ID
  posterName?: string;            // 过账人姓名
  postDate?: string;              // 过账日期
  status?: VoucherStatus;         // 凭证状态
  createdAt?: Date;               // 创建时间
  updatedAt?: Date;               // 更新时间
}

interface VoucherEntry {
  id?: string;                    // 分录ID
  sequence?: number;              // 序号
  summary?: string;               // 摘要
  accountCode?: string;           // 科目代码
  accountName?: string;           // 科目名称
  direction?: DebitCreditDirection; // 借贷方向
  currency?: string;              // 币别
  exchangeRate?: number;          // 汇率
  originalAmount?: number;        // 原币金额
  debitAmount?: number;           // 借方金额
  creditAmount?: number;          // 贷方金额
  auxiliaryDimensions?: AuxiliaryDimension[]; // 辅助核算
}

interface AuxiliaryDimension {
  dimensionType?: string;         // 维度类型
  dimensionTypeName?: string;     // 维度类型名称
  dimensionValueCode?: string;    // 维度值编码
  dimensionValueName?: string;    // 维度值名称
}

enum VoucherStatus {
  Draft = 'DRAFT',               // 暂存
  Submitted = 'SUBMITTED',       // 已提交
  Approved = 'APPROVED',         // 已审核
  Posted = 'POSTED',             // 已过账
  Rejected = 'REJECTED',         // 已拒绝
  Voided = 'VOIDED'             // 已作废
}
```

## 使用方法

### 基本使用

```tsx
import AccountingVoucherPreview from './AccountingVoucherPreview';

<AccountingVoucherPreview
  billDetails={billData}
  bus={eventBus}
  userInfo={currentUser}
  isActiveInfo={true}
/>
```

### API接口配置

组件调用的API接口：`https://qoe540tu84.gzg.sealos.run/get-voucher`

请求格式：
```json
{
  "corporationId": "企业ID",
  "formData": "单据数据对象",
  "formState": "单据状态",
  "formId": "单据ID",
  "formCode": "单号",
  "formVersion": "单据版本"
}
```

响应格式：
```json
{
  "voucher": [
    {
      "id": "凭证ID",
      "year": 2025,
      "period": 4,
      "voucherGroup": "记",
      "voucherNumber": "1",
      "date": "2025-04-29",
      "voucherType": "费用报销凭证",
      "entries": [
        {
          "id": "分录ID",
          "sequence": 1,
          "summary": "报销4月费用",
          "accountCode": "6601.01",
          "accountName": "管理费用-办公费",
          "direction": "DEBIT",
          "debitAmount": 1000.00,
          "creditAmount": 0.00
        }
      ]
    }
  ]
}
```

## 国际化配置

组件中所有文案都通过 `i18n.get()` 方法处理，需要在项目的国际化配置中添加以下键值：

```json
{
  "会计年份": "Accounting Year",
  "会计区间": "Period",
  "凭证日期": "Voucher Date",
  "凭证字": "Voucher Group",
  "凭证号": "Voucher Number",
  "凭证类型": "Voucher Type",
  "制单人": "Preparer",
  "审核人": "Auditor", 
  "过账人": "Poster",
  "凭证状态": "Status",
  "过账日期": "Post Date",
  "更新日期": "Update Date",
  "凭证摘要": "Summary",
  "借方": "Debit",
  "贷方": "Credit",
  "借方合计": "Total Debit",
  "贷方合计": "Total Credit",
  "平衡": "Balanced",
  "不平衡": "Unbalanced",
  "加载中": "Loading",
  "重试": "Retry",
  "暂无凭证数据": "No Voucher Data",
  "加载凭证数据失败": "Failed to Load Voucher Data",
  "功能未开通": "Feature Not Enabled",
  "凭证预览功能暂未开通，请联系管理员开通相关权限": "Voucher preview feature is not enabled. Please contact administrator.",
  "显示更多": "Show More",
  "收起": "Collapse"
}
```

## 样式定制

组件使用 CSS Modules + Less 的方式：

- **CSS Modules**: 用于组件级别的样式隔离
- **Less**: 提供变量和嵌套语法支持
- **响应式**: 支持移动端适配

关键样式类：
- `.voucher-preview-container`: 主容器
- `.voucher-selector`: 凭证选择器
- `.voucher-info`: 凭证信息展示
- `.voucher-entry`: 分录展示
- `.voucher-summary`: 合计区域
- `.feature-not-enabled`: 功能未开通状态

## 错误处理

### 403 功能未开通处理

当API返回403状态码时，组件会：
1. 在服务层识别403错误并抛出 `FEATURE_NOT_ENABLED` 标识
2. Hook层捕获特殊错误标识
3. 组件层显示专门的功能未开通页面，包含：
   - 🔒 锁定图标
   - "功能未开通" 标题
   - 详细说明文字
   - 重试按钮

### 其他错误处理

- 网络错误：显示通用错误信息和重试按钮
- 数据格式错误：安全转换，避免应用崩溃
- 空数据：友好的空状态提示

## 开发指南

### 本地开发

1. 组件已接入真实API，可直接使用
2. 修改API地址请编辑 `services/voucherService.ts` 中的接口URL
3. 所有样式使用CSS Modules，避免全局污染

### 扩展功能

1. **添加新的辅助核算维度**: 扩展 `AuxiliaryDimension` 接口
2. **自定义凭证格式**: 修改 `safeTransformVoucherData` 转换逻辑
3. **添加新状态**: 扩展 `VoucherStatus` 枚举和状态映射
4. **国际化扩展**: 在 i18n 配置中添加新文案

### 测试建议

1. **正常流程**: 测试正常的凭证数据加载和显示
2. **边界情况**: 测试空数据、错误数据的处理
3. **权限测试**: 模拟403错误，验证功能未开通页面
4. **交互测试**: 测试凭证切换、辅助核算标签切换等交互

## 注意事项

1. **空值处理**: 所有可能为空的字段都会显示"--"，保持界面一致性
2. **单凭证优化**: 只有一个凭证时自动隐藏选择器，优化用户体验
3. **权限控制**: 403错误时提供清晰的权限引导，而不是技术错误信息
4. **数据安全**: 所有API数据都经过安全转换，防止字段缺失导致的显示问题

## 版本历史

- **v1.0.0**: 初始版本，基础凭证预览功能
- **v1.1.0**: 添加国际化支持和组件拆分  
- **v1.2.0**: 完善数据服务层和Hook管理
- **v1.3.0**: 接入真实API，添加403错误处理
- **v1.4.0**: 优化UI交互，统一空值处理，添加单凭证隐藏逻辑

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

请确保所有更改都有相应的测试覆盖，并遵循项目的代码规范。 