import { useState, useEffect } from 'react';
import { VoucherService, VoucherData } from '../services/voucherService';

export interface UseVoucherDataResult {
  vouchers: VoucherData[];
  selectedVoucherIndex: number;
  currentVoucher: VoucherData | null;
  loading: boolean;
  error: string | null;
  totalDebit: number;
  totalCredit: number;
  isBalanced: boolean;
  selectVoucher: (index: number) => void;
  refreshData: () => Promise<void>;
}

export const useVoucherData = (billDetails: any): UseVoucherDataResult => {
  const [vouchers, setVouchers] = useState<VoucherData[]>([]);
  const [selectedVoucherIndex, setSelectedVoucherIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const currentVoucher = vouchers[selectedVoucherIndex] || null;

  // 计算当前凭证的合计
  const { totalDebit, totalCredit, isBalanced } = currentVoucher 
    ? VoucherService.calculateVoucherTotals(currentVoucher)
    : { totalDebit: 0, totalCredit: 0, isBalanced: false };

  // 加载凭证数据
  const loadVoucherData = async () => {
    if (!billDetails) return;

    setLoading(true);
    setError(null);

    try {
      const voucherData = await VoucherService.getVoucherPreview(billDetails);
      setVouchers(voucherData);
      setSelectedVoucherIndex(0); // 重置选择
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : i18n.get('加载凭证数据失败');
      // 特殊处理功能未开通的错误
      if (errorMessage === 'FEATURE_NOT_ENABLED') {
        setError('FEATURE_NOT_ENABLED');
      } else {
        setError(errorMessage);
      }
      console.error('Failed to load voucher data:', err);
    } finally {
      setLoading(false);
    }
  };

  // 选择凭证
  const selectVoucher = (index: number) => {
    if (index >= 0 && index < vouchers.length) {
      setSelectedVoucherIndex(index);
    }
  };

  // 刷新数据
  const refreshData = async () => {
    await loadVoucherData();
  };

  // 当单据详情变化时重新加载数据
  useEffect(() => {
    loadVoucherData();
  }, [billDetails]);

  return {
    vouchers,
    selectedVoucherIndex,
    currentVoucher,
    loading,
    error,
    totalDebit,
    totalCredit,
    isBalanced,
    selectVoucher,
    refreshData,
  };
}; 