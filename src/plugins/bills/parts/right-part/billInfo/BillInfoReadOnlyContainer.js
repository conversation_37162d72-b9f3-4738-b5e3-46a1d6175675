import React, { PureComponent } from 'react'
import BillInfoReadonly from './BillInfoReadonly'
import FlowPlanReadonly from './FlowPlanReadonly'
import ExpressReadonly from './ExpressReadonly'
import ETabs from '../../../../../elements/ETabs'
import BudgetControl from '../../../../../elements/expense-budget-control'
import VoucherView from './VoucherView'
import { EnhanceConnect } from '@ekuaibao/store'
import {
  getBudgetList,
  getVoucherData,
  getShowBudgetTab,
  getRepayInfo,
  applyRepayment,
  getLoanPackageByFlowId,
  getFlowInfoById,
  syncTravel,
  getPublicExpansion,
  getFormExtendByIds,
  grantEkbCode
} from '../../../bills.action'
import { app as api } from '@ekuaibao/whispered'
import RightPartHeader from './RightPartHeader'
import MessageCenter from '@ekuaibao/messagecenter'
import { get } from 'lodash'
import styles from './newSearchBillInfo.module.less'
import MyLoanDetailView from './../../../../../elements/myLoan-detail-view'
import { handleBillDetailClickById } from './../../../bill-stacker-manager/util'
import { trackLoadBillFlowTime } from '../../../util/trackBill'
import { toJS, isObservable } from 'mobx'
import PrintPreview from './PrintPreview'
import MoreInfo from '../billMore/MoreInfo'
import { HABAsyncComponent } from '../../../../../components/dynamic/HABWidget/HABAsyncComponent'
import { getBoolVariation } from '../../../../../lib/featbit'
import classNames from 'classnames'
import BillTopOperator from '../billInfo/BillTopOperator'

@EnhanceConnect(
  state => ({
    remunerationBatchField: state['@remuneration'].remunerationBatchField,
    userinfo: state['@common'].userinfo && state['@common'].userinfo.staff,
    KA_PREVIEW_PRINT_IN_MODAL: state['@common'].powers.KA_PREVIEW_PRINT_IN_MODAL,
    Express: state['@common'].powers.Express,
    staffDisplayConfigField: state['@common'].organizationConfig?.staffDisplayConfig?.[1] || '',
    printPreviewUrl: state['@bills'].printPreviewUrl
  }),
  { getBudgetList, getVoucherData, getRepayInfo, getLoanPackageByFlowId, applyRepayment, getFlowInfoById }
)
export default class BillInfoReadOnlyContainer extends PureComponent {
  constructor(props) {
    super(props)
    this.ref = React.createRef()
    this.bus = props.bus || new MessageCenter()
    this.state = {
      remunerationTabs: null,
      activeKey: 'billInfo',
      budgetPower: false,
      voucherPower: false,
      dataSource: props.dataSource,
      isShowBudgetTab: false,
      loanBag: null,
      ownerId: '',
      billType: 'loanTab',
      habList: []
    }
    this.initPrintPreviewData()
  }

  initPrintPreviewData = () => {
    // 表格预览charge打开时，在首个页签中展示打印预览结果
    const { dataSource, showAllFeeType, privilegeId, KA_PREVIEW_PRINT_IN_MODAL } = this.props
    if (!KA_PREVIEW_PRINT_IN_MODAL) return
    const obj = api.invokeService('@share:get:print:param', dataSource)
    const data = [obj]
    data.isTablePreview = true
    data.showAllFeeType = showAllFeeType
    data.privilegeId = privilegeId
    const { doPrint } = api.require('@audit/service-print')
    doPrint(data, !!privilegeId)
  }

  clearPrintPreviewUrl = () => {
    if (!this.props.KA_PREVIEW_PRINT_IN_MODAL) return
    api.invokeService('@bills:set:print:preview:url', '')
  }

  componentWillUnmount() {
    this.clearPrintPreviewUrl()
    this.bus.un('right:risk:open:details', this.onOpenOwnerLoanList)
  }

  componentWillMount() {
    api.invokeService('@remuneration:get:remuneration:config:setting')
    // 获取预算的权限
    api
      .dataLoader('@common.powers')
      .load()
      .then(powers => powers.Budget)
      .then(value => {
        this.setState({ budgetPower: value })
      })
    //获取凭证预览权限
    api
      .dataLoader('@common.powers')
      .load()
      .then(powers => powers.VoucherPreview)
      .then(value => {
        this.setState({ voucherPower: value })
      })
    this.bus.on('right:risk:open:details', this.onOpenOwnerLoanList)
  }

  componentDidMount() {
    const { isNewSearchInfo = true, dataSource } = this.props
    const typeList = ['reconciliation', 'settlement']
    const billType = get(dataSource, 'formType')
    if (isNewSearchInfo && !!this.ref.current) {
      const tabLine = this.ref.current.getElementsByClassName('ant-tabs-ink-bar')
      if (tabLine && tabLine.length) {
        const child = document.createElement('div')
        child.className = 'new-search-tab-line-child'
        tabLine[0].appendChild(child)
      }
    }
    const { logs = [] } = dataSource
    const havePaymentLogs = logs.filter(item => {
      return item.attributes && item.attributes.paymentInfos
    })
    if (havePaymentLogs.length > 0) {
      let payeeIds = []
      let feeTypeIds = []
      havePaymentLogs.forEach(logs => {
        logs.attributes.paymentInfos &&
          logs.attributes.paymentInfos.forEach(item => {
            const payeeId = get(item, 'payeeId')
            const feetypeId = get(item, 'feeTypeId')
            if (typeof payeeId === 'string' && payeeId && payeeIds.indexOf(payeeId) < 0) payeeIds.push(payeeId)
            if (typeof feetypeId === 'string' && feetypeId && feeTypeIds.indexOf(feetypeId) < 0)
              feeTypeIds.push(feetypeId)
          })
      })
      if (payeeIds.length > 0 && feeTypeIds.length > 0) {
        Promise.all([
          api.invokeService('@payeeAccount:get:payeeInfo:by:ids', payeeIds),
          api.invokeService('@custom-feetype:get:feeType:by:ids', feeTypeIds)
        ]).then(data => {
          this.dsData = data
          this.fnCleanDataSourceWithDSData()
        })
      } else if (payeeIds.length > 0) {
        api.invokeService('@payeeAccount:get:payeeInfo:by:ids', payeeIds).then(data => {
          this.dsData = data
          this.fnCleanDataSourceWithDSData()
        })
      }
    }
    // 对账单结算单不参与预算相关逻辑
    if (typeList.indexOf(billType) === -1) {
      api.dispatch(getShowBudgetTab({ id: dataSource.id })).then(res => {
        this.setState({ isShowBudgetTab: res.value })
      })
    }

    if (!!window.__LOAD_FLOW_TIME) {
      trackLoadBillFlowTime({ startTime: window.__LOAD_FLOW_TIME, endTime: Date.now() })
    }
    window.__LOAD_FLOW_TIME = 0
    // 申请单就会行程到第三方平台
    if (billType === 'requisition') {
      syncTravel({ id: dataSource?.id })
    }

    // 获取hab相关数据，可以空闲获取
    this.getHabData()
  }

  createRemunerationTabs = dataSource => {
    let { remunerationBatchField } = this.props
    api
      .invokeService('@remuneration:creat:tabs', {
        bus: this.bus,
        dataSource,
        isReadonly: true,
        remunerationBatchField
      })
      .then(res => {
        const { value } = res
        this.setState({ remunerationTabs: value })
      })
  }

  fnCleanDataSourceWithDSData(dataSource = this.props.dataSource) {
    const { logs = [] } = dataSource
    const havePaymentLogs = logs.filter(item => item.attributes && item.attributes.paymentInfos)

    const data = this.dsData || [{}, {}]
    if (!Array.isArray(data)) {
      havePaymentLogs.forEach(logs => {
        logs.attributes.paymentInfos &&
          logs.attributes.paymentInfos.forEach(item => {
            item.payeeId = data.items.find(payee => payee.id === item.payeeId)
          })
      })
    } else if (data[0].items && data[1].items) {
      havePaymentLogs.forEach(logs => {
        logs.attributes.paymentInfos &&
          logs.attributes.paymentInfos.forEach(item => {
            item.payeeId = data[0].items.find(payee => payee.id === item.payeeId)
            item.feeTypeId = data[1].items.find(feeType => feeType.id === item.feeTypeId)
          })
      })
    }

    this.setState({ dataSource })
    this.bus.emit('render:billinforeadonly:logs')
  }

  componentWillReceiveProps(nextProp) {
    if (this.props.dataSource !== nextProp.dataSource) {
      this.fnCleanDataSourceWithDSData(nextProp.dataSource)
    }
  }

  showBudgetTab(tabSource) {
    let { budgetPower = {} } = this.state
    if (!budgetPower) return
    let { isShowBudgetTab } = this.state
    let { dataSource, getBudgetList } = this.props
    if (isShowBudgetTab) {
      tabSource.push({
        tab: i18n.get('预算占用'),
        children: <BudgetControl bus={this.bus} doc={dataSource} getBudgetList={getBudgetList} />,
        key: 'budgetControl'
      })
    }
  }

  showReviewTab(tabSource) {
    const { KA_PREVIEW_PRINT_IN_MODAL, printPreviewUrl } = this.props
    if (KA_PREVIEW_PRINT_IN_MODAL) {
      tabSource.splice(1, 0, {
        tab: i18n.get('审批预览'),
        children: <PrintPreview url={printPreviewUrl} />,
        key: 'printPreview'
      })
    }
  }
  showFlowTab(tabSource) {
    const { isEditConfig, userinfo, staffDisplayConfigField, privilegeId } = this.props
    const { flowlogType, dataSource = {} } = this.state
    const { plan } = dataSource
    if (plan) {
      tabSource.push({
        tab: i18n.get('审批流程'),
        children: (
          <FlowPlanReadonly
            versionItemAction={this._handleHistoryVersionClick}
            bus={this.bus}
            dataSource={dataSource}
            isEditConfig={isEditConfig}
            userInfo={userinfo}
            flowlogType={flowlogType}
            staffDisplayConfigField={staffDisplayConfigField}
            privilegeId={privilegeId}
          />
        ),
        key: 'flowPlan'
      })
    }
  }

  showExpressTab(tabSource) {
    const { Express, dataSource, showExpressButton } = this.props
    const isHaveNode =
      dataSource.plan &&
      dataSource.plan.nodes.find(item => {
        return item.expressConfig && item.expressConfig.type && item.expressConfig.type === 'send'
      })
    if (Express && isHaveNode) {
      tabSource.push({
        tab: i18n.get('寄送信息'),
        children: <ExpressReadonly bus={this.bus} showExpressButton={showExpressButton} flowId={dataSource.id} />,
        key: 'express'
      })
    }
  }

  getLoanBag = async () => {
    const { dataSource, getRepayInfo, getLoanPackageByFlowId } = this.props
    const configsList = get(dataSource, 'form.specificationId.configs')
    const loanMoney = get(dataSource, 'form.loanMoney.standard')
    const state = get(dataSource, 'state')
    const id = get(dataSource, 'id')
    let isLoan = configsList.find(item => {
      return item.ability === 'loan'
    })
    if (isLoan && loanMoney > 0 && (state === 'paid' || state === 'archived')) {
      getLoanPackageByFlowId &&
        (await getLoanPackageByFlowId({ id }).then(action => {
          if (!action.error) {
            let loanDetail = action.payload && action.payload.value
            this.setState({ ownerId: loanDetail.ownerId })
            getRepayInfo({ loanInfoId: loanDetail.id }).then(action => {
              if (action.error) return
              let data = action.payload
              let loanBag = { loanDetail, repayInfo: data.items[0] }
              this.setState({ loanBag })
            })
          }
        }))
    }
  }

  handleExpenseClick(id) {
    let { layer, flowId, privilegeId = '' } = this.props
    //增加flowId后台用来判断是否有权限查看
    const params = flowId ? { id, flowId, privilegeId } : { id, privilegeId }
    handleBillDetailClickById.call(this, params, !layer)
  }

  handleLoanClick = id => {
    let { layer, flowId, privilegeId = '' } = this.props
    const params = flowId ? { id, flowId, privilegeId } : { id, privilegeId }
    handleBillDetailClickById.call(this, params, !layer)
  }
  showLoanTab(tabSource) {
    const {
      dataSource,
      applyRepayment,
      userinfo,
      getLoanPackageByFlowId,
      getRepayInfo,
      needHideLoanDetail
    } = this.props
    const { ownerId } = this.state
    const configsList = get(dataSource, 'form.specificationId.configs')
    const loanMoney = get(dataSource, 'form.loanMoney.standard')
    const state = get(dataSource, 'state')
    const isOwner = ownerId === userinfo.id
    const { loanBag, activeKey } = this.state
    // !loanBag && this.getLoanBag()
    let isLoan = configsList.find(item => {
      return item.ability === 'loan'
    })
    if (isLoan && loanMoney > 0 && !needHideLoanDetail && (state === 'paid' || state === 'archived')) {
      tabSource.push({
        tab: i18n.get('借还详情'),
        children: (
          <MyLoanDetailView
            active={activeKey === 'loan'}
            loanDetail={''}
            loanLogs={''}
            repayInfo={''}
            loanType={'fromBill'}
            getLoanBag={this.getLoanBag}
            applyRepayment={applyRepayment}
            isOwner={isOwner}
            onExpenseClick={this.handleExpenseClick.bind(this)}
            onLoanClick={this.handleLoanClick.bind(this)}
            dataSource={dataSource}
            getLoanPackageByFlowId={getLoanPackageByFlowId}
            getRepayInfo={getRepayInfo}
          />
        ),
        key: 'loan'
      })
    }
  }

  showVoucherTab(tabSource) {
    const { dataSource } = this.props
    let { activeKey } = this.state
    tabSource.push({
      tab: i18n.get('会计凭证'),
      children: <VoucherView dataSource={dataSource} active={activeKey === 'voucher'} />,
      key: 'voucher'
    })
  }

  // 获取 habtab相关配置，根据配置来确定是否要展示
  getHabData = async () => {
    const { dataSource } = this.props

    const configsList = get(dataSource, 'form.specificationId.configs')
    const id = get(dataSource, 'form.specificationId.id')
    const formType = get(dataSource, 'formType')
    const flowId = get(dataSource, 'id')
    const flowCode = get(dataSource, 'form.code')
    const habExtend = configsList.find(item => {
      return item.ability === 'habExtend'
    })
    let habList = []
    // 默认不可见
    let permisson = false
    // 是否支持hab tab
    const isSupportHabExtend = habExtend?.isSupportHabExtend
    // 可优化，如果没有包含子部门的可以改为前端判断，这里统一调用接口来看是否有查看权限
    // const needFetch = habExtend?.visibility?.departmentsIncludeChildren && habExtend?.visibility?.departments?.length > 0
    if (habExtend?.visibility?.fullVisible) {
      permisson = true
    } else {
      const fetchResult = await getPublicExpansion({
        extendType: 'habExtend',
        id
      })
      permisson = fetchResult?.items?.applyResult
    }
    // 不需要处理的情况：不支持hab、 没有权限
    if (!isSupportHabExtend || !permisson) {
      this.setState({
        habList: []
      })
      return false
    }

    try {
      if (!window.__HAB_EkbCode) {
        const codeResult = await grantEkbCode()
        window.__HAB_EkbCode = codeResult?.id
      }
    } catch (error) {
      console.log('ekbCode获取失败：', error)
    }
    const formExtendData =
      (await getFormExtendByIds(habExtend?.extendData.map(item => item.relationExtendId)))?.items || []
    habList = formExtendData.map(item => ({
      name: item.name,
      id: item.id,
      children: (
        <HABAsyncComponent field={{ reference: item }} formType={formType} flowId={flowId} flowCode={flowCode} />
      )
    }))
    this.setState({
      habList
    })
  }

  // habtab，根据配置来确定是否要展示
  showHabTab = tabSource => {
    this.state.habList.forEach(item => {
      tabSource.push({
        tab: item.name,
        children: item.children,
        key: item.id
      })
    })
  }

  handleClickNotice = e => {
    e.stopPropagation()
    e.preventDefault()
    this.setState({ activeKey: 'budgetControl' })
  }

  _handleHistoryVersionClick = item => {
    const {
      dataSource: { id },
      privilegeId,
      showAllFeeType
    } = this.props
    api.open('@bills:BillHistoryVersionModal', { item, flowId: id, privilegeId, showAllFeeType })
  }

  handleClickTab = (activeKey = 'billInfo') => {
    this.changeTab(activeKey)
  }

  changeTab = (activeKey = 'billInfo', flowlogType = '') => {
    this.setState({ activeKey, flowlogType }, () => {
      activeKey === 'budgetControl' && this.bus.has('budget:tab:click') && this.bus.emit('budget:tab:click')
    })
  }

  onOpenOwnerLoanList = () => {
    let { dataSource, privilegeId, showAllFeeType } = this.props
    api.invokeService('@audit:get:loan:Details', dataSource, privilegeId, showAllFeeType)
  }

  isTableMode = () => {
    const { mode = 'table', isModal } = this.props
    // 列表模式和内联模式不展示顶部操作栏
    // 列表模式在“首页-我的单据”使用
    // 内联模式在“首页-我的借款”、“首页-申请事项”使用
    // 这里没有使用table正向判断是因为table的场景太多，且大部分场景下没有传mode
    return mode === 'table' && !isModal
  }

  render() {
    let {
      isModal,
      dataSource,
      keel,
      stackerManager,
      renderRiskTips,
      onOpenOwnerLoanList,
      userinfo,
      isShowWarningTips,
      detailStack,
      suppleInvoiceBtn,
      source,
      noRiskWarning,
      riskData,
      privilegeId,
      isNewSearchInfo = true,
      offsetWidth,
      size,
      isFlowEditable,
      showAllFeeType,
      backLogOwnerId,
      isBacklog,
      isEditConfig,
      hiddenFlowLinks = false,
      showWidget,
      hasPopupTitle,
      showHeaderClose,
      showFullScreenDrawer,
      showBillMore,
      staffDisplayConfigField,
      showBillApproveResult,
      mode,
      canDeleteComment,
      billFooter,
      showUpDown
    } = this.props
    let { activeKey, voucherPower, budgetPower, isShowBudgetTab, flowlogType } = this.state
    const budgetParams = { budgetPower, isShowBudgetTab, handleClickNotice: this.handleClickNotice }
    let tabSource = [
      {
        tab: i18n.get('单据详情'),
        children: (
          <BillInfoReadonly
            key="billInfo"
            bus={this.bus}
            isModal={isModal}
            keel={keel}
            showBillMore={showBillMore}
            showAllFeeType={showAllFeeType}
            stackerManager={stackerManager}
            dataSource={isObservable(dataSource) ? toJS(dataSource) : dataSource}
            renderRiskTips={renderRiskTips}
            onOpenOwnerLoanList={onOpenOwnerLoanList || this.onOpenOwnerLoanList}
            changeTabAction={this.changeTab}
            userInfo={userinfo}
            isShowWarningTips={isShowWarningTips}
            suppleInvoiceBtn={suppleInvoiceBtn}
            source={source}
            detailStack={detailStack}
            noRiskWarning={noRiskWarning}
            singleRiskData={riskData}
            budgetParams={budgetParams}
            privilegeId={privilegeId}
            offsetWidth={offsetWidth}
            size={size}
            isFlowEditable={isFlowEditable}
            backLogOwnerId={backLogOwnerId}
            isBacklog={isBacklog}
            isEditConfig={isEditConfig}
            flowlogType={flowlogType}
            versionItemAction={this._handleHistoryVersionClick}
            hiddenFlowLinks={hiddenFlowLinks}
          />
        ),
        key: 'billInfo'
      }
    ]
    this.showReviewTab(tabSource)
    if (!getBoolVariation('off_old_flow_tab')) {
      this.showFlowTab(tabSource)
    }
    this.showBudgetTab(tabSource)
    this.showExpressTab(tabSource)
    let { formType } = dataSource
    // 显示凭证预览的条件：报销单和借款单的只读状态 并且有凭证预览的powercod权限
    if ((formType === 'expense' || formType === 'loan') && voucherPower) {
      this.showVoucherTab(tabSource)
    }
    this.showLoanTab(tabSource)
    this.showHabTab(tabSource)
    return (
      <section className={
        classNames(
          styles['bill-info-readonly-container'],
          this.isTableMode() ? styles['bill-info-readonly-container-table'] : styles['bill-info-readonly-container-list']
        )
      }>
        {this.isTableMode() && <BillTopOperator
          showFullScreenDrawer={showFullScreenDrawer}
          showHeaderClose={showHeaderClose}
          bus={this.bus}
          layer={this.props.layer}
          showUpDown={showUpDown}
        />}
        <div className={styles['bill-info-readonly-container-content']}>
          <div className={classNames('dis-f fd-c flex-1 ovr-y-a', styles['bill-info-readonly-container-content-details'])}>
            <RightPartHeader
              bus={this.bus}
              showHeaderClose={showHeaderClose}
              showFullScreenDrawer={showFullScreenDrawer}
              dataSource={dataSource}
              offsetWidth={offsetWidth}
              showWidget={showWidget}
              hasPopupTitle={hasPopupTitle}
              layer={this.props.layer}
              className={classNames(tabSource?.length > 1 && 'bill-info-readonly-more-tabs')}
            />
            <div
              className={classNames(
                'dis-f flex-1 ovr-y-h',
                styles['e-tabs-wrapper'],
                isNewSearchInfo && styles['new-search-bill-info'],
                tabSource?.length > 1 && styles['bill-info-readonly-more-tabs']
              )}
              ref={this.ref}
            >
              <ETabs
                type={'line'}
                activeKey={activeKey}
                onTabClick={this.handleClickTab}
                dataSource={tabSource}
                isNewSearchInfo={true}
                isHoseEUI
                tabBarStyle={{ marginBottom: 0, display: tabSource?.length > 1 ? '' : 'none' }}
              />
            </div>
            {billFooter}
          </div>
          <MoreInfo
            isActiveInfo={activeKey === 'billInfo'}
            bus={this.bus}
            billDetails={this.props.dataSource}
            showBillMore={showBillMore}
            riskData={this.props.riskData}
            userInfo={userinfo}
            staffDisplayConfigField={staffDisplayConfigField}
            isEditConfig={isEditConfig}
            showBillApproveResult={showBillApproveResult}
            versionItemAction={this._handleHistoryVersionClick}
            mode={mode}
            privilegeId={privilegeId}
            canDeleteComment={canDeleteComment}
          />
        </div>
      </section>
    )
  }
}
