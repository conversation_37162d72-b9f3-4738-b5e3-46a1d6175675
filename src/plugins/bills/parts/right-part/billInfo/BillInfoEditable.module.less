@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.e-tabs-wrapper {
  :global {
    .eui-tabs {
      overflow-y: hidden;
      .eui-tabs-content-holder {
        overflow-y: auto;
        background-color: @color-bg-2;
      }
    }
  }
}

.bill-info-editable {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  position: relative;
  background-color: var(--eui-bg-body-overlay);

  :global {
    .label-template {
      padding: 0;
      font-size: 14px;
      line-height: 1.57;
      margin-bottom: 8px;
      color: rgba(0, 0, 0, 0.65);
    }

    // .left-line{
    //   margin-right: 4px;
    //   height: 12px;
    //   width: 3px;
    //   background-color: var(--eui-primary-pri-500);
    // }

    .line {
      height: 8px;
      background: rgba(0, 0, 0, 0.04);
      margin: 0 -24px;
    }

    .template {
      display: flex;
      flex-direction: row;
      align-items: center;
      border-radius: 6px;
      padding: 10px 16px;
      font: var(--eui-font-body-r1);
      color: var(--eui-text-title);
      background-color: var(--eui-bg-body-overlay);

      .icon-my {
        width: 18px;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .template-name {
        white-space: pre-wrap;
        text-align: left;
        word-break: break-all;
        margin-right: 12px;
      }

      .template-btn {
        cursor: pointer;
      }

      span {
        font-size: 12px;
        color: var(--brand-base);
        flex-shrink: 0;
      }
    }

    .content-wrap {
      // padding: 16px;
      min-height: 100%;
    }

    .layout5-content {
      padding: 0;
      border-radius: 0;
      .ant-form-item > .ant-form-item-label,
      .ant-form-explain {
        text-align: left;
        word-break: break-all;
        white-space: pre-wrap;
      }
      .group-item {
        padding: 20px 24px;
        background-color: @color-white-1;
        margin-bottom: 8px;
        border-radius: 8px;

        &:first-child {
          margin-top: 8px;
        }
      }

      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }
}

.receiving-except-message {
  .header {
    margin-top: 17px;
    font-size: 12px;
  }

  .content {
    min-height: 30px;
    overflow: auto;
    color: var(--brand-base);
  }
}

// :global {
//   .bills-view-wrapper-list {
//     #bill-info-editable-container {
//       .content-wrap {
//         padding: 8px;
//       }
//     }
//   }
// }
