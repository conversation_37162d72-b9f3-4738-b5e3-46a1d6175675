import moment from 'moment'
import { set, get } from 'lodash'
import { TPL } from './tpl'
// 转交待确认
const isShowTransferConfirm = (item: any, id: string) => {
  return item?.transferId === id
}
// 共享待确认
const isShowShareConfirm = (item: any, id: string) => {
  return item?.notConfirmOwnerIds?.includes(id)
}
export function formatData2DSL(data: any[] = [], emptyStr: string, loanState: any, isEnableMC: any, id: string): any[] {
  const defaultValue = Array.isArray(data) ? data : []

  if (!defaultValue.length) {
    return [{ type: 'empty', text: emptyStr }]
  }

  return data.map(item => {
    // copy from tpl
    const tpl = JSON.parse(JSON.stringify(TPL))
    const standardStrCode = (item.totalMoneyNode && item.totalMoneyNode.standardStrCode) || 'CNY'
    const total = loanState === 'paid' ? item.total : item.remain

    let ti = moment(item?.repaymentDate).format('YYYY.MM.DD') + i18n.get('到期')
    if (loanState !== 'paid') {
      const rDate = moment(item?.repaymentDate).format('YYYY-MM-DD') + ' 00:00:00'
      const nDate = moment().format('YYYY-MM-DD') + ' 00:00:00'
      let day = moment(rDate).diff(moment(nDate), 'days', false)

      if (day === 0) {
        ti = `*$warning:${i18n.get('今天到期')}$*`
      } else if (day < 0) {
        day = Math.abs(day)
        ti = `*$danger:${i18n.get('已经逾期{day}', { day })}$*`
      } else if (day < 8) {
        ti = `*$warning:${i18n.get('{day}天后到期', { day })}$*`
      }
    }
    if (!moment(item?.repaymentDate).isValid()) {
      ti = `${i18n.get('无还款期限')}`
    }
    const showConfirm = isShowTransferConfirm(item, id) || isShowShareConfirm(item, id)
    let tip = ''
    if (isShowTransferConfirm(item, id)) {
      tip = `${tip} (${i18n.get('转交待确认')})`
    }
    if (isShowShareConfirm(item, id)) {
      tip = `${tip}(${i18n.get('共享待确认')})`
    }
    if (!!tip) {
      tip = `*$danger: ${tip}$*`
    }
    const title = (item?.title || '').replace(/\*/g, ' * ')
    const code = (item?.code || '')
    const loanType = item?.infoType === 'EBUSSCARD' ? `*$primary: ${i18n.get('E商卡')}$*`  : ''
    const mdText1 = `#### ${title} ${tip} ${code && `~~(${code})~~`} ${loanType} \n ${ti}`
    const mdText2 = `**${formatNumber(total)}** \n ~~(${standardStrCode})~~`
    set(tpl, 'text[0].text[0].text[0].text', mdText1)
    set(tpl, 'text[0].text[0].text[1].text', mdText2)
    if (loanState === 'paid') {
      set(tpl, 'text[0].text[1].type', 'markdown')
      set(tpl, 'text[0].text[1].text', '')
      set(tpl, 'text[0].text[1].action_id', '')
    } else {
      if (isEnableMC) {
        set(tpl, 'text[0].text[1].value', item)
        set(tpl, 'text[0].text[1].text', i18n.get('还款'))
        set(tpl, 'text[0].text[1].action_id', 'app:repayment')
      } else {
        set(tpl, 'text[0].text[1].text', i18n.get('详情'))
        set(tpl, 'text[0].text[1].value', item.id)
        set(tpl, 'text[0].text[1].action_id', 'app:open')
      }
    }
    if (showConfirm) {
      set(tpl, 'text[0].text[1].text', i18n.get('详情'))
      set(tpl, 'text[0].text[1].value', item.id)
      set(tpl, 'text[0].text[1].action_id', 'app:open')
    }
    set(tpl, 'value', item?.id)

    // 企微ISV不显示右侧详情按钮
    if (window.isInWeComISV) {
      if (get(tpl, 'text[0].text[1].text') === i18n.get('详情')) {
        set(tpl, 'text[0].text[1]', { type: 'space' })
      }
    }

    return tpl
  })
}

export function coverPlatformMap() {
  if (window.IS_SMG) {
    return [i18n.get('还没有任何预支'), i18n.get('您还没有任何预支')]
  }

  return [i18n.get('还没有任何借款'), i18n.get('您还没有任何借款')]
}

export function formatNumber(number: number | string, scale: number = 2): string {
  return Number(number).toLocaleString(undefined, {
    minimumFractionDigits: scale,
    maximumFractionDigits: scale
  })
}

const repaymentTypeMap = {
  OBTAIN: window.IS_SMG ? i18n.get('获得预支') : i18n.get('获得借款'),
  CASHIER: window.IS_SMG ? i18n.get('关闭该预支') : i18n.get('关闭该借款'),
  WRITEOFF: i18n.get('核销还款'),
  MANUAL: i18n.get('手动还款'),
  SHIFT: window.IS_SMG ? i18n.get('将预支转交给') : i18n.get('将借款转交给'),
  DELAY: i18n.get('将还款日期顺延至'),
  SHARE: i18n.get('共享给'),
  CANCEL_SHARE: i18n.get('cancel-share'),
  TRANSFER: i18n.get('发起转交给'),
  CONFIRM: i18n.get('确认接收此转交借款'),
  REFUSE: i18n.get('拒绝接收此转交借款'),
  CONFIRM_SHARE: i18n.get('确认接收此共享借款'),
  REJECT_SHARE: i18n.get('拒绝接收此共享借款'),
  CHARGEAGAINST: i18n.get('冲销手动还款'),
  REVISE:i18n.get('将借款待还款金额从'),
  AUTO:i18n.get('报销单自动还款'),
}

export enum RepaymentEnum {
  OBTAIN = repaymentTypeMap.OBTAIN,
  CASHIER = repaymentTypeMap.CASHIER,
  WRITEOFF = repaymentTypeMap.WRITEOFF,
  MANUAL = repaymentTypeMap.MANUAL,
  SHIFT = repaymentTypeMap.SHIFT,
  DELAY = repaymentTypeMap.DELAY,
  SHARE = repaymentTypeMap.SHARE,
  CANCEL_SHARE = repaymentTypeMap.CANCEL_SHARE,
  TRANSFER = repaymentTypeMap.TRANSFER,
  CONFIRM = repaymentTypeMap.CONFIRM,
  REFUSE = repaymentTypeMap.REFUSE,
  CONFIRM_SHARE = repaymentTypeMap.CONFIRM_SHARE,
  REJECT_SHARE = repaymentTypeMap.REJECT_SHARE,
  CHARGEAGAINST = repaymentTypeMap.CHARGEAGAINST,
  REVISE = repaymentTypeMap.REVISE,
  AUTO = repaymentTypeMap.AUTO,
}


export const MY_LOAN_PAGE_NAME = 'myLoan'