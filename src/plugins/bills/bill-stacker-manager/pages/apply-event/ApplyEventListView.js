import styles from './ApplyEventListView.module.less'
import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent } from 'react'
import EmptyBody from '../../../elements/EmptyBody'
import Money from '../../../../../elements/puppet/Money'
import { EnhanceConnect } from '@ekuaibao/store'
import moment from 'moment'
import SearchInput from '../../../../../elements/search-input'
import { fnFormatTrips } from '../../../util/fnFormatTrips'
import { getApplyEvent, getDetailList, getSubsidyConfig, getTravelConfirmList } from '../../../bills.action'
import { showMessage } from '@ekuaibao/show-util'
import { getDateTime, parseSubsidyData } from '../../../layers/subsidy-drawer/utils'
import { connect } from '@ekuaibao/mobx-store'
import ApplyTableView from './element/table-view'
import ViewSwitcher from '../../../../../elements/ViewSwitcher'
import { Pagination } from 'antd'
import { Button, Checkbox, Tabs } from '@hose/eui'
const pageSize = 20
@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(
  state => ({
    userInfo: state['@common'].userinfo.data,
    SubsidyManage: state['@common'].powers.SubsidyManage,
    applyList: state['@bills'].applyList || [],
    applyListClosed: state['@bills'].applyListClosed || [],
    applyEventCount: state['@bills'].applyEventCount,
    redirectToRequisition: state['@bills'].redirectToRequisition,
    baseDataProperties: state['@common'].globalFields.data
  }),
  { getApplyEvent, getDetailList, getSubsidyConfig, getTravelConfirmList }
)
export default class ApplyEventListView extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      searchValue: '',
      eventState: 'PROCESS',
      selectRowKeys: [],
      subsidyConfig: [],
      loading: false,
      viewType: localStorage.getItem(`ApplyEventViewType-${props.userInfo?.staff?.id}`) || 'list',
      pageNumber: 1
    }
  }

  redirectToRequisitionEvent = () => {
    const { applyList, params, redirectToRequisition } = this.props
    const { requisitionId } = params || {}
    if (applyList?.length && requisitionId && !redirectToRequisition) {
      const requisitionEvent = applyList.find(el => el?.id === requisitionId)
      if (requisitionEvent) {
        api.invokeService('@bills:save:requisition:redirect:status', true)
        this.handleShowDetail(requisitionEvent)
      }
    }
  }

  componentDidMount() {
    this.fetchData()
    this.fetchSubsidyConfig()
    this.redirectToRequisitionEvent()
  }

  fetchSubsidyConfig = () => {
    const { SubsidyManage } = this.props
    if (SubsidyManage) {
      getSubsidyConfig().then(res => {
        const subsidyConfig = res.items || []
        this.setState({ subsidyConfig })
      })
    }
  }

  fetchData(pageNumber = this.state.pageNumber) {
    const subsidyTravelConfig = this.getSubsidyTravelConfig()
    const reqSpecificationIds = subsidyTravelConfig.reqSpecificationIds || []
    const { getApplyEvent } = this.props
    const { eventState, searchValue } = this.state
    const limit = {
      count: pageSize,
      start: (pageNumber - 1) * pageSize
    }
    getApplyEvent(eventState, undefined, searchValue?.trim(), limit, reqSpecificationIds).then(action => {
      if (action.error) return
    })
  }

  handleShowDetail = item => {
    const { stackerManager, keel, canEditDraft } = this.props
    if (keel) {
      keel.open('ApplyEventDetail', {
        detail: item,
        canEditDraft,
        title: i18n.get('申请事项详情'),
        showButton: true
      })
    } else if (stackerManager) {
      stackerManager.push('ApplyEventDetail', {
        detail: item,
        canEditDraft,
        showButton: true
      })
    }
  }

  handleSelectItRowKeys = id => {
    const { selectRowKeys } = this.state
    if (selectRowKeys.includes(id)) {
      selectRowKeys.splice(selectRowKeys.indexOf(id), 1)
    } else {
      selectRowKeys.push(id)
    }
    this.setState({ selectRowKeys })
  }

  handleApplyMoney = async subsidyTravelConfig => {
    const { selectRowKeys } = this.state
    const { canModifyDate, confirmCity } = subsidyTravelConfig
    if (!selectRowKeys.length) {
      showMessage.warning('请选择事项!')
      return
    }
    this.setState({
      loading: true
    })
    const result = await getTravelConfirmList(selectRowKeys).catch(e => {
      this.setState({ btnLoading: false })
      showMessage.warning(e.message || e.msg)
      this.setState({
        loading: false
      })
    })
    this.setState({
      loading: false
    })
    if (result?.value?.requisitionIds) {
      const travelConfirmList = result.value
      const { startTime, endTime, data } = travelConfirmList
      let checkDate = true
      const now = getDateTime()
      if (!startTime) {
        travelConfirmList.startTime = now
      }
      if (!endTime) {
        travelConfirmList.endTime = now
      }
      if (!data.length) {
        travelConfirmList.data = [{ date: now, items: [{ city: '', dimensionItems: [], orderIds: [] }] }]
        checkDate = false
      }
      if (data.length) {
        travelConfirmList.data = data.map(dataItem => {
          dataItem.items = parseSubsidyData(dataItem.items)
          return dataItem
        })
      }
      api
        .open('@bills:SubsidyTravelDrawer', {
          travelConfirmList,
          requisitionIds: selectRowKeys,
          canModifyDate,
          confirmCity,
          checkDate,
          title: i18n.get('行程确认')
        })
        .then(res => {
          if (res.value) {
            api.store.dispatch('@layout5/activeMenu')('myBill')
          }
        })
    }
  }

  fnGetIsTravel = line => {
    const specificationName = line?.specificationName
    const components = line?.specificationId?.components ?? []
    if (/差旅|出差|商旅/.test(specificationName)) {
      return true
    }
    if (components.find(oo => oo.field === 'u_行程规划' || oo.field === 'trips' || /差旅|出差|商旅/.test(oo.field))) {
      return true
    }
  }

  handleBooking = line => {
    const { getDetailList } = this.props
    getDetailList(line.id).then(async res => {
      const list = res.payload.items
      let latestRequisition = list[0]
      list.forEach(el => {
        if (el.createTime > latestRequisition.createTime) {
          latestRequisition = el
        }
      })
      let trips = []
      if (latestRequisition?.form?.u_行程规划) {
        const components = latestRequisition?.form?.specificationId?.components
        let field = components.find(oo => oo.field === 'u_行程规划')
        const list = latestRequisition?.form?.u_行程规划 ?? []
        trips = await fnFormatTrips(field, list)
      } else if (latestRequisition?.form?.trips) {
        trips = latestRequisition?.form?.trips || []
      }
      const flightTrip = trips.find(
        el =>
          el?.tripTypeId?.endsWith(':flight') &&
          moment(el?.tripForm?.tripDate)
            .add(1, 'd')
            .isAfter(Date.now(), 'day')
      )
      const trainTrip = trips.find(
        el =>
          el?.tripTypeId?.endsWith(':train') &&
          moment(el?.tripForm?.tripDate)
            .add(1, 'd')
            .isAfter(Date.now(), 'day')
      )
      const hotelTrip = trips.find(
        el =>
          el?.tripTypeId?.endsWith(':hotel') &&
          moment(el?.tripForm?.tripDatePeriod?.end)
            .add(1, 'd')
            .isAfter(Date.now(), 'day')
      )
      api.open('@bills:TicketBookingModal', {
        trips: { flightTrip, trainTrip, hotelTrip }
      })
    })
  }

  renderTags(dataSource) {
    let { userInfo } = this.props
    let { changeLogs = [], sharedOwnerIds = [], ownerId } = dataSource
    let staffId = userInfo && userInfo.staff.id
    const specName = i18n.currentLocale === 'en-US' ? dataSource.specificationId?.enName || dataSource.specificationName : dataSource.specificationName
    let tags = [specName]
    let isTransfer = changeLogs.filter(item => item.action === 'SHIFT').length > 0
    if (ownerId.id === staffId && isTransfer) {
      tags = [...tags, i18n.get('来自转交')]
    }
    if (ownerId.id === staffId && sharedOwnerIds.length > 0) {
      tags = [...tags, i18n.get('已共享')]
    }
    if (ownerId.id !== staffId) {
      tags = [...tags, i18n.get('来自共享')]
    }
    return tags.filter(i => i).join(' | ')
  }

  fnBuildItem(item, eventState, selectRowKeys) {
    const currencySymbol = item?.requisitionMoneyNode?.standardSymbol
    const isTravel = this.fnGetIsTravel(item)
    const isTravelRole = api.getState()['@common'].mallRole?.mallRole === '0' //散客
    return (
      <div className="list-item" key={item.id}>
        {eventState === 'otherStatus' && (
          <div className="checkbox-wrapper">
            <Checkbox
              defaultChecked={selectRowKeys.includes(item.id)}
              onChange={() => this.handleSelectItRowKeys(item.id)}
            />
          </div>
        )}
        <div className="event" onClick={this.handleShowDetail.bind(this, item)}>
          <div className="top">
            <div className="name">{item.name}</div>
            {item.requisitionMoney === null ? (
              <span className="color-gray-8">{i18n.get('无金额')}</span>
            ) : (
              <Money
                currencySize={12}
                valueSize={14}
                color="var(--eui-text-title)"
                value={item.requisitionMoney}
                currencySymbol={currencySymbol}
              />
            )}
          </div>
          <div className="bottom">
            <div className="bill">{this.renderTags(item)}</div>
            <div className="label">{i18n.get('申请金额')}</div>
          </div>
          {isTravelRole && isTravel && (
            <div className="ticket">
              <div
                className="ticket-btn"
                onClick={e => {
                  e.preventDefault()
                  e.stopPropagation()
                  this.handleBooking(item)
                }}
              >
                {i18n.get('一键订票')}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  handleValueChange = e => {
    const searchText = e.target.value
    if (searchText) {
      this.setState({
        searchValue: searchText,
        pageNumber: 1
      })
    } else {
      this.setState({ searchValue: '', pageNumber: 1 }, () => {
        this.fetchData()
      })
    }
  }
  handleOnSearch = () => {
    this.fetchData()
  }

  handleChangeStatus = btnState => {
    const { selectRowKeys, eventState } = this.state
    this.setState(
      { eventState: btnState, selectRowKeys: eventState === 'otherStatus' ? selectRowKeys : [], pageNumber: 1 },
      () => {
        this.fetchData()
      }
    )
  }

  filterByPage = pageNumber => {
    this.setState({ pageNumber }, () => {
      this.fetchData()
    })
  }

  renderList = subsidyTravelConfig => {
    const { eventState, selectRowKeys, searchValue } = this.state
    const { applyList, applyListClosed } = this.props
    const reqSpecificationIds = subsidyTravelConfig.reqSpecificationIds || []
    let dataList = []
    if (eventState === 'PROCESS') {
      dataList = applyList
    } else if (eventState === 'otherStatus') {
      dataList = applyList.filter(item =>
        reqSpecificationIds.find(ii => item.flowId?.form?.specificationId?.includes(ii))
      )
    } else {
      dataList = applyListClosed
    }
    return (
      <>
        {dataList && dataList.length > 0 ? (
          <div className="body">{dataList.map(item => this.fnBuildItem(item, eventState, selectRowKeys))}</div>
        ) : (
          searchValue.trim().length > 0 ? <EmptyBody label={i18n.get(`没有找到您所要的结果`)} /> : null
        )}
      </>
    )
  }

  getSubsidyTravelConfig = () => {
    const { subsidyConfig } = this.state
    return subsidyConfig.find(item => item.subsidyType === 'TRAVEL') || {}
  }

  handleTableSelectKeys = ids => {
    this.setState({ selectRowKeys: ids })
  }

  renderTabs = () => {
    const { eventState } = this.state
    const subsidyTravelConfig = this.getSubsidyTravelConfig()

    const items = [
      {
        key: 'PROCESS',
        label: i18n.get('进行中'),
        children: this.renderContent()
      },
      {
        key: 'DONE',
        label: i18n.get('已完成'),
        children: this.renderContent()
      },
      subsidyTravelConfig.active && subsidyTravelConfig.canMulti ? {
        key: 'otherStatus',
        label: i18n.get('可申请补助'),
        children: this.renderContent(),
      } : null,
    ].filter(Boolean)

    return (
      <Tabs className={styles.tabs} items={items} onChange={this.handleChangeStatus} />
    )
  }

  renderListView = () => {
    let { searchValue, loading, pageNumber, applyEventCount } = this.state
    const subsidyTravelConfig = this.getSubsidyTravelConfig()
    return <div>
      <div style={{ padding: '8px 16px' }}>
        <SearchInput
          className={styles['search-input']}
          value={searchValue}
          placeholder={i18n.get('搜索标题、申请单号')}
          onChange={this.handleValueChange}
          onSearch={this.handleOnSearch}
        />
      </div>
      {this.renderList(subsidyTravelConfig)}
      {
        applyEventCount > 0 && (
          <div className="action-footer">
            {eventState === 'otherStatus' ? (
              <Button loading={loading} onClick={() => this.handleApplyMoney(subsidyTravelConfig)}>
                申请补助
              </Button>
            ) : (
              <div />
            )}
            <Pagination current={pageNumber} total={applyEventCount} pageSize={pageSize} onChange={this.filterByPage} />
          </div>
        )
      }
    </div>
  }

  renderTableView = () => {
    const { size, baseDataProperties } = this.props
    let { eventState } = this.state
    const subsidyTravelConfig = this.getSubsidyTravelConfig()
    return  (
      <ApplyTableView
        size={size}
        tabKey={eventState || 'done'}
        baseDataProperties={baseDataProperties}
        subsidyTravelConfig={subsidyTravelConfig}
        onApplyMoney={() => this.handleApplyMoney(subsidyTravelConfig)}
        openDetail={this.handleShowDetail}
        onSelectedChange={this.handleTableSelectKeys}
      />
    )
  }

  renderContent = () => {
    return this.state.viewType === 'list' ? this.renderListView() : this.renderTableView()
  }

  render() {
    const { userInfo } = this.props
    let { viewType } = this.state
    return (
      <div id={'applyEventListView'} className={styles['apply-list']}>
        <div className="header">
          {/* <div className="label">{i18n.get('单据状态')}:</div> */}
          {this.renderTabs()}
          <div className="view-switch">
            <ViewSwitcher
              isList={viewType === 'list'}
              handleClickList={() => {
                this.setState({ viewType: 'list', selectRowKeys: [] }, () => {
                  localStorage.setItem(`ApplyEventViewType-${userInfo?.staff?.id}`, 'list')
                })
              }}
              handleClickTable={() => {
                this.setState({ viewType: 'table', selectRowKeys: [] }, () => {
                  localStorage.setItem(`ApplyEventViewType-${userInfo?.staff?.id}`, 'table')
                })
              }}
              data-platform-wx2-hidden={window.isInWeComISV}
            />
          </div>
        </div>
      </div>
    )
  }
}
