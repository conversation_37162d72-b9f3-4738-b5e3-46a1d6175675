/**
 * Created by LinK on 2017/12/13.
 */
import { Tabs } from '@hose/eui'
import React from 'react'
import { thousandBitSeparator } from '../../../../../../components/utils/fnThousandBitSeparator'
import ListItem from '../../../../elements/ListItem'
import SVG_FEETYPE_EMPTY from '../../../../images/feeType-empty.svg'
import styles from './ApplyList.module.less'
import ApplyChangeLogs from './ApplyChangeLogs'
import RequisitionCard from './RequisitionCard'
import TripPlanning from './TripPlanning'
import OrderRelatedList from './orderRelatedList'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import ECardConsumeList from './ECardConsumeList'
const TabPane = Tabs.TabPane

export default function ApplyList(props) {
  let {
    related = [],
    budgetInfoList,
    handleShowDetail,
    inBudget,
    handleFeeDetail,
    detailList,
    standardSymbol,
    standardStrCode,
    travelTabHasShow,
    associatedOrder = false,
    orderListData,
    orderEntityMap,
    staffsActives,
    orderRelated = []
  } = props
  const { dataSource } = props
  const { changeLogs = [], id, submitterId } = dataSource
  function fnBuildItem(item = {}, idx) {
    if (item.flow && item.flow.form) {
      item.flow.form.specificationId = { name: item.specificationName }
    } else {
      item.flow = {
        code: item.code,
        formType: item.formType,
        state: item.state,
        form: {
          title: item.name,
          specificationId: { name: item.specificationName },
          submitDate: item.submitDate
        }
      }
      item.flow.form[`${item.formType}Money`] = item.money
    }
    let style = { paddingLeft: 24, paddingRight: 24 }
    return (
      <ListItem
        key={idx}
        style={style}
        onClick={_ => handleShowDetail(item.id, item.formType, item.state, item.code)}
        dataSource={item.flow}
        standardStrCode={standardStrCode}
      />
    )
  }

  function fnBuildBudgeItem(item = {}, idx, standardSymbol) {
    let { color, content, icon, occupied, total, title, budgetCurrency } = item
    occupied = new Big(occupied || 0).toFixed(2)
    total = new Big(total || 0).toFixed(2)
    icon = icon || SVG_FEETYPE_EMPTY
    const allStandardCurrency = api.getState()['@common'].allStandardCurrency
    const budgetItemCurrency = allStandardCurrency.find(v => v.id === budgetCurrency)
    const budgetSymbol = get(budgetItemCurrency, 'strCode')
    return (
      <div className="budget-item" key={idx}>
        <div className="info-part">
          <div className="icon-box" style={{ backgroundColor: color }}>
            <img src={icon} />
          </div>
          <div className="info-box">
            <span className="info-title">{title}</span>
            <span className="info-sub-title">{content}</span>
          </div>
        </div>
        <div className="amount-part">
          <div className="amount-box">
            <span className="amount">
              <span style={{ marginRight: '4px' }}>{budgetSymbol || standardSymbol || window.CURRENCY_SYMBOL}</span>
              {thousandBitSeparator(occupied)}
            </span>
            <span className="amount-text">{i18n.get('报销金额')}</span>
          </div>
          <div className="line" />
          <div className="amount-box">
            <span className="amount">
              <span style={{ marginRight: '4px' }}>{budgetSymbol || standardSymbol || window.CURRENCY_SYMBOL}</span>
              {thousandBitSeparator(total)}
            </span>
            <span className="amount-text">{i18n.get('申请金额')}</span>
          </div>
        </div>
      </div>
    )
  }

  const renderOrderList = () => {
    const submitterName = get(dataSource, 'flowId.form.submitterId.name')
    return (
      <OrderRelatedList
        orderRelated={orderRelated}
        orderListData={orderListData}
        associatedOrder={associatedOrder}
        orderEntityMap={orderEntityMap}
        staffsActives={staffsActives}
        submitterId={submitterId}
        requisitionId={id}
        submitterName={submitterName}
      />
    )
  }

  const budgetPane = inBudget ? (
    <TabPane tab={i18n.get('预算占用')} key="1">
      <div className="list-part">
        {budgetInfoList.length > 0 ? (
          <div className="list">{budgetInfoList.map((item, idx) => fnBuildBudgeItem(item, idx, standardSymbol))}</div>
        ) : (
          <div className="title">{i18n.get('该申请单未占用任何预算')}</div>
        )}
      </div>
    </TabPane>
  ) : null

  const renderECardConsumeList = () => {
    if (
      !!dataSource?.flowId?.form?.corporateExpenseCardForm?.offlineAuthorized ||
      !!dataSource?.corporateExpenseCard?.offlineAuthorized
    ) {
      return (
        <TabPane tab={i18n.get('易商卡消费')} key="eCardConsume">
          <ECardConsumeList requisitionId={dataSource?.id} />
        </TabPane>
      )
    }
    return null
  }

  return (
    <Tabs defaultActiveKey="2" className={styles['apply-list']}>
      <TabPane tab={i18n.get('关联单据')} key="2">
        <div className="list-part">
          {related && related.length > 0 ? (
            <div className="list">{related.map((item, idx) => fnBuildItem(item, idx))}</div>
          ) : (
            <div className="title">{i18n.get('该申请未被任何单据关联')}</div>
          )}
        </div>
      </TabPane>
      <TabPane tab={i18n.get('关联订单')} key="6">
        <div className="list-part">{renderOrderList()}</div>
      </TabPane>
      {renderECardConsumeList()}
      {!window.isInWeComISV && <TabPane tab={i18n.get('事项明细')} key="4">
        <div className="list-part">
          {detailList &&
            detailList.length > 0 &&
            detailList.map((item, index) => {
              return <RequisitionCard handleFeeDetail={handleFeeDetail} billInfo={item} key={index} />
            })}
        </div>
      </TabPane>}
      {travelTabHasShow && (
        <TabPane tab={i18n.get('行程规划')} key="5">
          <div className="list-part">
            <TripPlanning flowId={id} dataSource={dataSource} detailList={detailList} />
          </div>
        </TabPane>
      )}
      {budgetPane}
      <TabPane tab={i18n.get('历史记录')} key="3">
        <div className="list-part">
          {changeLogs && changeLogs.length ? <ApplyChangeLogs logs={changeLogs} /> : null}
        </div>
      </TabPane>
    </Tabs>
  )
}
