/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/21.
 */
import styles from './ExpenseLinkListView.module.less'
import React from 'react'
import EmptyBody from '../../elements/EmptyBody'
import Money from '../../../../elements/puppet/Money'
import { Input, Radio, Checkbox } from 'antd'
import { Button, Space } from '@hose/eui'
import moment from 'moment'
import { EnhanceConnect } from '@ekuaibao/store'
import { getFlowInfoById, getApplyEventById } from '../../bills.action'
import { showModal } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import { QuerySelect } from 'ekbc-query-builder'
import { isEqual, difference } from 'lodash'
import { SortSelect } from './WrittenOffListView'
import { debounce } from 'lodash'
const Search = Input.Search
const RadioGroup = Radio.Group
const CheckboxGroup = Checkbox.Group

const sortFieldMap = {
  submitDate: 'submitDate',
  requisitionMoney: 'requisitionMoney',
  requisitionDate: 'requisitionDate'
}

const sortTypeMap = {
  DESC: 'DESC',
  ASC: 'ASC'
}

const sortList = [
  {
    title: i18n.get('提交时间'),
    key: sortFieldMap.submitDate
  },
  {
    title: i18n.get('申请金额'),
    key: sortFieldMap.requisitionMoney
  },
  {
    title: i18n.get('申请日期'),
    key: sortFieldMap.requisitionDate
  }
]

function filterData(data, isLinkRequisition) {
  if (isLinkRequisition) {
    return data?.filter(item => item?.specificationId?.configs?.find(oo => oo?.linkRequisitionInfo))
  }
  return data
}

@EnhanceConnect(
  state => {
    return {
      requisitionInfo: state['@bills'].requisitionInfo,
      requisitionDataSource: filterData(state['@bills'].requisitionDataSource, state['@bills'].isLinkRequisition),
      isLinkRequisition: state['@bills'].isLinkRequisition
    }
  },
  {
    getFlowInfoById,
    getApplyEventById
  }
)
export default class ExpenseLinkListView extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      value: props.value,
      searchList: [],
      searchText: '',
      inSearch: false,
      sortType: sortTypeMap.DESC,
      sortFieldName: sortFieldMap.submitDate,
      reload: false,
      checkedValueMap: props.refValue ?? {}
    }
  }

  componentDidMount() {
    if (!this.props.isSupplement) {
      // 关联申请获取数据
      const { updateApplyFromExpense } = this.props
      updateApplyFromExpense && updateApplyFromExpense()
    }
    this.getIsLinkRequisition()
  }

  getIsLinkRequisition = async () => {
    const { isLinkRequisition } = this.props
    isLinkRequisition === null && (await api.invokeService('@bills:get:link:requisition:coprs'))
  }

  componentWillReceiveProps(nextProps) {
    this.onSort(nextProps)
  }

  componentWillUnmount() {
    if (this.props.isSupplement) {
      // 补充申请重置数据
      const { updateApplyFromExpense } = this.props
      updateApplyFromExpense && updateApplyFromExpense()
    }
  }

  handleChange = e => {
    // TODO: NQ07-关联申请查询性能优化-选中项改变
    const { getApplyEventById, requisitionInfo } = this.props
    const checkedValueMap = this.state.checkedValueMap
    const isArray = Array.isArray(e)
    if (isArray && e.length > 100) {
      return showModal.warning({ title: i18n.get('提示'), content: i18n.get('最多只能关联100个申请事项！') })
    }
    if (isArray) {
      let diff = difference(e, this.state.value)
      if (diff?.length) {
        getApplyEventById(diff[0])
      }
    } else {
      getApplyEventById(e.target.value)
    }
    const value = isArray ? e : e.target.value
    if (isArray) {
      value.forEach(id => {
        checkedValueMap[id] = checkedValueMap[id] || requisitionInfo.find(v => v.id === id)
      });
    } else {
      checkedValueMap[value] = checkedValueMap[value] || requisitionInfo.find(v => v.id === value)
    }
    this.setState({ value, checkedValueMap })
  }

  handleModalSave = () => {
    const { value, checkedValueMap } = this.state
    if (this.props.isSupplement && value) {
      api.invokeService('@bills:get:linkRequisitionControlled', value).then(resp => {
        if (!resp.value.supplement) {
          return showModal.warning({
            title: i18n.get('无法关联该申请'),
            content: i18n.get('补充申请事项次数已达上限')
          })
        }
        this.props.onOk(value)
      })
    } else {
      const selectedData = Array.isArray(value) ? Object.keys(checkedValueMap).filter(key => value.includes(key)).map(id => checkedValueMap[id]) : checkedValueMap[value]
      this.props.onOk({ id: this.state.value, selectedData })
    }
  }

  handleModalClose = () => {
    this.props.onCancel()
  }

  getDataList = props => {
    const { isSupplement, requisitionDataSource, requisitionInfo } = props
    return isSupplement ? requisitionDataSource : requisitionInfo || []
  }

  onSearch = e => {
    const { isSupplement } = this.props
    let value = e.target.value.trim()
    if (isSupplement) {
      if (!value.length) {
        this.setState({ inSearch: false, searchList: [], searchText: '', inSearchAsync: false })
        return
      }
      const dataList = this.getDataList(this.props)
      let searchList = dataList.filter(v => !!~v.code.indexOf(value) || !!~v.name.indexOf(value))
      this.setState({ inSearch: true, searchList, searchText: value })
    } else {
      this.onSearchAsync(value)
    }
  }

  onSearchAsync = debounce(async (searchValue) => {
    const { updateApplyFromExpense } = this.props
    if (!searchValue.length) {
      updateApplyFromExpense()
      this.setState({ inSearch: false, searchText: '', inSearchAsync: false })
    } else {
      let { sortFieldName, sortType } = this.state
      this.setState({ inSearch: true, inSearchAsync: true, searchText: searchValue })
      const filters = `lower(name).containsIgnoreCase(lower(${JSON.stringify(searchValue)}))||lower(code).containsIgnoreCase(lower(${JSON.stringify(searchValue)}))`
      if (sortFieldName === 'submitDate' && !this.props.isSupplement) {
        sortFieldName = 'flowId.form.submitDate'
      }
      const query = new QuerySelect().orderBy(sortFieldName, sortType).filterBy(filters).value()
      updateApplyFromExpense({ query: query })
    }
  }, 400)

  handleCheckDetail = id => {
    const { getApplyEventById } = this.props
    getApplyEventById(id).then(() => {
      const dataList = this.getDataList(this.props)
      let item = dataList.find(v => v.id === id)
      this.props.stackerManager.push('ApplyEventDetail', {
        detail: item,
        showButton: false
      })
    })
  }

  onSort = nextProps => {
    if (
      !isEqual(this.props.requisitionDataSource, nextProps.requisitionDataSource) ||
      !isEqual(this.props.requisitionInfo, nextProps.requisitionInfo)
    ) {
      this.setState({ reload: true }, () => {
        const { inSearch, searchText, inSearchAsync } = this.state
        if (inSearch && !inSearchAsync) {
          const dataList = this.getDataList(nextProps)
          let searchList = dataList.filter(v => !!~v.code.indexOf(searchText) || !!~v.name.indexOf(searchText))
          this.setState({ searchList })
        }
        this.setState({ reload: false })
      })
    }
  }

  onQueryChange = query => {
    const { updateApplyFromExpense, isSupplement } = this.props
    if (isSupplement) {
      updateApplyFromExpense && updateApplyFromExpense(query.orderBy)
    } else {
      const { searchText } = this.state
      if (searchText) {
        const filters = `lower(name).containsIgnoreCase(lower(${JSON.stringify(searchText)}))||lower(code).containsIgnoreCase(lower(${JSON.stringify(searchText)}))`
        const filterBy = new QuerySelect().filterBy(filters).value()
        query = { ...query, ...filterBy }
      }
      updateApplyFromExpense && updateApplyFromExpense({ query })
    }
  }

  handleSortFieldChange = value => {
    const { sortType } = this.state
    this.setState({ sortFieldName: value })
    if (value === 'submitDate' && !this.props.isSupplement) {
      value = 'flowId.form.submitDate'
    }
    const query = new QuerySelect().orderBy(value, sortType).value()
    this.onQueryChange(query)
  }

  handleSortTypeChange = value => {
    let { sortFieldName } = this.state
    this.setState({ sortType: value })
    if (sortFieldName === 'submitDate' && !this.props.isSupplement) {
      sortFieldName = 'flowId.form.submitDate'
    }
    const query = new QuerySelect().orderBy(sortFieldName, value).value()
    this.onQueryChange(query)
  }

  renderExpenseLinkList() {
    let { isSupplement, requisitionDataSource, isMultipleExpenseLink, requisitionInfo } = this.props
    let { searchList, inSearch, value, inSearchAsync, reload } = this.state
    let list = (inSearch && !inSearchAsync) ? searchList : isSupplement ? requisitionDataSource : requisitionInfo || []
    const [Group, GroupItem] = isMultipleExpenseLink ? [CheckboxGroup, Checkbox] : [RadioGroup, Radio]
    const sList = list.slice(0, 500)
    if (reload) return
    return sList.length ? (
      <div className="expense-list">
        <Group className={'radio-group'} onChange={this.handleChange} value={value}>
          {sList.map((el, i) => {
            return (
              <GroupItem className={'radio-content'} key={el.id} value={el.id}>
                <div className={'radio-item'} data-testid={`bills-expenseLinkItem-${el.id}`}>
                  <div className="p-part">
                    <div className="left">
                      <div className="title">{el.name}</div>
                      <div className="label">
                        {el.code}
                        &nbsp;&nbsp;&nbsp;
                        {moment(el.requisitionDate).format('YYYY/MM/DD')}
                      </div>
                    </div>
                    <div className="right">
                      {el.requisitionMoney === null ? (
                        <div className="color-gray-8">{i18n.get('无金额')}</div>
                      ) : (
                        <Money
                          currencySize={13}
                          valueSize={15}
                          color="#333333"
                          className={'money-l'}
                          value={el.requisitionMoney}
                          currencySymbol={el?.requisitionMoneyNode?.standardStrCode}
                        />
                      )}
                      <div className="label">{i18n.get('申请金额')}</div>
                    </div>
                  </div>
                  <div className="r-part" onClick={this.handleCheckDetail.bind(this, el.id)}>
                    {i18n.get('详情')}
                  </div>
                </div>
              </GroupItem>
            )
          })}
        </Group>
      </div>
    ) : (
      <EmptyBody label={inSearch ? i18n.get('没有搜到相关申请') : i18n.get('您还没有任何可关联申请')} />
    )
  }

  renderContent() {
    const { sortType, sortFieldName } = this.state
    return (
      <div className="content">
        <div className={'search-line'}>
          <Search placeholder={i18n.get('搜索标题和单号')} onChange={this.onSearch} />
        </div>
        <div className="sort-wrap">
          <SortSelect width={'182px'} data={sortList} value={sortFieldName} onChange={this.handleSortFieldChange} />
          <SortSelect
            width={'188px'}
            data={[
              {
                title: sortFieldName === sortFieldMap.requisitionMoney ? i18n.get('从大到小') : i18n.get('从近到远'),
                key: sortTypeMap.DESC
              },
              {
                title: sortFieldName === sortFieldMap.requisitionMoney ? i18n.get('从小到大') : i18n.get('从远到近'),
                key: sortTypeMap.ASC
              }
            ]}
            value={sortType}
            onChange={this.handleSortTypeChange}
          />
        </div>
        {this.renderExpenseLinkList()}
      </div>
    )
  }

  render() {
    return (
      <div className={styles['expense-link-list']}>
        {this.renderContent()}
        <div className="modal-footer">
          <Space wrap>
            <Button category="secondary" data-testid="bills-expenseLinkSelect-cancel" onClick={this.handleModalClose}>
              {i18n.get('取消')}
            </Button>
            <Button data-testid="bills-expenseLinkSelect-confirm" onClick={this.handleModalSave}>{i18n.get('确认')}</Button>
          </Space>
        </div>
      </div>
    )
  }
}
