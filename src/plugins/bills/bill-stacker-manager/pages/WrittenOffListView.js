/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/20.
 */
import React, { PureComponent } from 'react'
import { Checkbox, Popover, Alert, Select, Radio } from 'antd'
import { But<PERSON>, Tooltip } from '@hose/eui'
import styles from './WrittenOffListView.module.less'
import { EnhanceConnect } from '@ekuaibao/store'
import { getloanpackageDetailInfo, getLoanPackageList } from '../../bills.action'
import { repaymentDateTip } from '../../../../lib/lib-util'
import EmptyBody from '../../elements/EmptyBody'
import { cloneDeep, get } from 'lodash'
import EditWrittenOff from './EditWrittenOff'
import MessageCenter from '@ekuaibao/messagecenter'
import classNames from 'classnames'
import SearchInput from '../../../../elements/search-input'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { QuerySelect } from 'ekbc-query-builder'
import { showMessage } from '@ekuaibao/show-util'
import { summarySelectType } from '../../../../components/consts'
export const sortFieldMap = {
  loanDate: 'loanDate',
  repaymentDate: 'repaymentDate'
}

export const sortTypeMap = {
  DESC: 'DESC',
  ASC: 'ASC'
}

const sortList = [
  {
    title: '借款时间',
    key: sortFieldMap.loanDate
  },
  {
    title: '还款时间',
    key: sortFieldMap.repaymentDate
  }
]

@EnhanceConnect(
  state => ({
    MULTICURRENCYWRITTENOFF: state['@common'].powers.MULTICURRENCYWRITTENOFF, //多币种核销charge
    standardCurrency: state['@common'].standardCurrency,
    CROSSCURRENCYWRITTENOFF: state['@common'].powers.CROSSCURRENCYWRITTENOFF
  }),
  { getloanpackageDetailInfo, getLoanPackageList },
  '@bills/checkedList'
)
export default class WrittenOffListView extends PureComponent {
  static defaultProps = {
    isRefresh: true, //控制是否需要刷新状态
    writtenOffItems: [], //用户已选择核销记录
    loanPackageList: [] //可核销的借款包
  }

  constructor(props) {
    super(props)
    this.state = {
      checkedList: [],
      loading: false,
      searchList: [],
      sortType: sortTypeMap.ASC,
      sortFieldName: sortFieldMap.loanDate
    }
    props.isRefresh && this.fnSetInitState(props)
    this.bus = new MessageCenter()
    this.onEditMoney = this.onEditMoney.bind(this)
  }

  componentDidMount() {
    this.bus.on('edit:writtenoff:money', this.onEditMoney)
  }

  componentWillUnmount() {
    this.bus.un('edit:writtenoff:money', this.onEditMoney)
  }

  onEditMoney({ amount }, id) {
    let { checkedList, loanPackageList: loanList } = this.props.state
    if (!loanList) {
      const { loanPackageList } = this.props
      loanList = this.props.loanPackageList = cloneDeep(loanPackageList)
    }

    const loan = loanList.find(line => line.id === id)
    if (loan) {
      loan.manualAmount = amount
    }

    let index = checkedList.findIndex(line => line.id === id)
    if (index <= -1) {
      parseFloat(amount) > 0 && checkedList.push(this.fnFormatRecord(loan))
    } else {
      if (parseFloat(amount) <= 0) {
        checkedList.splice(index, 1)
      } else {
        checkedList.forEach(v => {
          if (v.id === id) {
            v.manualAmount = amount
          }
        })
      }
    }
    this.forceUpdate()
  }

  fnSetInitState = (props, showList) => {
    let { checkedValues = [], loanPackageList = [], MULTICURRENCYWRITTENOFF, writtenOffAbility } = props
    const canWrittenOffForeignCurrency = get(writtenOffAbility, 'foreignCurrency')

    checkedValues.forEach(line => {
      if (!line.manualAmount) {
        if (canWrittenOffForeignCurrency && (MULTICURRENCYWRITTENOFF || writtenOffAbility?.crossCurrencyWrittenOff)) {
          const { maxWTAmount, foreignCurrencyLoan, loanInfoId } = line
          if (foreignCurrencyLoan) {
            line.manualAmount = loanInfoId.foreignRemain
          }
        } else {
          // line.manualAmount = line.manualAmount
        }
      }
    })
    let copyList = (showList || loanPackageList).map(v => {
      v.maxWTAmount = new Big(v.remain).toFixed(2)
      const checkedItem = this.getCheckedLoan(checkedValues, v.id)
      if (checkedItem) {
        v.amount = checkedItem.amount
        v.manualAmount = checkedItem.manualAmount
      }

      return v
    })

    //转交走得借款处理
    const loanListMap = {}
      ; (showList || loanPackageList).forEach(o => {
        const id = this.fnGetLoanInfoId(o)
        loanListMap[id] = o
      })
    const checkedList = checkedValues.filter(checkedLoan => {
      const id = this.fnGetLoanInfoId(checkedLoan)
      return !!loanListMap[id]
    })

    // 开启多币种核销情况
    this.filterLoanPackage(copyList)
    this.props.setState({ checkedList: checkedList, loanPackageList: copyList })
  }

  filterLoanPackage = (items = []) => {
    const { billData, MULTICURRENCYWRITTENOFF, writtenOffAbility, receiptAbility = {} } = this.props
    const { details = [] } = billData
    const { feeDetailNotRequired, summarySelect } = receiptAbility
    //开启多币种核销并且没有明细
    if (MULTICURRENCYWRITTENOFF && !details.length && !writtenOffAbility.crossCurrencyWrittenOff) {
      //不允许选
      // 未开启费用明细非必填
      if (!feeDetailNotRequired) {
        items.forEach(item => (item.disabled = true))
      }
      // 未开启多币种核销
      if (!writtenOffAbility?.foreignCurrency) {
        items.forEach(item => {
          if (item.foreignCurrencyLoan) {
            item.disabled = true
          }
        })
      }
      //有明细
    } else if (MULTICURRENCYWRITTENOFF && billData?.details?.length && !writtenOffAbility.crossCurrencyWrittenOff) {
      //取明细外币或本位币
      const strCodes = billData.details.map(item => {
        const amount = getV(item, 'feeTypeForm.amount', {})
        if (amount.foreignStrCode) {
          return amount.foreignStrCode
        } else {
          return amount.standardStrCode
        }
      })
      //允许核销相同币种外币
      if (writtenOffAbility?.foreignCurrency) {
        //禁止外币核销本位币
        if (writtenOffAbility?.sameCurrencyWrittenOff) {
          items.forEach(item => {
            const foreignStrCode = getV(item, 'foreignCurrencyLoan.foreignStrCode', window.CURRENCY_STRCODE)
            const isEstablished = foreignStrCode && !strCodes.includes(foreignStrCode)
            if (isEstablished) {
              if (summarySelect !== summarySelectType.repay) {
                item.disabled = true
              }
            }
          })
        }
      } else {
        items.forEach(item => {
          const loanMoney = getV(item, 'foreignCurrencyLoan', {})
          const foreignStrCode = getV(item, 'foreignCurrencyLoan.foreignStrCode', '')
          const standardStrCode = getV(item, 'foreignCurrencyLoan.standardStrCode', '')
          //模板没勾选外币核销外币 禁用外币包
          if (!writtenOffAbility?.foreignCurrency && (foreignStrCode || standardStrCode)) {
            item.disabled = true
          }
          //勾选配置 并且是外币包 并且不是相同币种
          else if (
            writtenOffAbility?.foreignCurrency &&
            loanMoney &&
            (foreignStrCode || standardStrCode) &&
            !strCodes.includes(foreignStrCode || standardStrCode)
          ) {
            if (summarySelect !== summarySelectType.repay) {
              item.disabled = true
            }
          }
        })
      }
    }
  }

  getCheckedLoan(checkedValues, id) {
    return checkedValues.find(line => line.id === id)
  }

  fnFormatRecord(item) {
    return {
      id: item.id,
      loanInfoId: item,
      maxWTAmount: item.maxWTAmount,
      manualAmount: item.manualAmount,
      title: item.title,
      repaymentDate: item.repaymentDate,
      fromApply: item.repaymentRecords && item.repaymentRecords[0].formType === 'requisition',
      flowId: item.flowId
    }
  }

  fnGetLoanInfoId = loanInfo => {
    if (loanInfo.loanInfoId) {
      return typeof loanInfo.loanInfoId === 'string' ? loanInfo.loanInfoId : loanInfo.loanInfoId.id
    }
    return loanInfo.id
  }

  handleClick(record, e) {
    let { flowId } = this.props
    let param = flowId ? { flowId, id: record.id } : { id: record.id }
    this.props.getloanpackageDetailInfo(param).then(response => {
      if (response.error) return
      let data = response.payload
      this.props.stackerManager.push('LoanPackageDetailView', {
        loanDetail: data.value
      })
    })
    e.preventDefault()
  }

  handleModalSave() {
    let { onOk, state, isReceiptTemplate, receiptAbility } = this.props
    let result = state.checkedList || []
    const hasForeign = result.find(i => i.loanInfoId.foreignCurrencyLoan)
    if (isReceiptTemplate && receiptAbility.summarySelect === summarySelectType.repay && hasForeign) {
      result = result.map(item => {
        const {
          manualAmount,
          loanInfoId: { foreignCurrencyLoan, foreignRemain }
        } = item
        delete item.foreignAmount
        if (!manualAmount) {
          item.foreignAmount = foreignRemain
        }
        return item
      })
    }
    onOk && onOk(result)
  }

  // 单选的 change 方法
  handleRadioLineChage(item, e) {
    let checkedList = []
    if (e.target.checked) {
      checkedList.push(this.fnFormatRecord(item))
    }
    this.props.setState({ checkedList })
  }

  handleLineChange(item, e) {
    const { isReceiptTemplate } = this.props
    let checkedList = cloneDeep(this.props.state.checkedList)
    const currForeign = item.foreignCurrencyLoan?.foreignNumCode
    const listForeign = checkedList.find(i => i.loanInfoId.foreignCurrencyLoan)?.loanInfoId?.foreignCurrencyLoan
      ?.foreignNumCode
    if (checkedList.length && isReceiptTemplate) {
      if (listForeign !== currForeign) {
        showMessage.error('请选择相同币种借款包')
        return
      }
    }
    if (e.target.checked) {
      checkedList.push(this.fnFormatRecord(item))
    } else {
      let idx = checkedList.findIndex(o => o.loanInfoId.id === item.id)
      checkedList.splice(idx, 1)
    }
    this.props.setState({ checkedList })
  }

  renderMyLoanListRepeat(checkedList, loanPackageList) {
    const { MULTICURRENCYWRITTENOFF, initValue = [], isModify, writtenOffAbility, CROSSCURRENCYWRITTENOFF } = this.props
    const { searchList, searchText } = this.state
    const showList = searchList.length || searchText ? searchList : loanPackageList
    return (
      <ul>
        {showList.length ? (
          showList.map((el, i) => {
            let checkedElement = checkedList.find(o => {
              let id = this.fnGetLoanInfoId(o)
              return id === el.id
            })
            let isDisabled = el.disabled || el.writtenOffRemainTimes === 0 || !(el.maxWTAmount * 1)
            if (isModify && el.writtenOffRemainTimes === 0 && !!initValue.find(o => o.id === el.id)) {
              isDisabled = false
            }
            const standardStrCode = el?.totalMoneyNode?.standardStrCode
            return (
              <li key={i}>
                <div data-testid={`bills-writtenOffItem-${el.id}`} className={classNames('repeat', { 'disabled-item': false })}>
                  <WrittenOffItem
                    bus={this.bus}
                    data={el}
                    standardCurrency={this.props.standardCurrency}
                    standardStrCode={standardStrCode}
                    checked={!!checkedElement}
                    isDisabled={isDisabled}
                    chargeMultCurrencyWrittenOffIsOpened={MULTICURRENCYWRITTENOFF || writtenOffAbility.crossCurrencyWrittenOff} //多币种核销功能charge
                    handleLineChange={this.handleLineChange.bind(this, el)}
                    onDetail={this.handleClick.bind(this, el)}
                    handleRadioLineChage={this.handleRadioLineChage.bind(this, el)}
                    isReceiptTemplate={this.props.isReceiptTemplate}
                    writtenOffAbility={writtenOffAbility}
                  />
                </div>
              </li>
            )
          })
        ) : (
          <EmptyBody style={{ height: 'calc(100% - 112px)' }} label={i18n.get('没有搜索到借款')} />
        )}
      </ul>
    )
  }

  handleSearch = value => {
    const searchText = value ? value.target.value.trim() : ''
    this.setState({ searchText: searchText })
    if (!value || !value.target || !value.target.value) {
      // 搜索清空
      this.setState({ searchList: [] })
      return
    }
    const { loanPackageList } = this.props.state
    const newList = loanPackageList.filter(loan => !!~loan?.title.indexOf(searchText))
    this.setState({ searchList: newList })
  }

  renderTip = () => {
    const { writtenOffAbility = {}, MULTICURRENCYWRITTENOFF } = this.props
    if (!MULTICURRENCYWRITTENOFF || writtenOffAbility?.crossCurrencyWrittenOff) return null
    const tipsMsg = writtenOffAbility?.foreignCurrency
      ? i18n.get('此报销单模板仅允许核销相同币种的借款包，请先添加费用明细')
      : i18n.get('此报销单模板仅允许核销本位币借款包，请先添加费用明细')
    return (
      <div style={{ background: '#FFF' }}>
        <Alert style={{ margin: '0 auto', width: '50%' }} message={tipsMsg} type="info" showIcon />
      </div>
    )
  }

  onQueryChange = query => {
    const { searchText } = this.state
    const { flowId, billData, isReceiptTemplate, getLoanPackageList } = this.props
    if (isReceiptTemplate) {
      //如果是收款单
      let param = {
        state: 'REPAID',
        orderBy: query.orderBy || {}
      }
      getLoanPackageList(param).then(res => {
        let newList = res.payload.items
        if (searchText) {
          // 存在搜索条件
          newList = res.payload.items.filter(loan => !!~loan?.title.indexOf(searchText))
          this.setState({ searchList: newList })
        }
        this.fnSetInitState(this.props, res.payload.items)
      })
    } else {
      api.invokeService('@bills:get:bill:loan:list', { orderBy: query.orderBy || {}, flowId, billData }).then(res => {
        let newList = res.items
        if (searchText) {
          // 存在搜索条件
          newList = res.items.filter(loan => !!~loan?.title.indexOf(searchText))
          this.setState({ searchList: newList })
        }
        this.fnSetInitState(this.props, res.items)
      })
    }
  }

  handleSortFieldChange = value => {
    const { sortType } = this.state
    const query = new QuerySelect().orderBy(value, sortType).value()
    this.setState({ sortFieldName: value })
    this.onQueryChange(query)
  }

  handleSortTypeChange = value => {
    const { sortFieldName } = this.state
    const query = new QuerySelect().orderBy(sortFieldName, value).value()
    this.setState({ sortType: value })
    this.onQueryChange(query)
  }

  render() {
    let { state, receiptAbility } = this.props
    let { checkedList = [], loanPackageList = [] } = state
    const { loading, sortType, sortFieldName } = this.state
    return (
      <div id={'WrittenOffListView'} className={styles['writtenoff-list-content']}>
        {!receiptAbility && this.renderTip()}
        <div className="writtenoff-list">
          <div style={{ padding: '8px 16px' }}>
            <SearchInput placeholder={i18n.get('搜索标题')} onChange={this.handleSearch} />
          </div>
          <div className="sort-wrap">
            <SortSelect width={'182px'} data={sortList} value={sortFieldName} onChange={this.handleSortFieldChange} />
            <SortSelect
              width={'188px'}
              data={[
                { title: i18n.get('从近到远'), key: sortTypeMap.DESC },
                { title: i18n.get('从远到近'), key: sortTypeMap.ASC }
              ]}
              value={sortType}
              onChange={this.handleSortTypeChange}
            />
          </div>
          {loanPackageList.length ? (
            this.renderMyLoanListRepeat(checkedList, loanPackageList)
          ) : !loading ? (
            <EmptyBody style={{ height: 'calc(100% - 112px)' }} label={i18n.get('您目前没有借款')} />
          ) : null}
        </div>
        <div className="footer-writtenOff">
          <Button data-testid="bills-writtenOff-confirm" onClick={this.handleModalSave.bind(this)}>
            {i18n.get('确认')}({checkedList.length})
          </Button>
        </div>
      </div>
    )
  }
}

class WrittenOffItem extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = { popOverShow: false }
  }

  onClose = (popOverShow = false) => {
    this.setState({ popOverShow })
  }

  renderWrittenOffRangeText = () => {
    //多币种核销功能charge
    const {
      data,
      standardCurrency,
      chargeMultCurrencyWrittenOffIsOpened,
      standardStrCode,
      isReceiptTemplate,
      writtenOffAbility
    } = this.props
    const { maxWTAmount, foreignCurrencyLoan, foreignRemain } = data
    // 外币借款包展示原币
    if (chargeMultCurrencyWrittenOffIsOpened && foreignCurrencyLoan) {
      const { foreignStrCode, foreignScale } = foreignCurrencyLoan
      const foreignAmount = Number(foreignRemain).toFixed(foreignScale)
      const writtenOffText = `${foreignStrCode} ${foreignAmount}`
      return (
        <>
          <div className="txt" key="1">
            {isReceiptTemplate ? i18n.get('应还款') : i18n.get('可核销')}
          </div>
          <div className="money" key="2">
            {writtenOffText}
          </div>
        </>
      )
    }

    // 原逻辑
    return (
      <>
        <div className="txt" key="1">
          {isReceiptTemplate ? i18n.get('应还款') : i18n.get('可核销')}
        </div>
        <div className="money" key="2">
          {standardStrCode || standardCurrency.strCode} {maxWTAmount}
        </div>
      </>
    )
  }

  renderManualMoney = () => {
    const { data, standardCurrency, chargeMultCurrencyWrittenOffIsOpened, standardStrCode } = this.props
    let { maxWTAmount, manualAmount, foreignCurrencyLoan, foreignRemain } = data
    // 外币借款包，展示原币  todo
    if (chargeMultCurrencyWrittenOffIsOpened && foreignCurrencyLoan) {
      const { foreignStrCode, foreignScale } = foreignCurrencyLoan
      // const foreignAmount = Number(foreignRemain).toFixed(foreignScale)
      if (manualAmount && manualAmount * 1 < foreignRemain * 1) {
        return `${foreignStrCode} ${manualAmount}`
      } else {
        const foreignAmount = Number(foreignRemain).toFixed(foreignScale)
        return `${foreignStrCode} ${foreignAmount}`
      }
    }

    const strCode = standardStrCode || standardCurrency.strCode
    // 原逻辑
    if (manualAmount && manualAmount * 1 < maxWTAmount * 1) {
      return `${strCode} ${manualAmount}`
    }
    return `${strCode} ${maxWTAmount}`
  }

  renderWrittenOffRemainTimes = () => {
    const {
      data: { writtenOffRemainTimes }
    } = this.props
    if (typeof writtenOffRemainTimes === 'number' && !isNaN(writtenOffRemainTimes)) {
      return (
        <>
          <div className="separation" key="4">
            {' '}
            |{' '}
          </div>
          <div>{i18n.get('可核销次数 {__k0}', { __k0: writtenOffRemainTimes })}</div>
        </>
      )
    }
    return null
  }

  render() {
    const {
      data,
      checked,
      handleLineChange,
      handleRadioLineChage,
      onDetail,
      bus,
      isDisabled,
      isReceiptTemplate,
      writtenOffAbility
    } = this.props
    let { maxWTAmount, repaymentDate, state, title, id, foreignCurrencyLoan, infoType } = data
    const { popOverShow } = this.state
    const disabled = isDisabled
    const editMoneyCls = classNames('edit-money', { disabled })
    return (
      <>
        <div className="left" key="1">
          {writtenOffAbility?.limitOnlyOneSubmitExpense ? (
            <Radio
              checked={checked}
              disabled={disabled}
              onClick={data.disabled ? null : handleRadioLineChage}
              className="check-button"
            >
              <Tooltip title={title}>
                <div className="title" key="1" onClick={onDetail}>
                  {title}
                </div>
              </Tooltip>
              <div className="time-money fs-12 color-gray-9c" key="2">
                {data?.code ? (
                  <>
                    <div>{data?.code}</div>
                    <div className="separation"> | </div>
                  </>
                ) : null}
                {repaymentDate && [
                  repaymentDateTip(repaymentDate, state),
                  <div className="separation" key="4">
                    |
                  </div>
                ]}
                {infoType === 'EBUSSCARD' && (
                  <>
                    <div className="code">E商卡</div>
                    <div className="separation"> | </div>
                  </>
                )}
                {this.renderWrittenOffRangeText()}
                {this.renderWrittenOffRemainTimes()}
              </div>
            </Radio>
          ) : (
            <Checkbox
              checked={checked}
              disabled={disabled}
              onChange={data.disabled ? null : handleLineChange}
              className="check-button"
            >
              <Tooltip title={title}>
                <div className="title" key="1" onClick={onDetail}>
                  {title}
                </div>
              </Tooltip>
              <div className="time-money fs-12 color-gray-9c" key="2">
                {data?.code ? (
                  <>
                    <div>{data?.code}</div>
                    <div className="separation"> | </div>
                  </>
                ) : null}
                {repaymentDate && [
                  repaymentDateTip(repaymentDate, state),
                  <div className="separation" key="4">
                    |
                  </div>
                ]}
                {infoType === 'EBUSSCARD' && (
                  <>
                    <div className="code">E商卡</div>
                    <div className="separation"> | </div>
                  </>
                )}
                {this.renderWrittenOffRangeText()}
                {this.renderWrittenOffRemainTimes()}
              </div>
            </Checkbox>
          )}
        </div>
        <div className="right" key="2">
          <div className="money" key="1">
            {this.renderManualMoney()}
          </div>
          <Popover
            visible={popOverShow && !disabled}
            content={
              <EditWrittenOff
                bus={bus}
                id={id}
                foreignCurrencyLoan={foreignCurrencyLoan}
                onClose={this.onClose}
                isReceiptTemplate={isReceiptTemplate}
                originalTotal={data.remain}
                foreignRemain={data.foreignRemain}
              />
            }
            title=""
            trigger="click"
            placement="bottomRight"
            onVisibleChange={this.onClose}
          >
            {!data.disabled && (
              <div className={editMoneyCls} onClick={this.onClose.bind(this, true)} key="3">
                {i18n.get('修改金额')}
              </div>
            )}
          </Popover>
          <div className={classNames('written-off', { 'written-off-alwayls': data.disabled })} key="4">
            {isReceiptTemplate ? i18n.get('还款金额') : i18n.get('本次核销')}
          </div>
        </div>
      </>
    )
  }
}

export class SortSelect extends React.PureComponent {
  render() {
    const { data = [], width, value, onChange } = this.props
    return (
      <Select style={{ width }} size={'large'} value={value} onChange={onChange}>
        {data.map(item => {
          return (
            <Select.Option key={item.key} value={item.key}>
              {i18n.get(item.title)}
            </Select.Option>
          )
        })}
      </Select>
    )
  }
}
