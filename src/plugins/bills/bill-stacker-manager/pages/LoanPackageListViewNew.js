import styles from './LoanPackageListViewNew.module.less'
import React from 'react'
import { app } from '@ekuaibao/whispered'
import { connect } from '@ekuaibao/mobx-store'
import EmptyBody from '../../elements/EmptyBody'
import { Pagination } from 'antd'
import { EnhanceConnect } from '@ekuaibao/store'
import {
  getLoanPackageList,
  getloanpackageDetailInfo,
  getRepayInfo,
  getRepayApplyByStatus,
  saveTempRepayInfo,
  searchLoanByOwnerIdAndState,
  applyRepayment,
  getLoanChart
} from '../../bills.action'
import { debounce, cloneDeep } from 'lodash'
import { getV } from '@ekuaibao/lib/lib/help'
import LoanList from './loan-package/list'
import { coverPlatformMap as CMP } from './loan-package/helper'
import Chart from './loan-package/chart'
import { Input, message } from "@hose/eui"
import iconSearch from '../../../../images/icon-search.svg'
import { observer } from 'mobx-react'
import { inject } from '@ekuaibao/react-ioc'
import ViewSwitcher from '../../../../elements/ViewSwitcher'
import TableView from './loan-package/table-view'
import { Tabs } from '@hose/eui'
import classNames from 'classnames'
const PAGE_SIZE = 10

@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(
  state => {
    return {
      loanPackage: state['@bills'].loanPackage,
      legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
      userInfo: state['@common'].userinfo.data,
      baseDataProperties: state['@common'].globalFields.data
    }
  },
  {
    getLoanPackageList,
    getloanpackageDetailInfo,
    getRepayInfo,
    getRepayApplyByStatus,
    saveTempRepayInfo,
    applyRepayment
  }
)
@observer
export default class LoanPackageList extends React.Component {
  @inject('permission') permission
  searchValue = ''
  constructor(props) {
    super(props)
    this.state = {
      loanList: [],
      total: 10,
      currentPage: 1,
      showLoading: false,
      count: {},
      viewType: localStorage.getItem(`LoanViewType-${this.props.userInfo?.staff?.id}`) || 'list',
      loanState: 'unpaid'
    }
    this.orignData = []
    const hash = getV(location, 'hash', '')
    this.isManagePage =
      hash.includes('requisition-manage') || hash.includes('loan-manage') || hash.includes('expense-manage')
  }

  componentDidMount() {
    this.getList()
  }
  getList = () => {
    this.getDataSource(this.props)
    getLoanChart().then(res => {
      this.setState({
        count: res?.value
      })
    })
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.loanState !== nextProps.loanState) {
      this.getDataSource(nextProps)
    }
  }

  getDataSource(props) {
    let { loanState, loanList } = props

    if (loanState === 'paid') {
      const { currentPage } = this.state
      this.getMyLoanList({
        state: 'PAID',
        start: (currentPage - 1) * 10,
        count: PAGE_SIZE
      })
    } else {
      if (loanList && typeof loanList === 'object') {
        this.setState({
          loanList
        })
        this.orignData = cloneDeep(loanList)
        return
      }
      this.getMyLoanList()
    }
  }

  getMyLoanList(param = {}) {
    let { getLoanPackageList, getRepayApplyByStatus } = this.props
    this.setState({ showLoading: true, loanList: [] })
    getLoanPackageList &&
      getLoanPackageList(param).then(action => {
        if (action.error) return
        let loanPackage = action.payload || {}
        let loanList = loanPackage.items || []
        if (param.state === 'PAID') {
          this.setState({
            loanList,
            total: loanPackage.count,
            showLoading: false
          })
          this.orignData = cloneDeep(loanList)
        } else {
          getRepayApplyByStatus({ state: 'REJECT' }).then(actionReject => {
            if (actionReject.error) return
            let data = actionReject.payload || {}
            let rejectList = data.items
            loanList.forEach(o => {
              if (!!~rejectList.findIndex(ob => ob.loanInfoId == o.id)) {
                o.repayStatusData = 'REJECT'
              }
            })
            this.setState({
              loanList,
              total: loanPackage.count,
              showLoading: false
            })
            this.orignData = cloneDeep(loanList)
          })
        }
      })
  }

  handleSelectMyLoan = id => {
    // 隐藏借款内容点击效果
    if (window.isInWeComISV) {
      return
    }

    app.invokeService('@layout5:show:mask')
    let {
      getloanpackageDetailInfo,
      getRepayInfo,
      loanState,
      hiddenButton,
      flowId,
      keel,
      stackerManager,
      showAllFeeTypeInManage
    } = this.props
    const params = flowId ? { id, flowId } : { id }
    getloanpackageDetailInfo &&
      getloanpackageDetailInfo(params)
        .then(action => {
          if (!action.error) {
            let loanDetail = action.payload && action.payload.value
            getRepayInfo({ loanInfoId: id }).then(action => {
              if (action.error) return
              let data = action.payload
              const params = {
                showAllFeeTypeInManage,
                loanDetail,
                repayInfo: data.items[0],
                showRepaymentButton: loanState !== 'paid' && !hiddenButton,
                flowId
              }
              if (keel) {
                keel.open('LoanPackageDetailView', params).then(() => {
                  this.getList()
                })
              } else if (stackerManager) {
                stackerManager.push('LoanPackageDetailView', params)
              }
              app.invokeService('@layout5:hide:mask')
            })
          }
        })
        .catch(_ => app.invokeService('@layout5:hide:mask'))
  }

  handleFinishedLoan = () => {
    const { keel, stackerManager } = this.props
    if (keel) {
      keel.open('LoanPackageListView', {
        loanState: 'paid',
        title: window.IS_SMG ? i18n.get('已还清预支') : i18n.get('已还清借款')
      })
    } else if (stackerManager) {
      stackerManager.push('LoanPackageListView', {
        loanState: 'paid',
        title: window.IS_SMG ? i18n.get('已还清预支') : i18n.get('已还清借款')
      })
    }
    this.searchValue = ''
  }
  handleClose = () => {
    const { keel } = this.props
    if (keel) {
      keel.closeTo(0)
    }
    this.searchValue = ''
  }
  handlePageChange = page => {
    this.setState(
      {
        currentPage: page
      },
      () => {
        this.handleSearch(this.searchValue)
      }
    )
  }
  renderPagination = () => {
    let { loanList, currentPage, total } = this.state
    if (loanList.length) {
      return (
        <div className="pagination-wrap">
          <Pagination
            className="pagination-style"
            current={currentPage}
            onChange={this.handlePageChange}
            total={total}
          />
        </div>
      )
    }
    return ''
  }
  handleSearch = value => {
    this.searchValue = value?.target?.value

    const fn = debounce(() => {
      const filters = value
        ? `(title.containsIgnoreCase("${this.searchValue?.trim()}")||flowId.form.code.containsIgnoreCase("${this.searchValue?.trim()}"))`
        : undefined
      const { loanState } = this.props
      let { currentPage = 0 } = this.state
      let param = { filters }
      if (loanState === 'paid') {
        param.start = (currentPage - 1) * 10
        param.count = 10
        param.state = 'PAID'
      }
      if (!value) {
        this.getDataSource(this.props)
      } else {
        const { owner, flowId } = this.props
        if (this.isManagePage) {
          this.searchLoanLocalList(value)
        } else if (owner && flowId) {
          this.searchLoanByOwnerIdAndState(param)
        } else {
          this.getMyLoanList(param)
        }
      }
    }, 200)
    fn()
  }
  searchLoanLocalList = value => {
    const list = this.orignData.slice().filter(line => line.title.indexOf(value) >= 0)
    this.setState({
      loanList: list
    })
  }
  searchLoanByOwnerIdAndState = param => {
    const { owner, flowId } = this.props
    searchLoanByOwnerIdAndState(param, owner.id, flowId).then(result => {
      if (result.error) return
      let loanList = result.items || []
      this.setState({
        loanList,
        total: result.count,
        showLoading: false
      })
    })
  }

  repayment = el => {
    let { getloanpackageDetailInfo, getRepayInfo, loanState, hiddenButton, flowId, showAllFeeTypeInManage } = this.props
    const params = flowId ? { id: el.id, flowId } : { id: el.id }
    getloanpackageDetailInfo &&
      getloanpackageDetailInfo(params)
        .then(action => {
          if (!action.error) {
            let loanDetail = action.payload && action.payload.value
            const configs = loanDetail?.flowSpecificationId?.configs
            if (
              configs &&
              configs.length &&
              configs.some(config => config.ability === 'manualRepayment' && config.allowManualRepayment)
            ) {
              message.warning(i18n.get('此单据不允许手动还款'))
              return
            }
            getRepayInfo({ loanInfoId: el.id }).then(async action => {
              if (action.error) return
              let data = action.payload

              const params = {
                showAllFeeTypeInManage,
                loanDetail,
                repayInfo: data.items[0],
                showRepaymentButton: loanState !== 'paid' && !hiddenButton
              }
              let btnDisabled =
                !params?.loanDetail?.remain || (params?.repayInfo && params?.repayInfo?.state === 'APPROVE')

              if (params.showRepaymentButton && !btnDisabled) {
                await this.handleRepaymentButton(params)
              } else {
                message.info(i18n.get('已有还款在审核中'))
              }
            })
          }
        })
        .catch()
  }

  handleRepaymentButton = async data => {
    const { loanDetail } = data
    const { legalEntityCurrencyPower, applyRepayment } = this.props
    const params = loanDetail.foreignCurrencyLoan
      ? { ...loanDetail, currencySelAble: false, selectCurrencyDisable: true, isManualRepayment: true }
      : { ...loanDetail }
    if (loanDetail.foreignCurrencyLoan) {
      params.foreignCurrencyLoan.standard = null
    }
    params.EBUSSCARD = loanDetail.infoType === 'EBUSSCARD'
    params.applyRepayment = applyRepayment
    params.loanInfoId = loanDetail.id
    if (legalEntityCurrencyPower && loanDetail?.totalMoneyNode?.standardNumCode) {
      const { value: currency } = await app.invokeService('@currency-manage:get:currency:info:by:id', {
        id: loanDetail.totalMoneyNode.standardNumCode
      })
      const { items: rates } = await app.invokeService(
        '@currency-manage:get:currency:rates:by:Id',
        loanDetail.totalMoneyNode.standardNumCode
      )
      const dimentionCurrency = { rates, currency }
      app.invokeService('@bills:update:dimention:currency', dimentionCurrency)
    }
    setTimeout(() => {
      app.open('@bills:RepaymentModal', { ...params }).then(resp => {
        if (resp) {
          this.getList()
        }
      })
    }, 300)
  }

  renderMyLoanListRepeat() {
    let {
      ownerName,
      loanState,
      userInfo: {
        staff: { id }
      },
      hiddenButton
    } = this.props

    const { loanList } = this.state
    const emptyStr = ownerName ? CMP?.()[0] : CMP?.()[1]
    const isEnableMC = !this.permission?.isMCDisabled()
    return loanList.length ? (
      <div className="data-list-con">
        <LoanList
          list={loanList}
          emptyStr={emptyStr}
          loanState={loanState}
          actions={{
            'app:open': this.handleSelectMyLoan,
            'app:repayment': this.repayment
          }}
          isEnableMC={isEnableMC && !hiddenButton}
          id={id}
        />
      </div>
    ) : (
      <div className="data-list-con">
        <EmptyBody
          label={
            ownerName
              ? window.IS_SMG
                ? i18n.get('还没有任何预支')
                : i18n.get('还没有任何借款', { name: ownerName })
              : window.IS_SMG
                ? i18n.get('您还没有任何预支')
                : i18n.get('您还没有任何借款')
          }
        />
      </div>
    )
  }

  get showChart() {
    const { hiddenButton } = this.props
    const { count } = this.state
    const legalEntityCurrencyPower = app.getState()['@common'].powers.legalEntityCurrency
    return !legalEntityCurrencyPower && !!count?.total && !hiddenButton
  }

  renderContent() {
    const { loanState, size, baseDataProperties, hideTable, name } = this.props
    const { showLoading, count, loanList, viewType } = this.state
    const standardStrCode = loanList?.length > 0 ? loanList[0]?.totalMoneyNode?.standardStrCode : 'CNY'
    let chartData = [
      { x: i18n.get('待还款'), y: count?.remain || 0 },
      { x: i18n.get('还款中'), y: count?.reserved || 0 },
      { x: i18n.get('已还款'), y: count?.repayment || 0 }
    ]
    const showListView = name === 'LoanManagement' ? true : viewType === 'list'

    if (count?.overage) {
      chartData.push({ x: i18n.get('借款余额'), y: 0 })
    }
    // 单据详情进入
    if (hideTable) {
      return (
        <div className='content'>
          {!showLoading && this.renderMyLoanListRepeat()}
        </div>
      )
    }
    return (
      <div className="content">
        {!!this.showChart && <Chart data={chartData} count={count} standardStrCode={standardStrCode} />}
        {this.renderSearch()}
        {name !== 'LoanManagement' && <div className="view-switch" style={{ top: this.showChart ? '194px' : '5px' }}>
          <ViewSwitcher
            isList={viewType === 'list'}
            handleClickList={() => {
              this.setState({ viewType: 'list' }, () => {
                localStorage.setItem(`LoanViewType-${this.props.userInfo?.staff?.id}`, 'list')
              })
            }}
            handleClickTable={() => {
              this.setState({ viewType: 'table' }, () => {
                localStorage.setItem(`LoanViewType-${this.props.userInfo?.staff?.id}`, 'table')
              })
            }}
            data-platform-wx2-hidden={window.isInWeComISV}
          />
        </div>}

        {showListView ? (
          <>
            {this.renderListTabs()}
          </>
        ) : (
          <TableView size={size} baseDataProperties={baseDataProperties} openDetail={this.handleSelectMyLoan} />
        )}
      </div>
    )
  }

  handleTabsChange = (key) => {
    this.setState({
      loanState: key,
    }, () => {
      this.getDataSource({ ...this.props, loanState: key })
    })
  }

  renderListContent = () => {
    const { showLoading, loanState } = this.state
    return (
      <>
        {!showLoading && this.renderMyLoanListRepeat()}
        {loanState === 'paid' && this.renderPagination()}
      </>
    )
  }

  renderListTabs = () => {
    const { loanState, owner } = this.props
    const items = [
      {
        key: 'unpaid',
        label: i18n.get('未结清'),
        children: this.renderListContent(),
      },
      !owner && {
        key: 'paid',
        label: window.IS_SMG ? i18n.get('已还清预支') : i18n.get('已还清借款'),
        children: this.renderListContent(),
      }
    ].filter(Boolean)
    return (
      <Tabs className={styles['list-tabs']} items={items} activeKey={loanState} onChange={this.handleTabsChange} />
    )
  }

  renderSearch = () => {
    const { viewType } = this.state

    if (viewType === 'table') {
      return null
    }

    const placeholder = this.isManagePage
      ? i18n.get('搜索标题')
      : window.IS_SMG
        ? i18n.get('搜索标题、预支单号')
        : i18n.get('搜索标题、借款单号')

    return (
      <div className={classNames('search-box', { 'search-box--withChart': this.showChart })}>
        <Input
          className="input-search"
          placeholder={i18n.get(placeholder)}
          prefix={<img src={iconSearch} style={{ color: 'rgba(0,0,0,.25)' }} />}
          onPressEnter={this.handleSearch}
        />
      </div>
    )
  }

  render() {
    return (
      <div id={'LoanPackageListView'} className={styles['loan-package-list-new']}>
        {this.renderContent()}
      </div>
    )
  }
}
