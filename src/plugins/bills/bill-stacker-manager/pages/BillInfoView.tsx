/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/27.
 */
import styles from './BillInfoView.module.less'
import React, { PureComponent } from 'react'
import BillInfoReadOnlyContainer from '../../parts/right-part/billInfo/BillInfoReadOnlyContainer'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import ButtonGroup from '../../../../elements/puppet/ButtonGroup'
import {
  checkSpecificationActive,
  confirmCopy,
  fnPushDingTalkShareBtn,
  formatCopyBillData,
  shareBillAction
} from '../../util/billUtils'
import classnames from 'classnames'
import { toJS } from 'mobx'
import { related } from '../../../../elements/feeDetailViewList/Related'
import { observer } from 'mobx-react'
import { inject } from '@ekuaibao/react-ioc'
import { connect } from '@ekuaibao/mobx-store'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { get } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import { retractFlow } from '../../bills.action'
import BillInfoButtons from '../../elements/BillInfoButtons'
import { FlowAction } from '../../layers/bill-info-popup/FlowAction'
import { enableCustomExtendButton } from '../../../../lib/featbit'
import { BillFooter } from '../../bill-details-new/BillFooter'
import { MY_LOAN_PAGE_NAME } from './loan-package/helper'

const okText = i18n.get('确认')
const cancelText = i18n.get('取消')
interface CommonProps {
  [key: string]: any
}

interface CommonState {
  [key: string]: any
}

@observer
@connect(store => ({ size: store.states['@layout'].size }))
@EnhanceConnect(
  state => ({
    userInfo: state['@common'].userinfo.data,
    autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction
  }),
  { retractFlow }
)
export default class BillInfoView extends PureComponent<CommonProps, CommonState> {
  @inject('permission') permission: any
  bus: MessageCenter

  constructor(props) {
    super(props)
    this.state = {
      dataSource: toJS(this.props.dataSource),
      showPrintBtn: false
    }
    this.lastTime = 0
    this.bus = props.bus || new MessageCenter()
  }

  componentDidMount() {
    const { source } = this.props
    if (source === 'MessageListView') {
      this.handleBillUpdate()
    }
    this.setState({})
    this.bus.watch('bills:update:flow', this.handleBillUpdate)
    this.getPrintInvoice()
  }

  componentWillUnmount() {
    this.bus.un('bills:update:flow', this.handleBillUpdate)
  }

  getPrintInvoice = () => {
    const { dataSource } = this.props
    const currentId = dataSource ? get(dataSource, 'id') : ''
    api.invokeService('@bills:get:show:print:invoice', { flowId: currentId }).then(data => {
      this.setState({ showPrintBtn: data?.value || false })
    })
  }

  handleBillUpdate = data => {
    let { invokeService = '@bills:get:flow-info', params } = this.props
    const dataParam = !!params ? params : data
    return api.invokeService(invokeService, dataParam).then(data => {
      const value = data.value
      this.setState({ dataSource: toJS(value) })
      return value
    })
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.dataSource !== nextProps.dataSource) {
      this.setState({ dataSource: nextProps.dataSource })
    }
  }

  handlePrint(dataSource) {
    const { doPrint } = api.require('@audit/service-print')
    const { showAllFeeType } = this.props
    const flowId = dataSource.id
    const obj = api.invokeService('@share:get:print:param', dataSource)
    let data = [obj]
    data.showAllFeeType = showAllFeeType
    doPrint(
      data,
      false,
      () => {
        api.invokeService('@bills:get:flow-info', { id: flowId }).then(result => {
          this.setState({ dataSource: result.value })
        })
      },
      false,
      '0'
    )
  }

  handlePrintInvoice(dataSource) {
    const { doPrint } = api.require('@audit/service-print')
    const { showAllFeeType } = this.props
    const flowId = dataSource.id
    const obj = api.invokeService('@share:get:print:param', dataSource)
    let data = [obj]
    data.showAllFeeType = showAllFeeType
    doPrint(
      data,
      false,
      () => {
        api.invokeService('@bills:get:flow-info', { id: flowId }).then(result => {
          this.setState({ dataSource: result.value })
        })
      },
      false,
      '1'
    )
  }

  handleComment = (dataSource, privilegeId) => {
    let flowId = dataSource.id
    api.open('@bills:BillCommentModal', { flow: dataSource }).then(params => {
      api.invokeService('@bills:comment:flow', { id: flowId, params, privilegeId }).then(action => {
        if (!action.error) {
          api.invokeService('@bills:get:flow-info', { id: flowId }).then(result => {
            this.setState({ dataSource: result.value })
          })
        }
      })
    })
  }

  handleChangeApply = (dataSource, privilegeId) => {
    const { fromPageV2 } = this.props
    const requisitionObj = dataSource.form?.specificationId?.configs?.find(i => i.ability === 'requisition') || {}
    const mustRequire = !requisitionObj.optionalComment
    let flowId = dataSource.id
    const line = {
      id: flowId,
      state: 'rejected'
    }
    api.open('@bills:ChangeApplyModal', { mustRequire }).then(async ({ message }) => {
      const data = {
        flowIds: [flowId],
        action: {
          name: 'freeflow.alter',
          comment: message
        }
      }
      const result = await api.invokeService('@bills:flowDoAction', data).catch(e => {
        showMessage.warning(e.message || e.msg)
      })
      if (result?.value?.success) {
        const { layerManager } = this.props
        layerManager?.destroy()
        if (fromPageV2 === 'myRequisition') {
          const resp = await api.invokeService('@bills:get:flow-info', { id: flowId })

          if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
            api.open('@bills:BillInfoDrawerV2', {
              title: i18n.get('修改单据'),
              currentId: flowId,
              flows: [resp.value],
              from: 'from_myBill',
              showUpDown: false,
              scene: 'OWNER',
              params: { autoClose: true, isNewSearchInfo: true },
              callBack: () => {
                this.props.callBack && this.props.callBack()
              }
            })
            return
          }

          api.open('@bills:BillInfoEditePopup', {
            title: i18n.get('修改单据'),
            buttons: dataSource.actions,
            backlog: { id: -1, flowId: resp.value },
            params: { id: '', autoClose: true, isNewSearchInfo: true },
            invokeService: '@bills:get:flow-info',
            from: 'from_myBill',
            callBack: () => {
              this.props.callBack && this.props.callBack()
            }
          })
          // api.emit('reload:left:data', line)
        } else {
          api.emit('reload:left:data', line)
        }
      } else {
        showMessage.warning(result?.value?.messages.join(','))
      }
    })
  }
  reloadData = (needNotClose = false) => {
    if (!needNotClose) {
      const { callBack } = this.props
      callBack && callBack()
    }
    api.invokeService('@layout5:refresh:menu:data')
  }

  handleRetract = dataSource => {
    let { retractFlow } = this.props
    let { id } = dataSource
    let _this = this
    showModal.confirm({
      title: i18n.get('您是否确认要撤回该单据?'),
      okText: okText,
      cancelText: cancelText,
      onOk() {
        retractFlow(id).then(action => {
          if (!action.error) {
            _this.reloadData(true)
          }
        })
      }
    })
  }

  handleReminde = backlog => {
    let newTime = new Date().valueOf()
    if (newTime - this.lastTime > 60000) {
      //60秒内只能执行一次催办功能
      this.fnReminde(backlog)
      this.lastTime = newTime
    } else {
      showMessage.warning(i18n.get('操作频繁'))
    }
  }

  fnReminde = flow => {
    let { id, plan } = flow
    let { taskId } = plan
    api.invokeService('@bills:bill:reminde', id, taskId).then(_ => {
      this.reloadData(true)
    })
  }

  handleCopyBill = async dataSource => {
    const { copyAction, layer, layerManager, fromPageV2 } = this.props
    layerManager?.destroy()
    const data = await formatCopyBillData(dataSource)
    if (fromPageV2 === 'myRequisition') {
      if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
        api.open('@bills:BillInfoDrawerV2', {
          title: i18n.get('新建单据'),
          currentId: '',
          from: 'from_myBill',
          params: {
            autoClose: true,
            isNewSearchInfo: true
          },
          flows: [{ form: data.form, state: 'new', ownerId: dataSource.ownerId, formType: dataSource.formType }],
          showUpDown: false,
        })
        return
      }

      api.open('@bills:BillInfoEditePopup', {
        title: i18n.get('新建单据'),
        buttons: dataSource.actions,
        backlog: {
          id: -1,
          flowId: { form: data.form, state: 'new', ownerId: dataSource.ownerId, formType: dataSource.formType }
        },
        params: { id: '', autoClose: true, isNewSearchInfo: true },
        invokeService: '@bills:get:flow-info',
        from: 'from_myBill'
      })
    } else {
      copyAction?.(dataSource)
    }
  }

  confirmCopy = async dataSource => {
    // 单据使用的模板被停用时，拦截复制动作
    const specOriginalId = get(dataSource, 'form.specificationId.originalId')
    const specActive = await checkSpecificationActive(specOriginalId)
    if (!specActive) return
    related.clearRelatedData()
    confirmCopy(dataSource).then(_ => {
      this.handleCopyBill(dataSource)
    })
  }

  fnGetFlowAction = () => {
    return {
      [FlowAction.Alter]: {
        onClick: () => {
          const { privilegeId } = this.props
          const { dataSource } = this.state
          this.handleChangeApply(dataSource, privilegeId)
        }
      },
      [FlowAction.Copy]: {
        onClick: () => {
          const { dataSource } = this.state
          this.confirmCopy(dataSource)
        }
      },
      [FlowAction.Urge]: {
        onClick: () => {
          const { dataSource } = this.state
          this.fnReminde(dataSource)
        }
      },
      [FlowAction.Print]: {
        onClick: () => {
          const { dataSource } = this.state
          this.handlePrint(dataSource)
        }
      },
      [FlowAction.PrintInvoice]: {
        onClick: () => {
          const { dataSource } = this.state
          this.handlePrintInvoice(dataSource)
        }
      },
      [FlowAction.Comment]: {
        onClick: () => {
          const { privilegeId } = this.props
          const { dataSource } = this.state
          this.handleComment(dataSource, privilegeId)
        }
      },
      [FlowAction.Retract]: {
        onClick: () => {
          const { dataSource } = this.state
          this.handleRetract(dataSource)
        }
      },
      [FlowAction.Share]: {
        onClick: () => {
          const { dataSource } = this.state
          shareBillAction(dataSource?.id)
        }
      }
    }
  }

  onRreshCurrentView = () => {
    const { dataSource } = this.state
    if (dataSource.id) {
      api.invokeService('@bills:get:flow-info', { id: dataSource.id }).then(result => {
        this.setState({ dataSource: result.value })
      })
    }
  }

  getFooter = (options: any) => {
    if (enableCustomExtendButton()) {
      return this.renderActionsV2(options)
    }
    return this.renderActionsV1(options)
  }

  renderActionsV2 = (options: any) => {
    const { dataSource, btns } = options;
    const { from, fromPageV2 } = this.props;
    if (!dataSource) {
      return null
    }
    const useOldActions = ['myRequisition', MY_LOAN_PAGE_NAME].includes(fromPageV2)
    const actionProps = useOldActions
      ? { hideFooter: btns.length === 0, buttons: btns }
      : {}
    return (
      <BillFooter
        flow={dataSource}
        forceUpdateFlow={this.onRreshCurrentView}
        bus={this.bus}
        from={from}
        {...actionProps}
      />
    )
  }

  renderActionsV1 = ({
    needConfigButton,
    dataSource,
    scene,
    privilegeId,
    btns,
    isDrawer
  }) => {
    return (
      <BillInfoButtons
        needConfigButton={needConfigButton}
        flowId={dataSource?.id}
        flowActionMap={this.fnGetFlowAction()}
        scene={scene}
        privilegeId={privilegeId}
        bus={this.bus}
        forceUpdateFlow={this.onRreshCurrentView}
      >
        {!!btns.length && (
          <div className={styles['bill-info-footer']} style={isDrawer ? { justifyContent: 'start' } : {}}>
            <ButtonGroup dataSource={btns} />
          </div>
        )}
      </BillInfoButtons>
    )
  }

  render() {
    let {
      keel,
      stackerManager,
      layerManager,
      detailStack,
      suppleInvoiceBtn,
      renderRiskTips,
      riskData,
      copyAble = false,
      isModal,
      type,
      privilegeId = '',
      source = '',
      showAllFeeType,
      fromSource,
      autoExpenseWithBillStriction,
      isDrawer,
      userInfo,
      fromPageV2,
      scene,
      needConfigButton
    } = this.props
    const { dataSource, showPrintBtn } = this.state
    const {
      form: {
        submitterId,
        specificationId: { configs }
      },
      plan
    } = dataSource
    const allowSubmitterRetract = configs?.find(line => line.ability === 'requisition')?.allowSubmitterRetract
    const btns = []
    const isMyFlow = submitterId?.id === userInfo?.staff?.id
    const NotAllowCreateApplication = api.getState()['@common'].powers?.powersList?.find(power => power.powerCode === '110415')?.state === 'using'
    if (
      dataSource.formType === 'requisition' &&
      (dataSource.state === 'archived' || dataSource.state === 'paid') &&
      allowSubmitterRetract &&
      (fromSource === 'myBill' || (fromPageV2 === 'myRequisition' && isMyFlow))
    ) {
      if (!NotAllowCreateApplication) {
        btns.push({
          action: FlowAction.Alter,
          label: i18n.get('变更'),
          type: 'secondary',
          onClick: _ => this.handleChangeApply(dataSource, privilegeId),
          'data-testid': 'bill-footer-alter'
        })
      }
    }
    if (fromPageV2 === 'myRequisition' && isMyFlow) {
      copyAble = true
    }
    if (
      copyAble &&
      dataSource.formType !== 'settlement' &&
      dataSource.formType !== 'reconciliation' &&
      !(dataSource.formType == 'expense' && autoExpenseWithBillStriction)
    ) {
      if(!(NotAllowCreateApplication && dataSource.formType === 'requisition')) {
        btns.push({
          action: FlowAction.Copy,
          label: i18n.get('复制'),
          type: 'secondary',
          onClick: _ => this.confirmCopy(dataSource),
          'data-testid': 'bill-footer-copy'
        })
      }
    }
    const stateList = ['approving', 'paying', 'sending']
    if (stateList.includes(dataSource?.state) && fromPageV2 === 'myRequisition' && isMyFlow) {
      btns.push({
        action: FlowAction.Urge,
        label: i18n.get('催办'),
        type: 'secondary',
        onClick: _ => this.fnReminde(dataSource),
        'data-testid': 'bill-footer-urge'
      })
    }
    if (type === 'MessageListView' || type === 'ArchivedTableView' || dataSource.state !== 'draft') {
      if (!dataSource?.form?.forbidOwnerPrint){
        btns.push({
          action: FlowAction.Print,
          label: i18n.get('打印单据'),
          type: 'secondary',
          onClick: _ => this.handlePrint(dataSource),
          'data-testid': 'bill-footer-print'
        })
        if (showPrintBtn) {
          btns.push({
            action: FlowAction.PrintInvoice,
            label: i18n.get('打印单据和发票'),
            onClick: _ => this.handlePrintInvoice(dataSource),
            'data-testid': 'bill-footer-printinvoice'
          })
        }
      }
    }

    if (dataSource.state !== 'draft') {
      btns.push({
        action: FlowAction.Comment,
        label: i18n.get('评论'),
        type: 'secondary',
        onClick: _ => this.handleComment(dataSource, privilegeId),
        'data-testid': 'bill-footer-comment'
      })
    }

    if (plan && fromPageV2 === 'myRequisition' && isMyFlow) {
      let { nodes, taskId } = plan
      let node = nodes && nodes.find(node => node.id === taskId)
      let config = (node && node.config) || {}
      if (node && config.allowSubmitterRetract) {
        btns.unshift({
          action: FlowAction.Retract,
          label: i18n.get('撤回'),
          onClick: () => this.handleRetract(dataSource),
          'data-testid': 'bill-footer-retract'
        })
      }
    }

    if (this.permission?.isMCDisabled()) {
      btns = btns.filter(el => el.label === i18n.get('导出') || el.label === i18n.get('打印'))
    }

    // 根据条件判断是否添加分享按钮
    fnPushDingTalkShareBtn(btns, get(dataSource, 'id'))

    const cls = classnames('dis-f', 'fd-c', 'flex-1', styles['bill-info-wrapper'])
    const billInfoView = document.getElementById('BillInfoView')
    const param = { ...this.props }
    if (billInfoView) {
      param.offsetWidth = billInfoView.offsetWidth
    }
    return (
      <div id={'BillInfoView'} className={cls}>
        <BillInfoReadOnlyContainer
          showAllFeeType={showAllFeeType}
          bus={this.bus}
          keel={keel}
          stackerManager={stackerManager}
          isModal={!!layerManager || isModal}
          suppleInvoiceBtn={suppleInvoiceBtn}
          renderRiskTips={renderRiskTips}
          riskData={riskData}
          detailStack={detailStack}
          privilegeId={privilegeId}
          source={source}
          mode={isDrawer ? 'table' : 'inline'}
          {...param}
          dataSource={dataSource}
          billFooter={
            this.getFooter({
              needConfigButton,
              dataSource,
              scene,
              privilegeId,
              btns,
              isDrawer
            })
          }
        />

      </div>
    )
  }
}
