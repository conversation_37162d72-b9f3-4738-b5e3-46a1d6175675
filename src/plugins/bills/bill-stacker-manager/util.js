/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/8/9.
 */

import parseQuery2Select from '@ekuaibao/lib/lib/parseQuery2Select'
import parseSelectUsePropertySet from '@ekuaibao/lib/lib/parseSelectUsePropertySet'
import filtersFixer from '../../../lib/filtersFixer'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import {
  fetchFlowInfoByIdData,
} from '../bills.action'

import { showMessage } from '@ekuaibao/show-util'
// import { Resource } from '@ekuaibao/fetch'
import { Resource, Fetch } from '@ekuaibao/fetch'
import { renderInvoiceRiskTips } from '../util/billUtils'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const searchPaid = new Resource('/api/flow/v1/flows/my')
const searchPrint = new Resource('/api/v1/print/remind/search')
const action = {
  printRemind: 0,
  print: 8,
  modify: 5,
  reject: 1,
  agree: 3,
  pay: 6,
  commnet: 4,
  addnode: 11,
  addExpress: 14,
  jumpExpress: 15,
  receiveExpress: 12,
  receiveExceptionExpress: 13,
  cancelReceiveExceptionExpress: 16
}
export const ACTIONBUTTONS = {
  'freeflow.pay': (footerClick, backlog) => ({
    label: i18n.get('支付'),
    onClick: () => footerClick(action.pay, backlog)
  }),
  'freeflow.agree': (footerClick, backlog) => ({
    label: i18n.get('同意'),
    onClick: () => footerClick(action.agree, backlog)
  }),
  'freeflow.send': (footerClick, backlog) => ({
    label: i18n.get('添加寄送信息'),
    onClick: () => footerClick(action.addExpress, backlog)
  }),
  'freeflow.receive': (footerClick, backlog) => ({
    label: i18n.get('确认收单'),
    onClick: () => footerClick(action.receiveExpress, backlog)
  }),
  'freeflow.receiveException': (footerClick, backlog) => ({
    label: i18n.get('收单异常'),
    onClick: () => footerClick(action.receiveExceptionExpress, backlog)
  }),
  'freeflow.cancelReceiveException': (footerClick, backlog) => ({
    label: i18n.get('取消收单异常'),
    onClick: () => footerClick(action.cancelReceiveExceptionExpress, backlog)
  }),
  'freeflow.submit': footerClick => ({
    label: i18n.get('提交送审'),
    main: true,
    onClick: () => footerClick()
  }),
  'freeflow.addnode': (footerClick, backlog) => ({
    label: i18n.get('转交审批'),
    onClick: () => footerClick(action.addnode, backlog)
  }),
  'freeflow.reject': (footerClick, backlog) => ({
    label: i18n.get('驳回'),
    onClick: () => footerClick(action.reject, backlog)
  }),
  'freeflow.editApproving': (footerClick, backlog) => ({
    label: i18n.get('修改'),
    onClick: () => footerClick(action.modify, backlog)
  }),
  'freeflow.remind': (footerClick, backlog) => ({
    label: i18n.get('打印提醒'),
    onClick: () => footerClick(action.printRemind, backlog)
  }),
  'freeflow.skip.send': (footerClick, backlog) => ({
    label: i18n.get('跳过寄送'),
    onClick: () => footerClick(action.jumpExpress, backlog)
  }),
  'freeflow.edit': footerClick => ({
    label: i18n.get('存为草稿'),
    onClick: () => footerClick()
  }),
  'freeflow.print': (footerClick, backlog) => ({
    label: i18n.get('打印'),
    onClick: () => footerClick(backlog)
  }),
  'freeflow.comment': (footerClick, backlog) => ({
    label: i18n.get('评论'),
    onClick: () => footerClick(backlog)
  }),
  'freeflow.retract': (footerClick, backlog) => ({
    label: i18n.get('撤回'),
    onClick: () => footerClick(backlog)
  }),
  'freeflow.delete': (footerClick, backlog) => ({
    label: i18n.get('删除'),
    onClick: () => footerClick(backlog)
  }),
  'freeflow.urge': (footerClick, backlog) => ({
    label: i18n.get('催办'),
    onClick: () => footerClick(backlog)
  }),
  'freeflow.copy': (footerClick, backlog) => ({
    label: i18n.get('复制'),
    onClick: () => footerClick(backlog)
  }),
  'freeflow.read': (footerClick, backlog) => ({
    label: i18n.get('标记为已读'),
    onClick: () => footerClick(backlog)
  }),
  'freeflow.printed': (footerClick, backlog) => ({
    label: i18n.get('收到打印'),
    onClick: () => footerClick(backlog)
  })
}

function _getPropertySet() {
  return api.getState('@common.globalFields.data') || []
}

// 下面弹框的改良版（目前用于已完成单据弹框）
export function handleCompleteBillDetails({
  params,
  copyAction,
  copyAble,
  needPosition = true,
  isOpenModal,
  showBillApproveResult,
  needConfigButton
}) {
  const { showAllFeeType, privilegeId } = params
  let { getFlowInfoById, viewKey } = this.props
  startOpenFlowPerformanceStatistics()
  getFlowInfoById(params).then(action => {
    if (action.error) {
      return
    }
    const dataSource = getV(action, 'payload.value', {})
    const args = {
      copyAble,
      copyAction,
      needPosition,
      type: viewKey,
      dataSource,
      isModal: isOpenModal,
      privilegeId,
      showAllFeeType,
      suppleInvoiceBtn: true,
      showBillApproveResult,
      fromSource: 'myBill', // 判断变更按钮是否显示 只有在我的已完成单据显示
      needConfigButton
    }

    api.open(
      '@bills:BillStackerPopup',
      {
        viewKey: 'BillInfoView',
        ...args
      },
      false
    )
  })
}

export function handleBillDetailClickById(
  params,
  isOpenModal,
  isRiskTipsAndSuppleBtn,
  copyAction,
  copyAble = false,
  needPosition = true
) {
  const { showAllFeeType, privilegeId, fromPageV2, scene, needConfigButton, needHideLoanDetail } = params;
  let { stackerManager, getFlowInfoById, viewKey, keel } = this.props;
  startOpenFlowPerformanceStatistics();
  let { billType } = this.state || {};

  const fetchDataPromise = typeof getFlowInfoById === 'function'
    ? getFlowInfoById(params)
    : fetchFlowInfoByIdData(params);

  fetchDataPromise.then(result => {
    let res = result;
    let riskDataFromAction = {};

    if (typeof getFlowInfoById === 'function' && result && result.payload && typeof result.payload.value !== 'undefined') {
      res = result.payload.value;
      riskDataFromAction = { singleInvoiceRiskWarning: result.payload.singleInvoiceRiskWarning };
    } else {
      res = result.value;
      riskDataFromAction = { singleInvoiceRiskWarning: result.singleInvoiceRiskWarning };
    }

    if (!res) {
      console.error("Failed to get valid data structure from fetch:", result);
      return;
    }

    const finalRiskData = riskDataFromAction.singleInvoiceRiskWarning ? riskDataFromAction : { singleInvoiceRiskWarning: res.singleInvoiceRiskWarning };

    const dataSource = res;

    if (billType === 'loanTab') {
      api.open('@bills:BillStackerModal', {
        viewKey: 'BillInfoView',
        dataSource,
        showButton: true
      });
      return;
    }

    const args = {
      copyAble,
      copyAction,
      needPosition,
      type: viewKey,
      dataSource,
      isModal: isOpenModal,
      privilegeId,
      showAllFeeType,
      suppleInvoiceBtn: true,
      fromSource: 'myBill',
      scene,
      needConfigButton,
      riskData: finalRiskData,
      needHideLoanDetail
    };

    if (isRiskTipsAndSuppleBtn) {
      args.renderRiskTips = renderInvoiceRiskTips(res);
    }

    if (isOpenModal) {
      if (keel) {
        keel.open('BillInfoView', args);
      } else if (stackerManager) {
        stackerManager.push('BillInfoView', args);
      } else {
        api.open('@bills:BillStackerModal', {
          viewKey: 'BillInfoView',
          dataSource,
          showAllFeeType,
          showButton: true
        });
      }
    } else {
       if (keel) {
        const openViewList = keel.dataset.keys() || []
        let indexBillInfoView = openViewList.findIndex(key => key.includes('BillInfoView'));

        if (indexBillInfoView < 0) {
          keel.open('BillInfoView', {
            ...args,
            fromPageV2,
            callBack: () => {
              keel.closeTo(0)
            }
          })
        } else {
           keel.closeTo(indexBillInfoView)
        }
      } else if (stackerManager) {
        const openViewList = stackerManager.values() || []
         let indexBillInfoView = openViewList.findIndex(view => view.key === 'BillInfoView');

        if (indexBillInfoView < 0) {
          stackerManager.push('BillInfoView', args)
        } else {
           stackerManager.open(indexBillInfoView, args)
        }
      }
    }
  }).catch(error => {
    console.error("Failed to fetch flow info:", error);
  });
}

export function handleBillDetailClickByIdNew(params, isOpenModal) {
  let { privilegeId, showAllFeeType, fromPageV2 } = params
  let { stackerManager, keel, getFlowInfoByIdNew } = this.props
  getFlowInfoByIdNew(params).then(action => {
    if (action.error) {
      return
    }
    let data = action.payload
    if (isOpenModal) {
      let data = action.payload
      if (keel) {
        keel.open('BillInfoView', {
          dataSource: data.value,
          showAllFeeType
        })
      } else if (stackerManager) {
        stackerManager.push('BillInfoView', {
          dataSource: data.value,
          showAllFeeType
        })
      }
    } else {
      const dataSource = data.value
      if (keel) {
        //keel组件全部替换完成后，会去掉stackerManager相关逻辑
        const openViewList = keel.dataset.keys() || []
        let indexBillInfoView = -1
        for (var i = 0; i < openViewList.length; i++) {
          if (openViewList[i].includes('BillInfoView')) {
            indexBillInfoView = i
            break
          }
        }
        if (indexBillInfoView < 0) {
          keel.open('BillInfoView', {
            dataSource,
            privilegeId,
            showAllFeeType,
            fromPageV2,
            callBack: () => {
              keel.closeTo(0)
            }
          })
        } else {
          keel.closeTo(indexBillInfoView)
        }
      } else if (stackerManager) {
        const openViewList = stackerManager.values() || []
        let indexBillInfoView = -1
        for (var i = 0; i < openViewList.length; i++) {
          if (openViewList[i].key === 'BillInfoView') {
            indexBillInfoView = i
            break
          }
        }
        if (indexBillInfoView < 0) {
          stackerManager.push('BillInfoView', { dataSource, privilegeId, showAllFeeType })
        } else {
          stackerManager.open(indexBillInfoView, { dataSource, privilegeId, showAllFeeType })
        }
      }
    }
  })
}

export function handleShowDetail({
  requestParam,
  detailId,
  needPosition = true,
  suppleInvoiceBtn,
  flowId,
  showAllFeeType,
  isOnlyShowCurrentIdx = false //仅展示当前页,不支持上一页下一页展示
}) {
  let { stackerManager, keel, isModal, getFlowInfoById } = this.props;

  const fetchDataPromise = typeof getFlowInfoById === 'function'
    ? getFlowInfoById(requestParam)
    : fetchFlowInfoByIdData(requestParam);

  fetchDataPromise.then(result => {
    let res = result;
    if (typeof getFlowInfoById === 'function' && result && result.payload && typeof result.payload.value !== 'undefined') {
      res = result.payload.value;
    } else {
      res = result.value
    }

    if (res && res.form && res.form.details) {
      let { form: { details } } = res;
      let { specificationId = {}, submitterId } = res.form;
      const dataSource = details.find(item => item?.feeTypeForm?.detailId === detailId);

      if (!dataSource) {
        console.error(`Detail with detailId "${detailId}" not found.`);
        showMessage.error(i18n.get('未找到对应的明细数据'));
        return;
      }

      const params = {
        dataSource: { ...dataSource, showAllFeeType },
        billSpecification: specificationId,
        details: isOnlyShowCurrentIdx ? [dataSource] : details,
        isEdit: false,
        submitterId,
        needPosition,
        suppleInvoiceBtn,
        flowId,
        showBillBar: true,
        billForm: res.form,
        onClickBillBar: handleBillDetailClickById.bind(
          this,
          { id: flowId, flowId },
          isModal,
          undefined,
          undefined,
          undefined,
          false
        )
      };

      if (stackerManager) {
        stackerManager.push('FeeDetailView', params);
      } else if (keel) {
        keel.open('FeeDetailView', params);
      } else {
        api.open('@bills:BillStackerModal', {
          viewKey: 'FeeDetailView',
          ...params
        });
      }
    } else {
      console.error("Received unexpected or incomplete data structure:", res);
    }
  }).catch(error => {
    console.error("Failed to fetch flow info:", error);
  });
}

export function searchPaidList(params, scene, dimensionItems) {
  const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage === 'en-US'
  const sceneFiltersQuery = scene ? filtersFixer(scene, 'flow', dimensionItems) : ''
  params.status = { state: ['paid'] }
  const query = parseQuery2Select(params, undefined, 'flow', lang)
  const content = parseSelectUsePropertySet(_getPropertySet(), params.options)
  query
    .desc('form.code')
    .filterBy(sceneFiltersQuery)
    .select(`ownerId(id,name,enName),calcRiskWarning(...),form(${content ? content + ',' : ''}...),...`)

  const queryString = query.value()

  return searchPaid
    .POST('', queryString)
    .then(data => {
      const { count, items } = data
      if (params.isSegment) {
        api.emit('refresh:segment:title:count', count)
      }
      return { dataSource: items, total: count }
    })
    .catch(e => {
      showMessage.error(e)
      return { dataSource: [], total: 0 }
    })
}

export function searchPrintList(params, scene, dimensionItems) {
  const sceneFiltersQuery = scene ? filtersFixer(scene, undefined, dimensionItems) : ''
  const query = parseQuery2Select(params)
  const content = parseSelectUsePropertySet(_getPropertySet(), params.options)
  query
    .filterBy('active == true')
    .desc('flowId.form.code')
    .filterBy(sceneFiltersQuery)
    .select(`flowId(ownerId(id,name,enName),calcRiskWarning(...),form(${content ? content + ',' : ''}...),...),...`)

  const queryString = query.value()

  return searchPrint
    .POST('', queryString, {
      join: 'id,nodeState,/flow/v2/backlogs/state'
    }, null, { hiddenLoading: true })
    .then(data => {
      const { count, items } = data
      return { dataSource: items, total: count }
    })
}

export function datalinkList(params, scene, dimensionItems) {
  const sceneFiltersQuery = scene ? filtersFixer(scene, undefined, dimensionItems) : ''
  const query = parseQuery2Select(params)
  const content = parseSelectUsePropertySet(_getPropertySet())
  query
    .filterBy('active == true')
    .desc('flowId.form.code')
    .filterBy(sceneFiltersQuery)
    .select(`flowId(ownerId(id,name,enName),form(${content},...),...),...)`)

  const queryString = query.value()

  return searchPrint
    .POST('', queryString)
    .then(data => {
      const { count, items } = data
      return { dataSource: items, total: count }
    })
    .catch(e => {
      showMessage.error(e)
      return { dataSource: [], total: 0 }
    })
}

export function createActionColumn(type = 'printList') {
  let buttonText = i18n.get('打印')
  if (type === 'paidList') {
    buttonText = i18n.get('确认')
  }
  return bus => {
    const result = {
      title: i18n.get('操作'),
      width: 140,
      filterType: false,
      dataIndex: 'action',
      key: 'action',
      label: i18n.get('操作'),
      value: 'action',
      fixed: 'right',
      className: 'actions-wrapper',
      render(text, line) {
        return (
          <div
            className="actions"
            onClick={e => {
              e.persist()
              e.nativeEvent.stopImmediatePropagation()
              e.stopPropagation()
              e.preventDefault()
              return false
            }}
          >
            <a className="ant-dropdown-link mr-16" onClick={e => bus.emit('table:row:action', type, line)}>
              {buttonText}
            </a>
          </div>
        )
      }
    }
    return result
  }
}
