import { EnhanceStackerManager } from '@ekuaibao/enhance-stacker-manager'
import React, { PureComponent } from 'react'
import Breadcrumb from '../../../elements/ekbc-basic/breadcrumb'
import styles from './BillStackerModal.module.less'
import EKBBreadcrumb from '../../../ekb-components/business/breadcrumb'
import classnames from 'classnames'
import { app as api } from '@ekuaibao/whispered'
import { observer } from 'mobx-react'
import { inject, provider } from '@ekuaibao/react-ioc'
import { PermissionVm } from '../vms/Permission.vm'
import { getV } from '@ekuaibao/lib/lib/help'
import { OutlinedTipsClose, OutlinedDirectionWindowMax, OutlinedDirectionWindowMini } from '@hose/eui-icons'
import { getDisplayName } from '../../../elements/utilFn'
import { NameCell } from '../../../elements/name-cell'

@EnhanceStackerManager([
  {
    key: 'WrittenOffListView',
    getComponent: () => import('./pages/WrittenOffListView'),
    title: window.IS_SMG ? i18n.get('选择要核销的预支') : i18n.get('选择要核销的借款')
  },
  {
    key: 'LoanPackageDetailView',
    getComponent: () => import('./pages/LoanPackageDetailViewNew'),
    title: window.IS_SMG ? i18n.get('预支详情') : i18n.get('借款详情')
  },
  {
    key: 'BillInfoView',
    getComponent: () => import('./pages/BillInfoView'),
    title: i18n.get('单据详情')
  },
  {
    key: 'NewBillInfoView',
    getComponent: () => import('./pages/NewBillInfoView'),
    title: i18n.get('单据详情')
  },
  {
    key: 'LoanPackageListView',
    getComponent: () => import('./pages/LoanPackageListViewNew'),
    title: window.PLATFORMINFO?.platform === 'SMG' ? i18n.get('我的预支') : i18n.get('我的借款')
  },
  {
    key: 'FeeDetailView',
    getComponent: () => import('./pages/FeeDetailView'),
    title: i18n.get('消费记录')
  },
  {
    key: 'MyCarBusinessInfo',
    getComponent: () => import('../../../elements/dataLink-card/MyCarBusinessDetailInfo'),
    title: i18n.get('用车补贴详情')
  },
  {
    key: 'ArchivedTableView',
    getComponent: () => import('./pages/CompletedAndUnconfirmedSeg'),
    title: i18n.get('已完成单据'),
    type: 'Archived'
  },
  {
    key: 'ApplyEventListView',
    getComponent: () => import('./pages/apply-event/ApplyEventListView'),
    title: i18n.get('事项列表')
  },
  {
    key: 'ApplyEventDetail',
    getComponent: () => import('./pages/apply-event/ApplyEventDetail'),
    title: i18n.get('申请事项')
  },
  {
    key: 'DataGridConditionView',
    getComponent: () => api.require('@layout/stackers/datagrid-condition-view'),
    title: i18n.get('新建条件')
  },
  {
    key: 'MessageListView',
    getComponent: () => import('./pages/CompletedAndUnconfirmedSeg'),
    title: i18n.get('待我打印')
  },
  {
    key: 'DataLinkList',
    getComponent: () => import('./pages/data-link-list/DataLinkList'),
    title: i18n.get('实例引用列表')
  },
  {
    key: 'ExpressListWrapper',
    getComponent: async () => {
      return await api.require('@audit/receiveExpress/ExpressListWrapper')
    },
    title: i18n.get('相符(未收单)')
  },
  {
    key: 'ExpenseLinkListView',
    getComponent: () => import('./pages/ExpenseLinkListView'),
    title: i18n.get('关联申请')
  },
  {
    key: 'DataLinkDetailModal',
    getComponent: () => import('../layers/dataLink-detail-modal/DataLinkDetailModal'),
    title: i18n.get('业务对象')
  }
])
@provider(['permission', PermissionVm])
@observer
export default class BillStackerModal extends PureComponent {
  @inject('permission') permission: any
  state = { isExpend: false, hiddenExpand: false }

  componentDidMount() {
    this.loadStackerView(this.props)
    api.on('close:bills:modal', this.handleClose)
  }

  componentWillUnmount() {
    api.un('close:bills:modal', this.handleClose)
  }

  handleClose = () => {
    this.props.layer.emitCancel()
  }

  loadStackerView(props) {
    let { viewKey, stackerManager, flowId, scopeIds, ...others } = props
    ;(viewKey === 'ExpenseLinkListView' || viewKey === 'WrittenOffListView') && this.setState({ hiddenExpand: true })
    switch (viewKey) {
      case 'WrittenOffListView':
        let { checkedValues, loanPackageList, initValue, isReceiptTemplate } = props
        stackerManager.push(viewKey, {
          loanPackageList: loanPackageList,
          checkedValues,
          initValue,
          flowId,
          scopeIds,
          isReceiptTemplate,
          title: isReceiptTemplate
            ? i18n.get('选择借款单')
            : window.IS_SMG
            ? i18n.get('选择要核销的预支')
            : i18n.get('选择要核销的借款')
        })
        break
      case 'LoanPackageDetailView':
        let { loanDetail, scene } = props
        stackerManager.push(viewKey, {
          loanDetail,
          scene
        })
        break
      case 'LoanPackageListView':
        let { loanList, owner, hiddenButton } = props
        const name = <NameCell type="user" id={owner.id} name={getDisplayName(owner)} />
        stackerManager.push(viewKey, {
          loanList,
          ownerName: name,
          hiddenButton,
          title: name ? <>{name}{i18n.get('的借款')}</> : i18n.get('借款')
        })
        break
      case 'PaidListView':
        let { paidList, paidListType } = this.props
        stackerManager.push(viewKey, {
          flowList: paidList,
          paidListType
        })
        break
      case 'BillInfoView':
        let { dataSource, scene } = this.props
        stackerManager.push(viewKey, {
          dataSource,
          hasPopupTitle: true,
          scene
        })
        break
      case 'MessageListView':
        let { type, count = 0 } = this.props
        stackerManager.push(viewKey, {
          title:
            type === 'paidList'
              ? window.isNewHome
                ? i18n.get('已完成单据')
                : i18n.get('需确认单据')
              : i18n.get('待我打印')
        })
        break
      case 'DataLinkDetailModal':
        const { entityInfo } = props
        stackerManager.push(viewKey, {
          title: entityInfo.name,
          ...props
        })
        break
      default:
        stackerManager.push(viewKey, {
          ...others
        })
        break
    }
  }

  handleMenuClick(line, i) {
    let { stackerManager, scopeIds } = this.props
    switch (line.key) {
      case 'WrittenOffListView':
        stackerManager.open(i, { scopeIds, isRefresh: false })
        break
      case 'LoanPackageDetailView':
        let { loanDetail, showRepaymentButton, repayInfo } = line
        stackerManager.open(i, {
          loanDetail,
          repayInfo,
          showRepaymentButton
        })
        break
      case 'LoanPackageListView':
        let { loanState, loanList, ownerName, hiddenButton } = line
        stackerManager.open(i, { loanState, loanList, ownerName, hiddenButton })
        break
      case 'PaidListView':
        stackerManager.open(i, {
          flowList: line.flowList,
          paidListType: line.paidListType
        })
        break
      case 'BillInfoView':
        let { dataSource, renderRiskTips, suppleInvoiceBtn, detailStack, ...others } = line
        stackerManager.open(i, {
          dataSource,
          renderRiskTips,
          suppleInvoiceBtn,
          detailStack, //穿透到明细detailStack:{id:''}
          ...others
        })
        break
      case 'ApplyEventListView':
        stackerManager.open(i, {})
        break
      case 'ApplyEventDetail':
        stackerManager.open(i, { detail: line.detail, showButton: line.showButton })
        break
      case 'MessageListView':
        stackerManager.open(i, {})
        break
      case 'DataLinkDetailModal':
        const { entityInfo } = this.props
        stackerManager.open(i, {
          title: entityInfo.name,
          ...props
        })
        break
      default:
        stackerManager.open(i, { ...line })
        break
    }
  }

  handleCancel = () => {
    let { onCancel, type } = this.props
    if (type && (type === 'Archived' || type === 'paidList')) {
      api.store.dispatch('@layout5/setSegmentActiveKey')(undefined)
    }
    onCancel && onCancel()
  }

  renderBreadcrumb() {
    const { immersiveable, isNewHeader } = this.props
    const array = this.props.stackerManager.values()
    let items = []
    array.forEach((line, i) => {
      items.push({
        key: i,
        onClick: () => this.handleMenuClick(line, i),
        title: line.viewTitle || line.title
      })
    })
    if (isNewHeader) {
      return <EKBBreadcrumb immersiveable={immersiveable} items={items} />
    }
    if (items.length === 1) {
      const specificationName = getV(array[0], 'dataSource.form.specificationId.name')
      return (
        <span className="flex">{specificationName ? `${specificationName}${i18n.get('详情')}` : items[0].title}</span>
      )
    } else {
      return <Breadcrumb items={items} />
    }
  }

  handleChangeModalSize = isExpend => {
    // const domArray = document.getElementsByClassName('ant-modal')
    const domArray = document.getElementsByClassName('respond-modal-layer')
    let dom = domArray.length > 0 ? domArray[domArray.length - 1] : domArray[0]
    if (!dom) return
    if (isExpend) {
      dom.className = dom.className.replace(/ fullScreenModal/g, '')
    } else {
      dom.className = dom.className + ' fullScreenModal'
    }
    // 为配合实现单据多列的响应式布局，触发mbox上的windowSizeChange方法
    window.onresize(undefined)
    this.setState({ isExpend: !isExpend })
  }

  render() {
    const { className, closable = true, stackerManager, viewKey, stackStyle } = this.props
    const cls = classnames(
      styles['BillStackerModal'],
      { [styles['BillStackerModal-new']]: !this.props.layer },
      className
    )
    let style = null
    if (stackerManager.values().length > 1) {
      style = stackStyle ? stackStyle : { top: '62px' }
    } else if (viewKey === 'NewBillInfoView') {
      style = { top: '52px' }
    }
    const { isExpend, hiddenExpand } = this.state
    const IconWindow = isExpend ? OutlinedDirectionWindowMini : OutlinedDirectionWindowMax
    // 当组件没有用到弹窗中的时候，不展示弹窗相关的操作。（场景：待我打印页面）
    const inModal = !!this.props.layer
    return (
      <>
        <div className={`modal-header ${styles['billStackerHeader']}`}>
          {this.renderBreadcrumb()}
          {inModal && (
            <div className="billStackerHeader-actions">
              {!hiddenExpand && <IconWindow onClick={() => this.handleChangeModalSize(isExpend)} />}
              {closable && <OutlinedTipsClose onClick={this.handleCancel} />}
            </div>
          )}
        </div>
        <div id="ImportThirdPartyModal" className={cls} style={style}>
          {this.props.children}
        </div>
      </>
    )
  }
}
