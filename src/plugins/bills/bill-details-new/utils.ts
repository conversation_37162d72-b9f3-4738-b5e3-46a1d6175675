import { app as api } from '@ekuaibao/whispered'
import { getBacklogIdByFlowId } from './api'
import { getFlowButtons } from '../bills.action'
import { pick } from 'lodash'
import { FlowItem } from './types'

export const getBacklogInfoByFlowId = async (flowId: string, backlogId? : string, privilegeId?: string) => {
  if (!backlogId) {
    // 如果backlogId不存在，通过flowId反查backlogId
    backlogId = await getBacklogIdByFlowId(flowId)
  }
  if (!backlogId) {
    return {
      id: '',
    }
  }
  return api.invokeService('@audit:get:backlog-info', {
    id: backlogId,
    privilegeId,
  })
}

export const getBacklogInfoByFlow = async (flow: FlowItem, backlogId? : string, privilegeId?: string) => {
  if (!backlogId) {
    // 如果backlogId不存在，通过flowId反查backlogId
    backlogId = await getBacklogIdByFlowId(flow.id)
  }
  if (!backlogId) {
    return {
      id: '',
      flowId: flow,
    }
  }
  const backlog = await api.invokeService('@audit:get:backlog-info', {
    id: backlogId,
    privilegeId,
  })
  return {
    ...backlog,
    flowId: flow,
  }
}

export const getButtonOptionsByFlowId = async (flowId: string, flow: FlowItem) => {
  const NotAllowCreateApplication = api.getState()['@common'].powers?.powersList?.find(power => power.powerCode === '110415')?.state === 'using'
  let disabledOperationButton = []
  if (NotAllowCreateApplication) {
    disabledOperationButton.push('ACTION_ALTER')
  }
  if (NotAllowCreateApplication && flow?.formType === 'requisition') {
    disabledOperationButton.push('ACTION_COPY')
  }
  
  const res = await getFlowButtons({
    flowId,
    deviceType: 'DESKTOP',
    platform: window.__PLANTFORM__,
  })
  console.log('kkkkkk+++=====res',res)
  
  // 根据 disabledOperationButton 数组过滤按钮
  if (res && res?.value?.buttons && disabledOperationButton && disabledOperationButton.length > 0) {
    res.value.buttons = res.value.buttons.filter(button => !disabledOperationButton.includes(button.id))
  }
  
  return res.value.buttons.map(button => ({
    ...pick(button, ['action', 'name', 'category', 'theme']),
    title: button.describe,
  }))
}

const BILL_DELETED_CODE = 412
export const getFlowInfoById = async (flowId: string, privilegeId?: string) => {
  return new Promise((resolve, reject) => {
    api.invokeService('@bills:get:flow-info', { id: flowId, privilegeId }, (data: never, err: any) => {
      if (err && err.errorCode === BILL_DELETED_CODE) {
        api.invokeService('@bills:get:flow-info', { id: flowId, privilegeId, inRecycle: true })
          .then(resolve)
          .catch(reject)
      } else {
        resolve(data)
      }
    })
  })
}
