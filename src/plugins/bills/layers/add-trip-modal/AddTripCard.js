import { app } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './AddTripCard.module.less'
import React from 'react'
import { Radio, Form, Tooltip } from 'antd'
import { DatePicker, Checkbox, Space, Button, Alert } from '@hose/eui'
import moment from 'moment'
import { getWeek } from '../../../../lib/lib-util'
import { app as api } from '@ekuaibao/whispered'
import { forIn, concat, sortBy } from 'lodash'
import { OutlinedDirectionSwitch } from '@hose/eui-icons'
import TripScene from './TripScene'
import { getBoolVariation } from '../../../../lib/featbit'

const CALENDER_ICON = require('../../images/calender-icon.svg')

const CityPickerNew = app.require('@components/dynamic/CityPickerComponentNew')
const AITripFeat = getBoolVariation('fkrd-4299-ai-trip-planning');

const white = ['FLIGHT', 'HOTEL', 'TRAIN', 'TAXI', 'FOOD', 'COMMON']

const LABEL_MAP_CITY = {
  'TAXI': i18n.get('用车城市'),
  'HOTEL': i18n.get('住宿城市'),
  'FOOD': i18n.get('用餐城市')
}

const LABEL_MAP_DATE = {
  'FLIGHT': i18n.get('出发日期'),
  'TRAIN': i18n.get('出发日期'),
  'TAXI': i18n.get('用车日期'),
  'HOTEL': i18n.get('住宿日期'),
  'FOOD': i18n.get('用餐日期')
}


/**
 * @description 添加行程组件(包括单程和往返)
 * @param {Boolean} isSingle: true-添加单程 false-添加往返
 */
const DATEFORMAT = 'YYYY.MM.DD'
const TRAVELMAP = {
  FLIGHT: ['FLIGHTDEPARTURE', 'FLIGHTDESTINATION'],
  TRAIN: ['TRAINDEPARTURE', 'TRAINDESTINATION'],
  HOTEL: ['HOTELACCOMMODATION'],
  TAXI: ['TAXICITY'],
  FOOD: ['FOODCITY'],
  COMMON: ['COMMONCITY'],
}

@EnhanceConnect(state => ({
  travelManagementConfig: state['@tpp-v2'].travelManagementConfig
}))
class AddTripCard extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      tripForm: {},
      tripType: '',
      dataLinkTemplateId: '',
      daysNumber: 0,
      isDateRange: false,
      templateList: [],
      travelType: ['', ''],
      travelerId: '',
      money: 0,
      tipShow: false,
      tipShowm: false,
      optionalVal: props?.fieldOpt?.optional || false,
      travelCheckList: [],
      AITripContextDetail: props?.AITripContextDetail || {},
      mallConfigInfo: '',
    }
  }

  componentWillReceiveProps(nextPros) {
    if (nextPros.template?.length !== this.props.template?.length) {
      setTimeout(() => {
        this.init()
      }, 200)
    }
    if (nextPros.value != this.props.value) {
      this.setState({
        money: nextPros.value?.money
      })
      this.updateForm(nextPros.value)
    }
    let { dataLinkTemplateId } = this.state
    if (this.props?.fieldOpt?.optional !== nextPros?.fieldOpt?.optional) {
      this.setState({
        optionalVal: nextPros?.fieldOpt?.optional
      }, () => {
        // 当必填属性optional有变动时，调一次tab切换
        this.handleUpdateType2(dataLinkTemplateId)
      })
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.on('set:trip:value', this.setTripValue)
    bus.on('set:tripdatalink:traveler:id', this.setTravelerId)
    this.getTravelCheckStatus()
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('set:trip:value', this.setTripValue)
    bus.un('set:tripdatalink:traveler:id', this.setTravelerId)
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.tripType !== this.state.tripType) {
      getBoolVariation('fkrd-4760-travel-plan-show-mall-config') && this.getMallTripConfig(this.state.tripType)
    }
  }

  getTravelCheckStatus = async () => {
    const result = this.getShowLabel()
    app
      .invokeService('@itinerary-manage:get:travelManagementConfig', { type: 'travelConsumptionConfig' })
      .then(({ value }) => {
        if (value) {
          const { contextDetail } = value
          const newTravelCheckList = []
          if (contextDetail?.hotelTripConfig && result.hotel) newTravelCheckList.push('hotel')
          if (contextDetail?.foodTripConfig && result.food) newTravelCheckList.push('food')
          if (contextDetail?.taxiTripConfig && result.taxi) newTravelCheckList.push('taxi')
          this.setState({ travelCheckList: newTravelCheckList })
        } else {
          this.setState({ travelCheckList: [] })
        }
      })
  }

  getMallTripConfig = async (type) => {
    app.invokeService('@bills:get:mall:trip:config', { travelType: type }).then((res) => {
      const typeMap = {
        FLIGHT: "机票",
        TRAIN: "火车票",
        HOTEL: "酒店",
        TAXI: "用车",
        FOOD: "餐饮",
      };

      const nameMap = {
        FLIGHT: "飞机",
        TRAIN: "火车",
        HOTEL: "酒店",
        TAXI: "用车",
        FOOD: "餐饮",
      };

      const disTypes = Object.values(res).map(type => `【${typeMap[type] || type}】`)

      if(Object.keys(res).length > 1) {
        this.setState({ mallConfigInfo: '根据企业配置，【' + nameMap[type] + '】行程可以在合思商城订购' + disTypes })
      }else{
        this.setState({ mallConfigInfo: '' })
      }
    })
  }

  setTripValue = async () => {
    const { isDateRange } = this.state
    if (isDateRange) {
      this.props.form.setFieldsValue({
        startCity: ''
      })
    } else {
      this.props.form.setFieldsValue({
        startCity: '',
        endCity: ''
      })
    }
    let trip = await this.props.bus.getFieldsValue()
    trip = Object.assign([], trip['u_行程规划'])
    forIn(trip[0].dataLinkForm, (value, key) => {
      if (key.includes('出发地') || key.includes('目的地') || key.includes('住宿地')) {
        trip[0].dataLinkForm[key] = ''
      }
    })
    this.props.bus.setFieldsValue({
      ['u_行程规划']: trip
    })
  }
  // 费用明细切换出行人更新过滤条件
  setTravelerId = travelerId => {
    this.setState({
      travelerId
    })
  }
  async componentDidMount() {
    this.props.onRef && this.props.onRef(this)
    this.init()
  }

  init = async () => {
    const { value = {}, isSingle, template } = this.props
    let { tripType, dataLinkTemplateId } = this.state
    if (template.length) {
      const templateList = isSingle ? template : template.filter(i => (i?.isDefault && i?.isReturn) || (i?.isReturn))
      tripType = tripType || value.tripType || templateList[0].entity.type
      let { travelerId, travelType } = await this.getCityFilter(tripType)
      dataLinkTemplateId = dataLinkTemplateId || value.dataLinkTemplateId || templateList[0].templateId
      this.updateForm(value)

      let config = this.getConfig(dataLinkTemplateId)
      let type = config.entity.type
      if ((type == 'FLIGHT' || type == 'TRAIN') && !config.isCityRange) {
        travelType = [type]
      }
      this.setState({
        travelerId,
        travelType,
        templateList,
        dataLinkTemplateId,
        tripType,
        isDateRange: config?.isDateRange || false,
        config,
        money: value?.money || 0
      })
      this.props.isSingleDataLink && this.getFieldsValue()
    }
  }
  getConfig = id => {
    let { template } = this.props
    const { AITripContextDetail } = this.state
    if (template?.length) {
      let config = template.find(i => i?.templateId == id)
      if (AITripContextDetail?.enable && !AITripContextDetail?.travelFlex) {
        if (config?.entity?.type === 'HOTEL') {
          config.isCityRange = false
          config.isDateRange = true
          config.multiple = false
        }
        if (config?.entity?.type === 'TAXI' || config?.entity?.type === 'FOOD') {
          config.isCityRange = false
          config.isDateRange = true
          config.multiple = true
        }
        if (config?.entity?.type === 'FLIGHT' || config?.entity?.type === 'TRAIN') {
          config.isCityRange = true
          config.isDateRange = false
          config.multiple = false
        }
      } else if (AITripContextDetail?.travelFlex) {
        config.multiple = true
        config.isDateRange = true
        if (config?.entity?.type === 'HOTEL') {
          config.isCityRange = false
        }
        if (config?.entity?.type === 'TAXI' || config?.entity?.type === 'FOOD') {
          config.isCityRange = false
        }
        if (config?.entity?.type === 'FLIGHT' || config?.entity?.type === 'TRAIN') {
          config.isCityRange = true
        }
      }

      return config
    }
    return null
  }

  updateForm = value => {
    let { tripTool } = this.props
    //是否勾选了【不设置默认出行时间】
    let notSetDefaultDate = false
    notSetDefaultDate = tripTool?.props?.field?.notSetDefaultDate
    let startTime
    let endTime
    if (!notSetDefaultDate) {
      startTime = (value?.startTime && moment(value?.startTime)) || moment(new Date())
      endTime = (value?.endTime && moment(value?.endTime)) || moment(startTime).add(1, 'days')
    } else {
      startTime = value?.startTime && moment(value?.startTime)
      endTime = value?.endTime && moment(value?.endTime)
    }
    const startCity = value.startCity
    const endCity = value.endCity || ''
    const startString = startTime && moment(startTime).format(`YYYY.MM.DD`)
    const endString = endTime && moment(endTime).format(`YYYY.MM.DD`)
    let daysNumber
    if (startString && endString) {
      daysNumber = moment(endString).diff(moment(startString), 'days')
    }
    this.setState({
      daysNumber,
      tripForm: { startTime, endTime, startCity, endCity, sceneList: value.sceneList }
    })
  }

  handleUpdateType = async e => {
    const value = Object.assign({}, this.props.form.getFieldsValue())
    api.emit('clear:city:picker')
    const { rangedate } = value
    if (rangedate) {
      value.startTime = rangedate[0]
      value.endTime = rangedate[1]
    }
    this.updateForm(value)


    this.props.form.resetFields()
    // 切换行程类型需重新获取当前类型下的城市
    this.props.form.setFieldsValue({
      startCity: '',
      endCity: '',
      sceneList: undefined
    })
    const dataLinkTemplateId = e.target.value
    const config = this.getConfig(dataLinkTemplateId)
    const tripType = config?.entity?.type
    let { travelerId, travelType } = await this.getCityFilter(tripType)
    let type = config.entity.type
    if ((type == 'FLIGHT' || type == 'TRAIN') && !config.isCityRange) {
      travelType = [type]
    }
    this.setState({
      travelerId,
      travelType,
      tripType,
      dataLinkTemplateId,
      config,
      isDateRange: config?.isDateRange || false,
      money: 0
    })
    this.props.isSingleDataLink && this.saveTrip()
  }

  // 当必填属性optional有变动时，调一次tab切换
  handleUpdateType2 = async dataLinkTemplateId => {
    const value = Object.assign({}, this.props.form.getFieldsValue())
    const { rangedate } = value
    if (rangedate) {
      value.startTime = rangedate[0]
      value.endTime = rangedate[1]
    }
    this.updateForm(value)
    // 切换行程类型需重新获取当前类型下的城市
    this.props.form.setFieldsValue({
      startCity: '',
      endCity: ''
    })
    // const dataLinkTemplateId = e.target.value
    const config = this.getConfig(dataLinkTemplateId)

    const tripType = config?.entity?.type
    let { travelerId, travelType } = await this.getCityFilter(tripType)
    let type = config.entity.type
    if ((type == 'FLIGHT' || type == 'TRAIN') && !config.isCityRange) {
      travelType = [type]
    }
    this.setState({
      travelerId,
      travelType,
      tripType,
      dataLinkTemplateId,
      config,
      isDateRange: config?.isDateRange || false,
      money: 0
    })
    this.props.isSingleDataLink && this.saveTrip()
  }

  handleUpdateDate = dates => {
    let value = this.props.form.getFieldsValue()
    if (dates[0]) {
      value.startTime = dates[0]
    }
    if (dates[1]) {
      value.endTime = dates[1]
    }
    value = Object.assign(this.state.tripForm, value)
    this.updateForm(value)
    this.props.isSingleDataLink && this.saveTrip()
  }

  handleUpdateScene = () => {
    this.props.isSingleDataLink && this.saveTrip()
  }

  handleChangeCity = () => {
    if (!this.props.editable) return
    const { startCity, endCity } = this.props.form.getFieldsValue(['startCity', 'endCity'])
    this.props.form.setFieldsValue({
      startCity: endCity,
      endCity: startCity
    })
    this.handleUpdateCity()
  }

  handleUpdateCity = () => {
    this.setState(
      {
        money: 0
      },
      () => {
        this.props.isSingleDataLink && this.saveTrip()
      }
    )
  }

  handleSaveTrip = bool => {
    this.props.form.validateFields(errors => {
      if (!errors) {
        bool && this.saveTrip()
      }
    })
  }

  saveTrip = () => {
    setTimeout(() => {
      this.props.form.validateFields(() => {
        this.getFieldsValue()
      })
    }, 100)
  }

  getArrBy = (a, b) => {
    let arra = sortBy(Array.isArray(a) ? a : [a])
    let arrb = sortBy(b)
    return arra.toString() == arrb.toString()
  }
  getCheckReturn = async () => {
    const { travelManagementConfig } = this.props
    const billValue = await this.props.bus.getFieldsValue()
    const values = this.props.form.getFieldsValue()
    const { startCity, endCity } = values
    let stCity = JSON.parse(startCity)
    let edCity = JSON.parse(endCity || startCity)
    const { key: startCityId, type: startCityType } = stCity[0]
    const { key: endCityId, type: endCityType } = edCity[0]
    let checkReturn = {
      returnHotel: true,
      returnFood: true,
      returnStartTaxi: true,
      returnEndTaxi: true,
      returnStart: true,
      returnBack: true
    }
    const { travelerId, tripType } = this.state
    if (tripType === 'CUSTOM') {
      return checkReturn
    }
    // if () {

    const params = {
      travelerId,
      cityIdAndTravelType: [
        {
          cityId: stCity.map(i => i.key),
          type: startCityType,
          travelCityTypes: concat(TRAVELMAP['TAXI'], TRAVELMAP[tripType][1])
        },
        {
          cityId: edCity.map(i => i.key),
          type: endCityType,
          travelCityTypes: concat(TRAVELMAP['HOTEL'], TRAVELMAP['FOOD'], TRAVELMAP['TAXI'], TRAVELMAP[tripType][0])
        }
      ]
    }
    let { items = [] } = await app.invokeService('@bills:check:return:city', params)
    const returnHotel = items.find(i => i.travelCityType === TRAVELMAP['HOTEL'][0])
    const returnFood = items.find(i => i.travelCityType === TRAVELMAP['FOOD'][0])
    const returnStartTaxi = items.find(
      i =>
        i.travelCityType === TRAVELMAP['TAXI'][0] &&
        this.getArrBy(
          i.cityId,
          stCity.map(i => i.key)
        )
    )
    const returnEndTaxi = items.find(
      i =>
        i.travelCityType === TRAVELMAP['TAXI'][0] &&
        this.getArrBy(
          i.cityId,
          edCity.map(i => i.key)
        )
    )
    const returnStart = items.find(i => i.travelCityType === TRAVELMAP[tripType][0])
    const returnBack = items.find(i => i.travelCityType === TRAVELMAP[tripType][1])
    checkReturn = {
      returnHotel: returnHotel?.canReturn,
      returnFood: returnFood?.canReturn,
      returnStartTaxi: returnStartTaxi?.canReturn,
      returnEndTaxi: returnEndTaxi?.canReturn,
      returnStart: returnStart?.canReturn,
      returnBack: returnBack?.canReturn
    }
    // }
    return checkReturn
  }

  getFieldsValue = async () => {
    const values = this.props.form.getFieldsValue()
    const { tripType, templateList, dataLinkTemplateId, money } = this.state
    const { isSingle, originalTripValue, isDetail, onCheckDateVailable } = this.props
    const { startTime, endTime, startCity, endCity } = this.formatResult(values)
    let data = []

    if (!isSingle) {
      let entity = this.props.tripTool.getEntityId({
        dataLinkTemplateId,
        tripType
      })
      const checkReturn = await this.getCheckReturn()
      let dataLinkId = originalTripValue?.dataLinkId ?? null
      const curTemplate = templateList.find(i => i.templateId === originalTripValue?.dataLinkTemplateId)
      // 行程申请撤回时，修改行程类型为“创建”，修改其他为“更新”
      if (curTemplate && tripType !== curTemplate.entity.type) {
        dataLinkId = null
      }

      data = [{ tripType, startCity, endCity, startTime, endTime: entity.isDateRange ? endTime : startTime, dataLinkTemplateId, dataLinkId, sceneList: values.sceneList }]
      if (values.hotel && checkReturn.returnHotel) {
        data.push({ tripType: 'HOTEL', startCity: endCity || startCity, startTime, endTime, sceneList: values.hotelSceneList })
      }
      if (values.food && checkReturn.returnFood) {
        data.push({ tripType: 'FOOD', startCity: endCity || startCity, startTime, endTime, sceneList: values.foodSceneList })
      }
      if (values.taxi) {
        if (checkReturn.returnStartTaxi) {
          data.push({ tripType: 'TAXI', startCity: startCity, startTime, endTime, sceneList: values.taxiSceneList })
        }
        if (checkReturn.returnEndTaxi && endCity) {
          data.push({ tripType: 'TAXI', startCity: endCity || startCity, startTime, endTime, sceneList: values.taxiSceneList })
        }
      }
      if (checkReturn.returnStart && checkReturn.returnBack) {
        data.push({
          dataLinkTemplateId,
          dataLinkId,
          tripType,
          startCity: endCity || startCity,
          endCity: startCity,
          startTime: entity.isDateRange ? startTime : endTime,
          endTime: endTime,
          sceneList: values.sceneList
        })
      }
    } else {
      let dataLinkId = originalTripValue?.dataLinkId ?? null
      const curTemplate = templateList.find(i => i.templateId === originalTripValue?.dataLinkTemplateId)
      // 行程申请撤回时，修改行程类型为“创建”，修改其他为“更新”
      if (curTemplate && tripType !== curTemplate.entity.type || (!curTemplate && originalTripValue?.dataLinkTemplateId)) {
        dataLinkId = null
      }
      console.log('1===>', values.sceneList)
      data = [
        {
          dataLinkTemplateId,
          dataLinkId,
          tripType,
          startTime,
          endTime,
          startCity,
          endCity,
          money: money,
          sceneList: values.sceneList
        }
      ]
    }

    if (!isDetail && onCheckDateVailable) {
      const error = await onCheckDateVailable({ startTime, endTime, dataLinkId: originalTripValue?.dataLinkId, dataLinkTemplateId })
      if (error !== 'ERROR') {
        this.props.onOk && this.props.onOk(data)
      }
    } else {
      this.props.onOk && this.props.onOk(data)
    }
  }


  formatResult = values => {
    let { startTime, endTime, rangedate } = values
    const formatDate = date => {
      return parseInt(moment(date).format('x'))
    }
    if (startTime) {
      values.startTime = formatDate(startTime.startOf('day'))
    }
    if (endTime) {
      values.endTime = formatDate(endTime.startOf('day'))
    }
    if (rangedate) {
      values.startTime = formatDate(rangedate[0].startOf('day'))
      values.endTime = formatDate(rangedate[1].endOf('day'))
    }
    return values
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  getCityFilter = async tripType => {
    let travelerId = ''
    let travelType = TRAVELMAP[tripType]
    if (app.has('get:bills:value')) {
      const billsValue = await app.invoke('get:bills:value')
      const value = await this.props.bus.getFieldsValue()
      const { travelers } = value

      if (!travelers || travelers.length === 0) {
        travelerId = billsValue.values.submitterId.id
      } else if (travelers.length === 1) {
        travelerId = travelers[0].id
      } else {
        // travelType = ''
      }
    }
    if (!white.includes(tripType)) {
      travelerId = undefined
      travelType = undefined
    }

    return {
      travelerId,
      travelType
    }
  }

  getDateOptions = (type) => {
    const { tripForm } = this.state
    let obj = {
      rules: [{ required: true, message: i18n.get('请选择日期!') }],
    }
    if (tripForm?.startTime) {
      if (type === 'start') {
        obj.initialValue = tripForm.startTime
      } else if (type === 'end') {
        obj.initialValue = tripForm.endTime
      } else if (type === 'range') {
        obj.initialValue = [tripForm.startTime, tripForm.endTime]
      }
    }
    return obj
  }

  getTravelCheckStatusOptions = type => {
    const { travelCheckList } = this.state

    let obj = { valuePropName: 'checked', initialValue: false }
    switch (type) {
      case 'hotel':
        if (travelCheckList.includes(type)) obj.initialValue = true
        return obj
      case 'food':
        if (travelCheckList.includes(type)) obj.initialValue = true
        return obj
      case 'taxi':
        if (travelCheckList.includes(type)) obj.initialValue = true
        return obj
      default:
        return obj
    }
  }

  renderTripDate = () => {
    const { isSingle, form, editable, dateEditable } = this.props
    const { getFieldDecorator } = form
    const { tripType, isDateRange, daysNumber, tripForm } = this.state
    let totalDate = ''
    if (daysNumber) {
      totalDate =
        tripType === 'HOTEL'
          ? i18n.get(`共{__k0}晚`, { __k0: daysNumber })
          : i18n.get(`共{__k0}天`, { __k0: daysNumber + 1 })
    }
    return (
      <div>
        {!isDateRange && isSingle && (
          <>
            <div className={styles.label_wrapper} style={{ marginTop: 16 }}>
              <div className="label">
                {LABEL_MAP_DATE[tripType] || i18n.get('日期')}
                <span className='required'>*</span>
              </div>
            </div>
            <div className={isSingle ? '' : 'date-item'}>
              <div className={styles["travel-date-picker-single"]}>
                <div className={`travel-date-picker-overwrite ${editable ? '' : 'disabled'}`}>
                  {
                    tripForm.startTime ?
                      <>
                        {moment(tripForm.startTime).format(DATEFORMAT)} {getWeek(tripForm.startTime)}
                      </>
                      : <span className="no-data-dec">{i18n.get('选择日期')}</span>
                  }
                </div>
                <Form.Item>
                  {getFieldDecorator('startTime', this.getDateOptions('start'))(
                    <DatePicker
                      className="travel-date-picker-custom"
                      disabled={!editable}
                      allowClear={false}
                      placeholder={i18n.get('请选择日期')}
                      onChange={date => this.handleUpdateDate([date, null])}
                    />
                  )}
                </Form.Item>
              </div>
            </div>
          </>
        )}
        {(isDateRange || !isSingle) && (
          <>
            <div className={styles.label_wrapper}>
              <div className="label" style={{ marginTop: 16 }}>
                {LABEL_MAP_DATE[tripType] || i18n.get('日期')}
                <span className='required'>*</span>
              </div>
            </div>
            <div className={styles['travel-date-picker-single']}>
              <div className={`travel-date-picker-overwrite ${editable || dateEditable ? '' : 'disabled'}`}>
                {
                  tripForm.startTime ?
                    <>
                      <span>{`${moment(tripForm.startTime)
                      ?.locale(i18n?.currentLocale)
                      ?.format('YYYY.MM.DD dddd')} ~ ${moment(tripForm.endTime)
                      ?.locale(i18n?.currentLocale)
                      ?.format('YYYY.MM.DD dddd')}`}</span>
                      <span className="date-line">|</span>
                      <span>{totalDate}</span>
                      <img src={CALENDER_ICON} alt="" />
                    </> : <span className="no-data-dec">{i18n.get('选择日期范围')}</span>
                }
              </div>
              <Form.Item>
                {getFieldDecorator('rangedate', this.getDateOptions('range'))(
                  <DatePicker.RangePicker
                    className="travel-date-picker-custom"
                    disabled={!editable && !dateEditable}
                    allowClear={false}
                    placeholder={[i18n.get('入住日期'), i18n.get('离店日期')]}
                    onChange={this.handleUpdateDate}
                  />
                )}
              </Form.Item>
            </div>
          </>
        )}
      </div>
    )
  }
  tipRender = (value, key) => {
    let cityValue = []
    if (value) {
      cityValue = JSON.parse(value)
    }
    return <div onMouseLeave={() => this.handleOut(key)}>{cityValue.map(i => i.label).join(', ')}</div>
  }
  handleOut = key => {
    let obj = {}
    obj[key] = false
    this.setState(obj)
  }
  handleEnter = (key, bool, arr) => {
    if (bool && arr?.length > 1) {
      let obj = {}
      obj[key] = true
      this.setState(obj)
    }
  }

  getRenderComponent = () => {
    return CityPickerNew
  }

  getLabelConfig = (config) => {
    return {
      departureCityLabel: undefined,
      destinationCityLabel: undefined,
      departurePlaceholder: i18n.get(config?.label || '城市'),
      destinationPlaceholder: i18n.get('到达：').slice(0, -1)
    }
  }

  renderTripCity = () => {
    const { getFieldDecorator } = this.props.form
    const { isDateRange, tripForm, tripType, travelerId, travelType, config, tipShow, tipShowm, optionalVal, AITripContextDetail } = this.state
    const { editable, isDetail = false, fieldOpt = {} } = this.props
    const isRequired = isDetail ? !fieldOpt?.optional : true
    if (!tripType) return null
    if (!editable) {
      return this.renderTripCityShow()
    }
    const CityPickerComponent = this.getRenderComponent();
    const labels = this.getLabelConfig(config);

    let isCityRange = config?.isCityRange

    return (
      <>
        <div className={styles.label_wrapper}>
          <div className="label" style={{ width: 258 }}>
            {isCityRange ? i18n.get('出发城市') : (LABEL_MAP_CITY[tripType] || i18n.get('城市'))}
            {isRequired && <span className='required'>*</span>}
          </div>
          {isCityRange && <div className="label" style={{ flex: 1 }}>
            {i18n.get('到达城市')}
            {isRequired && <span className='required'>*</span>}
          </div>}
        </div>
        <div
          className={`city-item ${!isCityRange
            ? isDetail
              ? !editable
                ? 'city-item-stay-type-no'
                : 'city-item-stay-type'
              : 'city-item-stay'
            : ''}`}
        >
          <Form.Item label={labels.departureCityLabel}>
            {getFieldDecorator('startCity', {
              rules: [{ required: (isDetail ? !fieldOpt?.optional : true), message: i18n.get(config?.requireText) }, {
                validator: (rule, value, callback) => {
                  if (isDetail && !fieldOpt?.optional && value === '[]') {
                    return callback(i18n.get(config?.requireText))
                  }
                  if (!isDetail && value === '[]') {
                    return callback(i18n.get(config?.requireText))
                  }
                  return callback()
                }
              }],
              initialValue: tripForm.startCity
            })(
              <CityPickerComponent
                size='large'
                filters={{
                  travelerId,
                  travelType: travelType?.length > 0 ? travelType[0] : undefined
                }}
                showRecently={!config?.isDefault}
                otherStyle={{ width: isCityRange ? '217px' : '472px' }}
                isAuto={!editable}
                multiple={config.multiple}
                onChange={this.handleUpdateCity}
                showType="trip"
                maxTagCount={isCityRange ? 1 : 3}
                placeholder={i18n.get(config.multiple ? '请选择城市(可多选)' : '请选择城市')}
                maxSelectCount={config.multiple ? 20 : 0}
                className={config.multiple ? 'city-picker-trip-mult' : null}
                needCheckDisabled
                ingoreBlackList={AITripContextDetail?.enable}
              />
            )}
          </Form.Item>
          {isCityRange && (
            <>
              <div className={`city-change ${editable ? '' : 'disabled'}`} onClick={this.handleChangeCity}>
                <OutlinedDirectionSwitch style={{ fontSize: 18 }} />
              </div>
              <Form.Item label={labels.destinationCityLabel}>
                {getFieldDecorator('endCity', {
                  rules: [{ required: (isDetail ? !fieldOpt?.optional : true), message: i18n.get('到达城市不能为空!') }, {
                    validator: (rule, value, callback) => {
                      if (isDetail && !fieldOpt?.optional && value === '[]') {
                        return callback(i18n.get('到达城市不能为空!'))
                      }
                      if (!isDetail && value === '[]') {
                        return callback(i18n.get('到达城市不能为空'))
                      }
                      return callback()
                    }
                  }],
                  initialValue: tripForm.endCity
                })(
                  <CityPickerComponent
                    multiple={config.multiple}
                    filters={{
                      travelerId,
                      travelType: travelType?.length > 1 ? travelType[1] : undefined
                    }}
                    showRecently={!config?.isDefault}
                    otherStyle={{ width: '217px' }}
                    isAuto={!editable}
                    onChange={this.handleUpdateCity}
                    showType="trip"
                    placeholder={i18n.get(config.multiple ? '请选择城市(可多选)' : '请选择城市')}
                    maxTagCount={1}
                    maxSelectCount={config.multiple ? 20 : 0}
                    className={config.multiple ? 'city-picker-trip-mult' : null}
                    size='large'
                    needCheckDisabled
                    ingoreBlackList={AITripContextDetail?.enable}
                  />
                )}
              </Form.Item>
            </>
          )}
        </div>
      </>

    )
  }
  renderTripCityShow = () => {
    const { getFieldDecorator } = this.props.form
    const { isDateRange, tripForm, tripType, travelerId, travelType, config, tipShow, tipShowm, optionalVal, AITripContextDetail } = this.state
    const { editable, isDetail = false, fieldOpt = {} } = this.props
    if (!tripType) return null
    const CityPickerComponent = this.getRenderComponent();
    const labels = this.getLabelConfig(config)
    const isRequired = isDetail ? !fieldOpt?.optional : true
    console.log('333==>', tripForm.endCity)
    let cityStartArr = []
    let cittEndArr = []
    try {
      cityStartArr = JSON.parse(tripForm?.startCity)
      cittEndArr = tripForm?.endCity ? JSON.parse(tripForm?.endCity) : []
    } catch (error) {

    }
    return (
      <>
        <div className={styles.label_wrapper}>
          <div className="label" style={{ width: 258 }}>
            {config?.isCityRange ? i18n.get('出发城市') : (LABEL_MAP_CITY[tripType] || i18n.get('城市'))}
            {isRequired && <span className='required'>*</span>}
          </div>
          {config?.isCityRange && <div className="label" style={{ flex: 1 }}>
            {i18n.get('到达城市')}
            {isRequired && <span className='required'>*</span>}
          </div>}
        </div>
        <div
          className={`city-item ${!config?.isCityRange
            ? isDetail
              ? !editable
                ? 'city-item-stay-type-no'
                : 'city-item-stay-type'
              : 'city-item-stay'
            : ''
            }`}
        >
          <Tooltip
            title={this.tipRender(tripForm.startCity, 'tipShow')}
            overlayClassName="trip-card-box"
            visible={tipShow}
          >
            <Form.Item>
              {getFieldDecorator('startCity', {
                rules: [{ required: (isDetail ? !fieldOpt?.optional : true), message: i18n.get(config?.requireText) }, {
                  validator: (rule, value, callback) => {
                    if (isDetail && !fieldOpt?.optional && value === '[]') {
                      return callback(i18n.get(config?.requireText))
                    }
                    if (!isDetail && value === '[]') {
                      return callback(i18n.get(config?.requireText))
                    }
                    return callback()
                  }
                }],
                initialValue: tripForm.startCity
              })(
                <span
                  onMouseLeave={() => this.handleOut('tipShow')}
                  onMouseEnter={() => this.handleEnter('tipShow', !editable, tripForm.startCity)}
                >
                  <CityPickerComponent
                    size='large'
                    filters={{
                      travelerId,
                      travelType: travelType?.length > 0 ? travelType[0] : undefined
                    }}
                    value={tripForm.startCity}
                    showRecently={!config?.isDefault}
                    otherStyle={{ width: config?.isCityRange ? '217px' : '472px' }}
                    isAuto={!editable}
                    multiple={config.multiple || cityStartArr?.length > 1}
                    onChange={this.handleUpdateCity}
                    showType="trip"
                    maxTagCount={config?.isCityRange ? 1 : 3}
                    maxSelectCount={config.multiple ? 20 : 0}
                    className={config.multiple ? 'city-picker-trip-mult' : null}
                    needCheckDisabled
                    ingoreBlackList={AITripContextDetail?.enable}
                  />
                </span>
              )}
            </Form.Item>
          </Tooltip>
          {config?.isCityRange && (
            <>
              <div className={`city-change ${editable ? '' : 'disabled'}`} onClick={this.handleChangeCity}>
                <OutlinedDirectionSwitch style={{ fontSize: 18 }} />
              </div>
              <Tooltip
                title={this.tipRender(tripForm.endCity, 'tipShowm')}
                visible={tipShowm}
                overlayClassName="trip-card-box"
              >
                <Form.Item>
                  {getFieldDecorator('endCity', {
                    rules: [{ required: (isDetail && fieldOpt?.optional), message: i18n.get('到达城市不能为空!') }, {
                      validator: (rule, value, callback) => {
                        if (isDetail && !fieldOpt?.optional && value === '[]') {
                          return callback(i18n.get('到达城市不能为空!'))
                        }
                        return callback()
                      }
                    }],
                    initialValue: tripForm.endCity
                  })(
                    <span
                      onMouseLeave={() => this.handleOut('tipShowm')}
                      onMouseEnter={() => this.handleEnter('tipShowm', !editable, tripForm.endCity)}
                    >
                      <CityPickerComponent
                        size='large'
                        value={tripForm.endCity}
                        multiple={config.multiple || cittEndArr?.length > 1}
                        filters={{
                          travelerId,
                          travelType: travelType?.length > 1 ? travelType[1] : undefined
                        }}
                        showRecently={!config?.isDefault}
                        otherStyle={{ width: '217px' }}
                        isAuto={!editable}
                        onChange={this.handleUpdateCity}
                        showType="trip"
                        maxTagCount={1}
                        maxSelectCount={config.multiple ? 20 : 0}
                        className={config.multiple ? 'city-picker-trip-mult' : null}
                        needCheckDisabled
                      />
                    </span>
                  )}
                </Form.Item>
              </Tooltip>
            </>
          )}
        </div>
      </>
    )
  }

  getShowLabel = () => {
    let result = { hotel: false, taxi: false, food: false }
    let { template = [] } = this.props
    if (template.find(i => i?.entity?.active && i?.entity?.type == 'HOTEL')) {
      result.hotel = true
    }
    if (template.find(i => i?.entity?.active && i?.entity?.type == 'TAXI')) {
      result.taxi = true
    }
    if (template.find(i => i?.entity?.active && i?.entity?.type == 'FOOD')) {
      result.food = true
    }
    return result
  }
  handleOpen = e => {
    let { tripForm, tripType } = this.state
    if (tripType == 'FLIGHT' || tripType == 'TRAIN') {
      if (!tripForm.startCity) {
        return
      }
    }
    if (tripType == 'HOTEL' && !tripForm.startCity) {
      return
    }
    this.props?.handleOpen(e, { ...tripForm, tripType }, 0)
  }
  renderMoney = (trip, money) => {
    const OPENLIST = ['FLIGHT', 'HOTEL', 'TRAIN']
    let { showMoney, showButton = false } = this.props
    if (!OPENLIST.includes(trip?.tripType) || !showMoney) {
      return null
    }
    return (
      <div className="info-money">
        {showButton && (
          <Button style={{ marginTop: 4 }} size="small" category='text' theme="highlight" onClick={e => this.handleOpen(e)}>{i18n.get(money > 0 ? '重新获取' : '获取报价')}</Button>
        )}
        {money ? (
          <>
            {i18n.get('参考报价:{money}元/', { money })}
            {i18n.get(trip?.tripType == 'HOTEL' ? '晚' : '张')}
          </>
        ) : null}
      </div>
    )
  }

  renderOtherScene = () => {
    const { getFieldDecorator, getFieldValue } = this.props.form
    const { isDetail, fieldOpt, isSingle, bus, template, editable } = this.props
    const { tripForm, config } = this.state
    if (isSingle) return null
    const foodChecked = getFieldValue('food')
    const taxiChecked = getFieldValue('taxi')
    const hotelChecked = getFieldValue('hotel')
    const foodConfig = template?.find(item => item?.entity?.type === 'FOOD')
    const taxtConfig = template?.find(item => item?.entity?.type === 'TAXI')
    const hotelConfig = template?.find(item => item?.entity?.type === 'HOTEL')
    console.log('foodConfig===>', foodConfig)
    return <Form.Item>
      {hotelChecked && getFieldDecorator('hotelSceneList', {
        rules: [{ required: hotelConfig?.travelSceneConfig?.sceneTypeField === 'open_required', message: i18n.get(`${config?.configName}场景不能为空!`) }]
      })(
        <TripScene
          editable={editable}
          travelSceneConfig={hotelConfig?.travelSceneConfig}
          billSpecification={this.props.billSpecification}
          travelType={{
            name: hotelConfig?.configName,
            type: hotelConfig?.entity?.type
          }}
          bus={bus}
          label={`${hotelConfig?.configName}场景`}
        />
      )}
      {foodChecked && getFieldDecorator('foodSceneList', {
        rules: [{ required: foodConfig?.travelSceneConfig?.sceneTypeField === 'open_required', message: i18n.get(`${config?.configName}场景不能为空!`) }]
      })(
        <TripScene
          editable={editable}
          travelSceneConfig={foodConfig?.travelSceneConfig}
          billSpecification={this.props.billSpecification}
          travelType={{
            name: foodConfig?.configName,
            type: foodConfig?.entity?.type
          }}
          bus={bus}
          label={`${foodConfig?.configName}场景`}
        />
      )}
      {taxiChecked && getFieldDecorator('taxiSceneList', {
        rules: [{ required: taxtConfig?.travelSceneConfig?.sceneTypeField === 'open_required', message: i18n.get(`${config?.configName}场景不能为空!`) }]
      })(
        <TripScene
          editable={editable}
          travelSceneConfig={taxtConfig?.travelSceneConfig}
          billSpecification={this.props.billSpecification}
          travelType={{
            name: taxtConfig?.configName,
            type: taxtConfig?.entity?.type
          }}
          bus={bus}
          label={`${taxtConfig?.configName}场景`}
        />
      )}
    </Form.Item>
  }

  renderScene = () => {
    const { getFieldDecorator } = this.props.form
    const { isSingle, bus, editable } = this.props
    const { tripForm, config } = this.state
    return <Form.Item>
      {getFieldDecorator('sceneList', {
        rules: [{ required: config?.travelSceneConfig?.sceneTypeField === 'open_required', message: i18n.get(`${config?.configName}场景不能为空!`) }],
        initialValue: tripForm.sceneList
      })(
        <TripScene
          editable={editable}
          travelSceneConfig={config?.travelSceneConfig}
          billSpecification={this.props.billSpecification}
          travelType={{
            name: config?.configName,
            type: config?.entity?.type
          }}
          bus={bus}
          onChange={date => this.handleUpdateScene()}
        />
      )}
    </Form.Item>
  }
  render() {
    const { tripType, templateList, dataLinkTemplateId, money, mallConfigInfo } = this.state
    let { isSingle, editable, value } = this.props
    const { getFieldDecorator } = this.props.form
    const result = this.getShowLabel()


    let renderTripTypeList = []
    const filterTripLists = editable
      ? []
      : templateList.filter(template => template.templateId === value.dataLinkTemplateId)
    renderTripTypeList = editable ? templateList : filterTripLists

    return (
      <div id="AddTripCard" className={styles['add-trip-warp']}>
      {mallConfigInfo && <Alert 
        style={{marginBottom: 16}}
        type="info"
			  message={mallConfigInfo}
		  />}
        <div className="add-trip">
          <div className="type-item">
            <Radio.Group buttonStyle="solid" onChange={this.handleUpdateType} value={dataLinkTemplateId}>
              {renderTripTypeList.map(i => (
                <Radio.Button disabled={!editable || !i?.entity?.active} value={i.templateId} key={i.templateId}>
                  {i?.isDefault ? i18n.get(i.entity.name) : i.entity.name}
                </Radio.Button>
              ))}
            </Radio.Group>
          </div>
          <Form>
            <div className="info-item">
              {this.renderTripCity()}
              {this.renderTripDate()}
              {this.renderScene()} 
            </div>
            {this.renderMoney(value, money)}
            {!isSingle && (
              <>
                <div className="info-content">{i18n.get('含其他消费')}</div>
                <Space direction='horizontal' style={{ height: 20 }}>
                  {result.hotel && (
                    <Form.Item>
                      {getFieldDecorator(
                        'hotel',
                        this.getTravelCheckStatusOptions('hotel')
                      )(<Checkbox>{i18n.get('住宿')}</Checkbox>)}
                    </Form.Item>
                  )}
                  {result.food && (
                    <Form.Item>
                      {getFieldDecorator(
                        'food',
                        this.getTravelCheckStatusOptions('food')
                      )(<Checkbox>{i18n.get('餐饮')}</Checkbox>)}
                    </Form.Item>
                  )}
                  {result.taxi && (
                    <Form.Item>
                      {getFieldDecorator(
                        'taxi',
                        this.getTravelCheckStatusOptions('taxi')
                      )(<Checkbox>{i18n.get('用车')}</Checkbox>)}
                    </Form.Item>
                  )}
                </Space>
              </>
            )}
            {this.renderOtherScene()}
          </Form>
          <p>
            {tripType == 'COMMON'
              ? i18n.get('支持通用审批的差旅平台可根据平台规则，审批通过后自由在多种行程中选择')
              : ' '}
          </p>
        </div>
      </div>
    )
  }
}
export default Form.create()(AddTripCard)
