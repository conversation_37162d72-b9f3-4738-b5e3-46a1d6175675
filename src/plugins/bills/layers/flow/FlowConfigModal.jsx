import styles from './FlowConfigModal.module.less'
import { Button, Tooltip, SkeletonParagraph } from '@hose/eui'
import React from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import MessageCenter from '@ekuaibao/messagecenter'

import { find, cloneDeep } from 'lodash'

import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import { logEvent } from '../../../../lib/logs'
import EditFlowPlanNode from './EditFlowPlanNode'
import { OutlinedTipsClose } from '@hose/eui-icons'

@EnhanceModal({
  title: '',
  footer: null,
  closable: false
})
export default class FlowConfigModal extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isLoading: true
    }
    this.selectCountersMap = {}
    this.messageCenter = new MessageCenter()
  }

  componentDidMount() {
    const { bus } = this.props
    bus && bus.on('closeFlowConfigModal', this.handleFlowConfigModalClose)
    this.fetchData()
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus && bus.un('closeFlowConfigModal', this.handleFlowConfigModalClose)
  }

  handleFlowConfigModalClose = () => {
    this.props.layer.emitCancel()
  }

  async fetchData() {
    const { overrideGetResult } = this.props
    const res = await this.props.fetch()
    const { data, dataSource } = res
    this.setState({
      staffs: data.staffs || [],
      isUrgent: false,
      isOther: data.urgent && data.urgent.urgentReasons && !data.urgent.urgentReasons.length,
      isUploading: false,
      sensitiveAttachment: data.sensitiveAttachment || [],
      data,
      dataSource,
      isLoading: false
    })
    overrideGetResult(this.getResult)
    logEvent('load_flow_config_modal', { title: '开始加载流程配置', data })
  }

  getResult = () => {
    return new Promise((resolve, reject) => {
      this.messageCenter
        .invoke('get:flow:plan:nodes')
        .then(values => {
          console.log(values)
          const { data } = this.state
          const { canUrgent } = data?.urgent || {}
          const { reason, otherReason, sensitive, sensitiveAttachment, isUrgent, nodes } = values
          let flowNodes = cloneDeep(data.flowNodes)
          Object.keys(nodes).forEach(key => {
            let node = find(flowNodes, node => node.id === key)
            const approved = nodes[key]
            node.approverId = (approved && approved.key) || undefined
            delete node.staff
          })
          this.handleSelectCounterSigners(flowNodes)
          const result = { flowNodes }
          if (canUrgent) {
            const urgentReasonStr = isUrgent ? otherReason : reason
            result.urgent = {
              isUrgent: isUrgent,
              urgentReason: isUrgent ? urgentReasonStr : ''
            }
          }
          if (sensitive) {
            result.sensitiveContent = sensitive
          }
          if (sensitiveAttachment && sensitiveAttachment.length) {
            result.sensitiveAttachment = sensitiveAttachment.map(attachment => {
              return {
                fileId: attachment.fileId,
                fileName: attachment.fileName,
                key: attachment.key
              }
            })
          }
          logEvent('get_result_flow_config_modal', { title: '获取流程配置弹窗结果', data: result })
          resolve(result)
        })
        .catch(err => {
          console.error(err)
          reject()
        })
    })
  }

  handleSelectCounterSigners(nodes = []) {
    nodes.forEach(line => {
      let selectSigners = this.selectCountersMap[line.id]
      if (selectSigners) {
        line.counterSigners = selectSigners
      }
      const approverId = getNodeValueByPath(line, 'approverId')
      if (!!approverId && typeof approverId === 'object') {
        line.approverId = approverId.id
      }
    })
  }

  handleModalClose = () => {
    const { dataSource } = this.state
    if (dataSource?.openFrom === 'flow-preview') {
      this.props.layer.emitCancel()
    } else {
      this.props.layer.emitOk('cancel')
    }
  }

  handleModalSave = () => {
    this.props.layer.emitOk()
  }

  getSelectCounters = (node, selectCounters) => {
    this.selectCountersMap[node.id] = selectCounters
  }

  handleUpload = isUploading => {
    this.setState({ isUploading })
  }

  fnFlowCannotSubmit = () => {
    const { flowNodes } = this.state.data
    const forbidNode = flowNodes?.find(el => {
      //skipWhenApproverNonMatched：匹配不到审批人时，是否可以跳过节点; true代表跳过节点，不禁止提交；
      const isEbot = el.type === 'ebot' || el.type === 'invoicingApplication' || el.type === 'aiApproval'
      const isCountersign = el.type === 'countersign' //是否是会签节点
      const isCarbonCopy = el.type === 'carbonCopy' // 抄送节点
      const isForbidNode =
        el.type !== 'recalculate' && !el.skipWhenApproverNonMatched && el.skippedType === 'NO_SKIPPED'
      const hasApprover =
        el.isAllStaffs ||
        el.staffIds?.length ||
        (isCountersign && el.counterSigners?.length) ||
        (isCountersign && el.counterSignersCandidate?.length)
      return !isEbot && isForbidNode && !hasApprover && !isCarbonCopy
    })
    return !!forbidNode
  }

  getSubmitBtn = disabled => {
    const { dataSource } = this.state
    if (dataSource?.openFrom === 'flow-preview') {
      return (
        <Button
          category="primary"
          data-testid="flow-configmodal-confirm"
          className="btn-ml"
          onClick={this.handleModalClose}
        >
          {i18n.get('确定')}
        </Button>
      )
    }
    return (
      <Button
        disabled={disabled}
        data-testid="flow-configmodal-submit"
        category="primary"
        className="btn-ml"
        onClick={this.handleModalSave}
      >
        {i18n.get('提交')}
      </Button>
    )
  }

  renderLoading() {
    return (
      <div className={styles['flow-config-modal']}>
        <div className="modal-header">
          <div className="title">{i18n.get('审批流程')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="scrollable">
          <SkeletonParagraph animated lineCount={12} />
        </div>
      </div>
    )
  }

  render() {
    if (this.state.isLoading) {
      return this.renderLoading()
    }

    const flowCannotSubmit = this.fnFlowCannotSubmit()
    const warningText = flowCannotSubmit ? i18n.get('存在匹配不到审批人则禁止提交的节点') : ''
    const { dataSource, data, onlySensitive, visible, isUploading } = this.state

    return (
      <div className={styles['flow-config-modal']}>
        <div className="modal-header">
          <div className="title">{i18n.get('审批流程')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="flow-config-modal-header">
          <div className="flex">
            {dataSource?.openFrom === 'flow-preview' && (
              <span style={{ color: '#999', fontSize: '14px' }}>{i18n.get('(审批流预览结果仅供参考)')}</span>
            )}
          </div>
        </div>

        <div className="scrollable" style={{ maxHeight: 'calc(100vh - 208px)' }}>
          <EditFlowPlanNode
            messageCenter={this.messageCenter}
            data={data}
            onlySensitive={onlySensitive}
            visible={visible}
            onUpload={this.handleUpload}
            onUpdateCountersMap={this.getSelectCounters}
          />
        </div>

        <div className="modal-footer">
          <Button category="secondary" className="btn-ml" data-testid="flow-config-cancel" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Tooltip arrowPointAtCenter placement="topRight" data-testid="flow-config-submit" title={warningText}>
            {this.getSubmitBtn(flowCannotSubmit || isUploading)}
          </Tooltip>
        </div>
      </div>
    )
  }
}
