import React, { useMemo } from 'react'
import styles from './OverflowText.module.less'
import { Tooltip } from '@hose/eui'

interface OverflowTextProps {
  text: string
  desc?: string
}

/**
 * 目前支持一行文本内容，超出部分显示省略号，鼠标悬浮时显示完整内容
 * */
export const OverflowText = ({ text, desc }: OverflowTextProps) => {
  const title = useMemo(() => {
    if(desc){
      return `${text}：${desc}`
    } else {
      return text
    }
  },[text, desc])
  return (
    <Tooltip title={title}>
      <span className={styles['overflow-text']} data-widget="tooltip">
        {text}
      </span>
    </Tooltip>
  )
}
