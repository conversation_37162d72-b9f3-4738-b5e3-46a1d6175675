import React from 'react'
import styles from './SelectDataLinkUpdateModal.module.less'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button, Icon } from 'antd'
import MessageCenter from '@ekuaibao/messagecenter'
import { showModal } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import { parseOptions } from '../../../../elements/DataLinkTable/tableUtil'
import { EKBDataGrid } from 'ekbc-datagrid/esm'
import { EuiDataGrid } from '../../../../components/eui_datagrid'
import { connect } from '@ekuaibao/mobx-store'
import { parseFields, parseSorters, parseFilter, parseData } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { parseColumns } from '../../../../elements/DataLinkTable/tableUtil'
import { cloneDeep } from 'lodash'
import { realName } from '../../../../elements/util'
import { getBoolVariation } from '../../../../lib/featbit'


@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer'
})
@connect(store => ({ size: store.states['@layout'].size }))
export default class SelectDataLinkUpdateModal extends React.Component {
  constructor(props) {
    super(props)
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult && props.overrideGetResult(this.getResult)
    this.bus = new MessageCenter()
    this.fieldMap = {}
    this.dataLinkMap = {}
    this.pathMap = {}
    const {
      entityInfo: { values = [] }
    } = this.props
    this.state = { 
      selectItem: { dataLink: {} }, 
      columns: [], 
      dataSource: [], 
      selectedKeys: values, 
      selectItems: [] 
    }
  }

  getTemps = async () => {
    const {
      entityInfo: { referenceData }
    } = this.props
    let data = await api.invokeService('@third-party-manage:get:entity:list', { id: referenceData.platformId.id })

    let temps = data.filter(i => i.id === referenceData.id)
    let tempobj = {}
    if (temps.length > 0) {
      temps.forEach(i => {
        if (i.children && i.children.length > 0) {
          i.children.forEach(t => {
            tempobj[t.id] = t.name
          })
        }
      })
    }
    this.temps = temps.length > 0 ? temps[0] : {}
    this.tempobj = tempobj
  }

  componentWillMount = async () => {
    // 根据特性开关决定是否监听 select:change 事件
    const useNewDataGrid = getBoolVariation('aprd-5310', false)
    if (!useNewDataGrid) {
      this.bus.on('select:change', this.handleMultiCheckClick)
    }
    
    this.bus.on('table:row:useCount:click', this.handleUseCountClick)
    await this.getTemps()
    this.formatColumns()
  }

  componentWillUnmount() {
    // 根据特性开关决定是否取消监听 select:change 事件
    const useNewDataGrid = getBoolVariation('aprd-5310', false)
    if (!useNewDataGrid) {
      this.bus.un('select:change', this.handleMultiCheckClick)
    }
    
    // this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:useCount:click', this.handleUseCountClick)
  }

  formatColumns = () => {
    const {
      entityInfo: {
        dataLink: { type }
      }
    } = this.props
    this.fetchData().then(data => {
      const { template, path, ...others } = data
      const showNameAndCode = type !== 'PRIVATE_CAR'
      const fields = parseFields({
        entityInfo: this.temps,
        res: template,
        showEdit: false,
        type,
        showUseCount: true,
        showNameAndCode
      })
      this.pathMap = path
      const useCountCanLink = type !== 'PRIVATE_CAR'
      const { columns, fieldMap } = parseColumns({
        entityInfoMap: this.tempobj,
        fields,
        bus: this.bus,
        path,
        useCountCanLink,
        platformType: type
      })
      this.fieldMap = fieldMap
      if (type !== 'PRIVATE_CAR' && type !== 'ELEM') {
        columns.push({
          title: i18n.get('操作'),
          filterType: false,
          dataIndex: 'action',
          key: 'action',
          label: i18n.get('操作'),
          sorter: false,
          value: 'action',
          fixed: columns.length > 4 ? 'right' : false,
          fixedWidth: 80,
          width: 80,
          className: 'fs-14 h-60',
          render: (text, line) => {
            return <a onClick={() => this.handlelickDetail(line)}>{i18n.get('详情')}</a>
          }
        })
      }
      this.setState({ columns, ...others })
    })
  }

  handlelickDetail = line => {
    const {
      entityInfo: { field }
    } = this.props
    api.open('@bills:DataLinkDetailModal', {
      entityInfo: { ...line, dataLink: { id: line.dataLink.id } },
      field,
      showClose: true
    })
  }

  fnFilterData = (data, disableStrategy) => {
    if (!Object.keys(this.dataLinkMap).length) return data

    if (disableStrategy === 'LIMIT_COUNT') {
      return data.filter(line => {
        const flowCount = line.dataLink.flowCount || 0
        let count = this.dataLinkMap[line.dataLink.id] || 0
        let availableCount = line.dataLink.totalCount - line.dataLink.useCount + flowCount - count
        line.dataLink.useCount = line.dataLink.totalCount - availableCount
        line.disabled = availableCount <= 0
        return line
      })
    }
    return data
  }

  fetchData = (options = { page: { currentPage: 0, pageSize: 10 } }) => {
    const {
      entityInfo: {
        referenceData,
        filterId,
        dataLink: { id, type },
        field
      },
      flowId,
      formValue,
      submitterId
    } = this.props
    // 由于界面上面显示的字段和传回到后台的字段名称不一致，为了不更改字段名称拷贝一份。。。
    const __options = cloneDeep(options)
    __options.sorters = parseSorters(options.sorters, this.pathMap)
    __options.filters = parseFilter(options.filters, this.pathMap)
    const query = parseOptions({
      options: __options,
      isSingleSelect: true,
      entityInfo: referenceData,
      fieldMap: this.fieldMap,
      dataLinkList: true,
      flowId,
      type
    })
    const pr = {
      entityId: id,
      type: 'TABLE',
      flowId,
      query,
      filterId,
      form: formValue && formValue.form,
      params: formValue && formValue.params,
    }
    if (formValue && formValue.form && field) {
      pr.field = field?.name
    }
    if (submitterId) {
      pr.submitterId = submitterId
    }
    return api
      .invokeService(
        '@third-party-manage:search:datalink:by:entityId',
        pr,
        { flowId: flowId }
      )
      .then(rep => {
        let { data, template, total, path } = rep.items
        data = this.fnFilterData(data, referenceData.disableStrategy)
        data = parseData(data)
        return { dataSource: data, template, path, total: total ? total : 1 }
      })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleModalSave = () => {
    const { multiple } = this.props.entityInfo
    const { selectItem, selectItems, selectedKeys } = this.state
    if (multiple) {
      if (selectedKeys.length === 0) {
        showModal.warning({ title: i18n.get('请选择要修改的数据') })
        return
      }
      const selectData = selectedKeys.map(it => selectItems[it])
      this.props.layer.emitOk({ data: selectData })
      return
    }
    if (!selectItem.dataLink.id) {
      showModal.warning({ title: i18n.get('请选择要修改的数据') })
      return
    }
    this.props.layer.emitOk({ data: selectItem })
  }

  handleTableRowClick = record => {
    let { selectedKeys } = this.state
    if (!record.disabled && selectedKeys.filter(i => i === record.id).length == 0) {
      this.setState({ selectItem: record })
    }
  }

  handleMultiCheckClick = (selectedKeys, selectItems) => {
    const { multiple } = this.props.entityInfo
    const useNewDataGrid = getBoolVariation('aprd-5310', false)
    
    if (!multiple) {
      if (useNewDataGrid) {
        // 新 EuiDataGrid：selectItems 是一个对象，包含选中行的数据
        const selectedKey = selectedKeys[0]
        if (selectedKey && selectItems[selectedKey]) {
          const record = selectItems[selectedKey]
          if (!record.disabled) {
            this.setState({ selectItem: record, selectedKeys, selectItems })
          }
        } else if (selectedKeys.length === 0) {
          // 如果没有选中任何行，清空选择
          this.setState({ selectItem: { dataLink: {} }, selectedKeys: [], selectItems: {} })
        }
      } else {
        // 旧 EKBDataGrid：selectItems 是数组格式
        const record = selectItems[selectedKeys[0]]
        if (record && !record.disabled) {
          this.setState({ selectItem: record, selectedKeys, selectItems })
        } else if (selectedKeys.length === 0) {
          this.setState({ selectItem: { dataLink: {} }, selectedKeys: [], selectItems: [] })
        }
      }
    } else {
      this.setState({ selectedKeys, selectItems })
    }
  }

  handleUseCountClick = line => {
    const { id } = line.dataLink
    api.open('@bills:ArchivedStackerModal', { viewKey: 'DataLinkList', instanceId: id })
  }

  getResult() {
    // This is intentional
  }

  render() {
    const {
      entityInfo: {
        dataLink: { type, name },
        field,
        multiple
      },
      size
    } = this.props
    const { columns, dataSource, total, selectItem, selectedKeys } = this.state
    const currentScroll = { y: size.y - 260 }

    return (
      <div id={'SelectDataLinkUpdateModal'} className={styles['data-link-wrapper']}>
        <div className="modal-header">
          <div className="flex">{name || realName({ name: field.cnLabel, enName: field.enLabel })}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>

        <div className="modal-body">
          <div className={`data-link-container ${type === 'PRIVATE_CAR' ? 'hide_search' : ''}`}>
            {getBoolVariation('aprd-5310', false) ? (
              <EuiDataGrid
              rowKey="id"
              columns={columns}
              searchPlaceholder={type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')}
              scroll={currentScroll}
              fetch={this.fetchData}
              dataSource={dataSource}
              total={total}
              size={'middle'}
              paginationSimple
              disabledSwitcher={true}
              isMultiSelect={multiple}
              bordered={false}
              isSingleSelect={!multiple}
              radioSelect={!multiple}
              tableWidth={900}
              settingLanguage={i18n.get('配置')}
              selectedRowKeys={selectedKeys}
              onSelectChange={this.handleMultiCheckClick}
              rowClassName={record => {
                if (selectedKeys.filter(i => i === record.id).length > 0) {
                  return styles.table_select_row_bg
                } else {
                  return record.dataLink.id === selectItem.dataLink.id ? styles.table_select_row_bg : styles.table_row_bg
                }
              }}
            />
            ) : (
              <EKBDataGrid
                rowKey="id"
                bus={this.bus}
                columns={columns}
                searchPlaceholder={type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')}
                scroll={currentScroll}
                fetch={this.fetchData}
                dataSource={dataSource}
                total={total}
                size={'middle'}
                paginationSimple
                disabledSwitcher={true}
                isMultiSelect={multiple}
                bordered={false}
                isSingleSelect={!multiple}
                radioSelect={!multiple}
                tableWidth={900}
              />
            )}
          </div>
        </div>

        <div className="modal-footer">
          <Button className="btn-ml" data-testid="bills-selectDataLinkUpdate-cancel" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('取  消')}
          </Button>

          <Button data-testid="bills-selectDataLinkUpdate-confirm" type="primary" className="btn-ml" onClick={this.handleModalSave.bind(this)}>
            {i18n.get('确  定')}
          </Button>
        </div>
      </div>
    )
  }
}
