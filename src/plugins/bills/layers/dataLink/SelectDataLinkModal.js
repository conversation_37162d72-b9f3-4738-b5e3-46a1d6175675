/**
 *  Created by gym on 2018/5/25 下午2:21.
 */

import React from 'react'
import styles from './SelecDataLinkModal.module.less'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { Button, Icon, Alert, Spin } from 'antd'
import { message } from '@hose/eui'
import MessageCenter from '@ekuaibao/messagecenter'
import { showModal } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import { parseOptions } from '../../../../elements/DataLinkTable/tableUtil'
import { EKBDataGrid } from 'ekbc-datagrid/esm'
import { EuiDataGrid as NewEKBDataGrid } from '../../../../components/eui_datagrid'
import { handleDataLinkUseCount, handleHeaderFields } from './dataLinkUtil'
import { connect } from '@ekuaibao/mobx-store'
import { LedgerWarningTip } from '../../../../elements/puppet/LedgerWarningTip'
import { parseFields, parseSorters, parseFilter, parseData } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { parseColumns } from '../../../../elements/DataLinkTable/tableUtil'
import { cloneDeep } from 'lodash'
import { filterMultiStaff } from '../../../../lib/mutil-staff-fetch'
import { getV } from '@ekuaibao/lib/lib/help'
import ETabs from '../../../../elements/ETabs'
import { Fetch } from '@ekuaibao/fetch'
import { SpecialAllowCancelDependenceUtils } from "../../../../components/dynamic/types";
import { getBoolVariation } from '../../../../lib/featbit'

@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer'
})
@connect(store => ({ size: store.states['@layout'].size }))
export default class SelectDataLinkModal extends React.Component {
  constructor(props) {
    super(props)
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult && props.overrideGetResult(this.getResult)
    this.bus = new MessageCenter()
    this.fieldMap = {}
    this.dataLinkMap = {}
    this.pathMap = {}
    const {
      entityInfo: {
        dataLink: { selectedEntity, id },
        values = [],
        multiple = false
      }
    } = this.props
    let selectItem = { dataLink: {} }
    let initialSelectedKeys = values.map(i => i.id)
    
    if (multiple === false) {
      selectItem = selectedEntity
        ? selectedEntity.dataLink
          ? selectedEntity
          : { dataLink: selectedEntity }
        : { dataLink: {} }
      
      // 如果是单选模式且有初始选中项，确保 selectedKeys 包含对应的 id
      if (selectItem.dataLink && selectItem.dataLink.id) {
        initialSelectedKeys = [selectItem.id || selectItem.dataLink.id]
      } else if (selectItem.id) {
        initialSelectedKeys = [selectItem.id]
      }
    }
    
    this.state = {
      selectItem,
      columns: [],
      dataSource: [],
      selectedKeys: initialSelectedKeys,
      HOSEMALL: false,
      hasAlert: true,
      loading: true,
      useDependenceData: true,// 是否使用 依赖关系筛选数据（因为允许取消筛选）
      isSkipCondition: false,
      useSkipConditionData: true,
      activeType: 'all',
      fetchParams: this.initFetchParams(),
      collectedData: {
        dataSource: [],
        total: 0,
        fetchParams: this.initFetchParams()
      }
    }
    this.isSkipLock = false
  }
  componentDidMount() {
    const {
      entityInfo: { referenceData }
    } = this.props
    if (referenceData && typeof referenceData === 'string') {
      api.invokeService('@bills:get:datalink:by:id', referenceData).then(res => {
        this.setState({ referenceDataState: res?.value })
      })
    }

  }

  initFetchParams = () => {
    return {
      page: { currentPage: 1, pageSize: 10 },
      searchText: '',
      filters: {},
      sorters: {},
      scene: undefined
    }
  }

  getPlatform = () => {
    api.invokeService('@common:get:powers').then(res => {
      if (res?.items) {
        let data = res.items.find(i => {
          return i.powerCode == '120209'
        })
        this.setState({
          HOSEMALL: data && data.powerCode ? true : false
        })
      }
    })
  }

  getTemps = async () => {
    const {
      entityInfo: { referenceData }
    } = this.props
    let data = await api.invokeService('@third-party-manage:get:entity:list', {
      id: referenceData?.platformId?.id || referenceData
    })

    let temps = data.filter(i => i.id === referenceData.id || referenceData)
    let tempobj = {}
    if (temps.length > 0) {
      temps.forEach(i => {
        if (i.children && i.children.length > 0) {
          i.children.forEach(t => {
            tempobj[t.id] = t.name
          })
        }
      })
    }
    this.temps = temps.length > 0 ? temps[0] : {}
    this.tempobj = tempobj
  }

  componentWillMount = async () => {
    let { multiple = false } = this.props.entityInfo
    this.bus.on('edit:table:supplier', this.handlelickDetail)

    // 根据特性开关决定是否监听 select:change 事件
    const useNewDataGrid = getBoolVariation('aprd-5310', false)
    if (!useNewDataGrid) {
      this.bus.on('select:change', this.handleMultiCheckClick)
    }

    // !multiple && this.bus.on('table:row:click', this.handleTableRowClick)
    this.bus.on('table:row:useCount:click', this.handleUseCountClick)
    const {
      entityInfo: { referenceData },
      fromDetail = false,
      dataLinkMap,
      bus,
      template
    } = this.props
    const { disableStrategy } = referenceData

    await this.getTemps()
    this.getPlatform()

    if (disableStrategy === 'LIMIT_COUNT') {
      if (fromDetail) {
        //如果是消费记录的话需要实时获取当前消费记录表单的值来计算引用次数，其余的消费记录以及表头的的引用次数从外面传过来，然后将两个值合并
        bus &&
          bus.getValue().then(value => {
            let map = {}
            handleHeaderFields(template, value, map)
            for (let key in map) {
              if (dataLinkMap[key]) {
                dataLinkMap[key] += 1
              } else {
                dataLinkMap[key] = 1
              }
            }
            this.dataLinkMap = dataLinkMap
            this.formatColumns()
          })
      } else {
        //不是消费记录的话直接获取整个单据的值来计算引用次数就可以了
        if (api.has('get:bills:value')) {
          api.invoke('get:bills:value').then(result => {
            this.dataLinkMap = handleDataLinkUseCount(result)
            this.formatColumns()
          })
        } else {
          this.formatColumns()
        }
      }
    } else {
      this.formatColumns()
    }
  }

  componentWillUnmount() {
    this.bus.un('edit:table:supplier', this.handlelickDetail)

    // 根据特性开关决定是否取消监听 select:change 事件
    const useNewDataGrid = getBoolVariation('aprd-5310', false)
    if (!useNewDataGrid) {
      this.bus.un('select:change', this.handleMultiCheckClick)
    }

    // this.bus.un('table:row:click', this.handleTableRowClick)
    this.bus.un('table:row:useCount:click', this.handleUseCountClick)
    this.isSkipLock = false
  }

  handleMultiCheckClick = (selectedKeys, selectItems) => {
    const { multiple } = this.props.entityInfo
    const useNewDataGrid = getBoolVariation('aprd-5310', false)

    if (!multiple) {
      if (useNewDataGrid) {
        // 新 EuiDataGrid：selectItems 是一个对象，包含选中行的数据
        const selectedKey = selectedKeys[0]
        if (selectedKey && selectItems[selectedKey]) {
          const record = selectItems[selectedKey]
          if (!record.disabled) {
            this.setState({ selectItem: record, selectedKeys })
          }
        } else if (selectedKeys.length === 0) {
          // 如果没有选中任何行，清空选择
          this.setState({ selectItem: { dataLink: {} }, selectedKeys: [] })
        }
      } else {
        // 旧 EKBDataGrid：selectItems 是数组格式
        const record = selectItems[selectedKeys[0]]
        if (record && !record.disabled) {
          this.setState({ selectItem: record, selectedKeys })
        } else if (selectedKeys.length === 0) {
          this.setState({ selectItem: { dataLink: {} }, selectedKeys: [] })
        }
      }
    } else {
      // 多选模式：只更新选中的keys
      this.setState({ selectedKeys })
    }
  }

  formatColumns = () => {
    const {
      entityInfo: {
        dataLink: { type }
      }
    } = this.props
    this.fetchData().then(data => {
      const { template, path, ...others } = data
      const showNameAndCode = type !== 'PRIVATE_CAR'
      const fields = parseFields({
        entityInfo: this.temps,
        res: template,
        showEdit: false,
        type,
        showUseCount: true,
        showNameAndCode
      })
      this.pathMap = path
      const useCountCanLink = type !== 'PRIVATE_CAR'
      let { columns, fieldMap } = parseColumns({
        entityInfoMap: this.tempobj,
        fields,
        bus: this.bus,
        path,
        useCountCanLink,
        platformType: type
      })
      if (columns?.length) {
        columns[0].width = 200
        columns[0].fixedWidth = 200
      }
      this.fieldMap = fieldMap
      if (type !== 'PRIVATE_CAR' && type !== 'ELEM') {
        columns.push({
          title: i18n.get('操作'),
          filterType: false,
          dataIndex: 'action',
          key: 'action',
          label: i18n.get('操作'),
          value: 'action',
          fixed: 'right',
          fixedWidth: 140,
          className: 'fs-14 h-60',
          sorter: false,
          render: (text, line) => {
            const tripOrder = this.getIsTripOrder()
            return <div>
              <a onClick={() => this.handlelickDetail(line)}>{i18n.get('详情')}</a>
              {!tripOrder && (<a className="ml-10" onClick={() => this.handleCollect(line)}>
                {line?.dataLink?.favoriteStatus ? i18n.get('取消收藏') : i18n.get('收藏')}
              </a>)
              }
            </div>
          }
        })
      }
      if (type === 'SUPPLIER') {
        columns = columns.filter(el => el.dataIndex !== 'dataLink.entityId')
        const payeeInfoCol =
          columns.find(col => !!~col.dataIndex.indexOf('payeeInfo')) ||
          columns.find(col => !!~col.dataIndex.indexOf('收款信息')) // @i18n-ignore
        if (payeeInfoCol) {
          payeeInfoCol.className = 'fs-14 bank-card-col'
        }
      }
      this.setState({ columns, ...others }, () => {
        // 数据加载完成后，如果是单选模式且有初始选中项，确保状态同步
        const { multiple } = this.props.entityInfo
        if (!multiple && this.state.selectItem && this.state.selectItem.id) {
          const { dataSource } = this.state
          const selectedRecord = dataSource.find(item => item.id === this.state.selectItem.id)
          if (selectedRecord && !selectedRecord.disabled) {
            this.setState({
              selectedKeys: [selectedRecord.id],
              selectItem: selectedRecord
            })
          }
        }
      })
    })
  }

  handlelickDetail = line => {
    const {
      entityInfo: { field, referenceData }
    } = this.props
    const { allowRecordLog } = referenceData
    api.open('@bills:DataLinkDetailModal', {
      entityInfo: { ...line, dataLink: { id: line.dataLink.id } },
      field,
      allowRecordLog,
      showClose: true
    })
  }

  fnFilterData = (data, disableStrategy) => {
    if (!Object.keys(this.dataLinkMap).length) return data

    if (disableStrategy === 'LIMIT_COUNT') {
      return data.filter(line => {
        const flowCount = line.dataLink.flowCount || 0
        let count = this.dataLinkMap[line.dataLink.id] || 0
        let availableCount = (line?.dataLink?.totalCount ?? 1) - (line?.dataLink?.useCount ?? 0) + flowCount - count
        line.dataLink.useCount = (line?.dataLink?.totalCount ?? 1) - availableCount
        line.disabled = availableCount <= 0
        return line
      })
    }
    return data
  }

  fetchData = async (options = { page: { currentPage: 0, pageSize: 10 } }) => {
    const {
      entityInfo: {
        referenceData,
        filterId,
        dataLink: { id, type },
        dependenceParams,
        allMatchList,
        field,
        allowCancelDependence
      },
      flowId,
      formValue,
      fromSupplier
    } = this.props
    const { referenceDataState, useDependenceData, isSkipCondition, activeType } = this.state
    // 由于界面上面显示的字段和传回到后台的字段名称不一致，为了不更改字段名称拷贝一份。。。
    const __options = cloneDeep(options)
    __options.sorters = parseSorters(options.sorters, this.pathMap)
    __options.filters = parseFilter(options.filters, this.pathMap)
    await filterMultiStaff(__options, this.fieldMap)
    const query = parseOptions({
      options: __options,
      isSingleSelect: true,
      entityInfo: (typeof referenceData === "string" && referenceDataState) ? referenceDataState : referenceData,
      fieldMap: this.fieldMap,
      dataLinkList: true,
      flowId,
      type,
      newFilter: true
    })
    const submitterId = getV(this.props, 'submitterId.id')

    const isSpecialAllowCancelDependence = SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence
    let params = {
      entityId: id,
      type: 'TABLE',
      flowId,
      query,
      filterId,
      form: formValue && formValue.form,
      params: formValue && formValue.params,
      allVisible: fromSupplier,
      field
    }
    if (allMatchList !== undefined) {
      //业务对象联查可选范围增加参数
      params.allMatchList = allMatchList
    }
    if (!!submitterId) {
      params.submitterId = submitterId
    }
    const isOrder = referenceData?.type === 'ORDER' && type === 'TRAVEL_MANAGEMENT'
    if (isOrder) {
      params = { ...params, params: { type: 'order' } }
    }
    //为空时可查看全量数据
    if (this.isSkipLock && isSkipCondition) {
      params.isSkipCondition = true
    }
    if (activeType === 'collect') {
      params.favoriteStatus = true
    }
    if (field === 'u_行车记录') {
      const shouldFixParam = api.getState()['@common'].toggleManage?.['tg_fix_searchDatalink_request']
      if (shouldFixParam && params?.form?.linkDetailEntities?.length > 0) {
        params?.form?.linkDetailEntities.forEach(el => {
          if (
            el?.specificationId
            && typeof el.specificationId === 'object'
            && el.specificationId?.id
          ) {
            el.specificationId = el.specificationId.id
          }
        })
      }
    }

    const promise = useDependenceData && dependenceParams && !isSpecialAllowCancelDependence
      ? api.invokeService('@bills:get:datalink:dependence:list', { ...dependenceParams, query }).then(res => {
        return { items: res?.data ? res : { data: [] } }
      })
      : api.invokeService('@third-party-manage:search:datalink:by:entityId', params, { flowId: flowId })
    return promise.then(rep => {
      let { data, template, total, path, rule } = rep.items
      data = this.fnFilterData(data, referenceData.disableStrategy)
      data = parseData(data)
      this.setState({
        loading: false,
        isSkipCondition: rule?.isCanViewAllDataWithResultBlank
      })
      if (this.isSkipLock && isSkipCondition && !data?.length) {
        this.setState({
          useSkipConditionData: false
        })
      }
      return { dataSource: data, template, path, total: total ? total : 1 }
    })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleModalSave = () => {
    const { selectItem } = this.state
    if (!selectItem.dataLink.id) {
      showModal.warning({ title: i18n.get('请选择要添加的数据') })
      return
    }
    this.props.layer.emitOk({ data: selectItem })
  }
  handleModalSaveList = () => {
    const { selectedKeys } = this.state
    if (selectedKeys.length === 0) {
      showModal.warning({ title: i18n.get('请选择要添加的数据') })
      return
    }
    this.props.layer.emitOk({ data: selectedKeys })
  }

  // handleTableRowClick = record => {
  //   // if (!record.disabled) {
  //   //   this.setState({ selectItem: record })
  //   // }
  // }

  handleUseCountClick = line => {
    const { id } = line.dataLink
    api.open('@bills:ArchivedStackerModal', { viewKey: 'DataLinkList', instanceId: id })
  }
  handleGet = () => {
    // 出现弹框
    api.open('@bills:SelectDateRangeTrip', {}).then(params => {
      api
        .invokeService('@bills:get:PullTravelOrderByStaff', {
          startDate: params.startTime,
          endDate: params.endTime
        })
        .then(res => {
          message.success(i18n.get('同步成功'))
          if (this.reftable) {
            this.reftable?.fetch(this.reftable?.state?.fetchParams)
          }
        })
    })
  }
  onAlertClose = () => {
    this.setState({
      hasAlert: false
    })
  }

  getResult() {
    // This is intentional
  }

  // 取消依赖取值结果
  cancelDependenceData() {
    this.setState({
      useDependenceData: false
    }, () => {
      this.formatColumns()
    })
  }

  handleViewAllData = () => {
    this.isSkipLock = true
    this.formatColumns()
  }

  getIsTripOrder = () => {
    const {
      entityInfo: {
        dataLink: { type },
        referenceData
      }
    } = this.props
    const { HOSEMALL } = this.state
    return type == 'TRAVEL_MANAGEMENT' && HOSEMALL && referenceData?.name == '订单' // @i18n-ignore
  }

  handleCollect = async item => {
    const { dataLink } = item
    if (!dataLink) return

    const { favoriteStatus } = dataLink
    const isAllType = this.state.activeType === 'all'
    const enable = !favoriteStatus
    const dataLinkId = dataLink?.id
    try {
      const res = await Fetch.PUT(`/api/v2/datalink/favorites/$${dataLinkId}/$${enable}`)
      if (res && res.value) {
        if (isAllType) {
          await this.handleUpdateAll()
        } else {
          await this.handleUpdateCollected()
        }
        return message.success(favoriteStatus ? i18n.get('收藏已取消') : i18n.get('收藏成功'))
      }
    } catch (err) {
      console.error(err)
    }
  }

  handleUpdateAll = async (params) => {
    const useNewDataGrid = getBoolVariation('aprd-5310', false)
    let curParams = params

    if (!curParams) {
      if (useNewDataGrid) {
        // 新组件：使用 state 中的 fetchParams
        curParams = this.state.fetchParams
      } else {
        // 旧组件：使用 bus.getFetchParams()
        curParams = this.bus.getFetchParams()
      }
    }

    const data = await this.fetchData(curParams)
    const { dataSource, total } = data

    this.setState({
      dataSource,
      total
    })
  }

  handleUpdateCollected = async (params) => {
    const useNewDataGrid = getBoolVariation('aprd-5310', false)
    let curParams = params

    if (!curParams) {
      if (useNewDataGrid) {
        // 新组件：使用 state 中的 fetchParams
        curParams = this.state.collectedData?.fetchParams || this.state.fetchParams
      } else {
        // 旧组件：使用 bus.getFetchParams()
        curParams = this.bus.getFetchParams()
      }
    }

    const data = await this.fetchData(curParams)
    const { dataSource, total } = data
    this.setState({
      collectedData: {
        ...this.state.collectedData,
        dataSource,
        total
      }
    })
  }

  handleTabChange = v => {
    if (v === 'all') {
      this.setState({
        activeType: v,
        dataSource: [],
        total: 0,
        fetchParams: this.initFetchParams()
      }, async _ => {
        await this.handleUpdateAll(this.initFetchParams())
      })
    } else {
      this.setState({
        activeType: v,
        collectedData: {
          total: 0,
          dataSource: [],
          fetchParams: this.initFetchParams()
        }
      }, async _ => {
        await this.handleUpdateCollected(this.initFetchParams())
      })
    }
  }

  renderContent() {
    const {
      entityInfo: {
        islock,
        ledgerLockList,
        dataLink: { type, name },
        multiple = false,
        allowCancelDependence,
        referenceData
      },
      size
    } = this.props
    const { columns, dataSource, total, selectItem, selectedKeys, HOSEMALL, hasAlert, useDependenceData,
      useSkipConditionData, isSkipCondition, fetchParams } = this.state
    const currentScroll = { y: size.y - 336 }
    let tripOrder = this.getIsTripOrder()
    if (tripOrder) {
      if (hasAlert) {
        currentScroll.y = currentScroll.y - 48
      } else {
        currentScroll.y = currentScroll.y - 10
      }
    }

    const locale = {
      filterConfirm: '确定',
      filterReset: '重置',
      emptyText: '暂无数据'
    }
    const isSpecialAllowCancelDependence = SpecialAllowCancelDependenceUtils.isRefSpecial() && allowCancelDependence

    if (!isSpecialAllowCancelDependence && allowCancelDependence && useDependenceData) {
      locale.emptyText = (
        <div className="cancel-dependence">
          {i18n.get('通过依赖关系筛选，暂无匹配结果')}，{i18n.get('点击')}
          <a href="javascript:void 0" onClick={this.cancelDependenceData.bind(this)}>
            {i18n.get('查看全量数据')}
          </a>
        </div>
      )
    } else if (isSkipCondition && useSkipConditionData) {
      locale.emptyText = (
        <div className="cancel-dependence">
          {i18n.get('暂无数据，点击')}
          <span className="dataLink-viewAll" onClick={this.handleViewAllData.bind(this)}>
            {i18n.get('查看全量数据')}
          </span>
        </div>
      )
    }

    return (
      <div className={styles['data-link-content']}>
        {islock ? (
          <div className="ledger-warning-bg">
            <LedgerWarningTip ledgerLockList={ledgerLockList} />
          </div>
        ) : (
          ''
        )}
        {tripOrder && hasAlert && (
          <Alert
            className="cover myAlert"
            message={i18n.get('找不到订单？因差旅平台同步订单有一定的时效性，可通过手动同步差旅订单')}
            type="info"
            showIcon
            closable
            onClose={this.onAlertClose}
          />
        )}
        <div className={`data-link-container ${type === 'PRIVATE_CAR' ? 'hide_search' : ''}`}>
          {this.state.loading ? (
            <div style={{ textAlign: 'center', padding: '150px 0px', width: '100%' }}><Spin /></div>
          ) : getBoolVariation('aprd-5310', false) ? (
            <NewEKBDataGrid
              fetchParams={fetchParams}
              rowKey="id"
              columns={columns}
              searchPlaceholder={
                type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')
              }
              locale={locale}
              scroll={currentScroll}
              fetch={this.fetchData}
              dataSource={dataSource}
              total={total}
              size={'middle'}
              paginationSimple
              disabledSwitcher={true}
              selectedRowKeys={selectedKeys}
              radioSelect={!multiple}
              isMultiSelect={multiple}
              bordered={false}
              isSingleSelect={!multiple}
              tableWidth={900}
              settingLanguage={i18n.get('配置')}
              onSelectChange={this.handleMultiCheckClick}
              onSearch={(params) => {
                // 同步更新 fetchParams 状态
                this.setState({ fetchParams: { ...this.state.fetchParams, ...params } })
              }}
              onPagination={(currentPage, pageSize) => {
                // 同步更新 fetchParams 状态，但不更新 dataSource 和 total
                // 因为这些会由 EuiDataGrid 内部的 fetchData 处理
                this.setState({
                  fetchParams: {
                    ...this.state.fetchParams,
                    page: { currentPage, pageSize }
                  }
                })
              }}
              onFiltersChange={(filters) => {
                // 同步更新 fetchParams 状态
                this.setState({
                  fetchParams: {
                    ...this.state.fetchParams,
                    filters
                  }
                })
              }}
              rowClassName={record => {
                let rowStyle
                if (record.disabled) {
                  rowStyle = styles.table_disabled_row_bg
                } else if (selectedKeys.filter(i => i === record.id).length > 0) {
                  return styles.table_select_row_bg
                } else {
                  rowStyle = record.dataLink.id === selectItem.dataLink.id
                    ? styles.table_select_row_bg
                    : styles.table_row_bg
                }
                if (window.PLATFORMINFO?.dataTopFunctionActive && !!record.dataLink?.topDate) {
                  //增加置顶样式
                  const setTopCls = multiple ? 'hascheck_table_row_set_top' : 'table_row_set_top'
                  const hasCheckboxPrefix = multiple ? 'hascheck_en_top' : 'en_top'
                  const enCls = i18n?.currentLocale === 'en-US' ? hasCheckboxPrefix : ''
                  return `${rowStyle} ${setTopCls} ${enCls}`
                }
                return rowStyle
              }}
              ref={ref => (this.reftable = ref)}
            />
          ) : (
            <EKBDataGrid
              fetchParams={fetchParams}
              bus={this.bus}
              rowKey="id"
              columns={columns}
              searchPlaceholder={
                type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')
              }
              locale={locale}
              scroll={currentScroll}
              fetch={this.fetchData}
              dataSource={dataSource}
              total={total}
              size={'middle'}
              paginationSimple
              disabledSwitcher={true}
              selectedRowKeys={selectedKeys}
              radioSelect={!multiple}
              isMultiSelect={multiple}
              bordered={false}
              isSingleSelect={!multiple}
              tableWidth={900}
              settingLanguage={i18n.get('配置')}
              rowClassName={record => {
                let rowStyle
                if (record.disabled) {
                  rowStyle = styles.table_disabled_row_bg
                } else if (selectedKeys.filter(i => i === record.id).length > 0) {
                  return styles.table_select_row_bg
                } else {
                  rowStyle = record.dataLink.id === selectItem.dataLink.id
                    ? styles.table_select_row_bg
                    : styles.table_row_bg
                }
                if (window.PLATFORMINFO?.dataTopFunctionActive && !!record.dataLink?.topDate) {
                  //增加置顶样式
                  const setTopCls = multiple ? 'hascheck_table_row_set_top' : 'table_row_set_top'
                  const hasCheckboxPrefix = multiple ? 'hascheck_en_top' : 'en_top'
                  const enCls = i18n?.currentLocale === 'en-US' ? hasCheckboxPrefix : ''
                  return `${rowStyle} ${setTopCls} ${enCls}`
                }
                return rowStyle
              }}
              ref={ref => (this.reftable = ref)}
            />
          )}
        </div>
      </div>
    )
  }

  renderCollected() {
    const {
      entityInfo: {
        dataLink: { type, name },
        multiple = false,
      },
      size
    } = this.props
    const { columns, selectedKeys, collectedData, selectItem } = this.state
    const { fetchParams, dataSource, total } = collectedData
    const currentScroll = { y: size.y - 336 }

    return (<div className={styles['data-link-content']}>
      <div className="data-link-container">
        {getBoolVariation('aprd-5310', false) ? (
          <NewEKBDataGrid
            fetchParams={fetchParams}
            ref="collect"
            rowKey="id"
            columns={columns}
            searchPlaceholder={i18n.get('输入名称或编码搜索')}
            locale={{
              filterConfirm: '确定',
              filterReset: '重置',
              emptyText: '暂无数据'
            }}
            scroll={currentScroll}
            fetch={this.fetchData}
            dataSource={dataSource}
            total={total}
            size={'middle'}
            paginationSimple
            disabledSwitcher={true}
            selectedRowKeys={selectedKeys}
            onSelectChange={this.handleMultiCheckClick}
            onSearch={(params) => {
              // 同步更新收藏页面的 fetchParams 状态
              this.setState({
                collectedData: {
                  ...this.state.collectedData,
                  fetchParams: { ...this.state.collectedData.fetchParams, ...params }
                }
              })
            }}
            onPagination={(currentPage, pageSize) => {
              // 同步更新收藏页面的 fetchParams 状态，但不更新 dataSource 和 total
              // 因为这些会由 EuiDataGrid 内部的 fetchData 处理
              this.setState({
                collectedData: {
                  ...this.state.collectedData,
                  fetchParams: {
                    ...this.state.collectedData.fetchParams,
                    page: { currentPage, pageSize }
                  }
                }
              })
            }}
            onFiltersChange={(filters) => {
              // 同步更新收藏页面的 fetchParams 状态
              this.setState({
                collectedData: {
                  ...this.state.collectedData,
                  fetchParams: {
                    ...this.state.collectedData.fetchParams,
                    filters
                  }
                }
              })
            }}
            rowClassName={record => {
              let rowStyle
              if (record.disabled) {
                rowStyle = styles.table_disabled_row_bg
              } else if (selectedKeys.filter(i => i === record.id).length > 0) {
                return styles.table_select_row_bg
              } else {
                rowStyle = record.dataLink.id === selectItem.dataLink.id
                  ? styles.table_select_row_bg
                  : styles.table_row_bg
              }
              if (window.PLATFORMINFO?.dataTopFunctionActive && !!record.dataLink?.topDate) {
                //增加置顶样式
                const setTopCls = multiple ? 'hascheck_table_row_set_top' : 'table_row_set_top'
                const hasCheckboxPrefix = multiple ? 'hascheck_en_top' : 'en_top'
                const enCls = i18n?.currentLocale === 'en-US' ? hasCheckboxPrefix : ''
                return `${rowStyle} ${setTopCls} ${enCls}`
              }
              return rowStyle
            }}
            isMultiSelect={multiple}
            radioSelect={!multiple}
            bordered={false}
            isSingleSelect={!multiple}
            tableWidth={900}
          />
        ) : (
          <EKBDataGrid
            fetchParams={fetchParams}
            ref="collect"
            bus={this.bus}
            rowKey="id"
            columns={columns}
            searchPlaceholder={i18n.get('输入名称或编码搜索')}
            locale={{
              filterConfirm: '确定',
              filterReset: '重置',
              emptyText: '暂无数据'
            }}
            scroll={currentScroll}
            fetch={this.fetchData}
            dataSource={dataSource}
            total={total}
            size={'middle'}
            paginationSimple
            disabledSwitcher={true}
            selectedRowKeys={selectedKeys}
            rowClassName={record => {
              let rowStyle
              if (record.disabled) {
                rowStyle = styles.table_disabled_row_bg
              } else if (selectedKeys.filter(i => i === record.id).length > 0) {
                return styles.table_select_row_bg
              } else {
                rowStyle = record.dataLink.id === selectItem.dataLink.id
                  ? styles.table_select_row_bg
                  : styles.table_row_bg
              }
              if (window.PLATFORMINFO?.dataTopFunctionActive && !!record.dataLink?.topDate) {
                //增加置顶样式
                const setTopCls = multiple ? 'hascheck_table_row_set_top' : 'table_row_set_top'
                const hasCheckboxPrefix = multiple ? 'hascheck_en_top' : 'en_top'
                const enCls = i18n?.currentLocale === 'en-US' ? hasCheckboxPrefix : ''
                return `${rowStyle} ${setTopCls} ${enCls}`
              }
              return rowStyle
            }}
            isMultiSelect={multiple}
            radioSelect={!multiple}
            bordered={false}
            isSingleSelect={!multiple}
            tableWidth={900}
          />
        )}
      </div>
    </div>)
  }

  render() {
    const {
      entityInfo: {
        dataLink: { name },
        multiple = false,
      }
    } = this.props
    const { activeType } = this.state

    let tripOrder = this.getIsTripOrder()

    const tabsSource = [
      {
        tab: i18n.get('全部'),
        key: 'all',
        children: this.renderContent()
      },
      {
        tab: i18n.get('我的收藏'),
        key: 'collect',
        children: this.renderCollected()
      }
    ]

    return (
      <div id={'SelectDataLinkModal'} className={styles['data-link-wrapper']}>
        <div className="modal-header">
          <div className="flex">{name}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose} />
        </div>
        <div className="modal-body">
          {tripOrder ? this.renderContent()
            : (<ETabs
              isHoseEUI={true}
              className="ekb-tab-line-left"
              tabBarStyle={{ paddingTop: 6, height: 40, width: '100%' }}
              defaultActiveKey={activeType}
              onChange={this.handleTabChange}
              dataSource={tabsSource}
            />)}
        </div>
        <div className="modal-footer">
          <Button className="btn-ml" data-testid="datalink-select-cancel" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('取  消')}
          </Button>
          {!multiple ? (
            <Button type="primary" className="btn-ml" data-testid="datalink-selectItem-confirm" onClick={this.handleModalSave.bind(this)}>
              {i18n.get('确  定')}
            </Button>
          ) : (
            <Button type="primary" className="btn-ml" data-testid="datalink-selectList-confirm" onClick={this.handleModalSaveList}>
              {i18n.get('确  定')}
            </Button>
          )}
          {tripOrder && (
            <Button className="btn-ml" data-testid="datalink-select-refresh" onClick={this.handleGet}>
              {i18n.get('刷 新')}
            </Button>
          )}
        </div>
      </div>
    )
  }
}
