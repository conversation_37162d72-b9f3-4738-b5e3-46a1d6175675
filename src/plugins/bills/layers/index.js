import { isHoseEUI } from '../util/config'
import ECardConsumeDetail from './ECardConsumeDetail'
import { billDrawerConfig } from '../../../elements/configure/bill-drawer'
import './index.less'
/**************************************************
 * Created by nanyuantingfeng on 13/07/2017 16:23.
 **************************************************/
export default [
  {
    key: 'BannerModal',
    getComponent: () => import('./BannerModal'),
    title: '',
    width: 700
  },
  {
    key: 'BlockUIModal',
    getComponent: () => import('./BlockUIModal'),
    width: 800
  },
  {
    key: 'SelectPayeeModal',
    getComponent: () => import('./SelectPayeeModal'),
    title: '',
    width: 800,
    isHoseEUI
  },
  {
    key: 'FeeDetailDynamicModal',
    getComponent: () => import('./FeeDetailDynamicModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'RepaymentModal',
    getComponent: () => import('./loan/RepaymentModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
    isHoseEUI
  },
  {
    key: 'ImportInvoiceModal',
    getComponent: () => import('./import-bill/ImportInvoiceModal'),
    width: 520,
    maskClosable: false,
    isHoseEUI:true,
    wrapClassName: 'vertical-center-modal',
    closable: true
  },
  {
    key: 'FlowConfigModal',
    getComponent: () => import('./flow/FlowConfigModal'),
    width: 520,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
    isHoseEUI,
    closable: true
  },
  {
    key: 'ImportCivilServiceCardModal',
    getComponent: () => import('./import-bill/importCivilServiceCardModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'SearchFeetypeTreeModal',
    getComponent: () => import('../../../elements/feetype-tree/feetype-tree-search-modal'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'AIFeedbackModal',
    getComponent: () => import('../../../elements/puppet/attachmentList/AIFeedbackModal'),
    width: 520,
    maskClosable: false,
    className: 'custom-modal-layer'
  },
  {
    key: 'PayeeAccountCreate',
    getComponent: () => import('../../../elements/payee-account/payee-account-create'),
    width: 600,
    okText: i18n.get('确定'),
    maskClosable: false,
    isHoseEUI: true
  },
  {
    key: 'PayeeAccountCreatePopup',
    getComponent: () => import('../PopupPage/PayeeAccountCreateViewPopup'),
    title: '',
    width: 600,
    timeout: 500,
    showClose: true,
    className: 'PayeeAccountCreatePopup'
  },
  {
    key: 'BillInfoPopup',
    getComponent: () => import('./bill-info-popup/BillInfoPopup'),
    title: '',
    width: '700',
    customWidth: billDrawerConfig.width,
    closable: false,
    destroyOnClose: true,
    timeout: 500
  },
  {
    key: 'BillInfoCreatePopup',
    getComponent: () => import('./bill-info-popup/BillInfoCreatePopup'),
    title: '',
    width: '700',
    closable: false,
    className: 'custom-modal-layer-no-footer',
    maskClosable: false
  },
  {
    key: 'BillInfoEditePopup',
    getComponent: () => import('./bill-info-popup/BillInfoEditePopup'),
    footer: null,
    closable: false,
    destroyOnClose: true,
    maskClosable: true,
    title: null
  },
  {
    key: 'BillInfoModal',
    getComponent: () => import('./bill-info-popup/BillInfoModal'),
    width: 800,
    maskClosable: true,
    className: 'custom-modal-layer-no-footer'
  },
  {
    key: 'ImportDetailByExcel',
    getComponent: () => import('./import-detail-excel/ImportDetailByExcelModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'BillHistoryVersionModal',
    getComponent: () => import('./bill-history-version/BillHistoryVersionView'),
    width: 960,
    style: { minHeight: '540px' },
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'BillInfoModifyModal',
    getComponent: () => import('./BillInfoModifyModal'),
    width: 1100,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'BillEditableModal',
    getComponent: () => import('./BillEditableModal'),
    width: 750,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'BillEditableDrawer',
    footer: null,
    closable: false,
    destroyOnClose: true,
    maskClosable: false,
    title: null,
    getComponent: () => import('./BillEditableDrawer')
  },
  {
    key: 'CostAttribution',
    footer: null,
    width: '100%',
    closable: false,
    destroyOnClose: true,
    maskClosable: false,
    title: null,
    getComponent: () => import('./CostAttribution')
  },
  {
    key: 'BillInfoModifyReasonModal',
    getComponent: () => import('./BillInfoModifyReasonModal'),
    title: '',
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'ImportThirdPartyModal',
    getComponent: () => import('./import-third-party'),
    width: '75%',
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'BillStackerModal',
    getComponent: () => import('../bill-stacker-manager/BillStackerModal'),
    width: 1030,
    style: { minHeight: '432px', overflow: 'visible' },
    enhancer: 'modal',
    enhancerOptions: { footer: [], className: 'respond-modal-layer' },
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'BillStackerPopup',
    style: { overflow: 'visible' },
    getComponent: () => import('../bill-stacker-manager/BillStackerPopup'),
    title: '',
    width: 750,
    closable: false,
    destroyOnClose: true,
    maskClosable: false,
    timeout: 500,
    isHoseEUI
  },
  {
    key: 'ArchivedStackerModal',
    getComponent: () => import('../bill-stacker-manager/BillStackerModal'),
    width: 960,
    style: { height: '93%' },
    enhancer: 'modal',
    enhancerOptions: { footer: [], className: 'respond-modal-layer' },
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'ApplyEventStackerModal',
    getComponent: () => import('../bill-stacker-manager/BillStackerModal'),
    width: 800,
    enhancer: 'modal',
    enhancerOptions: { footer: [], className: 'respond-modal-layer' },
    maskClosable: false
  },
  {
    key: 'BillCommentModal',
    getComponent: () => import('./comment/BillCommentModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI
  },
  {
    key: 'ChangeApplyModal',
    getComponent: () => import('./comment/ChangeApplyModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'BillWithdrawModal',
    getComponent: () => import('./withdraw/BillWithdrawModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'AddTripsModal',
    getComponent: () => import('./add-trips/AddTripsModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'AddTripModal',
    getComponent: () => import('./add-trip-modal/AddTripModal'),
    width: 536,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'AddTravelPlanModal',
    getComponent: () => import('./add-travel-plan-modal/AddTravelPlanModal'),
    width: 552,
    maskClosable: false,
    wrapClassName: ''
  },

  {
    key: 'RequisitionEventCloseModal',
    getComponent: () => import('./requisition-event-close-modal/RequisitionEventCloseModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI
  },
  {
    key: 'SelectSubsidyModal',
    getComponent: () => import('./select-subsidy-modal/SelectSubsidyModal'),
    width: 416,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI
  },
  {
    key: 'FollowWeChatModal',
    getComponent: () => import('./FollowWeChatModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI
  },
  {
    key: 'ImportInputInvoiceModal',
    getComponent: () => import('./import-bill/ImportInputInvoiceModal'),
    width: 600,
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'CheckedIdNumModal',
    getComponent: () => import('./invoice-check/checkedIdNumModal'),
    width: 600,
    style: { minHeight: '318px' },
    maskClosable: false
  },
  {
    key: 'CheckedInvoiceModal',
    getComponent: () => import('./invoice-check/checkedInvoiceModal'),
    width: 600,
    style: { minHeight: '432px' },
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'EnterPayertaxnoModal',
    getComponent: () => import('./invoice-check/enterPayertaxnoModal'),
    width: 600,
    style: { minHeight: '253px' },
    maskClosable: false
  },
  {
    key: 'SelectDataLinkModal',
    getComponent: () => import('./dataLink/SelectDataLinkModal'),
    width: 960,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'SelectEbotDataLinkModal',
    getComponent: () => import('./dataLink/SelectEbotDataLinkModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'SelectDataLinkUpdateModal',
    getComponent: () => import('./dataLink/SelectDataLinkUpdateModal'),
    width: 960,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'SelectAssociatedDataLinkModal',
    enhancer: 'drawer',
    enhancerOptions: {
      title: '',
      footer: [],
      placement: 'bottom',
      className: 'custom-drawer-layer',
      closable: false,
      width: '100%',
      height: 'calc(100% - 48px)'
    },
    getComponent: () => import('./dataLink/SelectAssociatedDataLinkModal')
  },
  {
    key: 'SelectDataLinkEditModal',
    getComponent: () => import('./dataLink/SelectDataLinkEditModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'MultiSelectDataLinkModal',
    getComponent: () => import('./dataLink/MultiSelectDataLinkModal'),
    width: 960,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'NewOrderMultiSelectDataLinkModal',
    getComponent: () => import('./dataLink/NewOrderMultiSelectDataLinkModal'),
    width: 960,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'ImportRequistitionModal',
    getComponent: () => import('./import-bill/ImportRequistitionModal'),
    width: 800,
    maskClosable: false
  },
  {
    key: 'ApplyShareModal',
    getComponent: () => import('./ApplyShareModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI
  },
  {
    key: 'ApplyTransferModal',
    getComponent: () => import('./ApplyTransferModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI
  },
  {
    key: 'ApproveTransferModal',
    getComponent: () => import('./ApproveTransferModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI
  },
  {
    key: 'RejectedAddNodeModal',
    getComponent: () => import('./RejectedAddNodeModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer',
    isHoseEUI
  },
  {
    key: 'AdminAddNodeModal',
    getComponent: () => import('./AdminAddNodeModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer'
  },
  {
    key: 'AdminSkipNodeModal',
    getComponent: () => import('./AdminSkipNodeModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: '',
    footer: [],
    className: 'custom-modal-layer'
  },
  {
    key: 'ImportInvoiceDetailModal',
    getComponent: () => import('./import-bill/invoice/ImportInvoiceDetailModal'),
    width: 800,
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'ChangeTemplateModal',
    getComponent: () => import('./changeTemplateModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI
  },
  {
    key: 'ChangeStandardTemplateModal',
    getComponent: () => import('./StandardTemplateModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'NewChangeTemplateModal',
    getComponent: () => import('./NewChangeTemplateModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI,
    style: { borderRadius: '8px' }
  },
  {
    key: 'CheckPayeeModal',
    getComponent: () => import('./CheckPayeeModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'BillSubmitModal',
    getComponent: () => import('./BillSubmitModal'),
    width: 416,
    maskClosable: false,
    wrapClassName: '',
    isHoseEUI
  },
  {
    key: 'ScanBillModal',
    getComponent: () => import('./ScanBillModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'EditInvoice',
    getComponent: () => import('./import-bill/edit-ocr-entity/EditOCREntity'),
    width: 1020,
    maskClosable: false,
    wrapClassName: '',
    className: 'custom-modal-layer'
  },
  {
    key: 'ImportOCRListModal',
    getComponent: () => import('./import-bill/ocr/ImportOCRListModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: ''
  },
  {
    key: 'ImportUploadOCRModal',
    getComponent: () => import('./import-bill/ocr/ImportUploadOCRModal'),
    width: 520,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
    isHoseEUI
  },
  {
    key: 'ImportGPYModal',
    getComponent: () => import('./import-bill/gpy/ImportGPYModal'),
    width: 600,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal gpyModel'
  },
  {
    key: 'OwnerAndPrivilegeModal',
    width: 600,
    getComponent: () => import('./mine-datalink-list/index'),
    maskClosable: false
  },
  {
    key: 'DataLinkDetailModal',
    width: 600,
    getComponent: () => import('./dataLink-detail-modal/index'),
    maskClosable: false
  },
  {
    key: 'TicketBookingModal',
    width: 600,
    getComponent: () => import('./TicketBookingModal'),
    maskClosable: false
  },
  {
    key: 'SelectDateRange',
    getComponent: () => import('./SelectDateRange'),
    title: i18n.get('请选择未找到订单的实际支付日期（最多可选7天）'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'SelectDateRangeTrip',
    getComponent: () => import('./SelectDateRangeTrip'),
    title: i18n.get('请选择未找到订单的实际支付日期（最多可选7天）'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'EditPayPlanModal',
    getComponent: () => import('./payPlan/editPayPlanModal'),
    title: i18n.get('请输入支付金额'),
    width: 500,
    maskClosable: false
  },
  {
    key: 'ReceiptFileModal',
    getComponent: () => import('./payPlan/receiptFileModal'),
    title: i18n.get('回单文件'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'InvoiceFeetypeRecommend',
    getComponent: () => import('./import-bill/InvoiceFeetypeRecommend/InvoiceFeetypeRecommend'),
    width: 800,
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'TripInfoPopup',
    getComponent: () => import('./trip-info-popup/TripInfoPopup'),
    title: '',
    width: 400,
    timeout: 500
  },
  {
    key: 'TripInfoPopupNew',
    getComponent: () => import('./trip-info-popup/TripInfoPopupNew'),
    title: '',
    width: 400,
    timeout: 500
  },
  {
    key: 'TripTmcGetMoney',
    getComponent: () => import('./trip-tmc-get-money/TripTmcGetMoney'),
    title: '',
    width: 1100,
    maskClosable: false,
    timeout: 500
  },
  {
    key: 'TripOrderPopup',
    getComponent: () => import('./trip-order-popup/TripOrderPopup'),
    title: '',
    width: 400,
    timeout: 500
  },
  {
    key: 'InvoiceEditTaxModal',
    getComponent: () => import('./../../../elements/InvoiceCard/InvoiceEditModal'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'InvoiceDisableModal',
    getComponent: () => import('./../../../elements/InvoiceCard/InvoiceDisableModal'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'FeeDetailViewPopup', // 查询消费详情的侧弹窗
    getComponent: () => import('../bill-stacker-manager/pages/FeeDetailViewPopup'),
    title: '',
    width: 700,
    timeout: 500
  },
  {
    key: 'FeeDetailViewApplyList',
    getComponent: () => import('../bill-stacker-manager/pages/FeeDetailViewApplyList'),
    title: i18n.get('添加关联明细'),
    width: 700
  },
  {
    key: 'NoteEditModal',
    getComponent: () => import('./note/NoteEditModal'),
    width: 400,
    maskClosable: true,
    wrapClassName: 'vertical-center-modal high-z-index-modal',
    isHoseEUI
  },
  {
    key: 'DeleteBillModal',
    getComponent: () => import('./DeleteBillModal'),
    width: 400,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal',
    isHoseEUI
  },
  {
    key: 'NullifyBillModal',
    getComponent: () => import('./NullifyBillModal'),
    width: 400,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'AliPayInvoiceListModal',
    getComponent: () => import('./import-bill/invoice/AlipayInvoiceListModal'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'SelectFeeTypeModal',
    getComponent: () => import('./selectFeeTypeModal'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'PreviewOCRImageModal',
    getComponent: () => import('./import-bill/edit-ocr-entity/PreviewOCRImage'),
    width: 600,
    maskClosable: true,
    wrapClassName: '',
    className: 'custom-modal-layer'
  },
  {
    key: 'FlowPlanModal',
    getComponent: () => import('./flow-plan/FlowPlanModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'InvoiceApproveAmountModal',
    getComponent: () => import('../../../elements/InvoiceCard/InvoiceApproveAmountModal'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'SelectDateModal',
    getComponent: () => import('./SelectDateModal'),
    maskClosable: true,
    wrapClassName: '',
    className: 'custom-modal-layer'
  },
  {
    key: 'IframeModal',
    getComponent: () => import('./IframeModal'),
    maskClosable: true,
    wrapClassName: '',
    className: 'custom-modal-layer'
  },
  {
    key: 'IframePreviewerModal',
    getComponent: () => import('./IframePreviewerModal'),
    maskClosable: true,
    wrapClassName: '',
    className: 'custom-modal-layer'
  },
  {
    key: 'ImportAiFaPiaoH5',
    getComponent: () => import('./import-bill/invoice/ImportAiFaPiaoH5'),
    maskClosable: false,
    width: 500,
    timeout: 500
  },
  {
    key: 'CollaborationApproveStaff',
    getComponent: () => import('./flow/CollaborationApproveStaff'),
    width: 600,
    maskClosable: false
  },
  {
    key: 'BudgetDimensionView',
    getComponent: () => import('./import-detail-excel/BudgetDimensionView'),
    width: 600,
    maskClosable: true,
    wrapClassName: '',
    className: 'custom-modal-layer'
  },
  {
    key: 'ImportDetailModal',
    getComponent: () => import('./import-bill/ImportDetailModal'),
    width: 832,
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'custom-modal-layer' },
    maskClosable: false,
    isHoseEUI
  },
  {
    key: 'AddBillFullScreenModal',
    footer: null,
    closable: false,
    destroyOnClose: true,
    maskClosable: false,
    title: null,
    getComponent: () => import('./add-bill-fullscreen'),
    className: 'respond-modal-layer-fullScreen fullScreenModal'
  },
  {
    key: 'EditBillDrawer',
    footer: null,
    closable: false,
    destroyOnClose: true,
    maskClosable: false,
    title: null,
    width: 900,
    customWidth: billDrawerConfig.width,
    getComponent: () => import('./edit-bill-drawer')
  },
  {
    key: 'SettingsDrawer',
    width: 400,
    timeout: 500,
    footer: null,
    closable: false,
    destroyOnClose: true,
    maskClosable: false,
    title: null,
    getComponent: () => import('./add-bill-fullscreen/SettingsDrawer'),
    className: 'min400-ant-drawer-content-wrapper'
  },
  {
    key: 'BillFillModal',
    getComponent: () => import('./add-bill-fullscreen/BillFillModal'),
    maskClosable: false,
    title: '',
    width: 400,
    footer: null,
    maskStyle: { backgroundColor: 'rgba(255, 255, 255, 0.8)' },
    wrapClassName: 'billfill-modal-wrap',
    className: 'billfill-modal'
  },
  {
    key: 'BusinessStatementModal',
    getComponent: () => import('./BusinessStatementModal'),
    timeout: 500
  },
  {
    key: 'LogModal',
    getComponent: () => import('./log-modal'),
    width: 800,
    maskClosable: false
  },
  {
    key: 'LogVersionModal',
    getComponent: () => import('./log-modal/log-version-modal'),
    width: 800,
    maskClosable: false
  },
  {
    key: 'SubsidyDrawer',
    getComponent: () => import('./SubsidyDrawer'),
    title: '',
    width: '600',
    timeout: 500
  },
  {
    key: 'SubsidyTravelDrawer',
    getComponent: () => import('./SubsidyTravelDrawer'),
    title: '',
    width: '600',
    timeout: 500,
    className: 'custom-modal-layer'
  },
  {
    key: 'RuleSettingModal',
    getComponent: () => import('./rule-setting-modal'),
    width: 800,
    maskClosable: false
  },
  {
    key: 'FlowLinksSelectModal',
    getComponent: () => import('./FlowLinksSelectModal'),
    width: 700,
    maskClosable: false
  },
  {
    key: 'BillRiskReasonModal',
    width: 600,
    maskClosable: false,
    wrapClassName: '',
    getComponent: () => import('./BillRiskReasonModal')
  },
  {
    key: 'AmountConfigModal',
    getComponent: () => import('../../../elements/ApprovalAmountConfig/AmountConfigModal'),
    width: 800,
    maskClosable: false,
    wrapClassName: 'vertical-center-modal'
  },
  {
    key: 'ImportExcelModal',
    getComponent: () => import('../../../elements/importExcel/ImportExcelModal'),
    enhancer: 'modal',
    enhancerOptions: { title: '', footer: [], className: 'ekb-custom-modal' },
    width: 540,
    maskClosable: true
  },
  {
    key: 'MoneyModal',
    getComponent: () => import('../../../components/dynamic/dataLinkEdit/DataLinkEditTable/MoneyModal'),
    title: '',
    width: 600,
    timeout: 500
  },
  {
    key: 'EditMoneyModal',
    getComponent: () => import('../../../components/tableEdit/MoneyModal'),
    title: '',
    width: 600,
    timeout: 500
  },
  {
    key: 'AttachmentsModal',
    getComponent: () => import('../../../components/dynamic/dataLinkEdit/DataLinkEditTable/AttachmentsModal'),
    title: '',
    width: 600,
    timeout: 500
  },
  {
    key: 'ApportionsModal',
    getComponent: () => import('../../../components/tableEdit/ApportionsModal'),
    title: '',
    width: 800,
    timeout: 500,
    maskClosable: false
  },
  {
    key: 'ApportionsReadonlyModal',
    getComponent: () => import('../../../components/tableEdit/ApportionsReadonlyModal'),
    title: '',
    width: 800,
    timeout: 500
  },
  {
    key: 'InvoiceSelectModal',
    getComponent: () => import('../../../components/tableEdit/InvoiceSelectModal'),
    title: '',
    width: 800,
    timeout: 500,
    maskClosable: false
  },
  {
    key: 'InvoiceSelectReadonlyModal',
    getComponent: () => import('../../../components/tableEdit/InvoiceSelectReadonlyModal'),
    title: '',
    width: 800,
    timeout: 500
  },
  {
    key: 'DataLinkEditableModal',
    getComponent: () => import('../../../components/tableEdit/DataLinkEditableModal'),
    title: '',
    width: 800,
    maskClosable: false
  },
  {
    key: 'DataLinkEditableReadonlyModal',
    getComponent: () => import('../../../components/tableEdit/DataLinkEditableReadonlyModal'),
    title: '',
    width: 800,
    maskClosable: false
  },
  {
    key: 'BatchUpdateCityModal',
    getComponent: () => import('./BatchUpdateCityModal'),
    title: '',
    width: 600,
    timeout: 500
  },
  {
    key: 'BillTypeModal',
    getComponent: () => import('./BillTypeModal'),
    title: '',
    width: 520,
    isHoseEUI
  },
  {
    key: 'ImportInvoiceFromPool',
    getComponent: () => import('./import-bill/invoice/ImportInvoiceFromPool'),
    enhancer: 'modal',
    width: 600,
    wrapClassName: 'vertical-center-modal',
    enhancerOptions: { title: '', footer: [], className: 'custom-modal-layer' }
  },
  {
    key: 'ECardConsumeDetail',
    getComponent: () => import('./ECardConsumeDetail'),
    title: '',
    width: '300',
    className: 'min400-ant-drawer-content-wrapper'
  },
  {
    key: 'BillInfoModal',
    getComponent: () => import('./bill-info-popup/BillInfoModal'),
    width: 800,
    maskClosable: true,
    className: 'custom-modal-layer-no-footer'
  },
  {
    key: 'BillHistoryModal',
    getComponent: () => import('../history'),
    width: 520,
    isHoseEUI: true,
    maskClosable: false
    // className: 'custom-modal-layer-no-footer'
  },
  {
    key: 'FlowHistoryModifyDetailModal',
    getComponent: () => import('../history/FlowHistoryModifyDetailModal'),
    width: 520,
    isHoseEUI: true,
    maskClosable: false
  },
  {
    key: 'InvoiceDetailModal',
    width: 600,
    getComponent: () => import('./dataLink-detail-modal/InvoiceDetailModal'),
    maskClosable: false
  },
  {
    key: 'BillInfoDrawer',
    getComponent: () => import('./bill-info-popup/BillInfoDrawer'),
    footer: null,
    id: 'bill-info-drawer',
    closable: false,
    customWidth: billDrawerConfig.width,
    destroyOnClose: true,
    title: null
  },
  {
    key: 'SelectPayeePopup',
    getComponent: () => import('./SelectPayeePopup'),
    title: '选择收款信息',
    isHoseEUI: true,
    className: 'new-select-paypee-drawer-wrapper'
  },
  {
    key: 'PayeeAccountCreateModal',
    getComponent: () => import('./PayeeAccountCreateModal'),
    title: '',
    width: 600,
    maskClosable: false,
    isHoseEUI,
    closable: true
  },
  {
    key: 'BillInfoDrawer',
    getComponent: () => import('./bill-info-popup/BillInfoDrawer'),
    footer: null,
    id: 'bill-info-drawer',
    closable: false,
    customWidth: billDrawerConfig.width,
    destroyOnClose: true,
    title: null
  },
  {
    key: 'BillInfoDrawerV2',
    getComponent: () => import('../bill-details-new/Drawer'),
    title: '',
    customWidth: billDrawerConfig.width,
    closable: false,
    destroyOnClose: true,
    mask: false,
    isEUI: true,
  },
  {
    key: 'InvoicesOrAttachmentsModal',
    getComponent: () => import('./invoices-or-attachments-modal'),
    title: '',
    width: 512,
    maskClosable: false,
    isHoseEUI,
    closable: true
  }
]
