import React, { Component } from 'react'
import { EnhanceDrawer, ILayerProps } from '@ekuaibao/enhance-layer-manager'
import { Button, Card, DatePicker, Form } from 'antd'
import styles from './SubsidyTravelDrawer.module.less'
import { getCalculateTravelAmount } from '../bills.action'
import { FormComponentProps } from 'antd/es/form'
import moment from 'moment'
import { cloneDeep } from 'lodash'
const Btn: any = Button
const { RangePicker } = DatePicker
const FormItem = Form.Item
import { showMessage, showModal } from '@ekuaibao/show-util'
import SubsidyTravelMoney from './subsidy-drawer/SubsidyTravelMoney'
import SubsidyTravelDrawerItem from './subsidy-drawer/SubsidyTravelDrawerItem'
import {
  formatCity,
  formatSubsidyRequestData,
  getDateTime,
  getDateTime2,
  getDefaultSubsidyItem,
  getDiffDays,
  layout,
  parseSubsidyData
} from './subsidy-drawer/utils'
import { app as api } from '@ekuaibao/whispered'
import { GlobalFieldIF } from '@ekuaibao/ekuaibao_types'
import { IDataGroup, IDataItem, ITravelConfirm } from './subsidy-drawer/types'
import { isObject } from '@ekuaibao/helpers'
interface IProps extends ILayerProps {
  requisitionIds: string[]
  travelConfirmList: ITravelConfirm
  canModifyDate: boolean
  confirmCity: boolean
  checkDate: boolean
  expressTrack?: (key: string, opt: unknown) => void
}

interface IState {
  travelConfirmList: ITravelConfirm
  startTime?: number
  endTime?: number
  current: number
  travelAmountData: any
  loading: boolean
  vDate: any[]
  confirmFieldObjects: GlobalFieldIF[]
}

const steps = [
  {
    key: 'SubsidyCalculate',
    content: '计算补助'
  },
  {
    key: 'SubsidyTravelMoney',
    content: '补助金额'
  }
]
@EnhanceDrawer({
  width: 700,
  class: {
    left: 'drag-hover'
  }
})
class SubsidyTravelDrawer extends Component<IProps & FormComponentProps, IState> {
  constructor(props: IProps & FormComponentProps) {
    super(props)
    this.state = {
      travelConfirmList: {} as ITravelConfirm,
      current: 0,
      travelAmountData: null,
      loading: false,
      vDate: [],
      confirmFieldObjects: []
    }
  }

  componentDidMount(): void {
    const { travelConfirmList } = this.props
    if (travelConfirmList?.requisitionIds) {
      const { startTime, endTime } = travelConfirmList
      this.setState({
        startTime,
        endTime
      })
      this.formatDataByTime(travelConfirmList)
    }
  }

  formatDataByTime = (result: ITravelConfirm) => {
    // 调试confirmFields使用:'u_职级Y', 'u_提交人部门', 'u_用车车型'
    // const { startTime, endTime, data, confirmFields = [] } = result
    // const allDate = getDiffDays(startTime, endTime).map(item => getDateTime(item))
    // const originalData = allDate.map(item => {
    //   const isMall = data.find(oo => oo.date === item)
    //   if (isMall) {
    //     return { ...isMall }
    //   } else {
    //     return {
    //       date: item,
    //       items: []
    //     }
    //   }
    // })
    // result.data = originalData
    this.setState({ travelConfirmList: {} as ITravelConfirm }, () => {
      this.setState({ travelConfirmList: result })
    })
  }

  handleChangeCity = (index, itemNumber, selectVal) => {
    const { travelConfirmList } = this.state
    const cloneData = cloneDeep(travelConfirmList)
    cloneData.data[index].items[itemNumber].city = `${selectVal}`
    this.setState({ travelConfirmList: cloneData })
  }

  handleSubsidyTravelChange = (dataItem: IDataItem, index: number, itemNumber: number) => {
    const { travelConfirmList } = this.state
    const cloneData = cloneDeep(travelConfirmList)
    cloneData.data[index].items[itemNumber] = dataItem
    this.setState({ travelConfirmList: cloneData })
  }

  handleChangeDimension = (index, itemNumber, selectVal) => {
    const { travelConfirmList } = this.state
    const cloneData = cloneDeep(travelConfirmList)
    cloneData.data[index].items[itemNumber].dimensionItems = selectVal
    this.props.form.setFieldsValue({
      [`dimension_${index}-${itemNumber}`]: selectVal
    })
    this.setState({ travelConfirmList: cloneData })
  }

  handleAddDimension = (index: number) => {
    const { travelConfirmList } = this.state
    const { confirmCity } = this.props
    const cloneData = cloneDeep(travelConfirmList)
    const fields = getDefaultSubsidyItem(travelConfirmList.confirmFields, confirmCity)
    cloneData.data[index].items = cloneData.data[index].items?.concat(fields)
    this.setState({ travelConfirmList: cloneData })
  }

  handleRemoveCard = (index, date) => {
    const { travelConfirmList } = this.state
    const cloneData = cloneDeep(travelConfirmList)
    if (cloneData.data.length === 1) {
      showMessage.warning('行程数据被删除后，该行程确认页将为空，无法申请补助，请不要删除。')
      return
    }
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get(`确定后将删除「${moment(date).format('YYYY-MM-DD')}」所有行程数据`),
      cancelText: i18n.get('取消'),
      okText: i18n.get('确定'),
      onOk: () => {
        const originalArr = cloneData.data
        originalArr.splice(index, 1)
        cloneData.data = originalArr
        this.setState({ travelConfirmList: {} as ITravelConfirm }, () => {
          this.setState({ travelConfirmList: cloneData })
        })
      }
    })
  }

  handleRemoveItem = (index, itemNumber) => {
    const { travelConfirmList } = this.state
    const cloneData = cloneDeep(travelConfirmList)
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get(`确认删除行程?`),
      cancelText: i18n.get('取消'),
      okText: i18n.get('确定'),
      onOk: () => {
        const originalArr = cloneData.data[index].items
        originalArr.splice(itemNumber, 1)
        cloneData.data[index].items = originalArr
        this.setState({ travelConfirmList: {} as ITravelConfirm }, () => {
          this.setState({ travelConfirmList: cloneData })
        })
      }
    })
  }

  handleCalculate = () => {
    const { form, requisitionIds, layer, expressTrack, confirmCity } = this.props
    const { travelConfirmList } = this.state
    expressTrack?.('calculate_subsidy_click', { actionName: '点击【计算补助】', subsidy_type: '定额补助' })
    travelConfirmList.ids = requisitionIds
    const validateErrorMap = {}
    travelConfirmList.data.forEach((dataItem, groupIndex) => {
      dataItem.items.forEach((dimensionItem, dataIndex) => {
        let hasEmpty = false
        dimensionItem.dimensionFields?.forEach(item => {
          const value = isObject(item.dimensionValue) ? item.dimensionValue.id : item.dimensionValue
          if (!value?.length) {
            hasEmpty = true
            return
          }
        })
        if (hasEmpty) {
          validateErrorMap[`city_${groupIndex}-${dataIndex}`] = {
            value: dimensionItem,
            errors: [new Error(i18n.get('请填写行程信息'))]
          }
        }
      })
    })
    if (Object.keys(validateErrorMap).length) {
      form.setFields(validateErrorMap)
      return
    }
    const empty = travelConfirmList.data.filter(oo => oo.items.length === 0).map(ii => ii.date)
    if (empty.length > 0 && confirmCity) {
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get(`${empty.map(i => moment(i).format('YYYY-MM-DD')).join(',')}未填写行程数据，是否继续`),
        cancelText: i18n.get('取消'),
        okText: i18n.get('确定'),
        onOk: () => {
          this.fnSubmit(layer, travelConfirmList)
        }
      })
      return
    }
    this.fnSubmit(layer, travelConfirmList)
  }

  fnSubmit = async (layer, travelConfirmData) => {
    this.setState({ loading: true })
    const cloneData = cloneDeep(travelConfirmData)
    cloneData.data = formatSubsidyRequestData(cloneData.data)
    const result = await getCalculateTravelAmount(cloneData).catch(e => {
      showMessage.warning(e.message || e.msg)
    })
    this.setState({ loading: false })
    if (result?.value?.requisitionIds) {
      const current = this.state.current + 1
      this.setState({ current, travelAmountData: result.value })
      layer.override({ title: '补助金额' })
    }
  }

  handleCreateBill = async () => {
    const { travelAmountData } = this.state
    const { layer, expressTrack } = this.props
    expressTrack?.('recognized_subsidy_click', { actionName: '点击【确认补助】', subsidy_type: '定额补助' })
    this.setState({ loading: true })
    const result = await api.invokeService('@bills:getTravelResult', travelAmountData).catch(e => {
      this.setState({ loading: false })
    })
    if (result) {
      if (result.value) {
        layer.emitOk(result)
      } else {
        this.setState({ loading: false })
        showMessage.warning('系统正在处理中，请稍后重试')
      }
    }
  }

  handleBack = () => {
    const { current } = this.state
    this.setState({ current: current - 1 })
    this.props.layer.override({ title: '行程确认' })
  }

  handleChangeDate = val => {
    const { startTime, endTime } = this.state
    const now = getDateTime() + 24 * 60 * 60 * 1000 - 1
    const sd = getDateTime2(val[0])
    const ed = getDateTime2(val[1])
    const { checkDate } = this.props
    if (!checkDate) {
      this.setState({ vDate: val })
      return
    }
    if (sd > startTime || sd > now) {
      showMessage.error(i18n.get('选择的行程起止时间，只能选包含商城消费的日期'))
    } else if (ed < endTime || ed > now) {
      showMessage.error(i18n.get('选择的行程起止时间，只能选包含商城消费的日期'))
    } else {
      this.setState({ vDate: val })
    }
  }

  handlePanelChangeDate = val => {
    const { travelConfirmList } = this.state
    const sd = getDateTime2(val[0])
    const ed = getDateTime2(val[1])
    showModal.confirm({
      title: i18n.get('提示'),
      content: i18n.get(`确定更改时间？`),
      cancelText: i18n.get('取消'),
      okText: i18n.get('确定'),
      onOk: () => {
        travelConfirmList.startTime = sd
        travelConfirmList.endTime = ed
        this.setState({ travelConfirmList, vDate: val }, () => {
          this.formatDataByTime(travelConfirmList)
        })
      },
      onCancel: () => {
        const { vDate } = this.state
        this.setState({ vDate })
      }
    })
  }

  disabledDated = (current: any) => {
    return current && current > moment().endOf('day')
  }

  render() {
    const { travelConfirmList, current, travelAmountData, loading, vDate, confirmFieldObjects } = this.state
    const { canModifyDate, confirmCity } = this.props
    const { data, startTime, endTime, confirmFields } = travelConfirmList
    if (!Object.keys(travelConfirmList).length) {
      return null
    }
    return (
      <div className={styles.subsidyDrawerWrapper}>
        {steps[current].key === 'SubsidyCalculate' ? (
          <>
            <Card className={styles.topHeader}>
              <FormItem label={i18n.get('行程起止时间')} {...layout} required>
                <RangePicker
                  style={{ width: '100%' }}
                  disabledDate={this.disabledDated}
                  allowClear={false}
                  disabled={!canModifyDate}
                  value={!!vDate.length ? vDate : [moment(startTime), moment(endTime)]}
                  onChange={this.handleChangeDate}
                  onOk={this.handlePanelChangeDate}
                  showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD HH:mm"
                  mode={['date', 'date']}
                />
              </FormItem>
            </Card>
            <div className={styles.subsidyDrawerContent}>
              <Form>
                {data.map((group: IDataGroup, index: number) => {
                  const { date, items } = group
                  const cardCanRemove = !items.filter(oo => oo.orderIds?.length)?.length
                  let disabledCity = []
                  items.forEach(dataItem => {
                    if (dataItem.city?.length) {
                      disabledCity = disabledCity.concat(formatCity(dataItem.city).map(oo => oo.key))
                    }
                    if (dataItem.dimensionFields?.length) {
                      dataItem.dimensionFields.map(dimensionItem => {
                        if (dimensionItem.dimensionField === 'city' && dimensionItem.dimensionValue?.length) {
                          disabledCity = disabledCity.concat(formatCity(dimensionItem.dimensionValue).map(oo => oo.key))
                        }
                      })
                    }
                  })
                  return (
                    <SubsidyTravelDrawerItem
                      key={date}
                      date={date}
                      index={index}
                      items={items}
                      onAddDimension={this.handleAddDimension}
                      onRemoveCard={this.handleRemoveCard}
                      onRemoveItem={this.handleRemoveItem}
                      form={this.props.form}
                      cardCanRemove={cardCanRemove}
                      disabledCity={disabledCity}
                      confirmCity={confirmCity}
                      onChange={this.handleSubsidyTravelChange}
                    />
                  )
                })}
              </Form>
            </div>
          </>
        ) : (
          <SubsidyTravelMoney travelAmountData={travelAmountData} startTime={startTime} endTime={endTime} />
        )}
        <div className={styles.subsidyDrawerFooter}>
          {current < steps.length - 1 && (
            <Btn type="primary" className="btn-ml mr-8" onClick={this.handleCalculate} loading={loading}>
              {i18n.get('计算补助')}
            </Btn>
          )}
          {current === steps.length - 1 && (
            <Btn type="primary" className="btn-ml mr-8" onClick={this.handleCreateBill} loading={loading}>
              {i18n.get('确定')}
            </Btn>
          )}
          {current > 0 && (
            <Btn className="btn-ml mr-8" onClick={() => this.handleBack()}>
              {i18n.get('上一步')}
            </Btn>
          )}
          {current > 0 && (
            <span className={styles['surplus-amount']}>￥ {travelAmountData.subsidyAmount.toFixed(2)}</span>
          )}
        </div>
      </div>
    )
  }
}

const SubsidyDrawerForm = Form.create()(SubsidyTravelDrawer)
export default SubsidyDrawerForm
