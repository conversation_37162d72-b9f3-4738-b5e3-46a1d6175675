import './NewSelectPayeeModal.less'
import React from 'react'
import { <PERSON><PERSON>, Tooltip, Tabs } from '@hose/eui'
import { IllustrationSmallNoSearch, OutlinedTipsAdd, OutlinedTipsInfo } from '@hose/eui-icons'
import { EnhanceConnect } from '@ekuaibao/store'
import SearchInput from '../../../elements/search-input'
import { app as api } from '@ekuaibao/whispered'
import { savePayee, getDefaultPayee, setDefaultPayee, cancelDefaultPayee } from './SelectPayeeModal.action'
import { Resource } from '@ekuaibao/fetch'
import InfiniteScroll from 'react-infinite-scroller'
import { memoize, debounce, remove } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'
import { payFromChannelMap } from '../../../elements/payee-account/account-list-item'
import MessageCenter from '@ekuaibao/messagecenter'
import { observer } from 'mobx-react'
import { inject, provider } from '@ekuaibao/react-ioc'
import BankCard from '../../../elements/bank-card/index'
const { PermissionVm } = api.require('@payeeAccount/vms/Permission.vm')
const EmptyBody = api.require('@bills/elements/EmptyBody')
const accounts = new Resource('/api/pay/v2/accounts')
const PAGE_SIZE = 10
const TabPane = Tabs.TabPane
const CustomSearch = api.require('@elements/data-grid-v2/CustomSearch')
const isWx = window.__PLANTFORM__ === 'WEIXIN'
import { getBoolVariation } from '../../../lib/featbit'

@EnhanceConnect(state => ({
  WalletPower: state['@common'].powers.Wallet,
  payeeConfig: state['@common'].payeeConfig,
  defaultPayee: state['@common'].defaultPayee,
  userInfo: state['@common'].userinfo.data,
  payeeConfigCheck: state['@user-info'].payeeConfigCheck,
  submitterData: state['@bills'].submitterData
}))
@provider(['permission', PermissionVm])
@observer
export default class NewSelectPayeeModal extends React.Component {
  @inject('permission') permission
  bus

  constructor(props) {
    super(props)
    let cardTabKey = this.getDataSource('cardTabKey')
    this.state = {
      defaultId: '',
      selectPayee: props.data,
      selectPayees: [],
      selectPayeeKeys: [],
      searchText: '',
      staffIds: '',
      list: [],
      walletAccountList: [],
      start: 0,
      hasMore: false,
      loading: false,
      curKey: cardTabKey?.length ? cardTabKey : 'ALL',
      cancelDependence: false
    }
    this.bus = props.bus || new MessageCenter()
    api.dataLoader('@common.payeeConfig').reload()
    props.overrideGetResult(this.getResult)
  }

  getDataSource = key => {
    const dataSource = isWx ? session.get(key) : localStorage.getItem(key)
    return dataSource ? dataSource : ''
  }

  setDataSource = (key, val) => {
    if (isWx) {
      session.set(key, val)
    } else {
      localStorage.setItem(key, val)
    }
  }

  componentWillMount() {
    api.invokeService('@common:get:mc:permission:byName', 'ACCOUNT_PEE').then(result => {
      if (result?.value) {
        this.permission?.setEnableMC(result?.value)
      }
    })
  }

  componentDidMount() {
    getDefaultPayee()
    this.fetch(true)
    this.fetchWalletAccount()
    this.bus.watch('payee:save:click', this.handleSaveAccount) //保存收款账户
  }

  componentWillUnmount() {
    this.bus.un('payee:save:click', this.handleSaveAccount) //保存收款账户
  }

  getFetch = async params => {
    const searchNew = getBoolVariation('account_search_optimization', false)
    const { fromSupplier, templateid, billSpecification, userInfo, submitterData } = this.props
    const formType = billSpecification?.type
    const submitterId = submitterData?.id
    const staffId = userInfo?.staff?.id
    if (fromSupplier) {
      params.allVisible = true
    }
    params.templateid = templateid
    params.formType = formType ?? 'expense'
    params.submitterId = submitterId ?? staffId
    return accounts.POST(searchNew ? '/mineByEs' : '/mine', params, { join: 'branchId,branchId,/pay/v1/banks' })
  }

  fetchWalletAccount = () => {
    const { curKey } = this.state
    const { WalletPower, flowId, dependenceList } = this.props
    if (!WalletPower) return
    this.setState({ loading: true })
    let tabFilter = ''
    if (curKey != 'ALL' && curKey != 'COLLECTION') {
      tabFilter = `&&type=="${curKey}"`
    }
    let filter = `(asPayee==true&&active==true&&sort=="WALLET"${tabFilter})`
    if (!!dependenceList?.length) {
      filter += `&&id.in(${dependenceList.map(v => `"${v}"`)})`
    }
    const params = {
      flowId,
      start: 0,
      count: 10,
      filter,
      favoriteStatus: curKey === 'COLLECTION' ? true : null
    }
    this.getFetch(params).then(
      ({ items }) => {
        if (curKey === this.state.curKey) {
          this.setState({
            loading: false,
            walletAccountList: items
          })
        }
      },
      error => this.setState({ loading: false })
    )
  }

  fetch(reload) {
    const { curKey, cancelDependence = false, staffIds } = this.state
    const { dependenceList } = this.props
    if (!reload && this.state['loading']) {
      return
    }
    const start = reload ? 0 : this.state['start'] + PAGE_SIZE
    const list = reload ? [] : this.state['list']
    this.setState({ loading: true, list })
    let tabFilter = ''
    if (curKey != 'ALL' && curKey != 'COLLECTION') {
      tabFilter = `&&type=="${curKey}"`
    }
    let filter = `(asPayee==true&&active==true&&sort!="WALLET"${tabFilter})`
    let nameLike = ''
    let searchValue = this.state.searchText
    if (searchValue) {
      filter = ` (asPayee==true&&active==true${tabFilter})` // && (accountName.contains(${searchValue}) || accountNo.contains(${searchValue}) || staffId.name.contains(${searchValue}))`
      nameLike = searchValue
    }
    if (staffIds) {
      filter = filter + `&&${staffIds}`
    }
    if (dependenceList) {
      if (!!dependenceList.length) {
        filter += `&&id.in(${dependenceList.map(v => `"${v}"`)})`
      } else if (!cancelDependence) {
        this.setState({
          loading: false,
          list: [],
          start: start,
          hasMore: false
        })
        return
      }
    }
    const params = {
      flowId: this.props.flowId,
      start: start,
      count: PAGE_SIZE,
      filter,
      nameLike,
      favoriteStatus: curKey === 'COLLECTION' ? true : null
    }
    this.getFetch(params).then(
      ({ items = [] }) => {
        if (curKey === this.state.curKey) {
          this.setState({
            loading: false,
            list: list.concat(items),
            start: start,
            hasMore: items.length === PAGE_SIZE
          })
        }
      },
      error => this.setState({ loading: false })
    )
  }

  getResult = () => {
    const { multiple } = this.props
    return multiple ? this.state.selectPayees : this.state.selectPayee
  }

  handleSetDefBank = doc => {
    setDefaultPayee({ id: doc.id })
      .then(() => {
        //新建后再刷新收款信息集合
        api.invokeService('@common:get:payeeinfos')
        getDefaultPayee()
        this.fetch(true)
      })
      .catch(err => {
        showMessage.error(err?.message)
      })
  }

  handleCancelDefault = doc => {
    cancelDefaultPayee({ accountId: doc.id })
      .then(() => {
        //新建后再刷新收款信息集合
        api.invokeService('@common:get:payeeinfos')
        getDefaultPayee()
        this.fetch(true)
      })
      .catch(err => {
        showMessage.error(err?.message)
      })
  }

  // 收藏账户
  handleCollect = line => {
    api
      .invokeService('@bills:add:or:cancel:collect', { id: line.id, enable: true })
      .then(() => {
        this.fetch(true)
      })
      .catch(err => {
        if (err.msg || err.errorMessage) showMessage.error(err.msg || err.errorMessage)
      })
  }
  // 取消收藏账户
  handleCancelCollect = line => {
    api.invokeService('@bills:add:or:cancel:collect', { id: line.id, enable: false }).then(() => {
      this.fetch(true)
    })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    this.props.layer.emitOk()
  }

  handleSelectedCard = item => {
    const { multiple } = this.props
    if (multiple) {
      const { selectPayees } = this.state
      if (item.filterActive) {
        const target = selectPayees.find(originItem => originItem.id === item.id)
        target ? remove(selectPayees, originItem => originItem.id === item.id) : selectPayees.push(item)
        const selectPayeeKeys = selectPayees.map(item => item.id)
        this.setState({ selectPayees, selectPayeeKeys })
      }
    } else {
      item.filterActive && this.setState({ selectPayee: item })
    }
  }

  __handleInsertAssist = title => {
    api.invokeService('@common:insert:assist:record', {
      title
    })
  }

  handleSaveAccount = data => {
    data.owner = 'INDIVIDUAL'
    savePayee(data)
      .then(() => {
        setTimeout(() => {
          this.fetch(true)
        }, 500)
      })
      .catch(err => (err?.errorMessage ? showMessage.error(err.errorMessage) : showMessage.error(i18n.get('保存失败'))))
    if (data && data.name) {
      if (data.isCreate) {
        this.__handleInsertAssist(`创建${data.name}收款信息`) // @i18n-ignore
      } else {
        this.__handleInsertAssist(`修改${data.name}收款信息`) // @i18n-ignore
      }
    }
  }

  handleAddBank = () => {
    api.open('@bills:PayeeAccountCreateModal', {
      bus: this.bus,
      showClose: true,
      payFromChannel: payFromChannelMap.personal
    })
  }

  handleSearchInputChange = e => {
    const { value } = e.target
    ;(!value || value.trim().length) && this.fnInputValueChange(value)
  }

  fnInputValueChange = debounce(value => {
    this.setState({ searchText: value }, () => {
      this.fetch(true)
    })
  }, 1200)

  handleCancelDependence = () => {
    this.setState(
      {
        cancelDependence: true
      },
      () => {
        this.fetch(true)
      }
    )
  }

  renderEmpty = () => {
    const { dependenceList, allowCancelDependence = false } = this.props
    const { cancelDependence = false, loading = false, curKey, searchText } = this.state
    const euiIcon = searchText ? <IllustrationSmallNoSearch fontSize={150} /> : null
    let allowCancelDependenceTemp = !!dependenceList && allowCancelDependence
    let tips = !!loading
      ? '正在查询中，请稍后...'
      : curKey === 'COLLECTION'
      ? i18n.get('暂无已收藏的账户信息')
      : i18n.get('您目前没有可用的收款信息')
    return (
      <EmptyBody euiIcon={euiIcon} size={150} label={i18n.get(tips)}>
        {!cancelDependence && allowCancelDependenceTemp && !loading && (
          <div>
            <span className="fw-b">{i18n.get('点击')}</span>
            <span className="clickable" onClick={this.handleCancelDependence} data-testid="pay-view-all-data">
              {i18n.get('查看全量数据')}
            </span>
          </div>
        )}
      </EmptyBody>
    )
  }

  renderPublicTab = () => {
    return (
      <>
        {i18n.get('对公账户')}
        <Tooltip placement="top" title={i18n.get('个体工商户、企业账户')}>
          <OutlinedTipsInfo style={{ marginLeft: 4 }} />
        </Tooltip>
      </>
    )
  }
  handleCustomSearch = value => {
    this.setState(
      {
        searchText: value.searchText,
        staffIds: value.staffIds
      },
      () => {
        this.fetch(true)
      }
    )
  }

  renderAccount() {
    const { isModify, payeeConfigCheck = {}, filterRules } = this.props
    const searchNew = getBoolVariation('account_search_optimization', false)
    const fitlerRuleType = filterRules?.find(item => item.name === 'type')
    const { curKey } = this.state
    const isAllowCreate = !isModify && (payeeConfigCheck.personalAccount || payeeConfigCheck.publicAccount)
    return (
      <div className="payee-card-wrapper">
        <div className="card-header">
          <div className="search-card">
            {searchNew ? (
              <CustomSearch
                options={[
                  {
                    key: 'searchText',
                    label: i18n.get('常用'),
                    placeholder: i18n.get('输入账户名称、银行卡号、证件号码、备注'),
                    type: 'text',
                    format: value => {
                      if (!value) {
                        return ''
                      }
                      return { searchText: value }
                    }
                  },
                  {
                    key: 'staffId',
                    label: '所有者',
                    type: 'staff',
                    placeholder: i18n.get('请输入要搜索的所有者'),
                    format: value => {
                      if (!value?.length) {
                        return ''
                      }
                      const str = value.map(item => `"${item}"`).join(',')
                      return { staffIds: `staffId.in(${str})` }
                    }
                  }
                ]}
                onSearch={this.handleCustomSearch}
                data-testid="pay-custom-search"
              />
            ) : (
              <SearchInput
                    className="search"
                    placeholder={i18n.get('搜索账户名称、银行卡号、证件号码、所有者、备注')}
                    onChange={this.handleSearchInputChange}
                    data-testid="pay-search-input"
                  />
            )}
          </div>
          {this.permission?.isEnableMC && isAllowCreate && !isModify && (
            <Button
                size="middle"
                category="secondary"
                theme={'highlight'}
                icon={<OutlinedTipsAdd />}
                onClick={this.handleAddBank}
                data-testid="pay-add-bank-button"
              >
              {i18n.get('新建')}
            </Button>
          )}
        </div>
        <Tabs defaultActiveKey={curKey} onChange={this.handleChange} data-testid="pay-account-tabs">
          <TabPane tab={i18n.get('全部')} key="ALL" data-testid="pay-tab-all" />
          {(!fitlerRuleType || fitlerRuleType.value.includes('PERSONAL')) && (
            <TabPane tab={i18n.get('个人账户')} key="PERSONAL" data-testid="pay-tab-personal" />
          )}
          {(!fitlerRuleType || fitlerRuleType.value.includes('PUBLIC')) && (
            <TabPane tab={this.renderPublicTab()} key="PUBLIC" data-testid="pay-tab-public" />
          )}
          <TabPane tab={i18n.get('我的收藏')} key="COLLECTION" data-testid="pay-tab-collection" />
        </Tabs>
        <div className="payee-card-list">{this.renderList()}</div>
      </div>
    )
  }

  handleChange = activeKey => {
    this.setDataSource('cardTabKey', activeKey)
    this.setState({ curKey: activeKey }, () => {
      this.fetch(true)
    })
  }

  fetchMore = memoize(() => () => this.fetch(false))

  renderItem = (item, idx) => {
    const { multiple, defaultPayee } = this.props
    const { selectPayee, selectPayeeKeys } = this.state
    const isSelected = multiple
      ? selectPayeeKeys.length > 0 && selectPayeeKeys.includes(item.id)
      : selectPayee && selectPayee.id === item.id
    return (
      <div key={idx}
        className="payer-item"
        onClick={() => {
          this.handleSelectedCard(item)
        }}
        data-testid={`pay-account-item-${idx}`}>
        <BankCard
          isSelected={isSelected}
          inSelectPayMethodModal={item.sort === 'WALLET'}
          formChannel="payee"
          className={`${item.filterActive ? '' : 'gray-bg'}`}
          isManangePage
          payFromChannel={payFromChannelMap.select}
          handleSetDefault={() => this.handleSetDefBank(item)}
          handleClickCancelDefault={() => this.handleCancelDefault(defaultPayee)}
          data={item}
          showCancleDefault={true}
          isDefault={item.isDefault}
          handleCollect={() => this.handleCollect(item)}
          handleCancelCollect={() => this.handleCancelCollect(item)}
          showCollectionBtns={true}
        />
      </div>
    )
  }

  renderWalletAccount = () => {
    const { walletAccountList, searchText } = this.state
    if (!walletAccountList.length || searchText) return null
    return walletAccountList.map(el => this.renderItem(el, el.id))
  }

  renderList() {
    const { list, hasMore } = this.state
    return (
      <InfiniteScroll hasMore={hasMore} loadMore={this.fetchMore()} useWindow={false}>
        {this.renderWalletAccount()}
        {list.length > 0 ? list.map(el => this.renderItem(el, el.id)) : this.renderEmpty()}
      </InfiniteScroll>
    )
  }

  render() {
    return (
      <div id={'bills-selectPayeeModal'} className="new-select-payee-modal">
        <div className="select-payee-content">{this.renderAccount()}</div>
        <div className="modal-footer">
          <Button key="ok" data-testid="bills-selectPayee-confirm" category="primary" onClick={this.handleOK} style={{ marginRight: 12 }}>
            {i18n.get('确认')}
          </Button>
          <Button key="cancel" data-testid="bills-selectPayee-cancel" category="secondary" className="mr-8" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
}
