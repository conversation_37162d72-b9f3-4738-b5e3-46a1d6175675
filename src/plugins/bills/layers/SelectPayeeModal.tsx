import './SelectPayeeModal.less'
import React from 'react'
import { Tabs } from 'antd'
import { Button, Toolt<PERSON> } from '@hose/eui'
import { OutlinedTipsClose, OutlinedTipsInfo } from '@hose/eui-icons'
import { EnhanceConnect } from '@ekuaibao/store'
import SearchInput from '../../../elements/search-input'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceModal, ILayerProps } from '@ekuaibao/enhance-layer-manager'
import { savePayee, getDefaultPayee, setDefaultPayee, cancelDefaultPayee } from './SelectPayeeModal.action'
import { Resource } from '@ekuaibao/fetch'
import InfiniteScroll from 'react-infinite-scroller'
import { memoize, debounce, remove } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'
import AccountListItem, { payFromChannelMap } from '../../../elements/payee-account/account-list-item'
import MessageCenter from '@ekuaibao/messagecenter'
import { observer } from 'mobx-react'
import { inject, provider } from '@ekuaibao/react-ioc'
import { getBoolVariation } from '../../../lib/featbit/init'
const { PermissionVm } = api.require<any>('@payeeAccount/vms/Permission.vm')
const EmptyBody = api.require<any>('@bills/elements/EmptyBody')
const accounts = new Resource('/api/pay/v2/accounts')
const PAGE_SIZE = 10
const TabPane = Tabs.TabPane
const isWx = window.__PLANTFORM__ === 'WEIXIN'

interface Props extends ILayerProps {
  bus: MessageCenter
  flowId: any
  fromSupplier: any
  dependenceList: any
  isFeeDetailPayeeId: boolean
  multiple: any
  defaultPayee: any
  templateid: any
  WalletPower: any
  data: any
  isModify: boolean
  payeeConfigCheck: any
  billSpecification?: any
  userInfo?: any
  submitterData?: any
  allowCancelDependence?: boolean
  filterRules?: any //过滤配置
}

interface States {
  defaultId: string
  selectPayee: any
  selectPayees: any[]
  selectPayeeKeys: any[]
  searchText: string
  list: any[]
  walletAccountList: any[]
  start: number
  count: number
  loading: boolean
  curKey: string
  cancelDependence: boolean
}

@EnhanceConnect(state => ({
  WalletPower: state['@common'].powers.Wallet,
  payeeConfig: state['@common'].payeeConfig,
  defaultPayee: state['@common'].defaultPayee,
  userInfo: state['@common'].userinfo.data,
  payeeConfigCheck: state['@user-info'].payeeConfigCheck,
  submitterData: state['@bills'].submitterData
}))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer'
})
@provider(['permission', PermissionVm])
@observer
export default class SelectPayeeModal extends React.Component<Props, States> {
  @inject('permission') permission: any
  bus: MessageCenter

  constructor(props: Props) {
    super(props)
    let cardTabKey = this.getDataSource('cardTabKey')
    this.state = {
      defaultId: '',
      selectPayee: props.data,
      selectPayees: [],
      selectPayeeKeys: [],
      searchText: '',
      list: [],
      walletAccountList: [],
      start: 0,
      count: 0,
      loading: false,
      curKey: cardTabKey?.length ? cardTabKey : 'ALL',
      cancelDependence: false
    }
    this.bus = props.bus || new MessageCenter()
    api.dataLoader('@common.payeeConfig').reload()
    props.overrideGetResult(this.getResult)
  }

  getDataSource = (key: string) => {
    const dataSource = isWx ? session.get(key) : localStorage.getItem(key)
    return dataSource ? dataSource : ''
  }

  setDataSource = (key: string, val: string) => {
    if (isWx) {
      session.set(key, val)
    } else {
      localStorage.setItem(key, val)
    }
  }

  componentWillMount() {
    api.invokeService('@common:get:mc:permission:byName', 'ACCOUNT_PEE').then(result => {
      if (result?.value) {
        this.permission?.setEnableMC(result?.value)
      }
    })
  }

  componentDidMount() {
    getDefaultPayee()
    this.fetch(true)
    this.fetchWalletAccount()
    this.bus.watch('payee:save:click', this.handleSaveAccount) //保存收款账户
  }

  componentWillUnmount() {
    this.bus.un('payee:save:click', this.handleSaveAccount) //保存收款账户
  }

  getFetch = async params => {
    const searchNew = getBoolVariation('account_search_optimization', false)
    const { fromSupplier, templateid, billSpecification, userInfo, submitterData } = this.props
    const formType = billSpecification?.type
    const submitterId = submitterData?.id
    const staffId = userInfo?.staff?.id
    if (fromSupplier) {
      params.allVisible = true
    }
    params.templateid = templateid
    params.formType = formType ?? 'expense'
    params.submitterId = submitterId ?? staffId
    return accounts.POST(searchNew ? '/mineByEs' : '/mine', params, { join: 'branchId,branchId,/pay/v1/banks' })
  }

  fetchWalletAccount = () => {
    const { curKey } = this.state
    const { WalletPower, flowId, dependenceList } = this.props
    if (!WalletPower) return
    this.setState({ loading: true })
    let tabFilter = ''
    if (curKey != 'ALL' && curKey != 'COLLECTION') {
      tabFilter = `&&type=="${curKey}"`
    }
    let filter = `(asPayee==true&&active==true&&sort=="WALLET"${tabFilter})`
    if (!!dependenceList?.length) {
      filter += `&&id.in(${dependenceList.map(v => `"${v}"`)})`
    }
    const params = {
      flowId,
      start: 0,
      count: 10,
      filter,
      favoriteStatus: curKey === 'COLLECTION' ? true : null
    }
    this.getFetch(params).then(
      ({ items }) => {
        if (curKey === this.state.curKey) {
          this.setState({
            loading: false,
            walletAccountList: items
          })
        }
      },
      error => this.setState({ loading: false })
    )
  }

  fetch(reload) {
    const { curKey, cancelDependence = false } = this.state
    const { dependenceList } = this.props
    if (!reload && this.state['loading']) {
      return
    }
    const start = reload ? 0 : this.state['start'] + PAGE_SIZE
    const list = reload ? [] : this.state['list']
    this.setState({ loading: true, list })
    let tabFilter = ''
    if (curKey != 'ALL' && curKey != 'COLLECTION') {
      tabFilter = `&&type=="${curKey}"`
    }
    let filter = `(asPayee==true&&active==true&&sort!="WALLET"${tabFilter})`
    let nameLike = ''
    let searchValue = this.state.searchText
    if (searchValue) {
      filter = ` (asPayee==true&&active==true${tabFilter})` // && (accountName.contains(${searchValue}) || accountNo.contains(${searchValue}) || staffId.name.contains(${searchValue}))`
      nameLike = searchValue
    }
    if (dependenceList) {
      if (!!dependenceList.length) {
        filter += `&&id.in(${dependenceList.map(v => `"${v}"`)})`
      } else if (!cancelDependence) {
        this.setState({
          loading: false,
          list: [],
          start: start,
          count: 0
        })
        return
      }
    }
    const params = {
      flowId: this.props.flowId,
      start: start,
      count: PAGE_SIZE,
      filter,
      nameLike,
      favoriteStatus: curKey === 'COLLECTION' ? true : null
    }
    this.getFetch(params).then(
      ({ count, items }) => {
        if (curKey === this.state.curKey) {
          this.setState({
            loading: false,
            list: list.concat(items),
            start: start,
            count: count
          })
        }
      },
      error => this.setState({ loading: false })
    )
  }

  getResult = () => {
    const { multiple } = this.props
    return multiple ? this.state.selectPayees : this.state.selectPayee
  }

  handleSetDefBank = doc => {
    setDefaultPayee({ id: doc.id })
      .then(() => {
        //新建后再刷新收款信息集合
        api.invokeService('@common:get:payeeinfos')
        getDefaultPayee()
        this.fetch(true)
      })
      .catch(err => {
        showMessage.error(err?.message)
      })
  }

  handleCancelDefault = doc => {
    cancelDefaultPayee({ accountId: doc.id })
      .then(() => {
        //新建后再刷新收款信息集合
        api.invokeService('@common:get:payeeinfos')
        getDefaultPayee()
        this.fetch(true)
      })
      .catch(err => {
        showMessage.error(err?.message)
      })
  }

  // 收藏账户
  handleCollect = line => {
    api
      .invokeService('@bills:add:or:cancel:collect', { id: line.id, enable: true })
      .then(() => {
        this.fetch(true)
      })
      .catch(err => {
        if (err.msg || err.errorMessage) showMessage.error(err.msg || err.errorMessage)
      })
  }
  // 取消收藏账户
  handleCancelCollect = line => {
    api.invokeService('@bills:add:or:cancel:collect', { id: line.id, enable: false }).then(() => {
      this.fetch(true)
    })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    this.props.layer.emitOk()
  }

  handleSelectedCard = item => {
    const { multiple } = this.props
    if (multiple) {
      const { selectPayees } = this.state
      if (item.filterActive) {
        const target = selectPayees.find(originItem => originItem.id === item.id)
        target ? remove(selectPayees, originItem => originItem.id === item.id) : selectPayees.push(item)
        const selectPayeeKeys = selectPayees.map(item => item.id)
        this.setState({ selectPayees, selectPayeeKeys })
      }
    } else {
      item.filterActive && this.setState({ selectPayee: item })
    }
  }

  __handleInsertAssist = title => {
    api.invokeService('@common:insert:assist:record', {
      title
    })
  }

  handleSaveAccount = data => {
    data.owner = 'INDIVIDUAL'
    savePayee(data)
      .then(() => {
        this.fetch(true)
      })
      .catch(err => (err?.errorMessage ? showMessage.error(err.errorMessage) : showMessage.error(i18n.get('保存失败'))))
    if (data && data.name) {
      if (data.isCreate) {
        this.__handleInsertAssist(`创建${data.name}收款信息`) // @i18n-ignore
      } else {
        this.__handleInsertAssist(`修改${data.name}收款信息`) // @i18n-ignore
      }
    }
  }

  handleAddBank = () => {
    api.open('@bills:PayeeAccountCreatePopup', {
      bus: this.bus,
      payFromChannel: payFromChannelMap.personal
    })
  }

  handleSearchInputChange = e => {
    const { value } = e.target
    ;(!value || value.trim().length) && this.fnInputValueChange(value)
  }

  fnInputValueChange = debounce(value => {
    this.setState({ searchText: value }, () => {
      this.fetch(true)
    })
  }, 1200)

  handleCancelDependence = () => {
    this.setState(
      {
        cancelDependence: true
      },
      () => {
        this.fetch(true)
      }
    )
  }

  renderEmpty = () => {
    const { dependenceList, allowCancelDependence = false } = this.props
    const { cancelDependence = false, loading = false, curKey } = this.state
    let allowCancelDependenceTemp = !!dependenceList && allowCancelDependence
    let tips = !!loading
      ? '正在查询中，请稍后...'
      : curKey === 'COLLECTION'
      ? i18n.get('暂无已收藏的账户信息')
      : i18n.get('您目前没有可用的收款信息')
    return (
      <EmptyBody label={i18n.get(tips)}>
        {!cancelDependence && allowCancelDependenceTemp && !loading && (
          <div>
            <span className="fw-b">{i18n.get('点击')}</span>
            <span className="clickable" onClick={this.handleCancelDependence}>
              {i18n.get('查看全量数据')}
            </span>
          </div>
        )}
      </EmptyBody>
    )
  }

  renderPublicTab = () => {
    return (
      <>
        {i18n.get('对公账户')}
        <Tooltip placement="top" title={i18n.get('个体工商户、企业账户')}>
          <OutlinedTipsInfo className="public-info-icon" />
        </Tooltip>
      </>
    )
  }

  renderAccount() {
    const { isModify, payeeConfigCheck = {}, filterRules } = this.props
    const fitlerRuleType = filterRules?.find((item: any) => item.name === 'type')
    const { curKey } = this.state
    const isAllowCreate = !isModify && (payeeConfigCheck.personalAccount || payeeConfigCheck.publicAccount)
    return (
      <div className="payee-card-wrapper">
        <div className="card-header">
          <div className="search-card">
            <SearchInput
              className="search"
              placeholder={i18n.get('搜索账户名称、银行卡号、证件号码、所有者、备注')}
              onChange={this.handleSearchInputChange}
            />
          </div>
          {this.permission?.isEnableMC && isAllowCreate && !isModify && (
            <Button category="primary" className="new-create" onClick={this.handleAddBank}>
              {i18n.get('新建')}
            </Button>
          )}
        </div>
        <div className="card-tab-personal">
          <Tabs defaultActiveKey={curKey} className="ekb-tab" type="card" onChange={this.handleChange}>
            <TabPane tab={i18n.get('全部')} key="ALL" />
            {(!fitlerRuleType || fitlerRuleType.value.includes('PERSONAL')) && (
              <TabPane tab={i18n.get('个人账户')} key="PERSONAL" />
            )}
            {(!fitlerRuleType || fitlerRuleType.value.includes('PUBLIC')) && (
              <TabPane tab={this.renderPublicTab()} key="PUBLIC" />
            )}
            <TabPane tab={i18n.get('我的收藏')} key="COLLECTION" />
          </Tabs>
        </div>
        <div className="payee-card-list">{this.renderList()}</div>
      </div>
    )
  }

  handleChange = activeKey => {
    this.setDataSource('cardTabKey', activeKey)
    this.setState({ curKey: activeKey }, () => {
      this.fetch(true)
    })
  }

  fetchMore = memoize(() => () => this.fetch(false))

  renderItem = (item, idx) => {
    const { multiple, defaultPayee } = this.props
    const { searchText } = this.state
    const isDefault = defaultPayee && item.id === defaultPayee.id
    // if (!searchText && isDefault && defaultPayee.filterActive) return null
    const { selectPayee, selectPayeeKeys } = this.state
    const isSelected = multiple
      ? selectPayeeKeys.length > 0 && selectPayeeKeys.includes(item.id)
      : selectPayee && selectPayee.id === item.id
    return (
      <div
        key={idx}
        className="payer-item"
        data-testid="bill-payee-itemselect"
        onClick={() => {
          this.handleSelectedCard(item)
        }}
      >
        <AccountListItem
          isSelected={isSelected}
          inSelectPayMethodModal={item.sort === 'WALLET'}
          formChannel="payee"
          className={`payee-account-list-item-wrap ${item.filterActive ? '' : 'gray-bg'}`}
          isManangePage
          payFromChannel={payFromChannelMap.select}
          handleSetDefault={() => this.handleSetDefBank(item)}
          handleClickCancelDefault={() => this.handleCancelDefault(defaultPayee)}
          data={item}
          showCancleDefault={true}
          isDefault={item.isDefault}
          handleCollect={() => this.handleCollect(item)}
          handleCancelCollect={() => this.handleCancelCollect(item)}
          showCollectionBtns={true}
        />
      </div>
    )
  }

  renderWalletAccount = () => {
    const { walletAccountList, searchText } = this.state
    if (!walletAccountList.length || searchText) return null
    return walletAccountList.map(el => this.renderItem(el, el.id))
  }

  renderDefaultPayee = () => {
    const { defaultPayee, multiple, dependenceList } = this.props
    const { searchText, selectPayee, selectPayeeKeys } = this.state
    if (
      !defaultPayee ||
      !defaultPayee.id ||
      searchText ||
      (dependenceList && dependenceList.findIndex(v => v === defaultPayee.id) === -1)
    ) {
      return null
    }

    const isSelected = multiple
      ? selectPayeeKeys.length > 0 && selectPayeeKeys.includes(defaultPayee.id)
      : selectPayee && selectPayee.id === defaultPayee.id
    return (
      <div key={'default'} className="payer-item" onClick={() => this.handleSelectedCard(defaultPayee)}>
        <AccountListItem
          isSelected={isSelected}
          formChannel="payee"
          payFromChannel={payFromChannelMap.select}
          className={`payee-account-list-item-wrap ${defaultPayee.filterActive ? '' : 'gray-bg'}`}
          isManangePage
          handleSetDefault={() => this.handleSetDefBank(defaultPayee)}
          handleClickCancelDefault={() => this.handleCancelDefault(defaultPayee)}
          data={defaultPayee}
          inSelectPayMethodModal={defaultPayee.sort === 'WALLET'}
          isDefault
          showCollectionBtns={true}
        />
      </div>
    )
  }

  renderList() {
    const { list, start, count } = this.state
    const hasMore = start + PAGE_SIZE < count
    return (
      <InfiniteScroll hasMore={hasMore} loadMore={this.fetchMore()} useWindow={false}>
        {this.renderWalletAccount()}
        {list.length > 0 ? list.map(el => this.renderItem(el, el.id)) : this.renderEmpty()}
      </InfiniteScroll>
    )
  }

  render() {
    return (
      <div id={'bills-selectPayeeModal'} className="select-payee-modal">
        <div className="modal-header">
          <div className="title flex">{i18n.get('收款信息')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleCancel} />
        </div>
        <div className="select-payee-content">{this.renderAccount()}</div>
        <div className="modal-footer">
          <div>{i18n.get('收款账户仅可选择符合条件的账户')}</div>
          <div>
            <Button key="cancel" category="secondary" className="mr-8" onClick={this.handleCancel}>
              {i18n.get('取消')}
            </Button>
            <Button key="ok" category="primary" onClick={this.handleOK}>
              {i18n.get('确认')}
            </Button>
          </div>
        </div>
      </div>
    )
  }
}
