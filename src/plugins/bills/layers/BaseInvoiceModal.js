/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/16 下午3:17.
 */
import React, { PureComponent } from 'react'
import { EnhanceStackerManager } from '@ekuaibao/enhance-stacker-manager'
import Breadcrumb from '../../../elements/ekbc-basic/breadcrumb'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { app as api } from '@ekuaibao/whispered'

@EnhanceStackerManager([
  {
    key: 'ImportInputInvoice',
    getComponent: () => import('./import-bill/ManualInputInvoice.tsx'),
    title: i18n.get('手动录入发票')
  },
  {
    key: 'InvoiceDetails',
    getComponent: () => import('./import-bill/InvoiceDetails'),
    title: i18n.get('查看发票详情')
  }
])
export default class ImportInputInvoiceModal extends PureComponent {
  componentDidMount() {
    let { invoiceDetail, stackerManager, isFrom: isF, showAllFeeType, unNeedTxt = true, unNeedTap = true } = this.props
    invoiceDetail && api.dataLoader('@common.payerInfo').load()
    invoiceDetail
      ? stackerManager.push('InvoiceDetails', { isFrom: isF, invoice: invoiceDetail, confirm: true, showAllFeeType })
      : stackerManager.push('ImportInputInvoice', { unNeedTxt, unNeedTap })
  }

  renderBreadcrumb() {
    const array = this.props.stackerManager.values()
    let items = []
    array.forEach((line, i) => {
      items.push({
        key: i,
        onClick: () => this.props.stackerManager.open(i),
        title: line.title
      })
    })
    return items.length === 1 ? <span className="flex">{items[0].title}</span> : <Breadcrumb items={items} />
  }

  handleClose = () => {
    this.props.stackerManager.clear()
    this.props.layer.emitCancel()
  }

  render() {
    return (
      <div>
        <div className="modal-header" style={{ borderBottom: 'none' }}>
          {this.renderBreadcrumb()}
          <OutlinedTipsClose onClick={this.handleClose} />
        </div>
        <div>{this.props.children}</div>
      </div>
    )
  }
}
