import React, { PureComponent } from 'react'
import styles from './DataLinkTableStacker.module.less'
import { Keel, registerComponentsCellar } from '@ekuaibao/keel'
import KeelViewBody from '../../../../elements/puppet/KeelViewBody'
import DataLinkTableHeader from './DataLinkTableHeader'
import { getBoolVariation } from '../../../../lib/featbit'
const uiOptimization = getBoolVariation('aprd-6057-datalink-table-ui-optimize')
@registerComponentsCellar([
  {
    key: 'DataLinkTable',
    getComponent: () => import('./DataLinkTable'),
    title: i18n.get('业务对象')
  },
  {
    key: 'DataLinkDetailModal',
    getComponent: () => import('../dataLink-detail-modal/DataLinkDetailModal'),
    title: i18n.get('业务对象详情')
  },
  {
    key: 'BillInfoView',
    getComponent: () => import('../../bill-stacker-manager/pages/BillInfoView'),
    title: i18n.get('单据详情')
  },
  {
    key: 'FeeDetailView',
    getComponent: () => import('../../bill-stacker-manager/pages/FeeDetailView'),
    title: i18n.get('消费记录')
  }
])
export default class DataLinkTableStacker extends PureComponent<any> {

  render() {
    const { isNewVersion } = this.props
    const cls = 'dis-f flex-1 fd-c ovr-x-a ' + (isNewVersion ? styles['new'] : '')

    return (
      <div className={cls}>
        <Keel>
          <DataLinkTableHeader {...this.props} />
          <KeelViewBody classNameKey={uiOptimization ? `dis-f flex-1 fd-c ovr-x-h` : `dis-f flex-1 fd-c`} {...this.props} />
        </Keel>
      </div>
    )
  }
}
