import React, { PureComponent } from 'react'
import styles from './DataLinkTable.module.less'
import DataLinkTableWrapper from '../../../../elements/DataLinkTable/DataLinkTable'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import Header from './Header'
import BatchExecutionStatus from './components/BatchExecutionStatus'
import { parseOptions } from '../../../../elements/DataLinkTable/tableUtil'
import { cloneDeep, get, debounce, orderBy } from 'lodash'
import { parseFields, parseFilter, parseSorters } from '@ekuaibao/lib/lib/entityUtil/entityTableUtil'
import { parseFieldMap } from '../../../../elements/DataLinkTable/tableUtil'
import { filterMultiStaff } from '../../../../lib/mutil-staff-fetch'
import { QuerySelect } from 'ekbc-query-builder'
import { Fetch } from '@ekuaibao/fetch'
import { showModal } from '@ekuaibao/show-util'
import { Space, message, Dropdown, Modal, MenuProps } from '@hose/eui'
import { getBoolVariation } from '../../../../lib/featbit'
const { confirm } = Modal
const uiOptimization = getBoolVariation('aprd-6057-datalink-table-ui-optimize')
interface Props {
  changeRole?: Function
  handleSearch?: Function
  changeView?: Function
  roleType: string
  entityData: any
  keel: any
  type: any
  data: any
  fromContract: any
}

interface State {
  SRDMap: { [key: string]: { staffMap: any; roleMap: any; departmentMap: any } }
  roleType: string
  selectedRowKeys: string[]
  selectedRows: any
  batchTimestamp: null | number
}

export default class DataLinkTable extends PureComponent<Props, State> {
  bus = new MessageCenter()
  pathMap = {}
  fieldMap = {}
  temps = {}
  tempobj = {}
  query = {}

  constructor(props: Props) {
    super(props)
    this.state = {
      SRDMap: {},
      roleType: props.roleType,
      selectedRowKeys: [],
      batchTimestamp: null,
      selectedRows: {}
    }
  }

  __handleInsertAssist = title => {
    api.invokeService('@common:insert:assist:record', {
      title
    })
  }

  handleSelectedChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows })
  }

  handleUpdateTimestamp = (batchTimestamp: number) => {
    this.setState({ batchTimestamp })
  }

  getTemps = async data => {
    const query = new QuerySelect().filterBy(`active==true`).value()
    let result = await api.invokeService('@third-party-manage:get:entity:list', { id: data.platformId, ...query })
    // @ts-ignore
    let temps = result.filter(i => i.id === data.id)
    let tempobj = {}
    if (temps.length > 0) {
      // @ts-ignore
      temps.forEach(i => {
        // @ts-ignore
        if (i.children && i.children.length > 0) {
          i.children.forEach((t: any) => {
            // @ts-ignore
            tempobj[t.id] = t.name
          })
        }
      })
    }
    this.temps = temps.length > 0 ? temps[0] : {}
    this.tempobj = tempobj
    this.__handleInsertAssist(`查看${temps[0].name}扩展管理列表`) // @i18n-ignore
  }

  componentWillMount = async () => {
    const { data, entityData } = this.props
    this.bus.on('table:row:edit:staff', this.handleEditStaff)

    let result = data ? data : entityData
    if (result && result.platformId) {
      await this.getTemps(result)
    }

    api
      .dataLoader('@common.staffs')
      .load()
      .then((staffs: any) => {
        const staffMap: any = {}
        staffs &&
          staffs.forEach((line: any) => {
            staffMap[line.id] = line
          })
        this.setState({ SRDMap: staffMap })
      })
  }

  componentWillReceiveProps(nextProps: Props) {
    if (this.props.roleType !== nextProps.roleType) {
      this.setState({ roleType: nextProps.roleType }, () => {
        // @ts-ignore
        this.bus.reload({ page: { currentPage: 1, pageSize: 10 } })
      })
    }
  }

  componentWillUnmount() {
    this.bus.un('table:row:edit:staff', this.handleEditStaff)
  }

  updataTableList = (data: any, params: any) => {
    const { isFZR, staffs } = params
    if (isFZR) {
      this.props.keel.open(0)
    } else {
      const { visibility } = data || { visibility: {} }
      const visib = { ...visibility, staffs }
      const dataSource = { ...data, visibility: visib }
      const { roleType } = this.props
      const showEdit = roleType === 'owner'
      this.props.keel.open(1, {
        ...this.props,
        entityInfo: dataSource,
        isFrom: 'dataLinkTable',
        showEdit,
        updataTable: this.updataTableList.bind(this, data),
        dataLinkTableWrapperRef: null
      })
    }
    // @ts-ignore
    this.bus.reload()
  }

  handleSearch = debounce(value => {
    this.setState(
      { start: 0, dataList: [], selectItem: undefined, loadMoreType: 'loadingMore', searchValue: value },
      () => {
        this.getLinkList(value)
      }
    )
  }, 500)

  handleTableRowClick = debounce((data: any) => {
    const { roleType, keel, fromContract } = this.props
    const showEdit = roleType === 'owner'
    const title = get(data, 'dataLink.entity.name')
    api.open('@bills:DataLinkDetailModal', {
      entityInfo: {
        ...data,
        isFrom: 'dataLinkTable',
        fromContract,
        platformName: title
      },
      keel,
      showEdit,
      updataTable: this.updataTableList.bind(this, data),
      showCustomizeAction: true,
      showClose: true,
      viewKey: 'DataLinkDetailModal'
    })
  }, 300)

  fnGetStaffArr = obj => {
    const staffs = obj ? obj.staff : []
    const role = obj ? obj.role : []
    const department = obj ? obj.department : []
    const oStaffs = staffs && staffs.map(s => s.id)
    const oRole = role && role.map(s => s.id)
    const oDepartment = department && department.map(s => s.id)
    return [
      {
        type: 'department-member',
        multiple: true,
        checkedKeys: oStaffs
      },
      {
        type: 'department',
        multiple: true,
        checkedKeys: oDepartment
      },
      { type: 'role', multiple: true, checkedKeys: oRole }
    ]
  }

  handleEditStaff = (data: any) => {
    const { type, obj, line } = data
    const multiple = type !== 'staffFZ'
    const checkedList =
      type === 'staffFZ'
        ? [{ type: 'department-member', multiple: false, checkedKeys: obj ? [obj.id] : [] }]
        : this.fnGetStaffArr(obj)
    api.open('@layout:SelectStaffsModal', { checkedList: checkedList, multiple: multiple }).then((result: any) => {
      const t = type === 'staffFZ' ? 'owner' : 'visibility'
      const { checkedList } = result
      const staffIds = checkedList.find(item => item.type === 'department-member').checkedKeys || []
      let params = { staffIds }
      if (t === 'visibility') {
        const roleIds = checkedList.find(item => item.type === 'role').checkedKeys || []
        const departmentIds = checkedList.find(item => item.type === 'department').checkedKeys || []
        params = { roleIds, departmentIds, ...params }
      }
      api
        .invokeService('@third-party-manage:put:dataLink:FZRorCYRList', {
          id: line.dataLink.id,
          type: t,
          ...params
        })
        .then(() => {
          // @ts-ignore
          this.bus.reload()
        })
    })
  }

  fetch = async (options = { page: { currentPage: 0, pageSize: 10 }, sorters: {}, filters: {} }) => {
    const { entityData, type } = this.props

    const { roleType } = this.state
    // 由于界面上面显示的字段和传回到后台的字段名称不一致，为了不更改字段名称拷贝一份。。。
    // @ts-ignore
    const __options = cloneDeep(options)
    __options.sorters = parseSorters(options.sorters, this.pathMap)
    __options.filters = parseFilter(options.filters, this.pathMap)
    await filterMultiStaff(__options, this.fieldMap)
    const query = parseOptions({ options: __options, entityInfo: entityData, fieldMap: this.fieldMap })
    const { limit, filterBy, orderBy } = query
    this.handleQueryChangeList(query)

    return api
      .invokeService('@third-party-manage:get:searchDataLink:data', {
        ...limit,
        filterBy,
        orderBy,
        type: 'TABLE',
        entityId: entityData.id,
        category: roleType
      })
      .then((rep: any) => {
        const { data, template, total, path } = rep.items
        this.pathMap = path
        if (!Object.keys(this.fieldMap).length) {
          const fields = parseFields({ res: template, type })
          this.fieldMap = parseFieldMap(fields, path)
        }
        return { dataSource: data, template, total: total ? total : 1, path }
      })
  }

  handleEmitEasoPipeline = async (pipelineId: string, entityId: string) => {
    const res = await api.invokeService('@third-party-manage:emit:easo:pipeline', { pipelineId, entityId })

    if (res?.code === 200) {
      message.success('执行成功')
    } else {
      message.error(res?.msg ?? '执行失败')
    }
  }

  renderAction = () => {
    // 在这里添加操作列

    return {
      title: i18n.get('操作'),
      filterType: false,
      dataIndex: 'action',
      key: 'action',
      label: i18n.get('操作'),
      value: 'action',
      fixed: 'right',
      fixedWidth: 70,
      sorter: false,
      className: 'fs-14',
      render: (_text: string, line: any) => {
        let actions = (line?.actions || [])
          .filter((item: any) => item.type === 'PIPELINE_ACTION')
          .filter(item => item?.active)
          .filter(item => item?.allowedLayout?.includes('TABLE'))

        actions = orderBy(actions, ['order', 'createTime', 'id'], ['desc', 'asc', 'asc'])

        if (actions.length > 1) {
          const [action, ...rest] = actions
          const items: MenuProps['items'] = rest.map(item => ({
            label: (
              <a
                onClick={() => {
                  confirm({
                    title: `请问确定要执行“${item.name}“吗？`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: () => {
                      this.handleEmitEasoPipeline(item?.context?.pipelineId, line?.dataLink?.id)
                    }
                  })
                }}
              >
                {item.name}
              </a>
            ),
            key: item.name
          }))
          return (
            <Space>
              <a onClick={() => this.handleTableRowClick(line)}>{i18n.get('详情')}</a>
              <a
                onClick={() => {
                  confirm({
                    title: `请问确定要执行“${action.name}“吗？`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: () => {
                      this.handleEmitEasoPipeline(action?.context?.pipelineId, line?.dataLink?.id)
                    }
                  })
                }}
              >
                {action.name}
              </a>
              <Dropdown menu={{ items }}>
                <a onClick={e => e.preventDefault()}>···</a>
              </Dropdown>
            </Space>
          )
        }

        return (
          <Space>
            <a onClick={() => this.handleTableRowClick(line)}>{i18n.get('详情')}</a>
            {actions.map((item: any) => (
              <a
                onClick={() => {
                  confirm({
                    title: `请问确定要执行“${item.name}“吗？`,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: () => {
                      this.handleEmitEasoPipeline(item?.context?.pipelineId, line?.dataLink?.id)
                    }
                  })
                }}
              >
                {item.name}
              </a>
            ))}
          </Space>
        )
      }
    }
  }

  handleQueryChangeList = query => {
    this.query = query
  }

  handleExportExcel = async () => {
    const { entityData, roleType } = this.props
    this.query.category = roleType
    let entityInfo = cloneDeep(entityData)
    let canExport = { value: false }
    try {
      canExport = (await Fetch.POST(`/api/v2/datalink/entity/canExport/$${entityInfo.id}`)) || {
        value: false
      }
    } catch (e) {
      message.warning(e.msg || '获取导出参数失败', 2)
    }
    const { value: result, staff } = await api.open('@third-party-manage:ExportOption', { canExport, showStaff: true })
    const _query: any = !!this.query ? Object.assign({}, this.query) : {}
    if (staff) {
      _query.config = {
        form: { staffType: staff }
      }
    }
    if (result === 'all') {
      api.invokeService('@third-party-manage:export:dataLink:mine', entityInfo.id, _query)
    } else if (result === 'export') {
      showModal.confirm({
        title: i18n.get('是否开始导出任务？'),
        content: i18n.get('系统将会在后台处理此任务。您可在右上角「我的个人信息-导出管理」中查看任务进度。'),
        onOk: () => {
          api.invokeService('@third-party-manage:export:dataLink:pdf', entityInfo.id, _query)
        },
        okText: i18n.get('开始导出'),
        cancelText: i18n.get('取消')
      })
    } else {
      api.invokeService('@third-party-manage:export:dataLink:layout:mine', entityInfo.id, _query)
    }
    this.__handleInsertAssist(`导出${entityInfo.name}扩展管理列表`) // @i18n-ignore
  }

  render() {
    const { SRDMap, selectedRowKeys, batchTimestamp, selectedRows } = this.state
    // @ts-ignore
    const { entityData, isNewVersion } = this.props
    const style = isNewVersion ? 'data-link-table-wrapper-new' : 'data-link-table-wrapper'
    let entityInfo = cloneDeep(entityData)
    // @ts-ignore
    if (this.temps.children) {
      // @ts-ignore
      entityInfo.children = this.temps.children
    }

    return (
      <div className={styles[style]} style={uiOptimization ? { overflowY: 'hidden' } : {}}>
        <Header
          {...this.props}
          isShowSearch={false}
          handleExportExcel={this.handleExportExcel}
          updateTimestamp={this.handleUpdateTimestamp}
          selectedRowKeys={selectedRowKeys}
          selectedRows={selectedRows}
          dataLinkTableWrapperRef={this.state.dataLinkTableWrapperRef}
        />

        <BatchExecutionStatus entityId={entityInfo?.id} batchTimestamp={batchTimestamp} />
        <div className="table-content" style={uiOptimization ? { height: 'calc(100% - 68px)' } : {}}>
          <DataLinkTableWrapper
            selectedRowKeys={selectedRowKeys}
            onSelectedChange={this.handleSelectedChange}
            entityInfo={entityInfo}
            isMultiSelect
            isSingleSelect={false}
            entityInfoMap={this.tempobj}
            bus={this.bus}
            getLocal={api.invokeService('@third-party-manage:get:local')}
            SRDMap={SRDMap}
            fetch={this.fetch}
            action={this.renderAction}
            showNameAndCode={false}
            queryChange={this.handleQueryChangeList}
            fromPage="homePage"
            onRef={ref => {
              this.setState({ dataLinkTableWrapperRef: ref })
            }}
            hiddenSearch={uiOptimization}
          />
        </div>
      </div>
    )
  }
}
