import React, { PureComponent } from 'react'
import { orderBy } from 'lodash'
import { Menu, Tooltip, Icon, Dropdown as AntdDropdown } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import styles from './Header.module.less'
import EKBIcon from '../../../../elements/ekbIcon'
import ViewSwitcher from '../../../../elements/ViewSwitcher'
import { Button, Space, message, Input } from '@hose/eui'
import BatchExecute from './BatchExecute'
import ColumnSetting from '../../../../elements/DataLinkTable/ColumnSetting'
import { OutlinedGeneralSetting } from '@hose/eui-icons'
import { getBoolVariation } from '../../../../lib/featbit'
const uiOptimization = getBoolVariation('aprd-6057-datalink-table-ui-optimize')
interface Props {
  changeRole?: Function
  handleSearch?: Function
  changeView?: Function
  isShowSearch?: boolean
  roleType?: string
  placeholder?: string
  icon?: any
  handleExportExcel?: Function
  isList?: boolean
  dataSource?: any
  selectedRowKeys?: string[]
  selectedRows: any
  updateTimestamp: (timestamp: number) => void
}

interface State {
  inputValue: string
}

type DataLink = {
  id: string
  name: string
  code: string
}

export const menuMap = {
  all: i18n.get('全部'),
  visibility: i18n.get('我参与的'),
  owner: i18n.get('我负责的')
}

export default class Header extends PureComponent<Props, State> {
  state = { inputValue: '', open: false }

  handleExport = () => {
    const { handleExportExcel } = this.props
    handleExportExcel && handleExportExcel()
  }

  changeRole = ({ key }: any) => {
    const { changeRole } = this.props
    changeRole?.(key)
    this.setState({ inputValue: '' })
  }

  handleSearch = (e: any) => {
    const { handleSearch, dataLinkTableWrapperRef, isShowSearch } = this.props
    const { value } = e.target || window.event.srcElement
    this.setState({ inputValue: value })
    if (!isShowSearch) {
      dataLinkTableWrapperRef?.handleSearch?.(e.target.value)
    } else {
      handleSearch?.(value)
    }
  }

  changeView = (type: string) => {
    const { changeView } = this.props
    changeView && changeView(type)
  }

  emitEasoBatchJobs = async (pipelineId: string, entityId: string, dataLinks: DataLink[], actionName: string) => {
    const res = await api.invokeService('@third-party-manage:emit:easo:batchJobs', {
      pipelineId,
      entityId,
      dataLinks,
      actionName
    })

    if (res?.code === 200) {
      message.success(res?.msg ?? '执行成功')
    } else {
      message.error(res?.msg ?? '执行失败')
    }
  }

  handleBatchJobs = (pipelineId: string, actionName: string, ids: string[]) => {
    const { dataSource, updateTimestamp, selectedRows } = this.props
    const entityId = dataSource?.id
    // 转换数据将ids 转换为 Datalink[]

    const dataLinks: DataLink[] = ids?.map((id: string) => {
      const thisRow = selectedRows[id]
      return {
        id,
        name: thisRow?.dataLink?.[`E_${entityId}_name`],
        code: thisRow?.dataLink?.[`E_${entityId}_code`]
      }
    })

    this.emitEasoBatchJobs(pipelineId, entityId, dataLinks, actionName).then(() => {
      updateTimestamp(+new Date())
    })
  }

  menu = (
    <Menu onClick={this.changeRole}>
      <Menu.Item key="all">{i18n.get('全部')}</Menu.Item>
      <Menu.Item key="visibility">{i18n.get('我参与的')}</Menu.Item>
      <Menu.Item key="owner">{i18n.get('我负责的')}</Menu.Item>
    </Menu>
  )

  getSwitcher() {
    // @ts-ignore
    const { isList, isNewVersion } = this.props

    if (isNewVersion) {
      return (
        <ViewSwitcher
          isList={isList}
          handleClickList={this.changeView.bind(this, 'list')}
          handleClickTable={this.changeView.bind(this, 'table')}
        />
      )
    }

    return (
      <div className="list-or-table-icon">
        <div className="list" onClick={this.changeView.bind(this, 'list')}>
          <Tooltip title={i18n.get('列表视图')} placement="bottom">
            <svg className="icon">
              <use xlinkHref="#EDico-list" />
            </svg>
          </Tooltip>
        </div>
        <div className="table" onClick={this.changeView.bind(this, 'table')}>
          <Tooltip title={i18n.get('表格视图')} placement="bottom">
            <svg className="icon">
              <use xlinkHref="#EDico-more" />
            </svg>
          </Tooltip>
        </div>
      </div>
    )
  }

  handleNewSearch = (value: string) => {
    const { dataLinkTableWrapperRef } = this.props
    dataLinkTableWrapperRef?.handleSearch?.(value)
  }

  handleClearSearch = () => {
    const { dataLinkTableWrapperRef } = this.props
    dataLinkTableWrapperRef?.handleClearSearch?.()
  }

  handleColumnSetting = () => {
    this.setState({
      open: true,
      defaultColumns: this.props.dataLinkTableWrapperRef.state.defaultColumns,
      filterColumnsMap: this.props.dataLinkTableWrapperRef.state.filterColumnsMap,
      onColumnsChange: this.props.dataLinkTableWrapperRef.handleColumnsChange
    })
  }

  renderTitle = () => {
    const {
      isShowSearch = true,
      roleType = 'visibility',
      isList,
      dataSource,
      selectedRowKeys,
      selectedRows,
      type,
      dataLinkTableWrapperRef
    } = this.props
    const filterClassName = window.isNewHome ? 'filter-text navigation-bar-title-main' : 'filter-text'
    const str = menuMap[roleType] || ''
    const actions = dataSource?.actions || []
    let batchActions = actions
      .filter(({ active }) => active)
      .filter((item: any) => item?.allowedLayout?.includes('MULTI_TABLE'))

    batchActions = orderBy(batchActions, ['order', 'createTime', 'id'], ['desc', 'asc', 'asc'])

    return (
      <div className="dropDown-wrapper">
        <div className="role-change">
          <AntdDropdown overlay={this.menu} trigger={['hover']}>
            <div className={filterClassName}>
              {i18n.get(str)}
              <EKBIcon name="#EDico-titledown" style={{ width: 16, height: 16 }} />
            </div>
          </AntdDropdown>
        </div>

        {isList ? (
          <>
            {!isShowSearch && (
              <Button onClick={this.handleExport} className="export-btn" category="secondary">
                {i18n.get('导出')}
              </Button>
            )}
            {this.getSwitcher()}
          </>
        ) : (
          <Space>
            {uiOptimization && <Input.Search
              placeholder={type === 'ELEM' ? i18n.get('输入餐品名称或订单号搜索') : i18n.get('输入名称或编码搜索')}
              onSearch={this.handleNewSearch}
              onClear={this.handleClearSearch}
            />}
            {!isShowSearch && (
              <Button onClick={this.handleExport} category="secondary">
                {i18n.get('导出')}
              </Button>
            )}
            <BatchExecute
              actions={batchActions}
              batchJobs={this.handleBatchJobs}
              dataLinkIds={selectedRowKeys}
              selectedRows={selectedRows}
            />
            {dataLinkTableWrapperRef && uiOptimization && <ColumnSetting
              open={this.state.open}
              columns={this.state.defaultColumns}
              onColumnsChange={dataLinkTableWrapperRef.handleColumnsChange}
              filterColumnsMap={this.state.filterColumnsMap}
              onOpenChange={() => {
                this.setState({ open: false })
              }} >
              <Button category="secondary" icon={<OutlinedGeneralSetting />} onClick={this.handleColumnSetting}>
                {i18n.get('列设置')}
              </Button>
            </ColumnSetting>}
            {this.getSwitcher()}
          </Space>
        )}
      </div>
    )
  }

  render() {
    const { inputValue } = this.state
    const { isShowSearch = true, placeholder = i18n.get('搜索') } = this.props

    return (
      <div className={styles['owner-privilege-header-wrapper']}>
        <div className="header-select">{this.renderTitle()}</div>
        {isShowSearch && (
          <div className="header-search">
            <Input
              className="input-search"
              value={inputValue}
              onChange={this.handleSearch}
              prefix={<Icon type="search" style={{ fontSize: '14px', color: '#1d2b3d' }} />}
              allowClear
              placeholder={placeholder}
            />
          </div>
        )}
      </div>
    )
  }
}