/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/14 下午3:51.
 */
import { get } from 'lodash'
import SVG_INVOICE_PDF from '../../../../images/icon-file-pdf.svg'
import SVG_XML from '../../../../images/SVG_Xml.svg'
import { Image } from "@hose/eui";
import {
  TwoToneDataLinkBlue,
  TwoToneDataOfd,
  TwoToneDataPdf,
  TwoToneDataZip,
} from "@hose/eui-icons";

export function getInvoiceType(fpdm) {
  let specFpdmList = []
  specFpdmList.push('144031539110')
  specFpdmList.push('131001570151')
  specFpdmList.push('133011501118')
  specFpdmList.push('111001571071')
  specFpdmList.push('111001471071')
  specFpdmList.push('111001471072')
  specFpdmList.push('111001571072')
  specFpdmList.push('111001671071')
  specFpdmList.push('111001671072')
  let industryNo
  let type = '99'
  let taxcode
  let batchno
  if (fpdm && fpdm.length == 12) {
    taxcode = fpdm.substring(0, 1) // 0为总局、1-国税、2-地税
    batchno = fpdm.substring(10, 12)
    industryNo = fpdm.substring(7, 8) // 行业代码:国税行业划分：1工业、2商业、3加工修理修配业、4收购业、5水电业、6其他；
    // 地税行业划分：1交通运输业、2建筑业、3金融保险业、4邮电通信业、5文化体育业、6娱乐业、7服务业、8转让无形资产、9销售不动产、0表示其他
    if (batchno == '12') {
      return '12'
    }
    if (fpdm.substring(1, 5) === '3100') {
      if (fpdm.substring(7, 10) === '701') {
        return '10'
      }
    } else if (fpdm.substring(1, 3) === '33') {
      if (batchno == '18') {
        return '10'
      }
    } else if (fpdm.substring(1, 5) === '4403') {
      if (
        (fpdm.substring(5, 7) === '15' && fpdm.substring(7, 9) === '39') ||
        (fpdm.substring(5, 7) === '16' && fpdm.substring(10, 12) === '11')
      ) {
        return '10'
      }
    }

    if (specFpdmList.indexOf(fpdm) > -1) {
      type = '10'
      return type
    }
    if (type === '99' && taxcode === '0' && batchno === '11') {
      type = '10'
      return type
    }
    if (type === '99' && taxcode === '0' && batchno === '17') {
      // 二手车发票
      type = '15'
      return type
    }
    if (type === '99') {
      if (industryNo === '2') {
        type = '03'
      } else {
        type = '11'
      }
    }
  } else if (fpdm && fpdm.length == 10) {
    industryNo = fpdm.substring(7, 8)
    if (industryNo === '1' || industryNo === '5') {
      type = '01'
    } else if (industryNo === '6' || industryNo === '3') {
      type = '04'
    } else if (industryNo === '7' || industryNo === '2') {
      type = '02'
    }
  }
  return type
}
/**
 *  @param selectedData //选中的申请明细
 *
 */
export function getDetailContainApportion(selectedData = []) {
  const containList = selectedData.find(item => {
    const { feeTypeForm } = item
    const apportions = get(feeTypeForm, 'apportions', [])
    if (apportions.length > 0) {
      return true
    }
    return false
  })
  return { containApportion: !!containList }
}

export function moveDetailApportion(details = []) {
  return details.map(detail => {
    detail.feeTypeForm.apportions = undefined
    return detail
  })
}

export function hasCopy(resDetails = [], items = []) {
  const resApportionIds = resDetails.map(detail => {
    let { feeTypeForm } = detail
    let apportion = get(feeTypeForm, 'apportions', [])[0]
    return get(apportion, 'specificationId.id', null)
  })
  const expenseApportionIds = items.map(detail => {
    let { components = [] } = detail
    const apportionC = components.find(c => c.field === 'apportions')
    if (!apportionC) return []
    return apportionC.specificationIds || []
  })
  let obj = { copy: true, idx: undefined, spc: undefined }
  resApportionIds.forEach((id, idx) => {
    const a = id?.split(':')
    const mokeId = a?.slice(0, a.length - 1)?.join(':')
    const line = expenseApportionIds[idx] || []
    if (!!line.length && !line.includes(mokeId) && id !== null) {
      obj.idx = idx
      obj.copy = false
      obj.spc = resDetails[idx]
      return true
    }
    return false
  })
  return obj
}

const IMG_REG = /^(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|tiff|png|raw|tga)$/i

export const isInvoiceImage = attachment => {
  return IMG_REG.test(attachment?.fileName)
}

export const geShowInvoiceImg = attachment => {
  if (!attachment) {
    return SVG_INVOICE_PDF
  }
  if (IMG_REG.test(attachment.fileName)) {
    return attachment.thumbUrl
  }
  if (attachment.fileName?.endsWith('.pdf') || attachment.fileName?.endsWith('.PDF')) {
    return SVG_INVOICE_PDF
  }
  if (attachment.fileName?.endsWith('.xml') || attachment.fileName?.endsWith('.XML')) {
    return SVG_XML
  }
  return SVG_INVOICE_PDF
}

export const canShowPreviewImage = fileName => {
  if (!fileName) {
    return true
  }
  if (fileName.endsWith('.zip') || fileName.endsWith('.ZIP')) {
    return false
  }
  return true
}

export const getExtension = (fileName) => {
  if (!fileName) {
    return "";
  }
  // 使用 lastIndexOf 方法找到最后一个点的位置
  const lastDotIndex = fileName?.lastIndexOf(".");
  if (lastDotIndex === -1) {
    return ""; // 没有找到点，返回空字符串
  }
  return fileName?.substring(lastDotIndex + 1).toLowerCase(); // 转换为小写以便统一比较
};

export const IMG_REG_PRE = /^(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|png|raw|tga)$/i;

export function getShowPreImg(props) {
  const { file } = props;
  let icon = <TwoToneDataLinkBlue fontSize={20} />;

  if (file.key && file.key.startsWith("DP:")) {
    return icon;
  }

  const type = getExtension(file.name);
  switch (type) {
    case "zip":
      icon = <TwoToneDataZip fontSize={20} />;
      break;
    case "pdf":
      icon = <TwoToneDataPdf fontSize={20} />;
      break;
    case "ofd":
      icon = <TwoToneDataOfd fontSize={20} />;
      break;
    default:
      icon = <TwoToneDataLinkBlue fontSize={20} />;
  }

  const img = file.url || file.thumbUrl;
  const flag = IMG_REG_PRE.test(file.name);
  return flag ? <Image className="file-img" src={img} /> : icon;
}
