/**
 *  Created by pw on 2019-12-13 19:15.
 */
import React, { PureComponent } from 'react'
import { Checkbox, Modal, Popconfirm } from 'antd'
import { Button } from '@hose/eui'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import './InvoiceFeetypeRecommend.less'
import { FeeTypeIF, FeeTypeMapIF, InvoiceIF, CorporationIF, StaffIF } from '@ekuaibao/ekuaibao_types'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { showMessage } from '@ekuaibao/show-util'
import { array2object } from '@ekuaibao/helpers'
import InvoicePannel from './InvoiceRecommendList'
import { T } from '@ekuaibao/i18n'
import FeeTypeSelect from '../../../../../elements/feeType-tree-select'
import { cloneDeep } from 'lodash'
import { Fetch } from '@ekuaibao/fetch'


const CheckboxGroup = Checkbox.Group
interface Props {
  specificationVersionId: string // 单据模板的版本ID
  feeTypes: FeeTypeIF[]
  feeTypeMap?: FeeTypeMapIF
  invoiceData: InvoiceIF[]
  submitterId: StaffIF
  isMerge: boolean
  layer: any
  corporation: CorporationIF
  visibleFeeTypeByFormDataMap?: FeeTypeMapIF
}

interface State {
  checkedKeys: string[]
  recommends: FeeTypeIF[]
  checkedList: string[]
  indeterminate: boolean
  checkAll: boolean
  currentFeeType: FeeTypeIF
  batchEditStatus: number // 批量编辑状态
}

type FeetypeSource = 'Default' | 'Recommend' | 'Other'

@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
@EnhanceConnect((state: any) => ({
  feeTypeMap: state['@common'].feetypes.map,
  userInfo: state['@common'].userinfo.data,
  visibleFeeTypeByFormDataMap: state['@bills'].visibleFeeTypeByFormDataMap
}))
export default class InvoiceFeetypeRecommend extends PureComponent<Props, State> {
  resultMap: { [key: number]: FeeTypeIF } = {}
  recommendMap: { [key: number]: FeeTypeIF[] } = {}
  resultMapHistory: { [key: number]: FeeTypeIF } = {}
  recommendMapHistory: { [key: number]: FeeTypeIF[] } = {}
  verifyInvoiceResult: any [] = []

  initInvoiceData = () => {
    const { invoiceData } = this.props
    if(invoiceData) {
      return  Array.isArray(invoiceData) ? invoiceData : [invoiceData]
    }
    return []
  }

  state = {
    checkedKeys: [],
    recommends: [],
    checkedList: [],
    indeterminate: false,
    checkAll: false,
    currentFeeType: null,
    invoiceResultData: this.initInvoiceData(),
    batchEditStatus: 1 // 1：未编辑，2：编辑中，3：取消编辑
  }

  fnVerifyFeeType = (resultFeeTypes, dataSource, isMerge): boolean => {
    const data = isMerge ? [dataSource] : dataSource
    if (resultFeeTypes.length < data.length) {
      let warnMsg = i18n.get('请选择费用类型')
      if (data.length > 1) {
        data.some((line, index) => {
          if (!this.resultMap[index]) {
            warnMsg = i18n.get(`第{__k0}条费用明细没有选择费用类型`, { __k0: index + 1 })
            return true
          }
          return false
        })
      }
      showMessage.error(warnMsg)
      return false
    }
    return true
  }

  fnGetFeeTypeTemplate = async resultFeeTypes => {
    const ids = resultFeeTypes.map((line: FeeTypeIF) => line.expenseSpecificationId)
    const { items } = await api.invokeService('@bills:get:Specifications', ids, undefined, {
      formSpecificationId: this.props.specificationVersionId
    })
    const specificationMap = array2object(items)
    return resultFeeTypes.map(feeType => {
      const specification = specificationMap[feeType.expenseSpecificationId]
      return { ...feeType, specification } as FeeTypeIF
    })
  }

  fnVerifyFeeTypeTemplate = resultFeeTypes => {
    return resultFeeTypes.some((line, index) => {
      const components = getV(line, 'specification.components', [])
      const component = components.find(c => c.field === 'invoiceForm')
      if (component && component.invoiceType.exist) {
        return false
      }
      showMessage.error(i18n.get(`第{__k0}条费用明细中的费用不可添加发票`, { __k0: index + 1 }))
      return true
    })
  }

  fnTrack = (feeTypes: FeeTypeIF[]) => {
    const { corporation } = this.props
    const fnFeeTypeTrack = (
      deafultFeeTypeId: string,
      deafultFeeTypeName: string,
      source: FeetypeSource,
      selectFeeTypeId: string,
      selectFeeTypeName: string
    ) => {
      window.TRACK &&
        window.TRACK('EKBFeeTypeRecommend', {
          actionName: i18n.get('选择费用类型的事件'),
          corpName: corporation ? corporation.name : '',
          corpId: corporation ? corporation.id : '',
          deafultFeeTypeId,
          deafultFeeTypeName,
          source,
          selectFeeTypeId,
          selectFeeTypeName
        })
    }

    feeTypes.forEach((line, index) => {
      const recommends = this.recommendMap[index]
      if (recommends && recommends.length) {
        const [defalutFeetype, ...others] = recommends
        if (defalutFeetype.id === line.id) {
          fnFeeTypeTrack(defalutFeetype.id, defalutFeetype.name, 'Default', line.id, line.name)
          return
        }
        if (others.find(oo => oo.id === line.id)) {
          fnFeeTypeTrack(defalutFeetype.id, defalutFeetype.name, 'Recommend', line.id, line.name)
          return
        }
        fnFeeTypeTrack(defalutFeetype.id, defalutFeetype.name, 'Other', line.id, line.name)
        return
      }
      fnFeeTypeTrack('', i18n.get('没有推荐'), 'Other', line.id, line.name)
    })
  }

  handleClearBindMessageOfFee = () => {
    const { invoiceResultData } = this.state
    const rData = invoiceResultData.map((item) => {
      if(item?.master?.bindValidateMessage) {
        item.master.bindValidateMessage = ''
      }
      return item
    })
    this.setState({invoiceResultData: rData})
  }

  handleFeeTypeChange = (index, feeType) => {
    const { batchEditStatus } = this.state
    // 非编辑态的时候记录历史
    if (batchEditStatus !== 2) {
      this.resultMapHistory[index] = feeType
    }

    this.resultMap[index] = feeType
    console.log(this.resultMapHistory, '非编辑态的时候记录历史', batchEditStatus)
    if(batchEditStatus !==2){
      this.handleClearBindMessageOfFee()
    }
  }

  handleUpdateRecommend = (index: number, recommends: FeeTypeIF[]) => {
    const { batchEditStatus } = this.state
    // 非编辑态的时候记录历史
    if (batchEditStatus !== 2) {
      this.recommendMapHistory[index] = recommends
    }
    this.recommendMap[index] = recommends
  }

  getInvoiceIds = (dataSource: any[]) => {
    let invoiceIds = []
    if(!dataSource?.length){
      return invoiceIds
    }
    dataSource?.forEach(v => {
      if(v?.master?.id) {
        invoiceIds.push(v.master.id)
      }
      let detailIds = []      // 发票明细id
      if(v?.details?.length) {
        detailIds = v.details.map(o => o?.id)
        invoiceIds  = invoiceIds.concat(detailIds)
      }
    })
    return  invoiceIds
  }

  getVerifyParam = (feeTypes: any[], dataSource: any[]) => {
    const { specificationVersionId } = this.props
    let params = []
    if(feeTypes.length === 1) {
      const feeTypeSpecId = feeTypes[0].id
      const invoiceIds = this.getInvoiceIds(dataSource)
      const temp = {formSpecId: specificationVersionId,feeTypeSpecId, invoiceIds}
      params.push(temp)
    }else {
      params = feeTypes.map((item, index) => {
        const temp = Array.isArray(dataSource[index]) ? dataSource[index] : [dataSource[index]]
        const invoiceIds = this.getInvoiceIds(temp)
        return {
          formSpecId: specificationVersionId,
          feeTypeSpecId: item.id,
          invoiceIds
        }
      })
    }
    return params
  }

  formatInvoiceResult = (rData: any[], targetData: any[]) => {
    const temp = {}
    rData.forEach(i => {
      if(i?.invoiceId && i?.bindValidateMessage?.length){
        temp[i.invoiceId] = i.bindValidateMessage.join()
      }
    })
    return  targetData?.map(i => {
      if(i?.master?.id && temp[i.master.id]) {
        i.master.bindValidateMessage = temp[i.master.id]
      }else if(i?.master?.bindValidateMessage){
        i.master.bindValidateMessage = ''
      }
      return i
    })
  }

  handleDisplayVerifyResult = (targetData: any[]) => {
    const cDataSource = this.formatInvoiceResult(this.verifyInvoiceResult, targetData)
    if(cDataSource && cDataSource.length) {
      this.setState({
        invoiceResultData: cDataSource
      })
    }
  }

  // 发票自定义校验逻辑
  handleVerifyInvoice = async (feeTypes: any[], dataSource: any[]) => {
    const param = this.getVerifyParam(feeTypes, dataSource)
    const res = await Fetch.POST('/api/v2/invoice/bindValidate',{},{ body: param })

    try {
      if(res.items && res.items.length) {
        this.verifyInvoiceResult = res.items
        return Modal.warning({
          title: i18n.get('禁止提交'),
          content: i18n.get('存在与所选费用类型不能关联的发票，请重新选择其他费用类型或上传其他发票再提交'),
          okText: i18n.get('我知道了'),
          onOk: () => {this.handleDisplayVerifyResult(dataSource)}
        })
      }
    }catch (e) {
      console.log(e)
    }
  }

  handleModalSave = async () => {
    const { invoiceData, isMerge } = this.props
    let resultFeeTypes = Object.values(this.resultMap) as FeeTypeIF[]

    const dataSource = Array.isArray(invoiceData) ? invoiceData : [invoiceData]
    const isVerify = this.fnVerifyFeeType(resultFeeTypes, dataSource, isMerge)
    if (!isVerify) {
      return
    }
    resultFeeTypes = await this.fnGetFeeTypeTemplate(resultFeeTypes)
    const hasError = this.fnVerifyFeeTypeTemplate(resultFeeTypes)
    if (hasError) {
      return
    }

    const hasInvoiceError =  await this.handleVerifyInvoice(resultFeeTypes, dataSource)
    if(!!hasInvoiceError) {
      return
    }
    this.fnTrack(resultFeeTypes)
    this.props.layer.emitOk(resultFeeTypes)
  }

  // 批量修改的保存
  handleModalSaveEdit = () => {
    this.resultMapHistory = cloneDeep(this.resultMap)
    this.recommendMapHistory = cloneDeep(this.recommendMap)
    this.setState({
      batchEditStatus: 1
    })
  }

  handleFeeTypeChangeSelected = id => {
    const { feeTypeMap } = this.props
    const feeType = feeTypeMap[id]
    this.setState({
      currentFeeType: feeType
    })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleImgPreview = imageList => {
    api.emit('@vendor:preview:images', [imageList], imageList)
  }

  handleFileDownload = line => {
    const url = getV(line, 'url', '')
    const fileName = getV(line, 'fileName', '')
    api.emit('@vendor:download', url, fileName)
  }

  // 切换为批量编辑模式
  handleModalEdit = () => {
    this.setState({
      batchEditStatus: 2,
      currentFeeType: null,
      checkedList: [],
      indeterminate: false,
      checkAll: false
    })
  }

  // 取消批量编辑
  handleModalCancelEdit = () => {
    this.setState(
      {
        batchEditStatus: 3
      },
      () => {
        this.resultMap = JSON.parse(JSON.stringify(this.resultMapHistory))
        this.recommendMap = JSON.parse(JSON.stringify(this.recommendMapHistory))
      }
    )
  }

  // 全选按钮变更后的逻辑
  onCheckAllChange = e => {
    const { invoiceData } = this.props
    this.setState({
      checkedList: e.target.checked ? invoiceData.map((v, index) => index.toString()) : [],
      indeterminate: false,
      checkAll: e.target.checked
    })
  }

  // checkbox group 变更后的逻辑
  onCheckboxGroupChange = checkedList => {
    const { invoiceData } = this.props
    this.setState({
      checkedList,
      indeterminate: !!checkedList.length && checkedList.length < invoiceData.length,
      checkAll: checkedList.length === invoiceData.length
    })
  }

  renderPanel = (index, invoices, showIndex) => {
    const { feeTypes, feeTypeMap, specificationVersionId, submitterId, visibleFeeTypeByFormDataMap } = this.props
    const { currentFeeType, checkedList, batchEditStatus } = this.state
    const feeType = checkedList.includes(index.toString()) ? currentFeeType : null
    const historyFeeType = this.resultMapHistory[index]
    return (
      <InvoicePannel
        key={index}
        index={index}
        invoices={invoices}
        feeTypeMap={feeTypeMap}
        feeTypes={feeTypes}
        showIndex={showIndex}
        specificationVersionId={specificationVersionId}
        submitterId={submitterId}
        visibleFeeTypeByFormDataMap={visibleFeeTypeByFormDataMap}
        onFeeTypeChange={this.handleFeeTypeChange}
        onFileDownload={this.handleFileDownload}
        onImgPreview={this.handleImgPreview}
        onUpdateRecommend={this.handleUpdateRecommend}
        currentFeeType={feeType}
        historyFeeType={historyFeeType}
        batchEditStatus={batchEditStatus}
      />
    )
  }

  renderHeader = isBatchEdit => {
    const { feeTypes, feeTypeMap } = this.props
    const { checkedKeys, recommends, indeterminate, checkAll, checkedList } = this.state
    if (isBatchEdit) {
      return (
        <div className="modal_header">
          <div className="title_wrapper">
            <div className="title">{i18n.get('批量编辑')}</div>
            <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
          </div>
          <div className="header_check_all">
            <Checkbox indeterminate={indeterminate} onChange={this.onCheckAllChange} checked={checkAll}>
              <span className="checked_desc">{i18n.get('全选')}</span>
              <span className="checked_desc">
                {i18n.get('已选{__count}条，批量修改费用类型为：', { __count: checkedList.length })}
              </span>
            </Checkbox>
            <div className="fee-type-select-container">
              <FeeTypeSelect
                className="fee-type-select"
                feeTypes={feeTypes}
                feeTypeMap={feeTypeMap}
                checkedKeys={checkedKeys}
                showFullPath={true}
                recommends={recommends}
                disabledCheckedFather
                allowClear={false}
                showCode={true}
                onChange={this.handleFeeTypeChangeSelected}
              />
            </div>
          </div>
        </div>
      )
    }
    return (
      <div className="modal_header">
        <div className="title_wrapper">
          <div className="title">{i18n.get('选择费用类型')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="header_desc">
          <T name="系统依据你所选发票(及发票明细)，已自动匹配出最为合适的费用类型，若匹配有误，你可在此修改" />
        </div>
      </div>
    )
  }

  renderFooter = (isBatchEdit, canEdit) => {
    if (isBatchEdit) {
      return (
        <div className="modal-footer">
          <Popconfirm
            title="退出批量模式会丢失未保存的修改， 确定要退出吗？"
            okText={i18n.get('确定')}
            cancelText={i18n.get('取消')}
            onConfirm={this.handleModalCancelEdit}
          >
            <Button category="secondary" className="mr-8 btn">
              <T name="退出批量编辑" />
            </Button>
          </Popconfirm>
          <Button className="btn" category="primary" onClick={this.handleModalSaveEdit}>
            <T name="保存" />
          </Button>
        </div>
      )
    }
    return (
      <div className="modal-footer">
        <Button category="secondary" className="mr-8 btn" onClick={this.handleModalClose}>
          <T name="返回修改" />
        </Button>
        {canEdit && (
          <Button category="secondary" className="mr-8 btn" onClick={this.handleModalEdit}>
            <T name="批量编辑" />
          </Button>
        )}
        <Button className="btn" category="primary" onClick={() => this.handleModalSave()}>
          <T name="确定生成" />
        </Button>
      </div>
    )
  }

  render() {
    const { invoiceData, isMerge } = this.props
    const { batchEditStatus, checkedList, invoiceResultData } = this.state
    const dataSource = invoiceResultData
    const showIndex = dataSource.length > 1
    const canEdit = showIndex && !isMerge
    return (
      <div className="invoice_feetype_recommend">
        {this.renderHeader(batchEditStatus === 2)}
        <div className="recommend_wrapper">
          {isMerge ? (
            this.renderPanel(0, dataSource, false)
          ) : (
            <CheckboxGroup value={checkedList} onChange={this.onCheckboxGroupChange}>
              {dataSource.map((line, index) => this.renderPanel(index, [line], showIndex))}
            </CheckboxGroup>
          )}
        </div>
        {this.renderFooter(batchEditStatus === 2, canEdit)}
      </div>
    )
  }
}
