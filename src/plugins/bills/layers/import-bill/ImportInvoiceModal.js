import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import style from './ImportInvoiceModal.module.less'
import { FilesUploader } from '@ekuaibao/uploader'
import classnames from 'classnames'
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'

import { Fetch } from '@ekuaibao/fetch'
import { uniqBy, get, includes } from 'lodash'
import { Button, Alert, Tooltip, Progress, message, Popconfirm } from '@hose/eui';
import { app as api } from '@ekuaibao/whispered'
import { OutlinedDirectionUpload, OutlinedEditDeleteTrash, OutlinedTipsWarning} from '@hose/eui-icons'
import { getShowPreImg } from './util'
import RichMessage from './RichMessage'

function buildData(file) {
  let { name } = file
  return { name: encodeURIComponent(name) }
}

@EnhanceModal({
  footer: [],
  title:i18n.get('导入电子发票'),
  className: 'custom-modal-layer eui-close'
})
export default class ImportInvoiceModal extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      uploaderFileList: [],
      visible: false,
      fileList: [],
      disabled: false,
      isVisible: true,
      disabledUpload: false
    }

    let { details = [], flowId = '' } = props

    this.orderIds = []
    this.checkedIds = []
    this.flowId = flowId
    details.forEach(o => {
      let orders = getNodeValueByPath(o, 'feeTypeForm.orders', [])
      let ids = orders.map(line => line.id)
      this.orderIds = this.orderIds.concat(ids)
      const invoices = getNodeValueByPath(o, 'invoiceForm.invoices', [])
      this.checkedIds = this.checkedIds.concat(this.fnGetInvoiceDetailIds(invoices))
    })
    this.getImportedIds()
    props.overrideGetResult(this.getResult.bind(this))
  }
  getImportedIds = () => {
    if (api.has('get:bills:value')) {
      api.invoke('get:bills:value').then(result => {
        const details = get(result, 'values.details', [])
        let invoiceList = []
        details &&
          details.forEach(line => {
            const invoices = get(line, 'feeTypeForm.invoiceForm.invoices', []) || []
            invoiceList = invoiceList.concat(invoices)
          })
        this.checkedIds = this.checkedIds.concat(this.fnGetInvoiceDetailIds(invoiceList))
      })
    }
  }

  fnGetInvoiceDetailIds = (invoices = []) => {
    let ids = []
    invoices.forEach(oo => {
      if (oo.details && Array.isArray(oo.details)) {
        oo.details.forEach(item => {
          ids.push(item.id)
        })
      }
    })
    return ids
  }

  getResult() {
    let invoiceList = this.sucFileList
    return { invoiceList }
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleChange = uploaderFileList => {
    this.setState({ uploaderFileList })
  }

  handleDelete(index) {
    let { fileList } = this.state
    fileList.splice(index, 1)
    fileList = fileList.slice()
    let isVisible = !(fileList.length >= 1)
    this.setState({ fileList, isVisible, disabledUpload: false })
  }

  handleOnDone = (uploaderFileList = []) => {
    const newList = this.state.fileList.slice(0).concat(uploaderFileList)
    const errorList = []
    const successList = []
    newList.forEach(item => {
      if (item?.response?.status === 'SUCCESS') {
        successList.push(item)
      } else {
        errorList.push(item)
      }
    })
    const fileList = uniqBy(successList, 'response.master.id') //之前直接过滤fileList导致失败的被过滤掉，并且只留了一个失败的,现在改成只过滤成功的
    const filteredErrorFileList = errorList.length > 0 ? uniqBy(errorList, 'response.master.id') : []
    if (filteredErrorFileList.length < errorList.length || fileList.length < successList.length) {
      message.warning(i18n.get('已经过滤掉重复的票据'))
    }
    this.setState({
      uploaderFileList: [],
      disabled: false,
      fileList: filteredErrorFileList.concat(fileList),
      isVisible: false,
      disabledUpload: false
    })
    let filesUpLoader = this.refs['filesUpLoader']
    let input = filesUpLoader && filesUpLoader.refs['input']
    if (input) input.value = ''
  }

  handleOnStart = file => {
    if (file && file.length > 0) {
      this.setState({
        disabled: true,
        disabledUpload: true
      })
    } else {
      message.warning(i18n.get('上传文件过大，每个文件上限5M！'))
    }
  }

  renderUpLoaderProgress(isShowProgress, name, index) {
    return (
      <div className={isShowProgress ? 'file-info text-color' : 'file-info'}>
        <div style={{ overflowX: 'hidden' }}>
          { getShowPreImg({file:{ name:name }})}
          <div className="file-name">{name}</div>
        </div>
        {!isShowProgress && (
          <Popconfirm
            title={i18n.get('确定删除该票据吗？')}
            content={i18n.get('删除后无法撤回，请谨慎操作')}
            okButtonProps={{ theme: 'danger' }}
            onConfirm={() => this.handleDelete(index)}
            okText={i18n.get('删除')}
          >
            <OutlinedEditDeleteTrash fontSize={14} />
          </Popconfirm>
        )}
      </div>
    )
  }

  renderUpLoaderError(messageErr, name, index, isShowProgress) {
    return (
      <div className={classnames("file-info", { "error-color": !isShowProgress })}>
        <Tooltip placement='topLeft' title={<RichMessage message={messageErr} />}>
          <div style={{ overflowX: 'hidden', cursor:"pointer" }}>
            { getShowPreImg({file:{ name:name }})}
            <div className="file-name">{name} </div>
              {!isShowProgress && <OutlinedTipsWarning className='ml-4' fontSize={12}/>}
          </div>
        </Tooltip>
        { !isShowProgress &&
          <Popconfirm
            title={i18n.get('确定删除该票据吗？')}
            content={i18n.get('删除后无法撤回，请谨慎操作')}
            onConfirm={() => this.handleDelete(index)}
            okText={i18n.get('删除')}
            okButtonProps={{ theme: 'danger' }}
          >
            <OutlinedEditDeleteTrash fontSize={14} />
          </Popconfirm>
         }
      </div>
    )
  }

  handleImportData() {
    let { fileList } = this.state
    const failedList = fileList.filter(line => line.isFailed)
    const sucFiledList = fileList.filter(line => !line.isFailed).map(line => line.response)
    return { failedList, sucFiledList }
  }

  handleValidatorFiles = (files = []) => {
    const fnGetFileType = file => {
      // 部分类型的文件 file 实例无 type类型 现以文件名后缀为 type
      const fileNameArr = file?.name?.split('.')
      return fileNameArr?.pop()?.toLocaleLowerCase()
    }
    const validSuffixes = ['pdf', 'xml', 'ofd']
    const invalidFiles = files.filter(file => !validSuffixes.includes(fnGetFileType(file)))
    if (invalidFiles.length) {
      message.warning(i18n.get('仅支持pdf、ofd、xml格式文件'))
      return false
    }
    return true
  }

  handleModalSave = () => {
    const { failedList, sucFiledList } = this.handleImportData()

    if (failedList.length) {
      return message.warning(i18n.get('存在不合法的发票'))
    }
    this.sucFileList = sucFiledList
    if (this.sucFileList.length > 0) {
      this.props.layer.emitOk()
    } else {
      message.destroy()
      message.warning(i18n.get('请选择可导入的发票！'))
    }
  }

  renderItem(line, index) {
    let { name, response, progress, status } = line

    let percent = 0
    let isShowProgress = status === 'uploading'

    if (progress && progress.percent && progress.percent !== 100) {
      percent = progress.percent
    } else if (isShowProgress) {
      percent = 95
    } else if (status === 'done' || status === 'error') {
      percent = 100
    }

    let importedFlag = false //是否重复导入
    let messageErr = ''

    if (percent === 100) {
      if (line.status === 'error') {
        if (line.response instanceof Error) {
          messageErr = line.response.message
        } else {
          messageErr = i18n.get('导入出错')
        }

        line.isFailed = !response.ischeck
      } else if (response) {
        if (response.value) {
          let flowId = response.value.flowId
          importedFlag = includes(this.orderIds, response.value.id) || (flowId && flowId !== this.flowId)
        }
        let checked = false
        if (response.details && this.checkedIds.length) {
          checked = this.fnCheckedImport(response.details)
        }
        messageErr =
          response.status === 504
            ? i18n.get('识别超时 请重试!')
            : importedFlag || checked
            ? i18n.get('该发票已经导入，不可重复导入')
            : response.message || response.errorMessage
        line.isFailed = !response.ischeck
      }
    }

    return (
      <div key={index} className="file-list-item">
        {response && response.ischeck && this.renderUpLoaderProgress(isShowProgress, name, index) }
        {!(response && response.ischeck) && this.renderUpLoaderError(messageErr, name, index, isShowProgress)} 
        {isShowProgress && <Progress className="progress" size="small" percent={percent} strokeWidth={2} showInfo={false} />}
      </div>
    )
  }

  fnCheckedImport = details => {
    if (!details?.length) {
      return false
    }
    let checkedIds = []
    let ids = details.map(line => line.id)
    for (let i = 0; i < details.length; i++) {
      const line = details[i]
      if (this.checkedIds.indexOf(line.id) >= 0) {
        line.active = false
        checkedIds.push(ids[i])
      }
    }
    return checkedIds.length === ids.length
  }

  render() {
    let { fileList, uploaderFileList, disabled, isVisible, disabledUpload } = this.state
    let { multiple, source, submitterId = {} } = this.props
    fileList = fileList.concat(uploaderFileList)
    let corpId = encodeURIComponent(Fetch.ekbCorpId)
    let accessToken = encodeURIComponent(Fetch.accessToken)
    const submitterIdId = submitterId.id || ''
    const url = `${Fetch.fixOrigin(
      window.UPLOAD_INVOICE_FILE_URL
    )}/api/v2/upload/invoice?corpId=${corpId}&accessToken=${accessToken}&staffId=${submitterIdId}`
    return (
      <div id={'bills-importInvoiceModal'} className={style['import-invoice-wrapper']}>
        <Alert
          message={
           <> <div>{'1、' + i18n.get('仅支持pdf、ofd、xml')}</div>
            <div>{'2、' + i18n.get('发票文件名建议字母和数字，长度不超过20，否则无法预览')}</div></>
          }
          type="info"
        />
        <div className="import-invoice-content">
          <div className="select-file">
            <div className="select-file-title">{i18n.get('票据')}</div>
            <div className={classnames('ekb-files-uploader-invoice', { 'IE-compatible': window.EKB_IS_IE  })}>
              <div>
                <FilesUploader
                  ref="filesUpLoader"
                  action={url}
                  multiple={multiple}
                  maxSize={5}
                  accept={'application/pdf,.pdf,.ofd,.xml'}
                  disabledUpload={disabledUpload}
                  onDone={this.handleOnDone}
                  onStart={this.handleOnStart}
                  onChange={this.handleChange}
                  data={file => buildData(file)}
                  validatorFiles={this.handleValidatorFiles}
                >
                  <Button icon={<OutlinedDirectionUpload />} category={'secondary'} size='small' className="upload-invoice" disabled={disabledUpload}>
                    {i18n.get('上传电子发票')}
                  </Button>
                </FilesUploader>
              </div>
              <div className="upload-file-list">
                {fileList && fileList.reverse().map((line, index) => this.renderItem(line, index))}
              </div>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <Button key="cancel" size="middle" category='secondary' className="mr-8 btn" onClick={this.handleModalClose}>
            {i18n.get('取消')}
          </Button>
          <Button
            key="ok"
            type="primary"
            size="middle"
            disabled={disabled}
            className="btn"
            onClick={() => this.handleModalSave()}
          >
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
