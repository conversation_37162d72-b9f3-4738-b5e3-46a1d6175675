@import '~@e<PERSON><PERSON><PERSON>/eui-styles/less/token.less';

// 手动校验发票新的页面样式
.new-check-invoice-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 500px;
  padding: 0 16px;

  :global {
    .check-invoice-btn {
      width: 100%;
      margin: 24px 0;
      text-align: center;
    }
    .input-content {
      flex: 1;

      .invoice-tab {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;
      }

      .new-check-invoice-form {
        margin: 0 auto;
      }
      .eui-form-item-explain{
        margin-bottom: 0;
      }

      .eui-segmented-block {
        margin-bottom: 16px;
      }

      .invoice-instruction {
        font-weight: 400;
        border-radius: 6px;
        background: var(--eui-bg-body-overlay, #F7F8FA);
        margin: auto 0;
        padding: 12px;
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.70));
        font:var(--eui-font-body-r1);
        
        .instruction-title {
          margin-bottom: 8px;
          color: var(--eui-text-caption, rgba(29, 33, 41, 0.70));
          font:var(--eui-font-body-b1);
          font-weight: 500;
        }
        
        .instruction-content {
          p {
            line-height: 20px;
            margin-bottom: 4px;
            &:last-child{
              margin-bottom: 0;
            }
          }
          
          ul {
            margin: 0px 0 0 20px;
            padding: 0;
            
            li {
              list-style-type: disc;
              line-height: 20px;
              margin-bottom: 4px;
            }
          }
        }
      }
    }
    
    .modal-footer {
      justify-content: flex-end;
      padding: 16px 0;
      box-shadow: none;
      height: auto;
      gap: 8px;
    }
  }
}