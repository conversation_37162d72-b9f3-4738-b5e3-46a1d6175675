/**
 *  Created by <PERSON><PERSON> on 2019/1/25 5:29 PM.
 */
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
// @ts-ignore
import styles from './ImportUploadOCRModal.module.less'
import { Button, message, Space, Alert } from '@hose/eui'
const classNames = require('classnames')
import AttachmentComponent from '../../../../../elements/attachment-component/AttachmentComponent'
import { showMessage } from '@ekuaibao/show-util'
import { OutlinedTipsClose, OutlinedDirectionUpload } from '@hose/eui-icons'
export const File_REG = /^(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|tiff|png|raw|tga|pdf|ofd|xml|zip)$/i
const MEDICAL_IMG_REG = /^(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|tiff|png|raw|tga|pdf)$/i
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer eui-modal-layer'
})
export default class ImportUploadOCRModal extends PureComponent<any, any> {
  constructor(props: any) {
    super(props)
    this.state = {
      isLoading: false,
      attachments: []
    }
  }
  handleModalSave = () => {
    if (this.state.attachments.length > 0) {
      if (this.state.attachments.length > 50) {
        message.destroy()
        message.warning(i18n.get('最多可上传50张，超出请分批次上传'))
      } else {
        this.props.layer.emitOk(this.state.attachments)
      }
    } else {
      message.destroy()
      message.warning(i18n.get('请选择可导入的图片！'))
    }
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
  }

  handleAttactFinshed = (attachments: []) => {
    this.setState({ attachments })
  }

  handleUploading = (isLoading: boolean) => {
    this.setState({ isLoading })
  }

  handleFormatFile = (files: any) => {
    const { invoiceType, isMedical} = this.props
    const reg = isMedical ? MEDICAL_IMG_REG : File_REG
    const formatFiles = files.filter((data: any) => reg.test(data.file.name))
    if (files.length !== formatFiles.length) {
      showMessage.warning(i18n.get('已经自动过滤非图片的文件'))
    }
    return formatFiles
  }

  filesExceedEvent = () => {
    showMessage.warning(i18n.get('已导入选取的前50张发票，超过部分请重新上传'), 5)
  }

  renderChildren = () => {
    const { isLoading } = this.state

    return (
      <div className="select-file">
        <div className={"select-file-title"}>
          <span className='label'>  {i18n.get('票据')}</span>
          {this.props.isOverseas && <span className="select-file-sub-title">{i18n.get('（请先确定法人实体再上传海外票据，否则切换后会对币种和金额产生影响）')}</span>}
        </div>
        <div>
          <Button icon={<OutlinedDirectionUpload />} size='small'  category="secondary"  disabled={isLoading}>
            {i18n.get('上传文件')}
          </Button>
        </div>
      </div>
    )
  }

  renderTips = () => {
    const { isMedical, isOverseas } = this.props
    const isOCR = !isMedical && !isOverseas
    return (
      <>
        {
          isMedical ? (
            <div className="tip">
              <div>{i18n.get('1、仅支持电子医疗票据')}</div>
              <div>{i18n.get('2、单次最多可上传50个文件，超过50请分批次上传')}</div>
              <div>{i18n.get('3、支持格式{__k0}', { __k0: 'jpg、jpeg、png、pdf' })}</div>
            </div>
          ) : <div></div>
        }
        {
          isOverseas ? (
            <div className="tip">
              <div>{i18n.get('1、单次最多可上传{__k0}个文件，超过{__k0}个请分批次上传', {  __k0: '10' })}</div>
              <div>{i18n.get('2、仅可上传海外票据、中国港澳台地区票据')}</div>
              <div>{i18n.get('3、支持格式{__k0}', { __k0: 'jpg、jpeg、png、pdf' })}</div>
              <div>{i18n.get('4、发票文件名字建议为字母和数字，长度不超过20，否则无法预览')}</div>
            </div>
          ): <div></div>
        }
        {
          isOCR ? (
            <div className="tip">
              <div>{i18n.get('1、单次最多可上传{__k0}个文件，超过{__k0}请分批次上传', {  __k0: '50' })}</div>
              <div>{i18n.get('2、可上传增值税发票、铁路客票、出租车票、飞机行程单等票据照片')}</div>
              <div>{i18n.get('3、支持格式{__k0}', { __k0: 'jpg、jpeg、png、pdf、ofd、xml、zip' })}</div>
              <div>{i18n.get('4、发票文件名字建议为字母和数字，长度不超过20，否则无法预览')}</div>
            </div>
          ): <div></div>
        }
      </>
    )
  }

  render() {
    const { uploadLimit, isMedical, isOverseas } = this.props
    let title = '智能识票'
    const { isLoading } = this.state
    if(isMedical && typeof(isMedical) !== 'object'){
      title = '医疗发票'
    }else if(isOverseas){
      title = '海外票据'
    }
    return (
      <div className={styles.import_upload_ocr_modal}>
        <div className="modal-header">
          <div className="flex header-title">{i18n.get(title)}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="tips-content"><Alert message={this.renderTips()} type="info"/></div>
        <div className="ocr_content">
          <AttachmentComponent
            onUploading={this.handleUploading}
            onFinshed={this.handleAttactFinshed}
            children={this.renderChildren()}
            isLoading={isLoading}
            isOCR={true}
            onFormatFile={this.handleFormatFile}
            filesNum={50}
            filesExceedEvent={this.filesExceedEvent}
            fileMaxSize={18}
            uploadLimit={uploadLimit}
          />
        </div>
        <div className="modal-footer">
          <Space wrap>
            <Button key="cancel" data-testid="import-uploadinvoice-cancel" category="secondary" onClick={this.handleModalClose}>
              {i18n.get('取消')}
            </Button>
            <Button key="ok" data-testid="import-uploadinvoice-ok" disabled={isLoading} loading={isLoading} onClick={() => this.handleModalSave()}>
              {i18n.get('确定')}
            </Button>
          </Space>
        </div>
      </div>
    )
  }
}
