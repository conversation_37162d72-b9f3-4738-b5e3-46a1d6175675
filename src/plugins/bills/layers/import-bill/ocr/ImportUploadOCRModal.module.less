.import_upload_ocr_modal {
  :global {
    .modal-header {
      border: none;
      .header-title {
        font-size: 16px;
      }
    }
    .tips-content {
      padding: 0 16px;
      .tips{
        color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
        font: var(--eui-font-body-r1);
      }
    }
    .ocr_content {
      position: relative;
      padding: 0 16px;
      min-height: 100px;
      .gpy-invoice {
        position: absolute;
        left: 42%;
        z-index: 101;
        top: 42px;
        .gpy-img {
          display: none;
        }
      }
      .ekb-files-uploader-wrapper {
        .input-wrapper > input {
          width: 40%;
        }
        .ekb-files-uploader-content {

          .select-file {
            display: flex;
            flex-direction: column;
            margin-bottom: 4px;
            margin-top: 16px;
            .upload-invoice {
              // display: flex;
              width: 96px;
              height: 32px;
              justify-content: center;
              align-items: center;
              border: solid 1px #d6dbdb;
            }
            .select-file-title {
              display: inline-block;
              flex-shrink: 0;
              color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
              font: var(--eui-font-body-r1);
              margin-bottom: 8px;
              .label:after{
                display: inline-block;
                margin-left: 2px;
                color: var(--eui-function-danger-500);
                font: var(--eui-font-body-b1);
                content: "*";
              }
            }
            .select-file-sub-title {
              color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
              font: var(--eui-font-note-r2);
            }
          }
        }
      }
      
    }
    .modal-footer {
      border-top: none;
      box-shadow: none;
    }
  }
}
