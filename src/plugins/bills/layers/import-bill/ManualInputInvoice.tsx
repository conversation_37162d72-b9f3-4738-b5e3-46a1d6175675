/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/14 上午11:38.
 */
import styles from './ManualInputInvoice.module.less'
import React, { PureComponent } from 'react'
import { Form, Input, Spin, DatePicker, Segmented } from '@hose/eui'
import { Button } from '@hose/eui'
import { showMessage } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import { getInputInvoiceData } from '../../bills.action'
import moment, { Moment } from 'moment'
import 'moment/locale/zh-cn'
import RichMessage from './RichMessage'

const FormItem = Form.Item

interface ViewData {
  label: string
  name: string
  msg: string
  placeholder: string
  onBlur?: boolean
  isRequired?: boolean
  addonAfter?: () => React.ReactNode
}

interface Props {
  isNewCheckInvoice?: boolean
  unNeedTxt?: boolean
  unNeedTap?: boolean
  isChecked?: boolean
  onGetInvoiceDetails?: (props: Props, data: any) => void
  onGetImportInvoiceDetails?: (props: Props, data: any) => void
  submitterId?: { id: string }
  onShowInvoiceDetail?: (data: any, form: any) => void
  stackerManager: any
  layer: any
}

interface State {
  viewDatas: ViewData[]
  imageData: string
  message: string
  isShowLoading: boolean
  invoiceTabType: 'normal' | 'token'
}

const commonViewDatas = (): ViewData[] => {
  return [
    {
      label: i18n.get('发票代码'),
      name: 'fpdm',
      msg: i18n.get('发票代码不可为空'),
      placeholder: i18n.get('请输入10或12位发票代码'),
      onBlur: true
    },
    {
      label: i18n.get('发票号码'),
      name: 'fphm',
      msg: i18n.get('发票号码不可为空'),
      placeholder: i18n.get('请输入8位发票号码')
    },
    {
      label: i18n.get('发票日期'),
      name: 'kprq',
      msg: i18n.get('发票日期不可为空'),
      placeholder: i18n.get('请输入发票日期'),
      isRequired:true
    }
  ]
}

const priceView = (isRequired: boolean = true): ViewData => ({
  label: i18n.get('金额'),
  name: 'fpje',
  isRequired,
  msg: i18n.get('金额（不含税）不可为空'),
  placeholder: i18n.get('请输入金额（不含税）')
})

const usedCarView = (): ViewData => ({
  label: i18n.get('车价合计'),
  name: 'fpje',
  msg: i18n.get('车价合计'),
  placeholder: i18n.get('请输入车价合计')
})

const checkCodeView = (isToken: boolean = false, isRequired: boolean = true): ViewData => ({
  label: i18n.get('校验码'),
  name: 'jym',
  isRequired,
  msg: i18n.get('校验码不可为空'),
  placeholder: !isToken ? i18n.get('请输入校验码末6位') : i18n.get('请输入5位校验码')
})

const getContainerPriceViewData = (): ViewData[] => 
  commonViewDatas().concat(priceView())

const getContainerCheckCodeViewData = (isToken: boolean = false): ViewData[] =>
  commonViewDatas().concat(checkCodeView(isToken))

const getContainerTokenViewData = (isToken: boolean = true, isRequired: boolean = true): ViewData[] =>
  commonViewDatas()
    .concat(priceView(isRequired))
    .concat(checkCodeView(isToken, isRequired))

const getContainerUsedCarViewData = (): ViewData[] =>
  commonViewDatas().concat(usedCarView())

// 移除 @EnhanceFormCreate() 装饰器
export default class ImportInputInvoice extends PureComponent<Props, State> {
  private formRef = React.createRef<any>()

  constructor(props: Props) {
    super(props)
    moment.locale('zh-cn')
    const invoiceViewDatas = getContainerPriceViewData()
    this.state = {
      viewDatas: invoiceViewDatas,
      imageData: '',
      message: i18n.get('请输入验证码'),
      isShowLoading: false,
      invoiceTabType: 'normal'
    }
  }

  fnGetContainerViewDataByType = (): void => {
    const invoiceCode = this.formRef.current?.getFieldValue('fpdm') // 使用 formRef
    const { invoiceTabType } = this.state
    if (invoiceTabType === 'token') {
      // 区块链发票
      this.setState({ viewDatas: getContainerTokenViewData(true, true) })
    } else if (invoiceCode) {
      api.invokeService('@bills:get:invoice:inputType', { fpdm: invoiceCode, fphm: '' }).then((res: any) => {
        const { fplx, showType } = res?.value || {}
        if (fplx === '15') {
          // 二手车
          this.setState({ viewDatas: getContainerUsedCarViewData() })
        } else if (showType === 'ALL') {
          this.setState({ viewDatas: getContainerTokenViewData(undefined, true) })
        } else if (showType === 'AMOUNT') {
          this.setState({ viewDatas: getContainerPriceViewData() })
        } else {
          this.setState({ viewDatas: getContainerCheckCodeViewData(false) })
        }
      })
    } else {
      this.setState({ viewDatas: getContainerPriceViewData() })
    }
  }

  handleCancel = (): void => {
    this.props.stackerManager.clear()
    this.props.layer.emitCancel()
  }

  handleOK = (): void => {
    const { viewDatas } = this.state
    const { onGetInvoiceDetails, onGetImportInvoiceDetails, submitterId = {}, onShowInvoiceDetail } = this.props
    const { invoiceTabType } = this.state
    
    this.formRef.current?.validateFields().then((values: any) => {
      const data: any = {
        staffId: (submitterId as any)?.id || ''
      }
      // 从 values 中获取表单值
      viewDatas.forEach((line: ViewData) => {
        const key = line.name
        let value = values[key]
        if (key === 'kprq') {
          value = (value as Moment).format('YYYYMMDD')
        }
        data[key] = value
      })
      
      if (!data.fpje && !data.jym) {
        return showMessage.error(i18n.get('请输入金额或校验码'), 2)
      }
      if (invoiceTabType === 'token' && data.fpdm?.length !== 10 && data.fpdm?.length !== 12) {
        return showMessage.error(i18n.get('查验失败：[发票代码：不合法的长度]，请稍后再试!'), 2)
      }

      if (invoiceTabType === 'token') {
        data.isBlockChain = true
      }

      this.setState({ isShowLoading: true })

      api.dispatch(getInputInvoiceData(data)).then(
        (data: any) => {
          this.setState({ isShowLoading: false })
          const { message, ischeck } = data
          if (!ischeck && message) {
            return showMessage.error({
              message: <RichMessage message={message} showIcon={false} />
            })
          }

          ischeck && onGetInvoiceDetails && onGetInvoiceDetails(this.props, data)
          ischeck && onGetImportInvoiceDetails && onGetImportInvoiceDetails(this.props, data)
          if (typeof onShowInvoiceDetail === 'function') {
            // 呼出新版发票详情页
            onShowInvoiceDetail(data, this.formRef.current)
          }
        },
        (err: any) => {
          this.setState({ isShowLoading: false })
          return showMessage.error(err.message)
        }
      )
    }).catch((errorInfo: any) => {
      console.log('Validate Failed:', errorInfo)
    })
  }

  renderFormItem(): React.ReactNode[] {
    const { viewDatas, invoiceTabType } = this.state

    return viewDatas.map((line: ViewData) => {
      let { label, name, msg, placeholder, isRequired = true } = line
      if (name === 'fpdm' && invoiceTabType === 'normal') {
        isRequired = false
      }
      
      if (name === 'kprq') {
        return (
          <FormItem
            key={name}
            label={label}
            name={name}
            rules={[{ type: 'object', required: true, whitespace: true, message: msg }]}
          >
            <DatePicker style={{ width: '100%' }} placeholder={placeholder} />
          </FormItem>
        )
      }

      if (name === 'jym') {
        return (
          <FormItem
            key={name}
            label={label}
            name={name}
            rules={[{ required: isRequired, whitespace: true, message: msg }]}
            normalize={(value: string = '') => value.replace(/\s+/g, '')}
          >
            <Input placeholder={placeholder} addonAfter={line.addonAfter && line.addonAfter()} />
          </FormItem>
        )
      }
     
      return (
        <FormItem
          key={name}
          label={label}
          name={name}
          rules={[{ required: isRequired, whitespace: true, message: msg }]}
        >
          <Input
            placeholder={placeholder}
            addonAfter={line.addonAfter && line.addonAfter()}
            onBlur={() => {
              if (name === 'fpdm') {
                this.fnGetContainerViewDataByType()
              }
            }}
          />
        </FormItem>
      )
    })
  }

  handleChangeType = (value: string | number): void => {
    const type = value as 'normal' | 'token'
    const { invoiceTabType } = this.state
    if (type === invoiceTabType) return
    this.setState({ invoiceTabType: type }, () => {
      this.formRef.current?.resetFields() // 使用 formRef
      this.fnGetContainerViewDataByType()
    })
  }

  renderFooter = (): React.ReactNode => (
    <div className="modal-footer">
      <Button key="cancel" category="secondary"  onClick={this.handleCancel}>
        {i18n.get('取消')}
      </Button>
      <Button key="ok" category="primary" onClick={this.handleOK}>
        {i18n.get('确认')}
      </Button>
    </div>
  )

  render(): React.ReactNode {
    const { unNeedTxt = true, unNeedTap = true } = this.props
    const { invoiceTabType } = this.state
    const tips = this.props.isChecked ? i18n.get('发票查验中，请稍等') + '...' : i18n.get('数据加载中') + '...'
    
    // Segmented options
    const segmentedOptions = [
      {
        label: i18n.get('增值税发票'),
        value: 'normal'
      },
      {
        label: i18n.get('区块链电子普通发票'),
        value: 'token'
      }
    ]
    
    return (
      <div className={styles['new-check-invoice-wrapper']}>
        <Spin spinning={this.state.isShowLoading} text={tips}>
          <div className="input-content">
            {unNeedTap && (
              <Segmented 
                options={segmentedOptions} 
                value={invoiceTabType} 
                onChange={this.handleChangeType} 
                block
              />
            )}
            <div className="new-check-invoice-form">
              <Form 
                ref={this.formRef}
                name="basic" 
                compact 
                scrollToFirstError={true} 
                layout="vertical"
              >
                {this.renderFormItem()}
              </Form>
            </div>
            {invoiceTabType === 'normal' && unNeedTxt && (
              <div className="invoice-instruction">
                <div className="instruction-title">{i18n.get('查验说明')}</div>
                <div className="instruction-content">
                  <p>{i18n.get('1、可查验使用增值税发票管理新系统开具的发票，范围如下：')}</p>
                  <ul>
                    <li>{i18n.get('增值税（电子）专用发票')} {i18n.get('、')} {i18n.get('增值税（电子）普通发票')}{i18n.get('、')}{i18n.get('电子发票（增值税专用发票')}{i18n.get('、')}{i18n.get('电子发票（普通发票')}{i18n.get('、')}{i18n.get('增值税普通发票（卷式')}{i18n.get('、')}{i18n.get('机动车销售统一发票')}{i18n.get('、')}{i18n.get('货物运输业增值税专用发票')}{i18n.get('、')}{i18n.get('二手车销售统一发票')}{i18n.get('、')}{i18n.get('通行费发票')}{i18n.get('。')}</li>
                    <li>{i18n.get('不在上述范围之内的发票，请按照原查验渠道进行查验。')}</li>
                  </ul>
                  <p>{i18n.get('2、可查验的时间范围：')}</p>
                  <ul>
                    <li>{i18n.get('可查验最近1年内增值税发票管理新系统开具的发票。')}</li>
                    <li>{i18n.get('当日开具的发票如开票方已将发票数据上传税局，仅当日可查验，否则最快次日查验。')}</li>
                  </ul>
                  <p>{i18n.get(`3、每张发票每天可在线查询`)} <label style={{color:"var(--eui-text-title, rgba(29, 33, 41, 0.90))",fontWeight: 500}}>{i18n.get('5')}</label> {i18n.get('次，超过次数后请于次日再进行查验操作。')}</p>
                </div>
              </div>
            )}
          </div>
          {this.renderFooter()}
        </Spin>
      </div>
    )
  }
}
