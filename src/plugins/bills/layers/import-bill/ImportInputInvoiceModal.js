/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/14 上午11:25.
 */
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import BaseinvoiceModal from '../BaseInvoiceModal'
import { app as api } from '@ekuaibao/whispered'
import { getCalculateField } from '../../bills.action'
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'
import { repeatData } from '../../parts/right-part/billInfo/BillImport/invoiceImport'
import { includes } from 'lodash'
import { message } from '@hose/eui'

import * as FormatData from '../../../../lib/third-data'

@EnhanceModal({
  footer: [],
  width: 520,
  className: 'custom-modal-layer eui-modal-layer'
})
export default class ImportInputInvoiceModal extends PureComponent {
  constructor(props) {
    super(props)
    let { details = [], flowId = '', invoices = [] } = props

    this.orderIds = []
    this.checkedIds = []
    this.flowId = flowId
    details.forEach(o => {
      let orders = getNodeValueByPath(o, 'feeTypeForm.orders', [])
      let ids = orders.map(line => line.id)
      this.orderIds = this.orderIds.concat(ids)
      const invoices = getNodeValueByPath(o, 'feeTypeForm.invoiceForm.invoices', [])
      this.checkedIds = this.checkedIds.concat(this.fnGetInvoiceDetailIds(invoices))
    })
    this.checkedIds = this.checkedIds.concat(this.fnGetInvoiceDetailIds(invoices))
  }
  fnGetInvoiceDetailIds = (invoices = []) => {
    let ids = []
    invoices.forEach(oo => {
      if (oo.details && Array.isArray(oo.details)) {
        oo.details.forEach(item => {
          ids.push(item.id)
        })
      }
    })
    return ids
  }

  fnCheckedImport = details => {
    let checkedIds = []
    let ids = details.map(line => line.id)
    for (let i = 0; i < details.length; i++) {
      const line = details[i]
      if (this.checkedIds.indexOf(line.id) >= 0) {
        line.active = false
        checkedIds.push(ids[i])
      }
    }
    return checkedIds.length === ids.length
  }

  handleInvoiceDetails = (props, data) => {
    // 在这个地方进行手录发票的去重
    let importedFlag = false
    if (data.value) {
      let flowId = data.value.flowId
      importedFlag = includes(this.orderIds, data.value.id) || (flowId && flowId !== this.flowId)
    }
    let checked = false
    if (data.details && this.checkedIds.length) {
      checked = this.fnCheckedImport(data.details)
    }
    if (importedFlag || checked) {
      message.warning(i18n.get('该发票已有费用明细关联'))
      return false
    }

    props.layer.emitOk(data)
  }

  handleImport(props, isContinue) {
    let { visibilityFeeTypes, submitterId, invoice, onChange, flowId, details } = props
    api
      .open('@bills:SearchFeetypeTreeModal', {
        canSelectLast: true,
        visibilityFeeTypes: visibilityFeeTypes
      })
      .then(({ selectFeeType: selectItem, specification }) => {
        //感觉这个地方已经废弃了
        api.dispatch(getCalculateField(specification.id, submitterId.id)).then(action => {
          if (action.error) return
          let autoRules = action.items
          let autoCalFields = autoRules[0]
          let flag = invoice.flowId && invoice.flowId !== flowId
          if (flag) {
            showMessage.warning(i18n.get('该发票已经导入，不可重复导入'))
            return
          }
          FormatData.invoiceConvertConsume(
            selectItem,
            { value: invoice },
            specification,
            autoCalFields,
            submitterId
          ).then(consume => {
            let orders = repeatData(details, [consume])
            if (orders.length === 0) {
              showMessage.warning(i18n.get('该发票已经导入，不可重复导入'))
              return
            }
            onChange && onChange(consume)
            if (isContinue) {
              props.stackerManager.open(0, {})
            } else {
              props.layer.emitOk({})
            }
          })
        })
      })
  }

  handleImportContinue = props => {
    this.handleImport(props, true)
  }
  handleImportOK = props => {
    this.handleImport(props, false)
  }

  render() {
    return (
      <div id={'ImportInputInvoiceModal'}>
        <BaseinvoiceModal
          {...this.props}
          onGetImportInvoiceDetails={this.handleInvoiceDetails}
          importContinue={this.handleImportContinue}
          importOk={this.handleImportOK}
        />
      </div>
    )
  }
}
