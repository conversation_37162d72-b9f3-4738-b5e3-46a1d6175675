/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/3/14 上午11:38.
 */
import styles from './ImportInputInvoiceModal.module.less'
import React, { PureComponent } from 'react'
import { Form, Input, Spin, DatePicker } from 'antd'
import { Button } from '@hose/eui'
import EnhanceFormCreate from '../../../../elements/enhance/enhance-form-create'
import { showMessage } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import { getInputInvoiceData } from '../../bills.action'
import moment from 'moment'
import 'moment/locale/zh-cn'
import classnames from 'classnames'
import RichMessage from './RichMessage'

const FormItem = Form.Item
const formItemLayout = { labelCol: { span: 3, offset: 2 }, wrapperCol: { span: 16, offset: 0.5 }, labelAlign: 'left' }
const commonViewDatas = isShowLabel => {
  return [
    {
      label: isShowLabel ? i18n.get('发票代码') : '',
      name: 'fpdm',
      msg: i18n.get('发票代码不可为空'),
      placeholder: i18n.get('请输入10或12位发票代码'),
      onBlur: true
    },
    {
      label: isShowLabel ? i18n.get('发票号码') : '',
      name: 'fphm',
      msg: i18n.get('发票号码不可为空'),
      placeholder: i18n.get('请输入8位发票号码')
    },
    {
      label: isShowLabel ? i18n.get('日期') : '',
      name: 'kprq',
      msg: i18n.get('发票日期不可为空'),
      placeholder: i18n.get('请输入发票日期 例如:20180314')
    }
  ]
}

const priceView = (isShowLabel, isRequired) => ({
  label: isShowLabel ? i18n.get('金额') : '',
  name: 'fpje',
  isRequired,
  msg: i18n.get('金额（不含税）不可为空'),
  placeholder: i18n.get('请输入金额（不含税）')
})

const usedCarView = isShowLabel => ({
  label: isShowLabel ? i18n.get('车价合计') : '',
  name: 'fpje',
  msg: i18n.get('车价合计'),
  placeholder: i18n.get('请输入车价合计')
})

const checkCodeView = (isShowLabel, isToken, isRequired) => ({
  label: isShowLabel ? i18n.get('校验码') : '',
  name: 'jym',
  isRequired,
  msg: i18n.get('校验码不可为空'),
  placeholder: !isToken ? i18n.get('请输入校验码末6位') : i18n.get('请输入5位校验码')
})

const getContainerPriceViewData = (isShowLabel = true) => commonViewDatas(isShowLabel).concat(priceView(isShowLabel))
const getContainerCheckCodeViewData = (isShowLabel = true, isToken = false) =>
  commonViewDatas(isShowLabel).concat(checkCodeView(isShowLabel, isToken))
const getContainerTokenViewData = (isShowLabel = true, isToken = true, isRequired = true) =>
  commonViewDatas(isShowLabel)
    .concat(priceView(isShowLabel, isRequired))
    .concat(checkCodeView(isShowLabel, isToken, isRequired))
const getContainerUsedCarViewData = (isShowLabel = true) =>
  commonViewDatas(isShowLabel).concat(usedCarView(isShowLabel))

@EnhanceFormCreate()
export default class ImportInputInvoice extends PureComponent {
  constructor(props) {
    super(props)
    moment.locale('zh-cn')
    const invoiceViewDatas = getContainerPriceViewData(!props.isNewCheckInvoice)
    this.state = {
      viewDatas: invoiceViewDatas,
      imageData: '',
      message: i18n.get('请输入验证码'),
      isShowLoading: false,
      invoiceTabType: 'normal'
    }
  }

  fnGetContainerViewDataByType = () => {
    const { isNewCheckInvoice } = this.props
    const invoiceCode = this.props.form.getFieldValue('fpdm') // 发票代码
    // let invoiceType = getInvoiceType(invoiceCode)
    const { invoiceTabType } = this.state
    const showLabel = !isNewCheckInvoice
    if (invoiceTabType === 'token') {
      // 区块链发票
      this.setState({ viewDatas: getContainerTokenViewData(showLabel, true, true) })
    } else if (invoiceCode) {
      api.invokeService('@bills:get:invoice:inputType', { fpdm: invoiceCode, fphm: '' }).then(res => {
        const { fplx, showType } = res?.value || {}
        if (fplx === '15') {
          // 二手车
          this.setState({ viewDatas: getContainerUsedCarViewData(showLabel) })
        } else if (showType === 'ALL') {
          this.setState({ viewDatas: getContainerTokenViewData(showLabel, undefined, false) })
        } else if (showType === 'AMOUNT') {
          this.setState({ viewDatas: getContainerPriceViewData(showLabel) })
        } else {
          this.setState({ viewDatas: getContainerCheckCodeViewData(showLabel, false) })
        }
      })
    } else {
      this.setState({ viewDatas: getContainerPriceViewData(showLabel) })
    }
  }

  handleCancel = () => {
    this.props.stackerManager.clear()
    this.props.layer.emitCancel()
  }

  handleOK = () => {
    let { viewDatas } = this.state
    let { form, onGetInvoiceDetails, onGetImportInvoiceDetails, submitterId = {}, onShowInvoiceDetail } = this.props
    const { invoiceTabType } = this.state
    this.props.form.validateFields(error => {
      if (error) {
        return
      }
      let data = {
        staffId: submitterId.id || ''
      }
      viewDatas.forEach(line => {
        let key = line.name
        let value = form.getFieldValue(key)
        if (key === 'kprq') {
          value = value.format('YYYYMMDD')
        }
        data[key] = value
      })
      if (!data.fpje && !data.jym) {
        return showMessage.error(i18n.get('请输入金额或校验码'), 2)
      }
      if (invoiceTabType === 'token' && data.fpdm.length !== 10 && data.fpdm.length !== 12) {
        return showMessage.error(i18n.get('查验失败：[发票代码：不合法的长度]，请稍后再试!'), 2)
      }

      if (invoiceTabType === 'token') {
        data.isBlockChain = true
      }

      this.setState({ isShowLoading: true })

      api.dispatch(getInputInvoiceData(data)).then(
        data => {
          this.setState({ isShowLoading: false })
          const { message, ischeck } = data
          if (!ischeck && message) {
            return showMessage.error({
              message: <RichMessage message={message} showIcon={false} />
            })
          }

          ischeck && onGetInvoiceDetails && onGetInvoiceDetails(this.props, data)
          ischeck && onGetImportInvoiceDetails && onGetImportInvoiceDetails(this.props, data)
          if (typeof onShowInvoiceDetail === 'function') {
            // 呼出新版发票详情页
            onShowInvoiceDetail(data, form)
          }
        },
        err => {
          this.setState({ isShowLoading: false })
          return showMessage.error(err.message)
        }
      )
    })
  }

  renderFormItem() {
    let { form, isNewCheckInvoice } = this.props
    let { getFieldDecorator } = form
    const { viewDatas, invoiceTabType } = this.state

    return viewDatas.map(line => {
      let { label, name, msg, placeholder, isRequired = true } = line
      if (name === 'fpdm' && invoiceTabType === 'normal') {
        isRequired = false
      }

      if (name === 'kprq') {
        return (
          <FormItem
            colon={isNewCheckInvoice}
            key={name}
            {...formItemLayout}
            label={label}
            className="new-check-invoice-input"
          >
            {getFieldDecorator(name, {
              rules: [{ type: 'object', required: true, whitespace: true, message: msg }]
            })(<DatePicker data-testid="manual-invoice-date" style={{ width: '100%' }} />)}
          </FormItem>
        )
      }

      if (name === 'jym') {
        return (
          <FormItem
            key={name}
            colon={isNewCheckInvoice}
            {...formItemLayout}
            label={label}
            className="new-check-invoice-input"
          >
            {getFieldDecorator(name, {
              rules: [{ required: isRequired, whitespace: true, message: msg }],
              normalize: (value = '') => value.replace(/\s+/g, '')
            })(<Input data-testid="manual-invoice-jym" placeholder={placeholder} addonAfter={line.addonAfter && line.addonAfter()} />)}
          </FormItem>
        )
      }

      return (
        <FormItem
          key={name}
          colon={isNewCheckInvoice}
          {...formItemLayout}
          label={label}
          className="new-check-invoice-input"
        >
          {getFieldDecorator(name, {
            rules: [{ required: isRequired, whitespace: true, message: msg }]
          })(
            <Input
              data-testid={`manual-invoice-${name}`}
              placeholder={placeholder}
              addonAfter={line.addonAfter && line.addonAfter()}
              onBlur={() => {
                if (name === 'fpdm') {
                  this.fnGetContainerViewDataByType()
                }
              }}
            />
          )}
        </FormItem>
      )
    })
  }

  handleChangeType = type => {
    const { invoiceTabType } = this.state
    if (type === invoiceTabType) return
    this.setState({ invoiceTabType: type }, () => {
      this.props.form.resetFields()
      this.fnGetContainerViewDataByType()
    })
  }

  renderFooter = () => (
    <div className="modal-footer">
      <Button key="ok" category="primary" className="mr-10 btn" data-testid="manual-invoice-confirm" onClick={this.handleOK}>
        {i18n.get('确认')}
      </Button>
      <Button key="cancel" category="secondary" className="mr-8 btn" data-testid="manual-invoice-cancel" onClick={this.handleCancel}>
        {i18n.get('取消')}
      </Button>
    </div>
  )

  renderCheckBtn = () => (
    <div className="check-invoice-btn">
      <Button key="ok" category="primary" className="check-btn" onClick={this.handleOK} data-testid="manual-invoice-check">
        {i18n.get('立即校验')}
      </Button>
    </div>
  )
  render() {
    const { isNewCheckInvoice = false, unNeedTxt = true, unNeedTap = true } = this.props
    const { invoiceTabType } = this.state
    const wrapperClassName = !isNewCheckInvoice ? styles['new-check-invoice-wrapper'] : styles['import-input-wrapper']
    let tips = this.props.isChecked ? i18n.get('发票查验中，请稍等') + '...' : i18n.get('数据加载中') + '...'
    return (
      <div className={wrapperClassName}>
        <Spin spinning={this.state.isShowLoading} tip={tips}>
          <div className="input-content">
            {unNeedTap && (
              <div className="invoice-tab">
                <div>
                  <span
                    className={classnames({ 'invoice-active': invoiceTabType === 'normal' })}
                    onClick={e => this.handleChangeType('normal')}
                    data-testid="manual-invoice-vattab"
                  >
                    {i18n.get('增值税发票')}
                  </span>
                  <span
                    className={classnames({ 'invoice-active': invoiceTabType === 'token' })}
                    onClick={e => this.handleChangeType('token')}
                    data-testid="manual-invoice-tab-normaltab"
                  >
                    {i18n.get('区块链电子普通发票')}
                  </span>
                </div>
              </div>
            )}
            <div className="new-check-invoice-form">
              <Form hideRequiredMark={isNewCheckInvoice}>{this.renderFormItem()}</Form>
            </div>
            {isNewCheckInvoice && this.renderCheckBtn()}
            {invoiceTabType === 'normal' && unNeedTxt && (
              <div className="invoice-instruction">
                <div className="instruction-item">
                  <p>{i18n.get('1、可查验使用增值税发票管理新系统开具的发票，包括：')}</p>
                  <div className="ml-8">
                    <p>{i18n.get('（1）增值税（电子）专用发票')} </p>
                    <p>{i18n.get('（2）增值税（电子）普通发票')} </p>
                    <p>{i18n.get('（3）电子发票（增值税专用发票）')}</p>
                    <p>{i18n.get('（4）电子发票（普通发票）')}</p>
                    <p>{i18n.get('（5）增值税普通发票（卷式）')}</p>
                    <p>{i18n.get('（6）机动车销售统一发票')}</p>
                    <p>{i18n.get('（7）货物运输业增值税专用发票')}</p>
                    <p>{i18n.get('（8）二手车销售统一发票')}</p>
                    <p>{i18n.get('（9）通行费发票')}</p>
                  </div>
                  <p className="ml-16">{i18n.get('不在上述范围之内的发票，请按照原查验渠道进行查验。')}</p>
                </div>
                <div className="instruction-item">
                  <p>{i18n.get('2、可查验的时间范围：')}</p>
                  <div className="ml-8">
                    <p> {i18n.get('（1）可查验最近1年内增值税发票管理新系统开具的发票')}</p>
                    <p>{i18n.get('（2）当日开具的发票如开票方已将发票数据上传税局，则当日可查验否则最快次日查验')}</p>
                  </div>
                </div>
                <div className="instruction-item">
                  <p>{i18n.get('3、每天每张发票可在线查询次数为5次，超过次数后请于次日再进行查验操作。')}</p>
                </div>
              </div>
            )}
          </div>
          {!this.props.isNewCheckInvoice && this.renderFooter()}
        </Spin>
      </div>
    )
  }
}
