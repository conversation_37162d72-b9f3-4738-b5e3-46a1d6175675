@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token';

.list-items {
  padding: 24px 16px 12px;

  :global {
    .item-title {
      display: flex;
      justify-content: space-between;
      .ant-checkbox + span,
      .ant-checkbox-wrapper + span {
        padding: 0;
      }
      .item-title-wrapper {
        display: flex;
        .file-title {
          white-space: nowrap;
          font-size: 16px;
          line-height: 24px;
          font-weight: 600;
          color: #1d2b3d;
          vertical-align: middle;
        }
        .real-card {
          .font-size-1;
          height: @space-7;
          padding: @space-2 @space-4;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #f2faec;
          border-radius: 4px;
          color: #52c41a;
        }
        .no-real-card {
          .font-size-1;
          height: @space-7;
          padding: @space-2 @space-4;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #ecedee;
          border-radius: @space-2;
          color: rgb(20,34,52,0.45);;
        }
        .metaile-tag {
          .font-size-1;
          height: @space-7;
          line-height: @space-7;
          padding: 0 @space-4;
          border-radius: @space-2;
        }
        .warning-icon {
          color: #fa8c16;
          font-size: 18px;
          vertical-align: -0.3em;
        }
      }
    }

    .item-body {
      // display: flex;
      padding: 0 0 0 32px;
      .signature-tag {
        .font-size-1;
        height: @space-7;
        line-height: @space-7;
        padding: 0 @space-4;
        border-radius: @space-2;
      }
      .list-type-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .item-title {
          font-size: 16px;
          font-weight: 600;
          color: rgba(29, 43, 61, 1);
          line-height: 24px;
        }

        .list-type {
          line-height: 26px;
          padding: 1px 6px;
          font-size: 14px;
        }

        .item-subtitle {
          flex: 1;
          margin-left: 18px;
          font-size: 14px;
          line-height: 26px;
          font-weight: 400;
          color: rgba(29, 43, 61, 0.75);
        }
        .icon {
          width: 60px;
          height: 60px;
          color: var(--brand-base);
          margin-right: 48px;
        }
        .action-wrapper {
          margin-top: @space-6;
          display: flex;
          flex-direction: column;
          .action-wrapper-row {
            display: flex;
            flex-direction: row;
            margin-bottom: @space-2;
            height: 32px;
            .checker-invoice {
              color: var(--brand-base);
              font-size: 14px;
              cursor: pointer;
            }
            .list-edit {
              display: block;
              color: var(--brand-base);
              font-size: 14px;
              font-weight: 400;
              cursor: pointer;
              margin-left: 12px;
            }
          }
          .open {
            color: var(--brand-base);
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            margin-left: 12px;
            text-align: right;
          }
        }

        .list-orange {
          height: 26px;
          font-size: 12px;
          color: #D25F00;
          background: #FFF3DB;
        }
        .list-blue {
          height: 26px;
          font-size: 12px;
          color: #1890ff;
          background-color: rgba(24, 144, 255, 0.1);
        }
        .list-other {
          height: 26px;
          font-size: 12px;
          border-radius: 4px;
          color: rgba(29, 43, 61, 0.75);
          background-color: rgba(29, 43, 61, 0.06);
        }
        .list-green {
          height: 26px;
          font-size: 12px;
          color: #18b694;
          background-color: rgba(24, 182, 148, 0.1);
        }
        .list-violet {
          height: 26px;
          font-size: 12px;
          color: rgba(102, 90, 181, 1);
          background: rgba(97, 78, 216, 0.1);
        }
        .list-type {
          padding: 1px 6px;
          height: 24px;
          font-size: 12px;
          line-height: 22px;
          border-radius: 4px;
          flex-shrink: 0;
          margin-left: 4px;
        }
        .no-metafile {
          height: 26px;
          padding: 1px 6px;
          line-height: 26px;
          font-size: 12px;
          color: #f4526b;
          background-color: rgba(244, 82, 107, 0.1);
        }
      }
      .invoice-content-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        .ant-checkbox-wrapper {
          max-width: 600px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex;
          text-align: center;
          align-items: center;
          .ant-checkbox {
            margin-right: 4px;
          }
        }
      }
      .invoice-right {
        font-size: 14px;
      }
      .type-error {
        font-size: 14px;
        font-weight: 400;
        color: #f4526b;
      }
      .NO_VISIBLE {
        color: #fa8c16;
      }
      .NO_RESULT {
        color: #f4526b;
      }
    }

    .item-right {
      .list-money {
        font-size: 14px;
        float: right;
        color: #1d2b3d;
      }

      .list-desc {
        height: 30px;
      }

      .list-edit {
        display: block;
        cursor: pointer;
        // float: right;
        // position: absolute;
        // top: 57px;
        // left: 42px;
      }

      .list-money {
        display: inline-block;
      }

      .list-money-desc {
        float: right;
        font-size: 10px;
        color: rgba(29, 43, 61, 0.5);
      }
    }
  }
}

.list-items:hover {
  background: rgba(29, 43, 61, 0.06);
  box-shadow: 0px 1px 0px 0px rgba(29, 43, 61, 0.06);
  :global {
    // .list-edit {
    //   display: block;
    // }
    .item-body {
      .list-type-item {
        .action-wrapper {
          .action-wrapper-row {
            .list-edit {
              display: block;
            }
          }
        }
        .list-edit {
          display: inline-block;
        }
      }
    }
  }
}
