/*!
 * Copyright 2019 liyang. All rights reserved.
 * @since 2019-12-12 15:09:36
 */

import React, { PureComponent } from 'react'
import { Checkbox } from 'antd'
import { app as api } from '@ekuaibao/whispered'
import { Attachment, SourceData, ListType } from './../invoice/type'
import { fnConinvoiceTrack } from '../../../../../elements/invoice-form/utils/invoiceTrack'
import { thousandBitSeparator } from '../../../../../components/utils/fnThousandBitSeparator'
import { EnhanceConnect } from '@ekuaibao/store'
import {
  OutlinedGeneralFolder,
  OutlinedTipsClose,
  OutlinedOtherOverseasBills,
  FilledGeneralCamera,
  FilledEditEdit,
  TwoToneDataPdf,
  FilledLogoAlipay,
  OutlinedTipsAdd
} from '@hose/eui-icons'
import { Button, Dropdown, Menu } from '@hose/eui'
enum importType {
  ocr = 'ocr',
  pdf = 'pdf',
  input = 'input',
  alipay = 'alipay',
  aifapiao = 'aifapiao',
  formPoolSelect = 'formPoolSelect',
  overseasInvoice = 'overseasInvoice'
}
interface IProps {
  checkedList: any[]
  checkedAlls: any
  totalMoney: string
  disabled: boolean
  isDisible: boolean
  onEdit(item): void
  flowId: string
  bus: any
  isOcr: boolean
  item: any
  checked: any
  submitterId: any
  billSpecification: any
  visibilityFeeTypes: any
  handleAllCheeck(): void
  handleModalClose(): void
  importListType: ListType[]
  importListTypeBatch: ListType[]
  isRecordExpends?: boolean
  importMode?: {
    query: string
    ocr: string
    pdf: string
  }
  attachmentList: Array<Attachment<string>>
  resetOcrInvoiceList(list: Array<Attachment<string>>, isReset: boolean, importType: importType): void
  initArtifical(list: SourceData[], isRepeat: boolean, importType: importType): void
  source: 'addDetails' | 'batch' | 'editDetail' | 'import' | 'formPoolSelect'
  AliPayCardPower?: boolean
  OverseasInvoice?: boolean
  OCRPower?: boolean
  RECEIPT_DOCUMENT?: boolean
}
interface InvoiceList {
  invoiceList: SourceData[]
}
interface IState {}

enum InputType {
  PDF = 'pdf',
  ARTIFICAL = 'artifical',
  OCR = 'orc',
  Medical = 'medicalInvoiceOCR',
  AliPay = 'alipay',
  AiFaPiao = 'aifapiao',
  formPoolSelect = 'formPoolSelect',
  overseasInvoice = 'overseasInvoice'
}

const menuMap = {
  invoicePdf: {
    key: InputType.PDF,
    name: i18n.get('电子发票文件'),
    smallIcon: '#EDico-PDF',
    color: '#F78885',
    icon: <TwoToneDataPdf fontSize={16} color="#F53F3F" />,
    textColor: '#197CD9'
  },
  invoiceManual: {
    key: InputType.ARTIFICAL,
    name: i18n.get('手录发票'),
    smallIcon: '#EDico-edit',
    color: 'var(--brand-base)',
    icon: <FilledEditEdit fontSize={16} color="#3491FA" />,
    textColor: '#197CD9'
  },
  invoiceOCR: {
    key: InputType.OCR,
    name: i18n.get('智能识票'),
    color: '#FA962A',
    smallIcon: '#EDico-photo-ai',
    icon: <FilledGeneralCamera fontSize={16} color="#FF7D00" />,
    textColor: '#197CD9'
  },
  invoiceAlipay: {
    key: InputType.AliPay,
    name: i18n.get('支付宝发票'),
    color: '#1E96FA',
    icon: <FilledLogoAlipay fontSize={16} color="#3491FA" />,
    smallIcon: '#EDico-payzfb1',
    textColor: '#0b5fff'
  },
  medicalInvoiceOCR: {
    key: InputType.Medical,
    name: i18n.get('医疗发票'),
    color: '#00C874',
    smallIcon: '#EDico-yiliaofapiao',
    textColor: '#197CD9'
  },
  aifapiao: {
    key: InputType.AiFaPiao,
    name: i18n.get('爱发票'),
    smallIcon: '#EDico-aifapiao',
    color: '#0b5fff',
    textColor: '#197CD9'
  },
  formPoolSelect: {
    key: 'formPoolSelect',
    name: i18n.get('票池选择'),
    icon: <OutlinedGeneralFolder fontSize={16} color="#665ab5" />
  },
  overseasInvoice: {
    key: InputType.overseasInvoice,
    name: i18n.get('海外票据识别'),
    icon: <OutlinedOtherOverseasBills fontSize={16} color="#8445d0" />
  }
}

@EnhanceConnect(state => ({
  AliPayCardPower: state['@common'].powers.AliPayCard,
  OCRMedical: state['@common'].powers.OCRMedical,
  OCRPower: state['@common'].powers.OCR,
  OCRMetering: state['@common'].powers.OCRMetering,
  RECEIPT_DOCUMENT: state['@common'].powers.RECEIPT_DOCUMENT,
  OverseasInvoice: state['@common'].powers.OverseasInvoice
}))
export default class InvoiceCommonHeader extends PureComponent<IProps, IState> {
  count: number = 0

  fnGetImportMenu = (importData: any[] = []) => {
    let listData = []
    const firstItem: any = importData?.length ? importData[0] : {}
    if (firstItem?.list?.length) {
      listData = (importData as any)?.reduce((result, group) => {
        result = result.concat(group.list)
        return result
      }, [])
    } else {
      listData = importData
    }
    return listData
  }

  renderMenuList = () => {
    const {
      importListType,
      importListTypeBatch = [],
      importMode,
      source,
      isRecordExpends,
      AliPayCardPower,
      OverseasInvoice,
      OCRPower,
      OCRMetering,
      RECEIPT_DOCUMENT
    } = this.props
    let listData = null
    if (['importBill', 'waitBatch', 'addDetails', 'editDetail'].includes(source) || isRecordExpends) {
      listData = []
      if (importMode['ocr'] && (OCRPower || OCRMetering)) {
        listData.push({ id: 'invoiceOCR' })
      }
      if (importMode['upload']) {
        listData.push({ id: 'invoicePdf' })
      }
      if (importMode['query']) {
        listData.push({ id: 'invoiceManual' })
      }
      if (importMode['alipaycard'] && AliPayCardPower) {
        listData.push({ id: 'invoiceAlipay' })
      }
      if (importMode['aifapiao']) {
        listData.push({ id: 'aifapiao' })
      }
      if (importMode['formPoolSelect'] && RECEIPT_DOCUMENT) {
        listData.push({ id: 'formPoolSelect' })
      }
      if (importMode['overseasInvoice'] && OverseasInvoice) {
        listData.push({ id: 'overseasInvoice' })
      }
    } else if (source === 'batch') {
      listData = this.fnGetImportMenu(importListTypeBatch)
    } else {
      listData = this.fnGetImportMenu(importListType)
    }
    return (
      <Menu onClick={this.handleReset}>
        {listData.map((line: any) => {
          const importItem = menuMap[line.id]
          return (
            !!importItem && (
              <Menu.Item key={importItem.key} data-testid={`invoice-add-${importItem.key}`}>
                <div className="horizontal">
                  {importItem.icon ? (
                    importItem.icon
                  ) : (
                    <svg className="icon" aria-hidden="true">
                      <use xlinkHref={importItem.smallIcon} style={{ fill: importItem.color }} />
                    </svg>
                  )}
                  <div style={{ marginLeft: 8 }}>{importItem.name}</div>
                </div>
              </Menu.Item>
            )
          )
        })}
      </Menu>
    )
  }

  handleReset = ({ key }) => {
    const { submitterId } = this.props
    let invoiceWay = ''
    if (key === InputType.PDF) {
      this.fnResetInputPdf()
      invoiceWay = 'upload'
    } else if (key === InputType.ARTIFICAL) {
      this.fnResetInputArtifical()
      invoiceWay = 'query'
    } else if (key === InputType.OCR) {
      this.fnResetInputOcr()
      invoiceWay = 'ocr'
    } else if (key === InputType.Medical) {
      this.fnResetInputOcr(true)
      invoiceWay = 'ocr'
    } else if (key === InputType.AliPay) {
      this.fnResetAlipay()
      invoiceWay = 'alipay'
    } else if (key === InputType.AiFaPiao) {
      this.fnResetAiFaPiao()
      invoiceWay = 'aifapiao'
    } else if (key === InputType.formPoolSelect) {
      this.importFromPool()
      invoiceWay = 'formPoolSelect'
    } else if (key === InputType.overseasInvoice) {
      invoiceWay = InputType.overseasInvoice
      this.fnResetInputOversea()
    }
    // 埋点点击继续添加次数
    this.count = this.count + 1
    fnConinvoiceTrack(submitterId, this.count, invoiceWay)
  }

  fnResetAiFaPiao = async () => {
    const isAuth = await api.invokeService('@bills:check:aifapiao:auth', this.importAiFaPiaoH5Callback)
    if (!isAuth) return null
    this.importAiFaPiaoH5Callback()
  }

  importFromPool = async () => {
    const { submitterId, billSpecification, initArtifical } = this.props
    const items = await api.open('@bills:ImportInvoiceFromPool', {
      billSpecification,
      submitterId
    })
    initArtifical && initArtifical(items, true, importType.formPoolSelect)
  }

  importAiFaPiaoH5Callback = async () => {
    const url = await api.invokeService('@bills:get:aifapiao:public:url')
    const res = await api.open('@bills:IframeModal', {
      src: url,
      handleListener: this.handleImportAifapiao
    })
  }

  handleImportAifapiao = async data => {
    const { initArtifical, submitterId } = this.props
    const { ids } = data
    const { items } = await api.invokeService('@bills:import:invoice:from:aifapiao', { ids, staffId: submitterId?.id })
    initArtifical && initArtifical(items, true, importType.aifapiao)
  }

  fnResetInputPdf = () => {
    const { flowId, initArtifical } = this.props
    api
      .open('@bills:ImportInvoiceModal', { details: [], flowId, multiple: true, source: 'import' })
      .then((data: InvoiceList) => {
        const { invoiceList = [] } = data
        initArtifical && initArtifical(invoiceList, true, importType.pdf)
      })
  }

  fnResetAlipay = async () => {
    const { initArtifical } = this.props
    const isAuth = await api.invokeService('@bills:check:aliPayCard:auth')
    if (!isAuth) {
      return
    }
    api.open('@bills:AliPayInvoiceListModal').then((data: SourceData[]) => {
      initArtifical && initArtifical(data, true, importType.alipay)
    })
  }

  fnResetInputOcr = (isMedical = false) => {
    const { resetOcrInvoiceList } = this.props
    api.open('@bills:ImportUploadOCRModal', { isMedical }).then((attachmentList: Array<Attachment<string>>) => {
      resetOcrInvoiceList(attachmentList, true, importType.ocr)
      return true
    })
  }

  fnResetInputOversea = () => {
    const { resetOcrInvoiceList } = this.props
    api.open('@bills:ImportUploadOCRModal', { isOverseas: true }).then((attachmentList: Array<Attachment<string>>) => {
      resetOcrInvoiceList(attachmentList, true, importType.overseasInvoice)
      return true
    })
  }

  fnResetInputArtifical = () => {
    const { flowId, visibilityFeeTypes, submitterId, initArtifical } = this.props
    api
      .open('@bills:ImportInputInvoiceModal', {
        details: [],
        invoices: [],
        flowId: flowId,
        visibilityFeeTypes: visibilityFeeTypes,
        submitterId
      })
      .then((data: SourceData) => {
        initArtifical && initArtifical([data], true, importType.input)
      })
  }
  render() {
    const {
      checkedList = [],
      checkedAlls,
      totalMoney,
      handleAllCheeck,
      handleModalClose,
      isDisible,
      source
    } = this.props
    const total = checkedList.length
    const value = thousandBitSeparator(totalMoney)
    const cls = value < 0 ? `footer-sum money-red` : `footer-sum`
    return (
      <>
        <div className="modal-header-invoice">
          <div className="modal-header-title">
            <div className="flex">{i18n.get('发票列表')}</div>
            <OutlinedTipsClose className="cross-icon" onClick={handleModalClose} />
          </div>
          <div className="select_all">
            {source === 'import' && (
              <p>
                {i18n.get(
                  '以下是根据录入信息自动识别出的发票及其明细，你可在此勾选全部或部分发票，来生成多条或合并生成一条消费'
                )}
              </p>
            )}
            <div className="select_all_group">
              <div className="select_all_count">
                <Checkbox data-testid="invoice-select-all" checked={checkedAlls} onChange={handleAllCheeck} disabled={isDisible}>
                  <span className="ml-8 mr-12">{i18n.get('全选')}</span>
                  <span>
                    {i18n.get('已选 ')}
                    {total}
                    {i18n.get(' 张发票')}, {i18n.get('共')}
                    <span className={cls}>{window.CURRENCY_SYMBOL + value}</span>
                  </span>
                </Checkbox>
              </div>
              <div>
                <Dropdown overlay={this.renderMenuList()} placement="bottomRight">
                  <Button theme={'highlight'} category="text" icon={<OutlinedTipsAdd />}>
                    {i18n.get('继续添加')}
                  </Button>
                </Dropdown>
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }
}
