/**
 *  Created by <PERSON><PERSON> on 2018/7/26 下午2:18.
 */
import React, { PureComponent } from 'react'
import { Modal } from 'antd'
import { Button } from '@hose/eui'
import { get, cloneDeep, isObject, uniqBy } from 'lodash'
import styles from './ImportInvoiceDetailModal.module.less'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import MessageCenter from '@ekuaibao/messagecenter'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { Fetch } from '@ekuaibao/fetch'
import { totalAmountAddTaxAmount, resetOverseasMasterInvoice } from '../../../../../lib/InvoiceUtil'
import { EnhanceConnect } from '@ekuaibao/store'
import InvoiceCommonHeader from './../common-element/InvoiceModalHeader'
import { formatOCRResult } from './../ocr/ocr-list-util'
import ImportInvoiceGroup from './ImportInvoiceGroup'
import InvoiceLoading from './InvoiceLoading'
import Big from 'big.js'
import pLimit from 'p-limit'
import { fnCreatecostsTrack, fnCreatecostTrack } from '../../../../../elements/invoice-form/utils/invoiceTrack'
import InvoiceMappingValue from '../../../../../lib/InvoiceMappingValue'
import {
  fnGetValidList,
  checkListName,
  fnChangeInvoiceActive,
  fnFilterInvoiceListData,
  fnReInitInvoiceList,
  fnGetLastValidInvoiceList,
  fnAddTaxInfo,
  fnUniqObjInArray
} from './ImportInvoiceUtils'
import { updateInvoiceDeduction } from '../../../../../lib/InvoicePriceTaxSeparated'
import { isFunction } from '@ekuaibao/helpers'
import { validateFeeTypeOnlyCurrency } from '../../../../../lib/fee-util'
import { asyncOCRinvoice } from '../../../../../lib/featbit/feat-switch'

@EnhanceConnect(state => ({
  checkedItemIds: state['@bills'].checkedItemIds,
  baseDataProperties: state['@common'].globalFields.data,
  baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
  DIGITAL_ORIGINAL_FILE: state['@common'].powers.DIGITAL_ORIGINAL_FILE,
  importListType: state['@custom-specification'].importListType
}))
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class ImportInvoiceDetailModal extends PureComponent {
  constructor(props) {
    super(props)
    let { invoiceList, importMode = {}, notShowModalIfAllInvoiceSuccess = false } = this.props
    if (notShowModalIfAllInvoiceSuccess && isHongShanTestingEnterprise(Fetch.ekbCorpId)) {
      this.props.layer.override({ zIndex: -1000 })
    }
    this.invoiceMap = {}
    this.invoiceCopyMap = {}
    this.initDisabledMap = {}
    this.invoiceCount = 0
    this.countFlag = 0
    this.details = []
    this.invoiceInfo = {}
    this.bill = {}
    this.repetInvoiceCount = 0
    this.verifyInvoiceResult = []
    let originInvoiceList = cloneDeep(invoiceList)
    this.state = {
      invoiceList: invoiceList,
      checked: false,
      isMergeBtn: false,
      originInvoiceList,
      checkListMap: {},
      importMode,
      total: 0,
      allMoneyMap: {},
      isDisible: false,
      canClick: true
    }
    this.invoiceMappingValue = new InvoiceMappingValue()
  }

  get buttonTitleEnum() {
    let _enum = new Map()
    _enum.importBill = i18n.get('与该单据绑定')
    _enum.import = i18n.get('合并生成消费')
    _enum.addDetails = i18n.get('与该消费绑定')
    _enum.editDetail = i18n.get('与该消费绑定')
    _enum.batch = i18n.get('与该批次绑定')
    _enum.waitBatch = i18n.get('与该批次绑定')
    return _enum
  }

  componentDidMount() {
    const { isOcr } = this.props
    const { importMode } = this.state
    !isOcr && this.fnInitInvoiceList()
    !!isOcr && this.fnInitOcrInvoiceList()
    if (importMode?.setVisible) {
      api.invokeService('@bills:get:feetype:invoice:importMode', importMode).then(res => {
        this.setState({ importMode: res.items })
      })
    }
  }

  fnInitInvoiceList = async () => {
    const { importType } = this.props
    const { invoiceList } = this.state
    this.setState(
      {
        invoiceList: invoiceList.concat([])
      },
      async () => {
        let cInvoiceList = invoiceList.concat()
        const newInvoiceList = await this.fnFilterInvoiceList(cInvoiceList, true, importType)
        this.setState({ invoiceList: newInvoiceList, originInvoiceList: cloneDeep(newInvoiceList) }, () => {
          this.handleSkip()
        })
      }
    )
  }

  fnInitOcrInvoiceList = async () => {
    if(asyncOCRinvoice()){
      this.fnGetInitOcrInvoiceListV2([], false, this.props.importType);
    }else{
      const { importType } = this.props
      const { invoiceList } = this.state
      let invoiceListData = await this.fnGetInitOcrInvoiceList(invoiceList, false, importType)
      this.setState(
        {
          invoiceList: invoiceListData,
          originInvoiceList: cloneDeep(invoiceListData)
        },
        async () => {
          this.handleSkip()
        }
      )
    }
  }

  handleSkip = async () => {
    const { notShowModalIfAllInvoiceSuccess = false } = this.props
    if (notShowModalIfAllInvoiceSuccess && isHongShanTestingEnterprise(Fetch.ekbCorpId)) {
      const { invoiceList = [] } = this.state
      const isNotAllSuccess = invoiceList.some(invoiceItem => {
        const { dataSource = [] } = invoiceItem
        return (
          Array.isArray(dataSource) &&
          dataSource.some(dataSourceItem => dataSourceItem.status && dataSourceItem.status !== 'SUCCESS')
        )
      })
      // 如果全部验证通过，则自动走全选并确定流程,不显示弹窗
      if (isNotAllSuccess) {
        console.log('[ handleSkip skip isNotAllSuccess ] >', notShowModalIfAllInvoiceSuccess)
        this.props.layer.override({ zIndex: 1000 })
      } else {
        // 直接全选并返回
        console.log('[ handleSkip skip ] >', notShowModalIfAllInvoiceSuccess)
        await this.handleBatchCheckAll({ target: { checked: true } })
        this.handleBatchGenerateDetails({ isMerge: true, isTrack: true })
      }
    } else {
      console.log('[ handleSkip not skip ] >', notShowModalIfAllInvoiceSuccess)
      this.props.layer.override({ zIndex: 1000 })
    }
  }

  fnFilterBillInvoiceMap = (invoices = [], recordHalf = false) => {
    const billsInvoiceMap = {}
    if (invoices !== null && !!invoices?.length) {
      invoices.forEach(invoice => {
        const { master, details } = invoice
        const detailKeys = []
        if (!!details?.length) {
          details.forEach(oo => {
            detailKeys.push(oo.index)
          })
        }
        if (master?.id) {
          if (!!billsInvoiceMap[master.id]) {
            const { details: cDetails, detailKeys: dk } = billsInvoiceMap[master.id]
            billsInvoiceMap[master.id].details = cDetails.concat(details)
            billsInvoiceMap[master.id].detailKeys = dk.concat(detailKeys)
          } else {
            billsInvoiceMap[master.id] = invoice
            billsInvoiceMap[master.id].detailKeys = detailKeys
          }
        }
      })
    }
    return billsInvoiceMap
  }

  handleInvoiceDataOnly = (invoiceListData, isReset) => {
    const promise = api.has('get:bills:value') ? api.invoke('get:bills:value') : Promise.resolve({})
    return promise.then(result => {
      let billsInvoiceMap = {}
      const billsInvoices = get(result, 'values.invoiceForm.invoices', [])
      if (!!billsInvoices?.length) {
        billsInvoiceMap = this.fnFilterBillInvoiceMap(cloneDeep(billsInvoices), false)
      }
      const billsInvoiceList = get(result, 'values.details')
      billsInvoiceList &&
        billsInvoiceList.forEach(item => {
          const invoices = get(item, 'feeTypeForm.invoiceForm.invoices', [])
          const invoicesMap = this.fnFilterBillInvoiceMap(cloneDeep(invoices), false)
          billsInvoiceMap = { ...billsInvoiceMap, ...invoicesMap }
        })
      invoiceListData = fnFilterInvoiceListData(invoiceListData, billsInvoiceMap)
      return invoiceListData
    })
  }

  handleInvoiceDataOnlyByFeeDetails = invoiceListData => {
    let billsInvoiceMap = {}

    const { source, invoices = [] } = this.props
    if (source !== 'addDetails' && source !== 'batch') return invoiceListData
    billsInvoiceMap = this.fnFilterBillInvoiceMap(cloneDeep(invoices), true)
    invoiceListData = fnFilterInvoiceListData(invoiceListData, billsInvoiceMap)
    return invoiceListData
  }

  // 继续添加
  handleResetOcrInvoiceList = async (list, isReset = false, importType) => {
    const { invoiceList } = this.state
    this.setState(
      {
        invoiceList: list.concat(invoiceList)
      },
      async () => {
        if(asyncOCRinvoice()){
          this.fnGetInitOcrInvoiceListV2(list, isReset, importType);
          this.setState({
            checked: false // 重置全选状态
          })
        }else{
          const invoiceListData = await this.fnGetInitOcrInvoiceList(list, isReset, importType)
          let invoiceData = invoiceList.concat(invoiceListData)
          invoiceData = invoiceData.filter(item => !!item.dataSource.length)
          this.setState({
            invoiceList: invoiceData,
            originInvoiceList: cloneDeep(invoiceData),
            checked: false
          })
        }
      }
    )
  }

  handleFilterInvoicesRepeat = async invoiceListData => {
    const validRepeatInvoiceEntityIds = ['system_火车票', 'system_出租车票', 'system_机打发票', 'system_定额发票']
    const validRepeatTypes = ['INVOICE_TRAIN', 'INVOICE_TAXI', 'INVOICE_MACHINE_PRINT', 'INVOICE_QUOTA']
    const validInvoices = invoiceListData?.items?.filter(item =>
      validRepeatInvoiceEntityIds.includes(get(item, 'master.entityId'))
    )
    if (!validInvoices?.length) {
      return invoiceListData
    }
    if (this.invoiceRepeatRule === undefined) {
      const ruleList = await api.invokeService('@invoice-manage:get:invoice:rule:list')
      const validInvoiceRepeatRule = ruleList.items?.find(rule => rule.normType === 'INVOICE_REPEAT_VERIFICATION')
      const hasRepeatRule = validRepeatTypes.find(type => validInvoiceRepeatRule.invoiceType?.includes(type))
      if (hasRepeatRule) {
        this.invoiceRepeatRule = validInvoiceRepeatRule
      }
    }
    if (!this.invoiceRepeatRule) {
      return invoiceListData
    }

    const getTrainValue = el => {
      return (
        get(el, 'master.form.E_system_火车票_车次') +
        get(el, 'master.form.E_system_火车票_乘车人姓名') +
        get(el, 'master.form.E_system_火车票_乘车时间') +
        get(el, 'master.form.E_system_火车票_号码') +
        get(el, 'master.form.E_system_火车票_序列码')
      )
    }

    const getQuotaValue = (el, entityId) => {
      return get(el, `master.form.E_${entityId}_发票代码`) + get(el, `master.form.E_${entityId}_号码`)
    }

    const getBaseInfoValue = (el, entityId) => {
      return get(el, `master.form.E_${entityId}_发票代码`) + get(el, `master.form.E_${entityId}_发票号码`)
    }

    const switchRuleAndEntityIdMap = {}
    const hasTrainRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_TRAIN')
    const hasTaxiRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_TAXI')
    const hasMachineRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_MACHINE_PRINT')
    const hasQuotaRepeatRule = this.invoiceRepeatRule.invoiceType?.includes('INVOICE_QUOTA')

    if (hasTrainRepeatRule) {
      switchRuleAndEntityIdMap['system_火车票'] = {
        rule: 'INVOICE_TRAIN',
        entityId: 'system_火车票',
        compareValueFunction: getTrainValue
      }
    }
    if (hasTaxiRepeatRule) {
      switchRuleAndEntityIdMap['system_出租车票'] = {
        rule: 'INVOICE_TAXI',
        entityId: 'system_出租车票',
        compareValueFunction: getBaseInfoValue
      }
    }
    if (hasMachineRepeatRule) {
      switchRuleAndEntityIdMap['system_机打发票'] = {
        rule: 'INVOICE_MACHINE_PRINT',
        entityId: 'system_机打发票',
        compareValueFunction: getBaseInfoValue
      }
    }
    if (hasQuotaRepeatRule) {
      switchRuleAndEntityIdMap['system_定额发票'] = {
        rule: 'INVOICE_QUOTA',
        entityId: 'system_定额发票',
        compareValueFunction: getQuotaValue
      }
    }

    const promise = api.has('get:bills:value') ? api.invoke('get:bills:value') : Promise.resolve({})
    return promise.then(result => {
      let billInvoices = Object.values(this.invoiceCopyMap)
      const billsInvoiceList = get(result, 'values.details')

      if (billsInvoiceList) {
        billsInvoiceList.forEach(item => {
          const invoices = get(item, 'feeTypeForm.invoiceForm.invoices', [])
          const filterInvoices = []
          invoices.forEach(invoice => {
            if (!!switchRuleAndEntityIdMap[get(invoice, 'master.entityId')]) {
              filterInvoices.push(invoice)
            }
          })
          if (filterInvoices.length) {
            billInvoices = billInvoices.concat(filterInvoices)
          }
        })
      }
      invoiceListData = this.fnFilterInvoiceRepeat(invoiceListData, billInvoices, switchRuleAndEntityIdMap)
      return invoiceListData
    })
  }

  fnFilterInvoiceRepeat = (invoiceListData, billInvoices, switchRuleAndEntityIdMap) => {
    if (invoiceListData.items.length) {
      invoiceListData.items = invoiceListData.items
        .map(item => {
          const entityId = get(item, 'master.entityId')
          const rule = switchRuleAndEntityIdMap[entityId]
          if (!!rule && entityId === rule.entityId) {
            const compareValueFunction = rule.compareValueFunction
            const isRepeat = !!billInvoices.find(invoice => {
              return compareValueFunction(item, entityId) === compareValueFunction(invoice, entityId)
            })
            if (isRepeat) {
              this.repetInvoiceCount++
              return undefined
            }
            billInvoices.push(item)
            return item
          }
          return item
        })
        .filter(el => !!el)
    }
    return invoiceListData
  }
  fnGetInitOcrInvoiceList = async (invoiceList, isReset, importType) => {
    const { submitterId, isMedical = false, billSpecification, invoiceType } = this.props

    // 限制Promise.all中的OCR请求并发数为5，解决SMG环境识别46张票时遇到的发票识别报错问题(偶发)
    const limit = pLimit(5)

    const promiseArr = invoiceList.map(invoice => {
      return limit(async () => {
        const isOverseasInvoice = importType === 'overseasInvoice'
        const service = isOverseasInvoice ? '@bills:get:ocr:overseas' : '@bills:get:ocr:list'
        let rep = await api.invokeService(service, {
          fileName: invoice.key,
          fileUrl: invoice.url,
          hash: invoice.hash,
          isMedical: false,
          invoiceType: invoiceType,
          staffId: submitterId && submitterId.id,
          appId: billSpecification?.appId || Fetch.appId,
          specificationId: billSpecification?.originalId?.id ?? billSpecification?.originalId
        })
        rep = await this.handleFilterInvoicesRepeat(rep)
        rep = this.handleResetInvoiceOnly(rep)
        rep = isOverseasInvoice ? this.handleOverseasInvoice(rep) : rep
        let dataSource = formatOCRResult(rep)
        dataSource = dataSource.map(item => {
          this.invoiceMap[item.id] = item.sourceData
          this.invoiceCopyMap[item.id] = item.sourceData
          return item
        })
        dataSource = {
          dataSource: dataSource,
          bus: new MessageCenter(),
          attachment: invoice,
          id: invoice.id,
          importType: importType,
          isLoaded: true
        }
        return dataSource
      })
    })

    let invoiceListData = await Promise.all(promiseArr)
    invoiceListData = invoiceListData.filter(o => !!o)
    invoiceListData = await this.handleInvoiceDataOnly(invoiceListData, isReset)
    invoiceListData = this.handleInvoiceDataOnlyByFeeDetails(invoiceListData)
    if (this.repetInvoiceCount > 0) {
      showMessage.warning(i18n.get('已经过滤掉重复的票据'))
      this.repetInvoiceCount = 0
    }

    return invoiceListData
  }

  //  初次进来 & 继续添加发票识别
  fnGetInitOcrInvoiceListV2 = async (appendInvoiceList = [], isReset, importType) => {
    const { invoiceList } = this.state
    const { submitterId, isMedical = false, billSpecification, invoiceType } = this.props
    const limit = pLimit(5)
    let finishedCount = 0
    // 过滤掉已经加载完的发票
    invoiceList.filter(v=>!v?.isLoaded).forEach((invoice, idx) => {
      limit(async () => {
        finishedCount++
        const isOverseasInvoice = importType === 'overseasInvoice'
        const service = isOverseasInvoice ? '@bills:get:ocr:overseas' : '@bills:get:ocr:list'
        let rep = await api.invokeService(service, {
          fileName: invoice.key,
          fileUrl: invoice.url,
          hash: invoice.hash,
          isMedical: false,
          invoiceType: invoiceType,
          staffId: submitterId && submitterId.id,
          appId: billSpecification?.appId || Fetch.appId,
          specificationId: billSpecification?.originalId?.id ?? billSpecification?.originalId
        })
        rep = await this.handleFilterInvoicesRepeat(rep)
        rep = this.handleResetInvoiceOnly(rep)
        rep = isOverseasInvoice ? this.handleOverseasInvoice(rep) : rep
        let dataSource = formatOCRResult(rep)
        dataSource = dataSource.map(item => {
          this.invoiceMap[item.id] = item.sourceData
          this.invoiceCopyMap[item.id] = item.sourceData
          return item
        })
        let data = {
          dataSource: dataSource,
          bus: new MessageCenter(),
          attachment: invoice,
          id: invoice.id,
          importType: importType,
          isLoaded: true
        }
        let filtered = [data] 
        // 1. 依次调用 handleInvoiceDataOnly 和 handleInvoiceDataOnlyByFeeDetails
        filtered = await this.handleInvoiceDataOnly(filtered, isReset)
        filtered = this.handleInvoiceDataOnlyByFeeDetails(filtered)
        // id 重复的
        const newData = uniqBy(filtered.concat(this.state.invoiceList), 'id')
        if(finishedCount === invoiceList?.length || finishedCount === appendInvoiceList?.length){
          this.setState({
            invoiceList: newData,
            originInvoiceList: cloneDeep(newData)
          },()=>{
            this.handleSkip()
          })
        }else{
          // 4. setState 渲染
          this.setState({
            invoiceList: newData,
            originInvoiceList: cloneDeep(newData)
          })
        }
        // 5. 统计完成数，全部完成后再提示重复票据
        if (finishedCount === invoiceList.length && this.repetInvoiceCount > 0) {
          showMessage.warning(i18n.get('已经过滤掉重复的票据'))
          this.repetInvoiceCount = 0
        }
        
      })
    })
  }

  // 海外发票上传检查是否和当前本位币一致
  handleOverseasInvoice = invoiceListData => {
    let { items = [] } = invoiceListData || {}
    items?.forEach(invoice => {
      invoice = resetOverseasMasterInvoice(invoice)
    })
    return invoiceListData
  }

  // 重复发票累加计数
  handleResetInvoiceOnly = invoiceListData => {
    let { items = [] } = invoiceListData

    items = items.reduce((acc, item) => {
      const id = get(item, 'master.id', '')
      if (!this.invoiceCopyMap[id]) {
        this.invoiceCopyMap[id] = item // 一次性上传多发票场景重复过滤逻辑
        acc.push(item)
      } else {
        this.repetInvoiceCount++
      }
      return acc
    }, [])

    invoiceListData.items = items
    return invoiceListData
  }

  handleInitArtifical = async (list = [], isReset = true, importType) => {
    const { invoiceList, originInvoiceList } = this.state
    const cList = list.concat()
    let newList = await this.fnFilterInvoiceList(cList, isReset, importType)
    newList = newList.filter(item => !!item.dataSource.length)
    this.setState({
      invoiceList: invoiceList.concat(newList),
      originInvoiceList: cloneDeep(originInvoiceList.concat(newList)),
      checked: false
    })
  }

  fnFilterInvoiceList = (list = [], isReset, importType) => {
    let cInvoiceList = list.concat() // 拷贝
    const promise = api.has('get:bills:value') ? api.invoke('get:bills:value') : Promise.resolve({})
    return promise.then(async result => {
      const details = get(result, 'values.details', [])
      let detailsIds = []
      details.forEach(line => {
        const invoiceDetails = get(line, 'feeTypeForm.invoiceForm.invoices.details', []) || []
        const detailsId = invoiceDetails.map(line => line.id)
        detailsIds = detailsIds.concat(detailsId)
      })
      if (!!isReset) {
        cInvoiceList = this.handleResetInvoiceOnly({ items: cInvoiceList })
        cInvoiceList = cInvoiceList.items
      }
      cInvoiceList.forEach(line => {
        let id = line?.master?.id
        if (id) {
          this.invoiceMap[id] = line
          this.invoiceCopyMap[id] = line
        }
        line.details.forEach(item => {
          if (detailsIds.indexOf(item.id) >= 0) {
            item.active = false
          }
        })
      })
      cInvoiceList = formatOCRResult(cInvoiceList)
      cInvoiceList = cInvoiceList.map((item, index, data) => {
        return {
          bus: new MessageCenter(),
          dataSource: [data[index]],
          id: item.id,
          importType,
          isLoaded: true
        }
      })
      cInvoiceList = await this.handleInvoiceDataOnly(cInvoiceList, isReset)
      cInvoiceList = this.handleInvoiceDataOnlyByFeeDetails(cInvoiceList)
      if (this.repetInvoiceCount > 0) {
        showMessage.warning(i18n.get('已经过滤掉重复的票据'))
        this.repetInvoiceCount = 0
      }
      return cInvoiceList
    })
  }

  handelInvoiceListChange = (listItem, idx, index) => {
    const { invoiceList, originInvoiceList } = this.state
    invoiceList[idx].dataSource[index] = listItem
    originInvoiceList[idx].dataSource[index] = listItem
    if (isObject(listItem)) {
      this.invoiceMap[listItem.id] = listItem
      this.invoiceCopyMap[listItem.id] = listItem
    }
    // 当发票代码或发票号码刚开始不对，然后通过编辑修正以后，id会改变，所以不能通过下面的方法更新数据
    // const list = invoiceList.map(item => {
    //   const { dataSource } = item
    //   const data = dataSource.map(line => {
    //     if (line?.id === listItem?.id) {
    //       return listItem
    //     }
    //     return line
    //   })
    //   item.dataSource = data
    //   return item
    // })
    this.setState({
      invoiceList: [...invoiceList],
      originInvoiceList: [...originInvoiceList]
    })
  }

  getResult = () => {
    const { isSupplyInvoice, isBillInvoice } = this.props
    return isBillInvoice ? this.bill : isSupplyInvoice ? this.invoiceInfo : this.details
  }

  fnGenerateBill = async ({ invoiceData }) => {
    const {
      submitterId,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      attachmentList,
      invoices = []
    } = this.props
    const invoiceList = invoices.concat(invoiceData)
    // await updateInvoiceDeduction([feeType], invoiceList, isMerge)
    api.invokeService('@bills:get:calculaterfield', billSpecification.id, submitterId.id).then(async reps => {
      let autoCalFields = reps.items[0]
      const data = await this.invoiceMappingValue.invoice2Detail({
        autoCalFields,
        invoices: invoiceList,
        specification: billSpecification,
        submitter: submitterId,
        baseDataProperties,
        baseDataPropertiesMap,
        attachmentList
      })
      this.bill = data.feeTypeForm
      this.props.layer.emitOk()
    })
  }

  fnGenerateDetail = async ({ invoiceData, isBatch, isMerge }) => {
    //生成消费明细
    const {
      visibilityFeeTypes,
      submitterId,
      source,
      editDetailObject,
      isSupplyInvoice,
      billData,
      billSpecification,
      currentSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      attachmentList,
      invoices = [],
      bus
    } = this.props
    let invoicesCopy = invoiceData
    if (source === 'addDetails' && invoiceData.find(v => v.master.entityId === 'system_海外发票')) {
      invoicesCopy = [...invoiceData, ...invoices]
    }

    if (!validateFeeTypeOnlyCurrency({ invoiceForm: { invoices: invoicesCopy } }) && isMerge) {
      showMessage.error(i18n.get('一个费用明细中只能存在一种消费币种（原币或本位币），请检查发票信息'))
      return
    }
    if (source === 'import') {
      api
        .open('@bills:InvoiceFeetypeRecommend', {
          feeTypes: visibilityFeeTypes,
          invoiceData,
          isMerge,
          specificationVersionId: billSpecification.id,
          submitterId
        })
        .then(async resultFeeTypes => {
          await updateInvoiceDeduction(resultFeeTypes, invoiceData, isMerge)
          this.invoiceCount = resultFeeTypes.length
          resultFeeTypes.forEach((resultFeeType, index) => {
            const { specification } = resultFeeType
            api.invokeService('@bills:get:calculaterfield', specification.id, submitterId.id).then(reps => {
              let autoCalFields = reps.items[0]
              this.fnFormatDetail({
                feeType: resultFeeType,
                invoiceData: isMerge ? invoiceData : [invoiceData[index]],
                autoCalFields,
                isBatch,
                specification,
                isMerge
              })
            })
          })
        })
    } else if (isSupplyInvoice) {
      const invoice = Array.isArray(invoiceData) ? invoiceData : [invoiceData]
      this.invoiceInfo = invoice
      await this.invoiceMappingValue.originalFileDownload(invoice)
      this.props.layer.emitOk()
    } else {
      const { autoCalFields, specification, feeType, isWholeInvoiceDetail } = editDetailObject
      const invoiceList = cloneDeep(invoices.concat(invoiceData))
      await updateInvoiceDeduction([feeType], invoiceList, isMerge)
      let currentFeeTypeForm
      if (isFunction(bus?.getFieldsValue)) {
        currentFeeTypeForm = await bus.getFieldsValue()
      }
      const consume = await this.invoiceMappingValue.invoice2Detail({
        feeType,
        invoices: invoiceList,
        specification,
        autoCalFields,
        submitter: submitterId,
        billData,
        billSpecification,
        baseDataProperties,
        baseDataPropertiesMap,
        attachmentList,
        currentFeeTypeForm,
        isImportInvoice: true // importType === 'overseasInvoice'
      })
      // TODO: 发票智能拍照埋点
      const invoiceIds = invoiceList?.map(it => {
        return it?.master?.id
      })
      api?.logger?.info('在明细上添加发票', {
        specificationId: currentSpecification?.id,
        specificationName: currentSpecification?.name,
        flowId: billData?.flowId,
        code: billData?.code,
        sceneName: '发票',
        feeTypeId: feeType?.id,
        feeTypeName: feeType?.name,
        operateType: '添加',
        invoiceIds,
        resultForm: consume?.feeTypeForm
      })
      this.details.push(consume)
      this.props.layer.emitOk()
    }
  }

  fnFormatDetail = async ({ isBatch, feeType, invoiceData, autoCalFields, specification, isMerge }) => {
    //格式化明细
    const { visibilityFeeTypes, bus, invoices = [] } = this.props

    const data = await this.invoiceMappingValue.invoice2Detail({
      feeType,
      invoices: invoices.concat(invoiceData),
      specification,
      isBatch
    })
    const result = isMerge && isBatch ? this.fnMergeDetailsToOneExpensesRecord(data) : data
    invoiceData = Array.isArray(invoiceData) ? invoiceData : [invoiceData]
    // TODO: 发票导入埋点
    const { billData, billSpecification } = this.props
    const invoiceIds = invoiceData?.map(it => {
      return it?.master?.id
    })
    api?.logger?.info('在单据上导入发票', {
      specificationId: billSpecification?.id,
      specificationName: billSpecification?.name,
      flowId: billData?.flowId,
      code: billData?.code,
      sceneName: '发票',
      feeTypeId: feeType?.id,
      feeTypeName: feeType?.name,
      operateType: '导入',
      invoiceIds,
      resultForm: result?.feeTypeForm
    })
    if (isMerge && isBatch) {
      bus
        .invoke('element:details:line:click', {
          details: [],
          line: result,
          visibleFeeTypes: visibilityFeeTypes,
          isEdit: true
        })
        .then(data => {
          if (data.length) {
            this.fnHandleResult(data, invoiceData)
          }
        })
    } else {
      this.fnHandleResult(result, invoiceData)
    }
  }

  fnMergeDetailsToOneExpensesRecord = list => {
    const result = Array.isArray(list) ? list[0] : list
    const invoices = get(result, 'feeTypeForm.invoiceForm.invoices')
    if (!Array.isArray(invoices)) {
      result.feeTypeForm.invoiceForm.invoices = [invoices]
    }
    return result
  }

  fnHandleResult = (resultData, invoiceData) => {
    let { invoiceList, checkListMap } = this.state
    invoiceData.forEach(line => {
      let invoiceInfo = this.invoiceMap[line.master.id]
      if (invoiceInfo) {
        let { originalData, details } = line
        let { details: orgiDetails } = originalData
        if (!details.length) {
          delete this.invoiceMap[line.master.id]
          invoiceList = fnReInitInvoiceList(line, invoiceList)
        } else {
          let detailsIds = details.map(o => o.id)
          orgiDetails = orgiDetails.filter(oo => !~detailsIds.indexOf(oo.id))
          if (!!orgiDetails.length) {
            invoiceList = this.fnReUpdateInvoiceList(line)
            const res = orgiDetails.filter(oo => !!oo.active)
            if (!res.length) {
              delete this.invoiceMap[line.master.id]
              invoiceList = fnReInitInvoiceList(line, invoiceList)
            }
          } else {
            delete this.invoiceMap[line.master.id]
            invoiceList = fnReInitInvoiceList(line, invoiceList)
          }
        }
      }
    })
    this.countFlag++
    if (this.countFlag === this.invoiceCount) {
      showMessage.success(i18n.get('导入成功'))
    }
    this.details = this.details.concat(resultData)
    if (invoiceList.length && !!fnGetLastValidInvoiceList(invoiceList)) {
      this.setState({ invoiceList: [].concat(invoiceList), total: 0, allMoneyMap: {}, checkListMap: {} })
    } else {
      this.props.layer.emitOk()
    }
  }

  fnReUpdateInvoiceList = line => {
    const { invoiceList } = this.state
    const {
      master: { id },
      details
    } = line
    invoiceList.forEach(item => {
      const { dataSource, bus } = item
      dataSource.forEach(oo => {
        if (oo.id === id) {
          const data = fnChangeInvoiceActive(oo.sourceData.details, details)
          oo.sourceData.details = data
          bus.emit('invoice:list:update:disabled', data)
        }
      })
    })
    return invoiceList
  }

  handleModalClose = () => {
    const { invoiceList } = this.state
    if (invoiceList.length) {
      const { source } = this.props
      let tipText = source === 'batch' || source === 'waitBatch' ? 'confirm-give' : 'confirm-back'
      let allDetails = []
      invoiceList.forEach(line => {
        const dataSource = get(line, 'dataSource') || []
        dataSource.forEach(item => {
          allDetails = allDetails.concat(
            !!item.sourceData.details.length ? item.sourceData.details : item.sourceData.master
          )
        })
      })
      allDetails = allDetails.filter(oo => Number(totalAmountAddTaxAmount(oo)) > 0 && oo.active)
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get(tipText, { index: allDetails.length }),
        onOk: () => {
          //调用emitOK在编辑费用类型的时候有问题，会把整个弹窗都关掉
          source === 'import' ? this.props.layer.emitOk() : this.props.layer.emitCancel()
        }
      })
    } else {
      this.props.layer.emitOk()
    }
  }

  getVerifyParam = async invoiceData => {
    let invoiceIds = []
    let param = {}
    if (api.has('get:current:feeType:value')) {
      const res = await api.invoke('get:current:feeType:value')
      param.formSpecId = res?.formSpecId ?? ''
      param.feeTypeSpecId = res?.feeTypeSpecId ?? ''
    }

    invoiceData?.forEach(v => {
      if (v?.master?.id) {
        invoiceIds.push(v.master.id)
      }
      // 发票明细id
      let detailIds = []
      if (v?.details?.length) {
        detailIds = v.details.map(o => o?.id)
        invoiceIds = invoiceIds.concat(detailIds)
      }
    })
    param.invoiceIds = invoiceIds
    return [param]
  }

  formatVerifyResult = () => {
    const { invoiceList } = this.state
    const target = {}

    this.verifyInvoiceResult.forEach(i => {
      if (i?.invoiceId && i?.bindValidateMessage?.length) {
        target[i.invoiceId] = i.bindValidateMessage.join()
      }
    })

    return invoiceList?.map(v => {
      v.dataSource = v?.dataSource?.map(o => {
        if (target[o.id] && o.sourceData) {
          o.sourceData.bindValidateMessage = target[o.id]
        } else if (o?.sourceData?.bindValidateMessage) {
          o.sourceData.bindValidateMessage = ''
        }
        return o
      })
      return v
    })
  }

  handleDisplayVerifyResult = () => {
    const cData = this.formatVerifyResult()
    if (cData && cData.length) {
      this.setState({
        invoiceList: cData
      })
    }
  }

  handleVerifyInvoice = async invoiceData => {
    const params = await this.getVerifyParam(invoiceData)
    try {
      const res = await Fetch.POST('/api/v2/invoice/bindValidate', {}, { body: params })
      if (res?.items?.length) {
        this.verifyInvoiceResult = res.items
        return Modal.warning({
          title: i18n.get('禁止提交'),
          content: i18n.get('存在与所选费用类型不能关联的发票，请重新选择其他费用类型或上传其他发票再提交'),
          okText: i18n.get('我知道了'),
          onOk: () => {
            this.handleDisplayVerifyResult()
          }
        })
      }
    } catch (e) {
      console.log(e)
    }
  }

  handleBatchGenerateDetails = ({ isMerge, isTrack }) => {
    const { submitterId, isBillInvoice } = this.props
    const { invoiceList, allMoneyMap } = this.state
    const promiseList = invoiceList.filter(line => line?.bus?.invoke).map(line => {
      return line.bus.invoke('invoice:get:detail:value')
    })
    //埋点生成n条消费与合并生成消费
    !isTrack && isMerge
      ? fnCreatecostTrack(submitterId, Object.values(allMoneyMap).length)
      : fnCreatecostsTrack(submitterId)
    Promise.all(promiseList).then(list => {
      let res = list.filter(line => !!line)
      let data = []
      res.forEach(item => {
        item = fnAddTaxInfo(item)
        data = data.concat([...item])
      })

      const { show, title } = checkListName(data)
      if (show) {
        return showMessage.error(title)
      }

      if (!data.length) {
        return showMessage.warning(i18n.get('请选择要导入的发票明细'))
      }

      // 自定义发票校验逻辑
      if (isTrack) {
        // 与该消费绑定
        this.handleVerifyInvoice(data).then(res => {
          if (!res) {
            isBillInvoice
              ? this.fnGenerateBill({ invoiceData: data })
              : this.fnGenerateDetail({ invoiceData: data, isBatch: true, isMerge })
          }
        })
      } else {
        isBillInvoice
          ? this.fnGenerateBill({ invoiceData: data })
          : this.fnGenerateDetail({ invoiceData: data, isBatch: true, isMerge })
      }
    })
  }

  handleBatchCheckAll = async e => {
    let { checked } = e.target
    let { invoiceList, checkListMap, allMoneyMap } = this.state
    let total = 0
    invoiceList.forEach(async line => {
      const { bus } = line
      const dataSource = get(line, 'dataSource') || []
      if (checked) {
        dataSource.forEach(item => {
          const { ischeck, active } = item
          if (ischeck && active) {
            checkListMap[line.id] = line
          } else {
            delete checkListMap[line.id]
          }
        })
      }
      bus && bus.emit('invoice:group:checked:detail', e)
    })
    checkListMap = checked ? checkListMap : {}
    allMoneyMap = checked ? allMoneyMap : {}
    this.setState({ total, checkListMap, allMoneyMap })
  }

  fnAllCheckedChange = (e, checkedMap, moneyMap, ischeck) => {
    let { invoiceList, checkListMap, allMoneyMap } = this.state
    const { id } = checkedMap
    const moneyId = Object.keys(moneyMap)
    const moneyValue = Object.values(moneyMap)
    e ? (checkListMap[id] = checkedMap) : delete checkListMap[id]
    ischeck ? (allMoneyMap[moneyId[0]] = moneyValue[0]) : delete allMoneyMap[moneyId[0]]
    const money = Object.values(allMoneyMap)
    const total = money.reduce((cur, next) => {
      return new Big(cur).plus(next).toFixed(2)
    }, 0)
    const validList = fnGetValidList(invoiceList)
    const checked = Object.values(checkListMap).length === validList.length
    this.setState({ checked, checkListMap, allMoneyMap, total })
  }

  fnGetIsInvoiceDisabled = (id, activeItem) => {
    this.initDisabledMap[id] = activeItem
    let activeList = Object.values(this.initDisabledMap)
    activeList = activeList.filter(item => !!item.length)
    this.setState({
      isDisible: !activeList.length
    })
  }

  handleRetryCheckerInvoiceClick = async (parentIndex, index, item) => {
    const { invoiceList, canClick } = this.state
    if (!canClick) return
    const invoiceInfo = invoiceList
    const invoiceId = item.id
    await this.setState({ canClick: false })
    try {
      const result = await api.invokeService('@bills:retry:checker:invoice', invoiceId)
      invoiceInfo[parentIndex].dataSource[index].sourceData = result.value
      invoiceInfo[parentIndex].dataSource[index].ischeck = result.value?.ischeck
      invoiceInfo[parentIndex].dataSource[index].active = result.value?.master?.active
      this.setState({
        invoiceList: invoiceInfo,
        canClick: true
      })
    } catch (e) {
      this.setState({
        canClick: true
      })
    }
  }

  handleUploadInvoiceMetaile = (parentIndex, index, invoice) => {
    const { invoiceList } = this.state
    invoiceList[parentIndex].dataSource[index].sourceData = invoice
    invoiceList[parentIndex].dataSource[index].ischeck = invoice?.ischeck
    this.setState({ invoiceList: [...invoiceList] })
  }

  renderFooter = () => {
    const { allMoneyMap } = this.state
    const { source } = this.props
    const lenItem = Object.values(allMoneyMap).length
    return ['addDetails', 'batch', 'waitBatch', 'editDetail', 'importBill'].includes(source) ? (
      <Button
        category="secondary"
        theme="highlight"
        className="btn merge_create_fee"
        disabled={!lenItem}
        data-testid="invoice-bind-details"
        onClick={this.handleBatchGenerateDetails.bind(this, { isMerge: true, isTrack: true })}
      >
        {this.buttonTitleEnum[source]}
      </Button>
    ) : (
      <>
        <Button
          category="secondary"
          theme="highlight"
          disabled={!lenItem}
          className="mr-8 btn create_fee"
          onClick={this.handleBatchGenerateDetails}
          data-testid="invoice-generate-details"
        >
          {i18n.get(`生成{__k0}条消费`, { __k0: Object.values(allMoneyMap).length })}
        </Button>
        <Button
          category="secondary"
          theme="highlight"
          className="btn merge_create_fee"
          disabled={!lenItem}
          data-testid="invoice-bind-details"
          onClick={this.handleBatchGenerateDetails.bind(this, { isMerge: true })}
        >
          {this.buttonTitleEnum[source]}
        </Button>
      </>
    )
  }

  render() {
    let { invoiceList, index, checked, total, allMoneyMap, isDisible, importMode } = this.state
    let {
      submitterId,
      isOcr,
      attachmentList,
      bus,
      flowId,
      visibilityFeeTypes,
      importListType,
      importListTypeBatch,
      source,
      isRecordExpends,
      billSpecification
    } = this.props
    return (
      <div id={'ImportInvoiceDetailModal'} className={styles.invoice_details_list_wrapper}>
        <div className={'invoice-select-header'}>
          <InvoiceCommonHeader
            totalMoney={total}
            checkedAlls={checked}
            isOcr={isOcr}
            bus={bus}
            source={source}
            submitterId={submitterId}
            billSpecification={billSpecification}
            flowId={flowId}
            isDisible={isDisible}
            importMode={importMode}
            visibilityFeeTypes={visibilityFeeTypes}
            attachmentList={attachmentList}
            importListType={importListType}
            isRecordExpends={isRecordExpends}
            importListTypeBatch={importListTypeBatch}
            checkedList={Object.values(allMoneyMap)}
            handleModalClose={this.handleModalClose}
            handleAllCheeck={this.handleBatchCheckAll}
            resetOcrInvoiceList={this.handleResetOcrInvoiceList}
            initArtifical={this.handleInitArtifical}
          />
        </div>
        <div className="invoice-select-content">
          {index > 0 && (
            <div className="info">{i18n.get(`还有{__k0}条项目未生成消费记录，请继续选择`, { __k0: index })}</div>
          )}
          <div className="invoice-list-content">
            {invoiceList &&
              invoiceList.map((line, index) => {
                const { isLoaded = false } = line
                return isLoaded ? (
                  <ImportInvoiceGroup
                    attachment={line && line.attachment}
                    dataSource={line && fnUniqObjInArray(line.dataSource, 'id')}
                    key={line && line.id}
                    item={line}
                    isLoaded={isLoaded}
                    bus={line && line.bus}
                    hasActive={true}
                    submitterId={submitterId}
                    importType={line && line.importType}
                    checked={false} //第一层也就是总的是不是全选
                    parentIndex={index}
                    onDataChange={this.handelInvoiceListChange}
                    fnGetIsInvoiceDisabled={this.fnGetIsInvoiceDisabled}
                    fnAllCheckedChange={this.fnAllCheckedChange}
                    onRetryCheckerInvoiceClick={this.handleRetryCheckerInvoiceClick}
                    onUploadInvoiceMetaile={this.handleUploadInvoiceMetaile}
                  />
                ) : (
                  <InvoiceLoading
                    attachment={line && line.attachment}
                    dataSource={line && line.dataSource}
                    key={line && line.id}
                    importType={line && line.importType}
                  />
                )
              })}
          </div>
        </div>
        <div className="modal-footer">{this.renderFooter()}</div>
      </div>
    )
  }
}
