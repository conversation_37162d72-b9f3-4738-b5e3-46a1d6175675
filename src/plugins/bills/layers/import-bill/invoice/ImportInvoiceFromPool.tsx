import React, { useEffect, useState, useMemo } from 'react'
import styles from './ImportInvoiceFromPool.module.less'
import { Dynamic, IExtendBus } from '@ekuaibao/template'
import { app } from '@ekuaibao/whispered'
import { Form } from 'antd'
import MessageCenter from '@ekuaibao/messagecenter'
import { Button, Space } from '@hose/eui'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { getInvoiceFromPool } from '../../../bills.action'
import InvoiceMappingValue from '../../../../../lib/InvoiceMappingValue'
import { EnhanceConnect } from '@ekuaibao/store'
import { showMessage } from '@ekuaibao/show-util'
const { editable } = app.require<any>('@components/index.editable')
const { ConditionWrapper } = app.require<any>('@elements/conditionComponents')

interface Props extends ILayerProps {
  billSpecification: any
  submitterId: any
  invoiceList: any[]
  baseDataProperties: any[]
  baseDataPropertiesMap: any
}

const invoiceMappingValue = new InvoiceMappingValue()
const ImportInvoiceFromPool: React.FC<Props> = ({ billSpecification, layer }) => {
  const [bus, setBus] = useState<IExtendBus>()
  const [invoiceType, setInvoiceType] = useState('')
  const [values, setValues] = useState()
  useEffect(() => {
    const bus = new MessageCenter()
    setBus(bus as IExtendBus)
  }, [])

  const handleOK = async () => {
    const result = await bus.getValueWithValidate(0)
    try {
      const { value } = await getInvoiceFromPool({
        ...result,
        invoiceAmount: result.invoiceAmount.standard,
        appId: billSpecification?.appId,
        specificationId: billSpecification?.originalId?.id ?? billSpecification?.originalId
      })
      layer.emitOk([value])
    } catch (e) {
      showMessage.error(e.message ?? e.msg)
    }
  }

  const handleCancel = () => {
    layer.emitCancel()
  }

  const invoiceCodeIsOptional = useMemo(() => {
    return invoiceType === 'FULL_DIGITAl_NORMAL' || invoiceType === 'FULL_DIGITAl_SPECIAL'
  }, [invoiceType])

  const template = useMemo(() => {
    bus?.getValue?.().then((values: any) => {
      setValues({ ...values, invoiceType: invoiceType })
    })
    return getTemplate(invoiceCodeIsOptional)
  }, [invoiceCodeIsOptional])
  const create = (T) => {
    return Form.create({
      onValuesChange: (props: any, changed: Record<string, any>) => {
        if (changed.invoiceType) {
          setInvoiceType(changed.invoiceType)
        }
      }
    })(T)
  }

  return (
    <ConditionWrapper condition={!!bus}>
      <div className={styles['pool-select-header']}>
        <div className="title">{i18n.get('票池选择')}</div>
        <OutlinedTipsClose onClick={handleCancel} />
      </div>
      <Dynamic
        isSimple
        bus={bus}
        className={styles['pool-select-content-wrapper']}
        template={template}
        elements={editable}
        create={create}
        tags={{ invoiceType: invoiceTypeOptions }}
        value={values}
      />
      <div className={styles['pool-select-bottom']}>
        <Space>
          <Button className="button" size={'large'} category={'secondary'} onClick={handleCancel}>
            {i18n.get('取消')}
          </Button>
          <Button className="button" size={'large'} category={'primary'} onClick={handleOK}>
            {i18n.get('确定')}
          </Button>
        </Space>
      </div>
    </ConditionWrapper>
  )
}

const invoiceTypeOptions = [
  { label: i18n.get('增值税普通发票'), value: 'PAPER_NORMAL' },
  { label: i18n.get('增值税专用发票'), value: 'PAPER_SPECIAL' },
  { label: i18n.get('全电纸质发票（增值税专用发票）'), value: 'FULL_DIGITAl_PAPER' },
  { label: i18n.get('全电纸质发票（增值税普通发票）'), value: 'FULL_DIGITAl_PAPER_NORMAL' },
  { label: i18n.get('增值税电子普通发票'), value: 'DIGITAL_NORMAL' },
  { label: i18n.get('增值税电子专用发票'), value: 'DIGITAL_SPECIAL' },
  { label: i18n.get('电子发票（普通发票）'), value: 'FULL_DIGITAl_NORMAL' },
  { label: i18n.get('电子发票（增值税专用发票）'), value: 'FULL_DIGITAl_SPECIAL' },
  { label: i18n.get('电子发票（航空运输电子客票行程单）'), value: 'ELECTRONIC_AIRCRAFT_INVOICE' },
  { label: i18n.get('电子发票（铁路电子客票）'), value: 'ELECTRONIC_TRAIN_INVOICE' },
  { label: i18n.get('电子发票（机动车销售统一发票）'), value: 'ELECTRONIC_PAPER_CAR' }
]

const getTemplate = invoiceCodeIsOptional => [
  {
    name: 'invoiceType',
    label: i18n.get('发票类型'),
    maxLength: 20,
    type: 'select',
    optional: false,
    placeholder: i18n.get('请选择发票类型'),
    editable: true,
    visible: true
  },
  {
    name: 'invoiceCode',
    label: i18n.get('发票代码'),
    maxLength: 20,
    type: 'text',
    optional: invoiceCodeIsOptional,
    placeholder: i18n.get('请输入发票代码'),
    editable: true,
    visible: true
  },
  {
    name: 'invoiceNumber',
    label: i18n.get('发票号码'),
    maxLength: 20,
    type: 'text',
    optional: false,
    placeholder: i18n.get('请输入发票号码'),
    editable: true,
    visible: true
  },
  {
    name: 'invoiceDate',
    label: i18n.get('发票日期'),
    maxLength: 20,
    type: 'date',
    optional: false,
    placeholder: i18n.get('请输入发票日期 例如:20160817'),
    editable: true,
    visible: true
  },
  {
    name: 'invoiceAmount',
    label: i18n.get('发票金额'),
    maxLength: 20,
    type: 'money',
    optional: false,
    placeholder: i18n.get('请发票金额'),
    editable: true,
    visible: true
  }
]

export default EnhanceConnect(state => {
  return {
    baseDataProperties: state['@common'].globalFields.data,
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
  }
})(ImportInvoiceFromPool)
