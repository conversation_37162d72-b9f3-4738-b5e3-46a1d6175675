@import '~@ekuaibao/web-theme-variables/styles/default';

.import-invoice-wrapper {
  padding: 0 16px;
  :global {
    .modal-header {
      border: none;
    }
    .modal-footer {
      border-top: none;
      box-shadow: none;
      padding: 16px 0;
      height: auto;
    }
    .import-invoice-content {
      overflow: auto;
      .select-file {
        display: flex;
        flex-direction: column;
        // margin-bottom: 45px;
        margin-top: 16px;
        gap: 8px;

        .select-file-title {
          display: inline-block;
          flex-shrink: 0;
          color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
          font: var(--eui-font-body-r1);
          &:after{
            display: inline-block;
            margin-left: 2px;
            color: var(--eui-function-danger-500);
            font: var(--eui-font-body-b1);
            content: "*";
          }
        }
        .upload-invoice {
          display: flex;
          justify-content: center;
          align-items: center;
          // width: 167px;
          // height: 40px;
          border: 1px solid var(--eui-line-border-component, rgba(29, 33, 41, 0.20));
        }
        .IE-compatible {
          div > .ekb-files-uploader-wrapper .input-wrapper > input {
            font-size: 110px;
          }
        }
        .ekb-files-uploader-invoice {
          flex: 1;
          .upload-file-list {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 4px;
            .file-list-item {
              margin-top: 4px;
              overflow:hidden;
              max-height:500px;
              .file-name {
                max-width: 600px;
                text-overflow: ellipsis;
                white-space: nowrap;
                word-wrap: normal;
                overflow: hidden;
                font-size: 12px;
                margin-left: 8px;
              }
              .file-info {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 12px;
                padding-right: 12px;
                padding: 8px;
                border-radius: 6px;
                background: var(--eui-bg-body-overlay, #F7F8FA);
                div:first-child {
                  display: flex;
                  align-items: center;
                  flex: 1;
                }
                .eui-icon-OutlinedEditDeleteTrash {
                  color: var(--eui-icon-n2);
                  padding: 4px;
                  cursor: pointer;
                  &:hover{
                    border-radius: 6px;
                    background: var(--eui-fill-pressed, rgba(29, 33, 41, 0.10));
                    color: var(--eui-icon-n1);
                  }
                }
                .delete-icon {
                  width: 12px;
                  flex-shrink: 0;
                }
              }
              .text-color {
                color: var(--brand-base);
              }
              .error-color {
                color: var(--eui-function-danger-500);
              }
              .progress {
                color: var(--brand-base);
              }
            }
          }
          .ekb-files-input {
            font-size: 0; // fix IE10 光标闪烁
            width: 100% !important;
          }
        }
      }
    }
  }
}
