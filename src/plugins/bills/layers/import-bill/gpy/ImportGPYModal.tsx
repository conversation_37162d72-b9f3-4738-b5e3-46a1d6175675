/**
 *  Created by lff on 2019/10/29
 */
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import styles from './ImportGPYModal.module.less'
import { Button, Icon, message } from 'antd'
import gpyIcon from './gpy-icon.png'

const gpy = {
  gpyStream: undefined
}
const dataURLtoBlob = function (dataurl) {
  const arr = dataurl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}
const generateFileName = function (OriginalName) {
  const random = Math.floor(Math.random() * 1000) + 1
  const now = Date.now()
  // tslint:disable-next-line:one-variable-per-declaration
  let filename = OriginalName,
    suffix = ''
  const arr = OriginalName && OriginalName.split('.')
  if (arr.length > 1) {
    suffix = `.${arr[arr.length - 1]}`
  }
  filename = filename.replace(suffix, '')
  filename = filename.replace(/\s/g, '')
  return `${filename}-${now}-${random}${suffix}`
}
const blobToFile = function (theBlob, fileName) {
  theBlob.lastModifiedDate = new Date()
  theBlob.name = fileName
  return theBlob
}
@EnhanceModal({
  footer: [],
  className: 'custom-modal-layer'
})
export default class ImportGPYModal extends PureComponent<any, any> {
  constructor(props: any) {
    super(props)
    this.state = {
      imageList: [],
      devices: [],
      index: 1,
      constraints: {
        audio: false,
        video: {
          width: { min: 1024, ideal: 1280, max: 1920 },
          height: { min: 600, ideal: 720, max: 1080 },
          deviceId: ''
        }
      },
      captureImg: false,
      disabledBtn: false
    }
  }

  componentDidMount(): void {
    this.onLoad(this.changeDevices)
  }

  onLoad = (changeDevices) => {
    if (navigator.mediaDevices === undefined) {
      navigator.mediaDevices = {}
    }
    if (navigator.mediaDevices.getUserMedia === undefined) {
      navigator.mediaDevices.getUserMedia = function (constraints) {
        const getUserMedia = navigator.mediaDevices.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.getUserMedia
        if (!getUserMedia) {
          return Promise.reject(new Error('getUserMedia is not implemented in this browser'))
        }
        return new Promise(function (resolve, reject) {
          getUserMedia.call(navigator, constraints, resolve, reject)
        })
      }
    }
    window.URL = (window.URL || window.webkitURL || window.mozURL || window.msURL)
    if(!navigator.mediaDevices.ondevicechange){
      navigator.mediaDevices.ondevicechange = changeDevices
    }
    changeDevices()
  }

  changeDevices = () => {
    let { index } = this.state
    if (navigator.mediaDevices.enumerateDevices) {
      navigator.mediaDevices.enumerateDevices().then(devices => {
        devices = devices.filter(device => device.kind === 'videoinput')
        if (devices && devices.length == 1) {
          index = 0
        }
        this.setState({ devices, index }, function () {
          this.startCamera()
        })
      })
    } else {
      message.warning(i18n.get('当前浏览器暂不支持'))
    }
  }

  handleChangeCamera = () => {
    // tslint:disable-next-line:prefer-const
    let { devices, index } = this.state
    if (devices && devices.length > 0) {
      if (index === devices.length - 1) {
        index = 0
      } else {
        index++
      }
      this.setState({ index }, function () {
        this.startCamera()
      })
    }
  }
  handleModalClose = () => {
    const { imageList } = this.state
    this.props.layer.emitCancel()
    this.props.layer.emitOk(imageList)
    gpy?.gpyStream?.getTracks()?.[0]?.stop()
    document.querySelector('video').pause()
    document.querySelector('video').src = ''
  }
  blankClose = () => {
    this.props.layer.emitCancel()
  }

  startCamera = () => {
    const video = document.querySelector('video')
    const { constraints, devices, index } = this.state
    if (devices && devices.length > 0) {
      constraints.video.deviceId = { exact: devices[index]['deviceId'] }
    } else {
      delete constraints.video.deviceId
    }
    if (!!gpy.gpyStream) {
      const tracks = gpy.gpyStream.getTracks()
      tracks.forEach(function (track) {
        track.stop()
      })
    }
    if (devices.length === 0) {
      message.warning(i18n.get('未检测到设备'))
      this.setState({
        disabledBtn: true
      })
      this.blankClose()
      return
    }
    let _this = this
    video && navigator.mediaDevices.getUserMedia(constraints)
      .then(function (stream) {
        // 旧的浏览器可能没有srcObject
        gpy.gpyStream = stream
        if ('srcObject' in video) {
          video.srcObject = stream
        } else {
          video.src = window.URL.createObjectURL(stream)
        }
        video.onloadedmetadata = function (e) {
          video.play()
        }
      })
      .catch(function (err) {
        console.log(err.name + ': ' + err.message)
        _this.setState({
          disabledBtn: true
        })
        // 没有授权摄像头的，重新授权一下，浏览器的安全策略
        navigator.mediaDevices.getUserMedia({
          video: { width: 10, height: 10 }
        }).then(res => {
          window.location.reload()
        }).catch(err => {
          message.warning(i18n.get('当前浏览器暂不支持,{__k0}', { __k0: err.message }))
        })
      })
  }
  renderVideo = () => {
    return (<div className="gpy-content">
      <video width={600} height={400} className="video" />
      <canvas style={{ position: 'fixed' }} />
    </div>)
  }
  handleTakeCapture = () => {
    const { disabledBtn } = this.state
    const { resultList } = this.props

    if (!disabledBtn) {
      this.setState({
        captureImg: true
      }, function () {
        const canvas = document.querySelector('canvas')
        const video = document.querySelector('video')
        const ctx = canvas.getContext('2d')
        ctx.mozImageSmoothingEnabled = false
        ctx.webkitImageSmoothingEnabled = false
        ctx.msImageSmoothingEnabled = false
        ctx.imageSmoothingEnabled = false

        const ratio = 3
        let sw = video.width
        let w = sw * ratio
        let sh = video.height
        let h = sh * ratio
        canvas.setAttribute('width', w.toString())
        canvas.setAttribute('height', h.toString())

        ctx.drawImage(video, 0, 0, w, h)
        ctx.scale(ratio, ratio)
        const base64 = canvas.toDataURL('image/png', 1)
        const blob = dataURLtoBlob(base64)
        const file = blobToFile(blob, generateFileName('gpy.png'))
        const { imageList = [] } = this.state
        const photoLen = resultList.length
        const residue = 10 - photoLen
        const imgLen = imageList.length
        if (imgLen >= residue) {
          message.warning(i18n.get('最多可上传10张，超出请分批次上传'))
          this.setState({ disabledBtn: true })
        } else {
          imageList.push(file)
        }
        const _this = this
        setTimeout(function () {
          _this.setState({ imageList, captureImg: false })
        }, 500)
      })
      this.forceUpdate()
    }
  }

  render() {
    const { captureImg, disabledBtn } = this.state
    const tips = captureImg ? i18n.get('正在拍照...') : i18n.get('拍照')
    return (
      <div className={styles.import_upload_gpy_modal}>
        {this.renderVideo()}
        <div className="gpy-footer">
          <div className="item">
            <Button key="changeCamera" type="default" size="large" className="btn-gpy active"
              onClick={this.handleChangeCamera}>
              {i18n.get('切换摄像头')}
            </Button>
          </div>
          <div className="item">
            <div className={disabledBtn ? 'btn-gpy btn-big disabledBtn' : 'btn-gpy btn-big'}
              onClick={this.handleTakeCapture}>
              <div className="btn-title">{i18n.get(tips)}</div>
              <div className="btn-icon"><img src={gpyIcon} /></div>
            </div>
          </div>
          <div className="item">
            <Button key="cancel" type="default" size="large" className="btn-gpy"
              onClick={this.handleModalClose}>
              {i18n.get('关闭')}
            </Button>
          </div>
        </div>
      </div>
    )
  }
}
