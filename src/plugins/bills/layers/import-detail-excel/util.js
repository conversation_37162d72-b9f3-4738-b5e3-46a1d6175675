import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { getV } from '@ekuaibao/lib/lib/help'
import { showMessage } from '@ekuaibao/show-util'
import { getLinkNodeElement, triggerClick } from '@ekuaibao/sdk-bridge/sdk/utils'
import moment from 'moment'
export function getDownloadTemplate(type, flag = {}, HSBCandKA) {
  let isUpgrade = api.getState('@common.isUpgrade')?.deduct
  switch (type) {
    case 'detail':
      return handleGetDetailTemplate.bind(this, flag)
    case 'dataLink':
      return downloadDataLinkTemplate.bind(this, flag)
    case 'payee':
      return downloadPayeeTemplate.bind(this, HSBCandKA)
    case 'payer':
      return downloadPayerTemplate.bind(this, HSBCandKA)
    case 'dimension':
      return downloadDimensionTemplate.bind(this, flag)
    case 'budget':
      return downloadBudgetTemplate.bind(this, flag)
    case 'dimensionMap':
      return downloadDimensionMapTemplate.bind(this, flag)
    case 'supplierFile':
      return downloadSupplierFileTemplate.bind(this, flag)
    case 'reconciliation':
      return downloadReconciliationTemplate.bind(this, flag)
    case 'editPermission':
      return downloadEditPermissionTemplate.bind(this)
    case 'apportion':
      return downloadApportionTemplate.bind(this, flag)
    case 'role':
      return downloadRoleTemplate.bind(this, flag)
    case 'staff':
      return downloadStaffTemplate.bind(this, flag)
    case 'cityGroup':
      return downloadCityGroupTemplate.bind(this, flag)
    case 'planPay':
      return downloadPlanPayTemplate.bind(this)
    case 'budgetCustomAdd':
      return downloadBudgetCustomAddTemplate.bind(this, flag)
    case 'corppayment':
      return flag?.type === 'amortizePlan' && isUpgrade
        ? downloadDeductAddTemplate.bind(this, flag)
        : downloadCorppaymentAddTemplate.bind(this, flag)
    case 'auditPaid':
      return downloadAuditPaidTemplate.bind(this)
    case 'approveMatrix':
      return downloadApproveMatrixTemplate.bind(this)
    case 'paymentSetting':
      return downloadPaymentSettingTemplate.bind(this)
    case 'datalink4flow':
      return downloadDatalink4flow.bind(this, flag)
    case 'receipt':
      return downloadReceiptTemplate.bind(this)
  }
}

export function getUploadUrl(type, flag = {}, autoFix) {
  let url = ''
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const accessToken = encodeURIComponent(Fetch.accessToken)
  const plateForm = window.__PLANTFORM__
  const { submitterId } = flag || {}
  const submitterIdString = typeof submitterId === 'object' ? submitterId.id : submitterId
  const subId = encodeURIComponent(submitterIdString)
  switch (type) {
    case 'dataLink':
      const { id, isRemuneration, batchId } = flag
      const entityId = encodeURIComponent(id)
      const url1 = `/api/v1/datalink/excel/import/remuneration/$${batchId}/$${entityId}?corpId=${ekbCorpId}`
      const url2 = `/api/v1/datalink/excel/import/$${entityId}?autoFix=${autoFix}&corpId=${ekbCorpId}`
      url = isRemuneration ? url1 : url2
      break
    case 'supplierFile':
      const supplierId = encodeURIComponent(flag.id)
      url = `/api/v1/supplier/supplierArchives/excel/import?autoFix=${autoFix}&corpId=${ekbCorpId}`
      break
    case 'reconciliation':
      url = `/api/v1/supplier/supplierDetails/excel/import/$${encodeURIComponent(flag.id)}/$${encodeURIComponent(
        flag.dataLinkId
      )}?autoFix=${false}&corpId=${ekbCorpId}`
      break
    case 'expenseStandard':
      url = `/api/costcontrol/v2/excel/import?corpId=${ekbCorpId}`
      break
    case 'detail':
      const { multiplePayeesMode = false, payPlanMode = false, dimention, isCancelLimit = false } = flag
      const legalEntityId = encodeURIComponent(dimention ? dimention.id : '')
      url = `/api/flow/v2/flows/export/details/excel/check?corpId=${ekbCorpId}&accessToken=${accessToken}&submitterId=${subId}&multiplePayeesMode=${multiplePayeesMode}&payPlanMode=${payPlanMode}&legalEntityId=${legalEntityId}&isCancelLimit=${isCancelLimit}`
      break
    case 'payee':
      url = `/api/pay/v2/accounts/import/excel?corpId=${ekbCorpId}&accessToken=${accessToken}&asPayee=true&asPayer=false`
      break
    case 'payer':
      url = `/api/pay/v2/accounts/import/excel?corpId=${ekbCorpId}&accessToken=${accessToken}&asPayee=false&asPayer=true`
      break
    case 'dimension':
      const { dimensionId, orgId } = flag
      const enDid = encodeURIComponent(dimensionId)
      url = `/api/v1/basedata/dimensionItems/byDimensionId/$${enDid}/excel?corpId=${ekbCorpId}&orgId=${orgId}`
      break
    case 'budget':
      url = `/api/v1/budget/info/upload/excel?corpId=${ekbCorpId}`
      if (plateForm === 'KD_CLOUD') {
        url = `${url}&&accessToken=${accessToken}`
      }
      break
    case 'dimensionMap':
      const { roleDefId } = flag
      url = `/api/v1/record/excel/import/$${roleDefId}/async?corpId=${ekbCorpId}`
      break
    case 'editPermission':
      url = `/api/pay/v2/chanpay/account/visibility/import/excel?corpId=${ekbCorpId}`
      break
    case 'apportion':
      let { apportionConfigId, rule, isBatch = false, currency } = flag
      apportionConfigId = encodeURIComponent(apportionConfigId)
      const baseCurrencyId = encodeURIComponent(currency ? currency.id : '')
      url = `/api/form/v2/apportion/excel/import?corpId=${ekbCorpId}&apportionConfigId=${apportionConfigId}&rule=${rule}&isBatch=${isBatch}&baseCurrencyId=${baseCurrencyId}&submitterId=${subId}`
      break
    case 'role':
      const { roleId } = flag
      url = `/api/v1/organization/roles/excel/import/$${roleId}?corpId=${ekbCorpId}`
      break
    case 'staff':
      const { powerCode } = flag
      url = `/api/glue/staffs/excel/import?corpId=${ekbCorpId}&powerCode=${powerCode}`
      break
    case 'cityGroup':
      url = `/api/v1/basedata/cityGroup/excel/import?corpId=${ekbCorpId}` + (flag?.id ? `&cityGroupId=${flag.id}` : '')
      break
    case 'planPay':
      const { templateid } = flag
      url = `/api/pay/v2/plan/import?corpId=${ekbCorpId}&accessToken=${accessToken}&templateid=${templateid}`
      break
    case 'supplier_import_excel':
      const { checkingBillId } = flag
      url = `/api/checking/v3/detail/excel/import/$${checkingBillId}?corpId=${ekbCorpId}&accessToken=${accessToken}`
      break
    case 'supplier_bill_import_excel':
      const { supplierAccountId } = flag
      url = `/api/checking/v3/category/excel/import/$${supplierAccountId}?corpId=${ekbCorpId}&accessToken=${accessToken}`
      break
    case 'budgetCustomAdd':
      url = `/api/v1/budget/info/custom/dimension/excel/upload?corpId=${ekbCorpId}`
      if (plateForm === 'KD_CLOUD') {
        url = `${url}&&accessToken=${accessToken}`
      }
      break
    case 'corppayment':
      const { dataLinkId, type } = flag
      let isUpgrade = api.getState('@common.isUpgrade')?.deduct
      if (type === 'amortizePlan' && isUpgrade) {
        url = `/api/supplier/v3/corppayment/excel/importDeductData?corpId=${ekbCorpId}`
      } else {
        url = `/api/supplier/v3/corppayment/excel/import?corpId=${ekbCorpId}`
      }
      break
    case 'auditPaid':
      url = `/api/pay/v2/paymentBatch/import/excel?corpId=${ekbCorpId}&accessToken=${accessToken}`
      break
    case 'approveMatrix':
      url = `/api/v2/matrix/excel/import?corpId=${ekbCorpId}&accessToken=${accessToken}`
      break
    case 'paymentSetting':
      url = `/api/pay/v2/paymentManagement/setting/import/excel?corpId=${ekbCorpId}&accessToken=${accessToken}`
      break
    case 'datalink4flow':
      url = `/api/v1/datalink/excel/import/datalink4flow/$${flag.id}?corpId=${ekbCorpId}&accessToken=${accessToken}`
      break
    case 'receipt':
      url = `/api/pay/v2/receipt/excel/import?corpId=${ekbCorpId}&accessToken=${accessToken}`
      break
    default:
      url = ''
  }

  return `${Fetch.fixOrigin(location.origin)}` + url
}

function getOnlyChild(tree) {
  const loop = (list, result = []) => {
    list.forEach(node => {
      if (node.children && node.children.length) {
        loop(node.children, result)
      } else {
        result.push(node)
      }
    })
    return result
  }
  const leafList = loop(tree)
  if (leafList.length === 1) {
    return leafList[0]
  }
  return false
}
function handleGetDetailTemplate(flag) {
  const { billSpecification, visibleFeeTypes, multiplePayeesMode, payPlanMode = false, dimention } = flag
  let onlyChild = getOnlyChild(visibleFeeTypes)
  const staffId = api.getState()['@common'].userinfo?.data?.staff?.userId
  const fnExport = () => {
    window?.TRACK?.('importcost_confirm_click', {
      actionName: i18n.get('确定并下载'),
      corpId: Fetch.ekbCorpId,
      staffId
    })
  }
  window?.TRACK?.('importcost_downloadtemplate_click', {
    actionName: i18n.get('费用明细导入excel时下载excel模版'),
    corpId: Fetch.ekbCorpId,
    staffId
  })
  this.props.stackerManager.push('ExportStepView', {
    billSpecification,
    visibleFeeTypes,
    fnExport,
    multiplePayeesMode,
    payPlanMode,
    onlyChild,
    dimention
  })
}

function downloadDataLinkTemplate(flag) {
  const { id } = flag
  const entityId = encodeURIComponent(id)
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const accessToken = encodeURIComponent(Fetch.accessToken)
  const url = `${Fetch.fixOrigin(
    location.origin
  )}/api/v1/datalink/excel/export/template/$${entityId}?corpId=${ekbCorpId}&accessToken=${accessToken}`
  api.emit('@vendor:download', url)
}

function downloadSupplierFileTemplate(flag) {
  const { id } = flag
  const entityId = encodeURIComponent(id)
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const accessToken = encodeURIComponent(Fetch.accessToken)
  const url = `${Fetch.fixOrigin(
    location.origin
  )}/api/v1/supplier/supplierArchives/excel/export/template?corpId=${ekbCorpId}&accessToken=${accessToken}`
  api.emit('@vendor:download', url)
}

function downloadReconciliationTemplate(flag) {
  const { id } = flag
  const entityId = encodeURIComponent(id)
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const accessToken = encodeURIComponent(Fetch.accessToken)
  const url = `${Fetch.fixOrigin(
    location.origin
  )}/api/v1/datalink/excel/export/template/$${entityId}?corpId=${ekbCorpId}&accessToken=${accessToken}`
  api.emit('@vendor:download', url)
}

function downloadPayeeTemplate() {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(
    location.origin
  )}/api/pay/v2/accounts/export/excel/getTemplate?corpId=${ekbCorpId}&asPayee=true&asPayer=false`
  api.emit('@vendor:download', url)
}
function downloadPayerTemplate() {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(
    location.origin
  )}/api/pay/v2/accounts/export/excel/getTemplate?corpId=${ekbCorpId}&asPayee=false&asPayer=true`
  api.emit('@vendor:download', url)
}
async function downloadPlanPayTemplate() {
  const result = await api.invokeService('@bills:export:planpay:details')
  const url = getV(result, 'url', '')
  const fileName = getV(result, 'fileName', '')
  api.emit('@vendor:download', url, fileName)
}
function downloadBudgetTemplate(flag) {
  const { budgetId } = flag
  Fetch.GET(`/api/v1/budget/info/$${budgetId}/exportWay`).then(v => {
    if (v.value.exportWay === 'async') {
      api.open('@layout:AsyncExportModal').then(v => {
        Fetch.GET(`/api/v1/budget/info/$${budgetId}/excel/template/async`, {
          taskName: v.taskName
        })
      })
    } else {
      const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
      let exportUrl = `${Fetch.fixOrigin(
        location.origin
      )}/api/v1/budget/info/$${budgetId}/excel/template?corpId=${ekbCorpId}`
      if (window.__PLANTFORM__ === 'KD_CLOUD') {
        exportUrl = `${exportUrl}&&accessToken=${encodeURIComponent(Fetch.accessToken)}`
      }
      api.emit('@vendor:download', exportUrl)
    }
  })
}

function downloadBudgetCustomAddTemplate(flag) {
  const { budgetId, ids } = flag
  const idList = !!ids?.length ? ids : 'default'
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  let exportUrl = `${Fetch.fixOrigin(
    location.origin
  )}/api/v1/budget/info/custom/dimension/$${budgetId}/excel/template/$${idList}?corpId=${ekbCorpId}`
  if (window.__PLANTFORM__ === 'KD_CLOUD') {
    exportUrl = `${exportUrl}&&accessToken=${encodeURIComponent(Fetch.accessToken)}`
  }
  api.emit('@vendor:download', exportUrl)
}

//创建摊销计划
function downloadDeductAddTemplate(flag) {
  const { type } = flag
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  let exportUrl = `${Fetch.fixOrigin(
    location.origin
  )}/api/supplier/v3/corppayment/excel/downloadDeductTemplate?corpId=${ekbCorpId}`
  if (window.__PLANTFORM__ === 'KD_CLOUD') {
    exportUrl = `${exportUrl}&&accessToken=${encodeURIComponent(Fetch.accessToken)}`
  }
  api.emit('@vendor:download', exportUrl)
}

function downloadCorppaymentAddTemplate(flag) {
  const { type } = flag
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  let exportUrl = `${Fetch.fixOrigin(
    location.origin
  )}/api/supplier/v3/corppayment/excel/download/$${type}/template?corpId=${ekbCorpId}`
  if (window.__PLANTFORM__ === 'KD_CLOUD') {
    exportUrl = `${exportUrl}&&accessToken=${encodeURIComponent(Fetch.accessToken)}`
  }
  api.emit('@vendor:download', exportUrl)
}

function downloadDimensionTemplate(flag) {
  // const url =
  //   Fetch.fixOrigin(window.location.origin) +
  //   (window.__PLANTFORM__ === 'DEBUGGER' ? i18n.get('/导入档案模板.xlsx') : i18n.get('/web/导入档案模板.xlsx'))
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(window.location.origin)}/api/v1/basedata/dimensionItems/byDimensionId/$${
    flag.dimensionId
  }/excel/template?corpId=${ekbCorpId}&orgId=${flag.orgId}`
  // const url = `${Fetch.fixOrigin(window.location.origin)}/api/v1/record/excel/export/template/$${
  //   flag.roleDefId
  // }?corpId=${ekbCorpId}`
  api.emit('@vendor:download', url)
}

function downloadDimensionMapTemplate(flag) {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(location.origin)}/api/v1/record/excel/export/template/$${
    flag.roleDefId
  }?corpId=${ekbCorpId}`
  api.emit('@vendor:download', url)
}

function downloadRoleTemplate(flag) {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(location.origin)}/api/v1/organization/roles/excel/export/template/$${
    flag.roleId
  }?corpId=${ekbCorpId}`
  api.emit('@vendor:download', url)
}
function downloadStaffTemplate(flag) {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(location.origin)}/api/glue/staffs/excel/export?corpId=${ekbCorpId}&powerCode=${
    flag.powerCode
  }`
  api.emit('@vendor:download', url)
}

function downloadReceiptTemplate() {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(location.origin)}/api/pay/v2/receipt/excel/export/template?corpId=${ekbCorpId}`
  api.emit('@vendor:download', url)
}
function downloadPaymentSettingTemplate() {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  const url = `${Fetch.fixOrigin(
    location.origin
  )}/api/pay/v2/paymentManagement/setting/import/excel/getTemplate?corpId=${ekbCorpId}`
  api.emit('@vendor:download', url)
}
function downloadEditPermissionTemplate() {
  const url =
    Fetch.fixOrigin(window.location.origin) +
    (window.__PLANTFORM__ === 'DEBUGGER'
      ? i18n.get('/银企联流水权限批量设置模板.xlsx')
      : i18n.get('/web/银企联流水权限批量设置模板.xlsx'))
  api.emit('@vendor:download', url)
}

function downloadAuditPaidTemplate() {
  const url =
    Fetch.fixOrigin(window.location.origin) +
    (window.__PLANTFORM__ === 'DEBUGGER' ? i18n.get('/支付结果校验.xlsx') : i18n.get('/web/支付结果校验.xlsx'))
  api.emit('@vendor:download', url)
}

async function downloadApportionTemplate(flag) {
  const { apportionConfigId, rule, isBatch = false } = flag
  const result = await api.invokeService('@bills:export:apportion:details', {
    apportionConfigId,
    rule,
    data: [],
    isBatch
  })
  const url = getV(result, 'url', '')
  const fileName = getV(result, 'fileName', '')
  api.emit('@vendor:download', url, fileName)
}

async function downloadCityGroupTemplate(flag) {
  const ekbCorpId = encodeURIComponent(Fetch.ekbCorpId)
  let url = '/api/v1/basedata/cityGroup/excel'
  if (flag?.id) {
    const cityGroupId = flag.id
    const { value } = await Fetch.GET(`${url}/exportWay`, { cityGroupId, corpId: ekbCorpId })
    if (value?.exportWay === 'async') {
      const { value: res } = await Fetch.GET(`${url}/export/async`, { cityGroupId, corpId: ekbCorpId })
      if (res?.code === 200) {
        showMessage.success(i18n.get('数据导出中，稍后请在「个人信息」-「导出管理」中下载'))
      } else {
        showMessage.error(res?.message || i18n.get('导出失败'))
      }
      return
    } else {
      url = `${url}/export/sync`
    }
  } else {
    url = `${url}/export/template`
  }
  url = `${Fetch.fixOrigin(location.origin)}${url}?corpId=${ekbCorpId}` + (flag.id ? `&cityGroupId=${flag.id}` : '')
  api.emit('@vendor:download', url)
}

async function downloadApproveMatrixTemplate() {
  const { getCurrentConfigtemp } = this.props
  return getCurrentConfigtemp()
}

async function downloadDatalink4flow(flag) {
  Fetch.POST(
    `/api/v1/datalink/excel/export/datalink4flow/$${flag.id}`,
    { corpId: encodeURIComponent(Fetch.ekbCorpId) },
    { body: { items: [] }, headers: { accept: '*' }, isBlob: true }
  ).then(blob => {
    const el = getLinkNodeElement()
    const url = window.URL.createObjectURL(blob)
    const filename = `${flag.fieldName}导出模版_${moment().format('YYYY-MM-DD HH:mm:ss')}.xlsx`
    const name = decodeURIComponent(filename)
    el.setAttribute('href', url)
    el.setAttribute('download', name)
    triggerClick(el)
  })
}
