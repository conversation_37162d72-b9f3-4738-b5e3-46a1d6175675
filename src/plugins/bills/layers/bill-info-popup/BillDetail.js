/**************************************************
 * Created by kaili on 2017/8/8 下午12:38.
 **************************************************/

import React, { PureComponent } from 'react'
import { app as api, app } from '@ekuaibao/whispered'
import BillInfoReadOnlyContainer from '../../parts/right-part/billInfo/BillInfoReadOnlyContainer'
import BillInfoFooter from './BillInfoFooter'
import MessageCenter from '@ekuaibao/messagecenter'
import messageCenter from '@ekuaibao/lib/lib/message-center'
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import style from './BillDetail.module.less'
import { get, keyBy } from 'lodash'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { Modal } from 'antd'
import {
  withdraw,
  getBillHistoryVersionList,
  getBillHistoryVersionDetail,
  getApproverRepeatConfig
} from '../../bills.action'
import {
  getDiffsBetweenVersions,
  getRiskReasonDataForVersionDiffModal,
  fnPushDingTalkShareBtn,
  canShiftNodeApproveFlow,
  canAddNodeApproveFlow,
  canDownAttachmentFLow,
  canPrintFlow,
  shareBillAction
} from '../../util/billUtils'
import { listenerToDrawerResize } from '../../../../elements/configure/bill-drawer'
import { Fetch } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { isPrintShow } from '../../../../plugins/bills/util/fnCheckPrintAvailable'
import { BillVersionDiffModal } from '../bill-version-diff/bill-version-diff-modal'
import { BillAdditionalMessageApi } from '@ekuaibao/ekuaibao_types'
import { FlowAction } from './FlowAction'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const noop = () => {
  // This is intentional
}
const action = {
  printRemind: 0,
  print: 8,
  modify: 5,
  reject: 1,
  agree: 3,
  pay: 6,
  commnet: 4,
  addnode: 11,
  addSignNode: 17,
  addExpress: 14,
  jumpExpress: 15,
  receiveExpress: 12,
  receivePrint: 16,
  nullify: 18, //单据作废
  compliance: 19, //合规性确认
  receiveExceptionExpress: 20, // 收单异常
  cancelReceiveExceptionExpress: 21, // 取消收单异常
  hangUp: 22, // 暂挂审批
  urge: 23, // 催办
  sign: 24, //发起签署
  signStatus: 25 //查看签署详情
}

@EnhanceConnect(
  state => {
    return {
      powersList: state['@common'].powers.powersList || [],
      Express: state['@common'].powers.Express,
      staffMe: state['@common']?.userinfo?.staff || {}
    }
  },
  {
    withdraw
  }
)
export default class BillDetail extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      riskData: props.riskData ?? {},
      userInfo: {},
      historyVersions: [],
      hasDocumentType: false,
      approverRepeatStatus: false, //单据当前审批人与前序节点重复时无权审批是否重复
      approverRepeatMessage: '', //单据当前审批人与前序节点重复时无权审批重复提示信息
      hasGetApproverRepeatConfig: false,
      riskWarningReason: [],
      showPrintBtn: false
    }
    this.bus = props.bus || new MessageCenter()
    props.overrideGetResult && props.overrideGetResult(this.getResult)
  }
  __handleInsertAssist = title => {
    api.invokeService('@common:insert:assist:record', {
      title
    })
  }

  async componentDidMount() {
    const id = get(this.props, 'backlog.flowId.id')
    const fromDataLink = get(this.props, 'fromDataLink')
    let { privilegeId, riskData, showBillMore = true } = this.props
    const me = await api.getState()['@common'].userinfo.staff
    this.setState({
      userInfo: me,
      historyVersions: []
    })
    messageCenter.on('on-click-modify-flow', this.handleModifyFlowChange)
    this.bus.watch('bills:view:diff', this.handleViewDiff)
    this.bus.watch('bills:update:flow', this.handleBillUpdate)
    // 已完成单据入口(false)进来，不再请求风险数据
    if (!fromDataLink && !riskData && showBillMore) {
      api.invokeService('@bills:get:flow:risk:warning', { id, privilegeId }).then(riskData => {
        riskData.isForbid = false
        this.setState({ riskData })
      })
    }
    this.getHistoryVersions({ id })
    this.fetchWidgetStatus(get(this.props, 'backlog.flowId'))

    if (window.isZhongDian) {
      //研究所私有化环境判断
      this.getApproverRepeated(id)
    }

    api.invokeService('@bills:get:show:print:invoice', { flowId: id }).then(data => {
      this.setState({ showPrintBtn: data?.value || false })
    })

    this._removeListenerToDrawerResize = listenerToDrawerResize()
  }

  // 获取小组件是否展示
  async fetchWidgetStatus(data) {
    this.setState({ showWidget: false })
    const isUsePower = (app.getState('@common').powers?.powerCodeMap || [])?.includes('160013')
    const specId = data.form.specificationId?.originalId?.id
    if (!isUsePower || !specId) {
      this.setState({
        showWidget: false
      })
      return
    }
    const config = await BillAdditionalMessageApi.fetchUserConfig(specId)
    this.setState({
      showWidget: !!config
    })
  }

  componentWillUnmount() {
    messageCenter.un('on-click-modify-flow', this.handleModifyFlowChange)
    this.bus.un('bills:update:flow', this.handleBillUpdate)
    this.bus.un('bills:view:diff', this.handleViewDiff)

    this._removeListenerToDrawerResize()
  }
  // 审批神策埋点函数
  newTrack(key, options) {
    window.TRACK &&
      window.TRACK(key, {
        source: window.__PLANTFORM__,
        userId: api.getState()['@common'].userinfo?.staff?.userId,
        corName: api.getState()['@common'].userinfo?.staff?.corporation?.name,
        ...options
      })
  }
  getResult = () => {}

  handleBillUpdate = backlogInfo => {
    let { invokeService, params, reload, privilegeId } = this.props
    const parameters = backlogInfo || params
    return api.invokeService(invokeService, parameters).then(data => {
      const value = data.value
        ? data.value.flowId
          ? data.value
          : { flowId: data.value }
        : data.flowId
        ? data
        : { flowId: data }
      const id = get(value, 'flowId.id') || get(this.props, 'backlog.flowId.id')
      api.invokeService('@bills:get:flow:risk:warning', { id, privilegeId }).then(riskData => {
        riskData.isForbid = false
        this.setState({ riskData })
      })

      this.setState({ backlog: value })
      reload && reload()
      return { backlog: value }
    })
  }
  handleModifyFlowChange = (node, flow, isVisibilityStaffs) => {
    let { backlog } = this.props
    api
      .open('@organizationManagement:SelectStaff', {
        title: i18n.get('选择人员'),
        staffLimitData: node.isAllStaffs ? undefined : node.staffIds,
        required: true,
        fetchDataSourceAction: {
          staff: api.invokeService('@organizationManagement:get:visibility:staffs'),
          department: api.invokeService('@organizationManagement:get:visibility:departments')
        },
        data: [
          {
            type: 'department-member',
            checkIds: node?.approverId?.id ? [node.approverId.id] : []
          }
        ]
      })
      .then(checkedList => {
        const staffData = checkedList[0]
        const staffs = staffData?.checkIds
        const staffId = staffs[0]
        const params = {
          id: node.id,
          flowId: flow.id,
          approverId: staffId,
          name: 'freeflow.select.approver'
        }
        api
          .invokeService('@audit:set:plan-node', params)
          .then(_ =>
            api.invokeService('@audit:get:backlog-info', {
              id: backlog.id,
              type: backlog.type
            })
          )
          .then(backlog => {
            this.setState({ backlog })
          })
      })
    // api.emit('@vendor:select-user', {
    //   whiteList: node.isAllStaffs ? undefined : node.staffIds,
    //   multiple: false,
    //   isVisibilityStaffs,
    //   callback: staff => {
    //     let params = {
    //       id: node.id,
    //       flowId: flow.id,
    //       approverId: staff.id,
    //       name: 'freeflow.select.approver'
    //     }
    //     api
    //       .invokeService('@audit:set:plan-node', params)
    //       .then(_ =>
    //         api.invokeService('@audit:get:backlog-info', {
    //           id: backlog.id,
    //           type: backlog.type
    //         })
    //       )
    //       .then(backlog => {
    //         this.setState({ backlog })
    //       })
    //   }
    // })
  }

  //审批条件
  checkApproveCondition(doc) {
    if (this.props.isShowCondition && doc.state !== 'pending') {
      let { plan } = doc
      if (!plan) return null
      let nodes = plan.nodes
      let conditions = []
      let taskId = plan.taskId
      let userInfo = api.getState('@common.userinfo.data')
      nodes.forEach(node => {
        if (taskId === node.id && node.agreeType === 'FAILED_AUTO_AGREE') {
          //自动同意失败
          conditions.push({ message: i18n.get('自动同意失效，需要您指定下一审批人') })
        }
        if (taskId === node.id && node.conditionalDescription && node.conditionalDescription.length) {
          let flag = node.type !== 'countersign' ? node.approverId && node.approverId.id === userInfo.staff.id : false
          let conditionalDescription = flag
            ? node.conditionalDescription.replace(i18n.get('需要此环节审批'), i18n.get('需要您审批'))
            : node.conditionalDescription
          if (node.config.showConditionInBill) conditions.push({ message: conditionalDescription })
        }
      })
      return conditions
    }
    return null
  }

  handlePrint(backlog) {
    const { doPrint } = api.require('@audit/service-print')
    let dataSource = backlog.flowId
    let { privilegeId, showAllFeeType, handlePrintCallback } = this.props
    let obj = api.invokeService('@share:get:print:param', dataSource)
    let data = [obj]
    data.privilegeId = privilegeId
    data.showAllFeeType = showAllFeeType
    doPrint(
      data,
      !!privilegeId,
      () => {
        this.props.reload &&
          this.props.reload().then(() => {
            handlePrintCallback && handlePrintCallback()
          })
        this.__handleInsertAssist('打印完成' + get(dataSource, 'form.title') + '单据') // @i18n-ignore
        api.close()
      },
      false,
      '0'
    )
  }

  handlePrintInvoice(backlog) {
    const { doPrint } = api.require('@audit/service-print')
    let dataSource = backlog.flowId
    let { privilegeId, showAllFeeType } = this.props
    let obj = api.invokeService('@share:get:print:param', dataSource)
    let data = [obj]
    data.privilegeId = privilegeId
    data.showAllFeeType = showAllFeeType
    doPrint(
      data,
      !!privilegeId,
      () => {
        this.props.reload && this.props.reload()
        this.__handleInsertAssist('打印完成' + get(dataSource, 'form.title') + '单据') // @i18n-ignore
        api.close()
      },
      false,
      '1'
    )
  }

  handleComment = backlog => {
    api.close()
    let dataSource = backlog.flowId
    let { privilegeId, noCheckPermissions } = this.props
    api.open('@bills:BillCommentModal', { flow: backlog.flowId, privilegeId }).then(params => {
      let { currRoleForMine } = this.props
      const paramsValue = noCheckPermissions ? { ...params, checkPermissions: false } : params
      let args = { params: paramsValue, id: backlog.flowId.id }
      args.privilegeId = privilegeId
      if (currRoleForMine) {
        args.privilegeId = currRoleForMine.id
      }
      this.__handleInsertAssist('评论' + get(dataSource, 'form.title') + '单据') // @i18n-ignore
      api.invokeService('@bills:comment:flow', args).then(() => {
        this.handleBillUpdate()
        this.props.reload && this.props.reload()
      })
    })
  }
  //小权限菜单下管理员操作加签/跳过
  handleAdminOperateNode = async params => {
    const { backlog, type } = params
    const privilegeId = get(this.props, 'privilegeId')
    const result = await api.invokeService('@order-manage:adminOperateNode', {
      line: backlog.flowId,
      privilegeId,
      type
    })
    result && this.props.layer.emitOk()
  }
  handleAddNode = async backlog => {
    const state = get(backlog, 'flowId.state')
    const id = get(backlog, 'flowId.id')
    const privilegeId = get(this.props, 'privilegeId')
    if (['nullify'].includes(state)) {
      return showMessage.error(i18n.get('已作废单据状态不支持此功能'))
    }
    await api.invokeService('@order-manage:deliverFlow', { flowIds: [id], privilegeId })
    this.props.layer.emitOk()
  }

  handleBack = async backlog => {
    const state = get(backlog, 'flowId.state')
    const id = get(backlog, 'flowId.id')
    const privilegeId = get(this.props, 'privilegeId')
    if (['nullify'].includes(state)) {
      return showMessage.error(i18n.get('已作废单据状态不支持此功能'))
    }
    await api.invokeService('@order-manage:backFlow', { flowIds: [id], privilegeId })
    this.props.layer.emitOk()
  }

  handleWithdraw = backlog => {
    // 找出纳的会签节点
    const cashierCountersign = backlog?.flowId?.plan?.nodes?.find(
      node => node?.name === '出纳支付' && node?.type === 'countersign'
    )
    api.close()
    const getApproverPromise = cashierCountersign?.counterSigners?.length
      ? Promise.resolve({})
      : api.invokeService('@bills:withdraw:approvers', { id: backlog.flowId.id })
    getApproverPromise
      .then(approver => {
        return api.open('@bills:BillWithdrawModal', { flow: approver, cashierCountersign })
      })
      .then(params => {
        const {
          flowId: { id }
        } = backlog
        params = { ...params, flowId: id }
        return api.invokeService('@bills:withdraw:save', params)
      })
      .then(_ => {
        this.bus.reload()
        api.invokeService('@common:get:backlog:count:payment')
      })
  }

  handleActiveFlow = backlog => {
    api.close()
    showModal.confirm({
      title: i18n.get('确认激活单据吗？'),
      content: i18n.get('此单据将会重新置于制单人的【我的单据】中，制单人可以重新提交单据。'),
      onOk: () => {
        api
          .invokeService('@bills:activate:flow', { name: 'freeflow.activate', id: backlog?.flowId?.id })
          .then(res => {
            showMessage.success(i18n.get('激活成功'))
            this.bus.reload()
          })
          .catch(err => {
            showMessage.error(err)
            this.bus.reload()
          })
      },
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消')
    })
  }

  //检查单据当前审批人与前序节点重复时是否无权审批
  getApproverRepeated = async id => {
    try {
      const res = await getApproverRepeatConfig(id)
      if (res?.value) {
        const { isRepeated, message } = res.value
        this.setState({
          approverRepeatStatus: isRepeated,
          approverRepeatMessage: message
        })
      }
    } finally {
      this.setState({
        hasGetApproverRepeatConfig: true
      })
    }
  }

  handleAgreeRepeatAction = async () => {
    return showModal.info({
      content: this.state.approverRepeatMessage,
      okText: i18n.get('确定')
    })
  }

  isIncludesActions = action => {
    const { isFromLoanManage, privilegeId, privilegeAcitons = [] } = this.props
    //isFromLoanManage  从借款管理来的
    //privilegeId  代表小权限id
    //privilegeAcitons 小权限配置的操作权限
    return !isFromLoanManage || !privilegeId || privilegeAcitons.includes(action)
  }

  handleAgreeAction = (backlog, riskData) => {
    const { onFooterButtonsClick = noop } = this.props
    const footerClick = onFooterButtonsClick
    const { approverRepeatStatus, hasGetApproverRepeatConfig } = this.state

    if (window.isZhongDian) {
      if (!hasGetApproverRepeatConfig) return
      if (approverRepeatStatus) {
        return this.handleAgreeRepeatAction()
      }
      return footerClick(action.agree, backlog, riskData)
    }
    return footerClick(action.agree, backlog, riskData)
  }

  getBackLogNode = backlog => {
    const nodes = get(backlog, 'flowId.plan.nodes', [])
    const taskId = get(backlog, 'flowId.plan.taskId', '')
    return nodes.find(v => v.id === taskId) || {}
  }

  getActionMap = backlog => {
    const { onFooterMarkedReadClick } = this.props
    const footerClick = this.props.onFooterButtonsClick || noop
    const { riskData } = this.state
    const node = this.getBackLogNode(backlog)
    // 审批节点禁止驳回
    const isForbidReject = !!node.forbidRejectNode
    return {
      [FlowAction.Agree]: {
        action: FlowAction.Agree,
        label: i18n.get('同意'),
        main: true,
        onClick: e => this.handleAgreeAction(backlog, riskData),
        buttonProps: {
          'data-testid': 'bill-footer-agree'
        }
      },
      [FlowAction.Reject]: {
        action: FlowAction.Reject,
        label: i18n.get('驳回'),
        dangerous: true,
        minorGroup: false,
        disabled: isForbidReject || false,
        onClick: e => footerClick(action.reject, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-reject'
        }
      },
      [FlowAction.PrintRemind]: {
        action: FlowAction.PrintRemind,
        label: i18n.get('打印提醒'),
        onClick: e => footerClick(action.printRemind, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-printremind'
        }
      },
      'freeflow.printed': {
        action: FlowAction.Print,
        label: i18n.get('打印单据'),
        onClick: e => this.handlePrint(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-print'
        }
      },
      [FlowAction.Print]: {
        action: FlowAction.Print,
        label: i18n.get('打印单据'),
        onClick: e => this.handlePrint(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-print'
        }
      },
      [FlowAction.PrintInvoice]: {
        action: FlowAction.PrintInvoice,
        label: i18n.get('打印单据和发票'),
        onClick: e => this.handlePrintInvoice(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-printinvoice'
        }
      },
      [FlowAction.ShiftNode]: {
        action: FlowAction.ShiftNode,
        label: i18n.get('转交审批'),
        onClick: e => footerClick(action.addnode, backlog, riskData),
        buttonProps: {
          'data-testid': 'bill-footer-shiftnode'
        }
      },
      [FlowAction.Back]: {
        action: FlowAction.Back,
        label: i18n.get('回退'),
        onClick: e => this.handleBack(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-back'
        }
      },
      [FlowAction.Comment]: {
        action: FlowAction.Comment,
        label: i18n.get('评论'),
        onClick: e => this.handleComment(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-comment'
        }
      },
      'freeflow.modify': {
        // 前端自己定义的action
        action: 'freeflow.modify',
        label: i18n.get('修改'),
        onClick: e => {
          startOpenFlowPerformanceStatistics()
          footerClick(action.modify, backlog, riskData)
        },
        buttonProps: {
          'data-testid': 'bill-footer-modify'
        }
      },
      [FlowAction.Modify]: {
        action: FlowAction.Modify,
        label: i18n.get('修改'),
        onClick: e => {
          startOpenFlowPerformanceStatistics()
          footerClick(action.modify, backlog, riskData)
        },
        buttonProps: {
          'data-testid': 'bill-footer-modify'
        }
      },
      [FlowAction.Activate]: {
        action: FlowAction.Activate,
        label: i18n.get('激活'),
        onClick: e => this.handleActiveFlow(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-activate'
        }
      },
      [FlowAction.Receive]: {
        action: FlowAction.Receive,
        label: i18n.get('确认收单'),
        main: true,
        onClick: e => footerClick(action.receiveExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-receive'
        }
      },
      [FlowAction.ReceiveException]: {
        action: FlowAction.ReceiveException,
        label: i18n.get('收单异常'),
        main: true,
        onClick: e => footerClick(action.receiveExceptionExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-receiveexception'
        }
      },
      [FlowAction.CancelReceiveException]: {
        action: FlowAction.CancelReceiveException,
        label: i18n.get('取消收单异常'),
        main: true,
        onClick: e => footerClick(action.cancelReceiveExceptionExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-cancelreceiveexception'
        }
      },
      [FlowAction.Nullify]: {
        action: FlowAction.Nullify,
        label: i18n.get('作废'),
        onClick: e => footerClick(action.nullify, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-nullify'
        }
      },
      [FlowAction.Pay]: {
        action: FlowAction.Pay,
        label: i18n.get('支付'),
        main: true,
        onClick: e => footerClick(action.pay, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-pay'
        }
      },
      [FlowAction.AddExpress]: {
        action: FlowAction.AddExpress,
        label: i18n.get('添加寄送信息'),
        main: true,
        onClick: e => footerClick(action.addExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-addexpress'
        }
      },
      [FlowAction.JumpExpress]: {
        action: FlowAction.JumpExpress,
        label: i18n.get('跳过寄送'),
        onClick: e => footerClick(action.jumpExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-jumpexpress'
        }
      },
      [FlowAction.ShiftApprove]: {
        action: FlowAction.ShiftApprove,
        label: i18n.get('转交审批'),
        onClick: e => footerClick(action.addnode, backlog, riskData),
        buttonProps: {
          'data-testid': 'bill-footer-shiftapprove'
        }
      },
      [FlowAction.AddSignNode]: {
        action: FlowAction.AddSignNode,
        label: i18n.get('加签审批'),
        onClick: e => footerClick(action.addSignNode, backlog, riskData),
        buttonProps: {
          'data-testid': 'bill-footer-addsignnode'
        }
      },
      [FlowAction.Urge]: {
        action: FlowAction.Urge,
        label: i18n.get('催办'),
        onClick: e => footerClick(action.urge, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-urge'
        }
      },
      [FlowAction.ExportAttachment]: {
        action: FlowAction.ExportAttachment,
        label: i18n.get('导出全部附件'),
        onClick: e => this.exportAttachment(),
        buttonProps: {
          'data-testid': 'bill-footer-exportattachment'
        }
      },
      [FlowAction.AdminShiftNode]: {
        action: FlowAction.AdminShiftNode,
        label: i18n.get('加签'),
        onClick: e => this.handleAddNode(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-addsignnode'
        }
      },
      [FlowAction.AdminSkipNode]: {
        action: FlowAction.AdminSkipNode,
        label: i18n.get('跳过'),
        onClick: e => this.handleAdminOperateNode({ backlog, type: 'adminSkipNode' }),
        buttonProps: {
          'data-testid': 'bill-footer-adminskipnode'
        }
      },
      [FlowAction.Suspend]: {
        action: FlowAction.Suspend,
        label: i18n.get('暂挂审批'),
        onClick: () => {
          footerClick(action.hangUp, backlog)
        },
        buttonProps: {
          'data-testid': 'bill-footer-suspend'
        }
      },
      [FlowAction.AdminBack]: {
        action: FlowAction.AdminBack,
        label: i18n.get('回退'),
        onClick: e => this.handleBack(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-adminback'
        }
      },
      [FlowAction.AddNode]: {
        action: FlowAction.AddNode,
        label: i18n.get('加签审批'),
        onClick: e => footerClick(action.addSignNode, backlog, riskData),
        buttonProps: {
          'data-testid': 'bill-footer-addsignnode'
        }
      },
      [FlowAction.Printed]: {
        action: FlowAction.Printed,
        label: i18n.get('收到打印'),
        onClick: e => footerClick(action.receivePrint, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-receiveprint'
        }
      },
      [FlowAction.Send]: {
        action: FlowAction.Send,
        label: i18n.get('添加寄送信息'),
        onClick: e => footerClick(action.addExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-send'
        }
      },
      [FlowAction.SkipSend]: {
        action: FlowAction.SkipSend,
        label: i18n.get('跳过寄送'),
        onClick: e => footerClick(action.jumpExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-skip-send'
        }
      },
      [FlowAction.StartSignature]: {
        action: FlowAction.StartSignature,
        label: i18n.get('发起签署'),
        onClick: e => footerClick(action.sign, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-startsignature'
        }
      },
      [FlowAction.ViewSignature]: {
        action: FlowAction.ViewSignature,
        label: i18n.get('查看签署文件'),
        main: true,
        onClick: e => footerClick(action.signStatus, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-viewsignature'
        }
      },
      [FlowAction.AdminAddNode]: {
        action: FlowAction.AdminAddNode,
        label: i18n.get('加签'),
        onClick: e => this.handleAdminOperateNode({ backlog, type: 'adminAddNode' }),
        buttonProps: {
          'data-testid': 'bill-footer-adminaddnode'
        }
      },
      [FlowAction.Withdraw]: {
        action: FlowAction.Withdraw,
        label: i18n.get('撤销支付'),
        onClick: e => this.handleWithdraw(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-withdraw'
        }
      },
      [FlowAction.MarkedRead]: {
        action: FlowAction.MarkedRead,
        label: i18n.get('标为已读'),
        onClick: e => onFooterMarkedReadClick(),
        buttonProps: {
          'data-testid': 'bill-footer-markedread'
        }
      },
      [FlowAction.Share]: {
        action: FlowAction.Share,
        label: i18n.get('转发'),
        onClick: () => {
          shareBillAction(backlog?.flowId?.id)
        }
      }
    }
  }

  getFooterByState = (state, backlog, params) => {
    const footerClick = this.props.onFooterButtonsClick || noop
    const { staffMe } = this.props
    const { userInfo, riskData, showPrintBtn } = this.state

    if (state === 'receivingExcep') {
      // 单据管理的单据详情
      if (backlog.scenesType === 'EXPENSE_MANAGE') {
        const buttonList = [
          {
            action: FlowAction.Print,
            label: i18n.get('打印单据'),
            onClick: e => this.handlePrint(backlog),
            buttonProps: {
              'data-testid': 'bill-footer-print'
            }
          },
          {
            action: FlowAction.Comment,
            label: i18n.get('评论'),
            onClick: e => this.handleComment(backlog),
            buttonProps: {
              'data-testid': 'bill-footer-comment'
            }
          }
        ]
        if (this.state.showPrintBtn) {
          buttonList.splice(1, 0, {
            action: FlowAction.PrintInvoice,
            label: i18n.get('打印单据和发票'),
            onClick: e => this.handlePrintInvoice(backlog),
            buttonProps: {
              'data-testid': 'bill-footer-printinvoice'
            }
          })
        }
        return buttonList
      }

      // 待我审批的单据详情
      return [
        {
          action: FlowAction.Receive,
          label: i18n.get('确认收单'),
          main: true,
          onClick: e => footerClick(action.receiveExpress, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-receive'
          }
        },
        {
          action: FlowAction.CancelReceiveException,
          label: i18n.get('取消收单异常'),
          onClick: e => footerClick(action.cancelReceiveExceptionExpress, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-cancelreceiveexception'
          }
        }
      ]
    }
    let footer = []
    const { canActivate } = params

    const nodes = get(backlog, 'flowId.plan.nodes', [])
    const actions = get(backlog, 'flowId.actions')
    const myAction = actions[userInfo.id] || []
    const node = this.getBackLogNode(backlog)
    const inPartialPayState = get(backlog, 'flowId.form.partialPayState') // 部分支付
    // 审批节点禁止驳回
    const isForbidReject = !!node.forbidRejectNode
    const submitNode = get(backlog, 'flowId.plan.submitNode')
    // 电子签功能按钮补充
    const SIGNING_ACTIONS = [
      {
        action: FlowAction.ShiftApprove,
        label: i18n.get('转交审批'),
        onClick: e => footerClick(action.addnode, backlog, riskData),
        buttonProps: {
          'data-testid': 'bill-footer-shiftapprove'
        }
      },
      {
        action: FlowAction.Comment,
        label: i18n.get('评论'),
        onClick: e => this.handleComment(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-comment'
        }
      },
      {
        action: FlowAction.Print,
        label: i18n.get('打印单据'),
        onClick: e => this.handlePrint(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-print'
        }
      },
      {
        action: FlowAction.Printed,
        label: i18n.get('收到打印'),
        onClick: e => footerClick(action.receivePrint, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-receiveprint'
        }
      },
      {
        action: FlowAction.PrintRemind,
        label: i18n.get('打印提醒'),
        onClick: e => footerClick(action.printRemind, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-printremind'
        }
      }
    ]
    if (backlog?.state === 'WAIT_SIGN') {
      return [
        {
          action: FlowAction.StartSignature,
          label: i18n.get('发起签署'),
          main: true,
          onClick: e => footerClick(action.sign, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-startsignature'
          }
        },
        {
          action: FlowAction.Reject,
          label: i18n.get('驳回'),
          dangerous: true,
          minorGroup: false,
          disabled: isForbidReject || false,
          onClick: e => footerClick(action.reject, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-reject'
          }
        },
        ...SIGNING_ACTIONS
      ]
    }
    if (backlog?.state === 'SIGNING') {
      return [
        {
          action: FlowAction.ViewSignature,
          label: i18n.get('查看签署文件'),
          main: true,
          onClick: e => footerClick(action.signStatus, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-viewsignature'
          }
        },
        {
          action: FlowAction.Comment,
          label: i18n.get('评论'),
          onClick: e => this.handleComment(backlog),
          buttonProps: {
            'data-testid': 'bill-footer-comment'
          }
        },
        {
          action: FlowAction.Print,
          label: i18n.get('打印单据'),
          onClick: e => this.handlePrint(backlog),
          buttonProps: {
            'data-testid': 'bill-footer-print'
          }
        }
      ]
    }
    if (backlog.id === -1) {
      if (this.isIncludesActions('freeflow.print')) {
        footer.push({
          action: FlowAction.Print,
          label: i18n.get('打印单据'),
          onClick: e => this.handlePrint(backlog),
          buttonProps: {
            'data-testid': 'bill-footer-print'
          }
        })
      }
      if (this.isIncludesActions('freeflow.comment')) {
        footer.push({
          action: FlowAction.Comment,
          label: i18n.get('评论'),
          onClick: e => this.handleComment(backlog),
          buttonProps: {
            'data-testid': 'bill-footer-comment'
          }
        })
      }
      if (showPrintBtn) {
        footer.splice(1, 0, {
          action: FlowAction.PrintInvoice,
          label: i18n.get('打印单据和发票'),
          onClick: e => this.handlePrintInvoice(backlog),
          buttonProps: {
            'data-testid': 'bill-footer-printinvoice'
          }
        })
      }

      const node_new = nodes.find(line => line?.config?.isNeedCashierNode === false)
      const isNeedCashierNode = node_new && node_new?.config?.isNeedCashierNode === false
      if (
        backlog.scenesType &&
        backlog.scenesType === 'EXPENSE_MANAGE' &&
        (state === 'paid' || state === 'archived') &&
        !isNeedCashierNode
      ) {
        const logs = get(backlog, 'flowId.logs', [])
        const isSkip = logs.find(item => item.state === 'paid' && item.action === 'freeflow.skipped')
        if (!isSkip) {
          footer.push({
            action: FlowAction.Withdraw,
            label: i18n.get('撤销支付'),
            onClick: e => this.handleWithdraw(backlog),
            buttonProps: {
              'data-testid': 'bill-footer-withdraw'
            }
          })
        }
      }
      if (myAction.includes('freeflow.activate') && canActivate) {
        footer.push({
          action: FlowAction.Activate,
          label: i18n.get('激活'),
          onClick: e => this.handleActiveFlow(backlog),
          buttonProps: {
            'data-testid': 'bill-footer-activate'
          }
        })
      }

      const { extendFooterBtn } = this.props
      if (!!extendFooterBtn) {
        footer = footer.concat(extendFooterBtn)
      }
      return footer
    }
    if (state === 'approving') {
      footer.push({
        action: FlowAction.Agree,
        label: i18n.get('同意'),
        main: true,
        onClick: e => this.handleAgreeAction(backlog, riskData),
        buttonProps: {
          'data-testid': 'bill-footer-agree'
        }
      })
    }
    if (state === 'paying' && backlog.state !== 'PROCESSED') {
      footer.push({
        action: FlowAction.Pay,
        label: i18n.get('支付'),
        main: true,
        onClick: e => footerClick(action.pay, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-pay'
        }
      })
    }
    if (state === 'sending') {
      footer.push({
        action: FlowAction.Send,
        label: i18n.get('添加寄送信息'),
        main: true,
        onClick: e => footerClick(action.addExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-send'
        }
      })
      footer.push({
        action: FlowAction.SkipSend,
        label: i18n.get('跳过寄送'),
        onClick: e => footerClick(action.jumpExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-skip-send'
        }
      })
    }
    if (state === 'receiving') {
      footer.push({
        action: FlowAction.Receive,
        label: i18n.get('确认收单'),
        main: true,
        onClick: e => footerClick(action.receiveExpress, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-receive'
        }
      })
    }

    if (state === 'approving' || (state === 'paying' && !inPartialPayState)) {
      footer.push({
        action: FlowAction.Reject,
        label: i18n.get('驳回'),
        dangerous: true,
        minorGroup: false,
        disabled: isForbidReject || false,
        onClick: e => footerClick(action.reject, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-reject'
        }
      })
    }
    if (
      state === 'approving' ||
      (state === 'paying' && !inPartialPayState) ||
      state === 'sending' ||
      state === 'receiving'
    ) {
      if (node.config?.allowApproverNullify) {
        footer.push({
          action: FlowAction.Nullify,
          label: i18n.get('作废'),
          onClick: e => footerClick(action.nullify, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-nullify'
          }
        })
      }
      if (node.allowModify) {
        footer.push({
          action: FlowAction.Modify,
          label: i18n.get('修改'),
          onClick: e => {
            startOpenFlowPerformanceStatistics()
            footerClick(action.modify, backlog, riskData)
          },
          buttonProps: {
            'data-testid': 'bill-footer-modify'
          }
        })
      }
    }
    footer.push({
      action: FlowAction.Comment,
      label: i18n.get('评论'),
      onClick: e => this.handleComment(backlog),
      buttonProps: {
        'data-testid': 'bill-footer-comment'
      }
    })
    if (canPrintFlow(staffMe, node, submitNode)) {
      footer.push({
        action: FlowAction.Print,
        label: i18n.get('打印单据'),
        onClick: e => this.handlePrint(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-print'
        }
      })
    }
    if (this.state.showPrintBtn) {
      footer.push({
        action: FlowAction.PrintInvoice,
        label: i18n.get('打印单据和发票'),
        onClick: e => this.handlePrintInvoice(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-printinvoice'
        }
      })
    }

    if (this.props.Express) {
      if (state === 'receiving') {
        footer.splice(-1, 0, {
          action: FlowAction.ReceiveException,
          label: i18n.get('收单异常'),
          onClick: e => footerClick(action.receiveExceptionExpress, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-receiveexception'
          }
        })
      }
      if (state === 'receivingException') {
        footer.splice(-1, 0, {
          action: FlowAction.CancelReceiveException,
          label: i18n.get('取消收单异常'),
          onClick: e => footerClick(action.cancelReceiveExceptionExpress, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-cancelreceiveexception'
          }
        })
      }
    }
    if (state === 'approving' || state === 'paying' || state === 'sending' || state === 'receiving') {
      footer.push(
        {
          action: FlowAction.PrintRemind,
          label: i18n.get('打印提醒'),
          onClick: e => footerClick(action.printRemind, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-printremind'
          }
        },
        {
          action: FlowAction.Printed,
          label: i18n.get('收到打印'),
          onClick: e => footerClick(action.receivePrint, backlog),
          buttonProps: {
            'data-testid': 'bill-footer-receiveprint'
          }
        }
      )
      const isReview = node.config && node.config.type === 'review'
      const isPermit = backlog?.flowId?.formType ==='permit'
      if (state !== 'sending' && state !== 'receiving' && !isReview && !inPartialPayState && !isPermit) {
        if (!node.forbidShiftNode) {
          if (canShiftNodeApproveFlow(staffMe, node, submitNode)) {
            footer.push({
              action: FlowAction.ShiftApprove,
              label: i18n.get('转交审批'),
              onClick: e => footerClick(action.addnode, backlog, riskData),
              buttonProps: {
                'data-testid': 'bill-footer-shiftapprove'
              }
            })
          }
        }

        if (!node.forbidBeforeAddNode || !node.forbidAftAddNode) {
          if (canAddNodeApproveFlow(staffMe, node, submitNode)) {
            footer.push({
              action: FlowAction.AddNode,
              label: i18n.get('加签审批'),
              onClick: e => footerClick(action.addSignNode, backlog, riskData),
              buttonProps: {
                'data-testid': 'bill-footer-addsignnode'
              }
            })
          }
        }
      }
    }
    // 待审批 待支付
    if (state === 'approving' || state === 'paying') {
      footer.push({
        action: FlowAction.Suspend,
        label: i18n.get('暂挂审批'),
        onClick: e => footerClick(action.hangUp, backlog),
        buttonProps: {
          'data-testid': 'bill-footer-suspend'
        }
      })
    }
    return footer
  }

  getCustomFooter(bottomToolbar, backlog, flowId, params) {
    if (Array.isArray(bottomToolbar)) {
      const node = this.getBackLogNode(backlog)
      // 审批节点禁止驳回
      const actionMap = this.getActionMap(backlog)
      return bottomToolbar.map(action => {
        // 兼容第三方入口传入的action错误问题
        const changeAction = action === 'freeflow.printed' ? 'freeflow.print' : action
        return actionMap[changeAction]
      })
    }
    return this.getFooterByState(flowId.state, backlog, params)
  }

  renderRiskTips() {
    let { onOpenOwnerLoanList = () => {}, riskTip } = this.props
    let backlog = this.state.backlog || this.props.backlog
    let { flowId } = backlog
    let approveCondition = this.checkApproveCondition(flowId)
    let tips = []
    if (
      flowId.state === 'approving' ||
      flowId.state === 'paying' ||
      flowId.state === 'receiving' ||
      flowId.state === 'sending'
    ) {
      if (approveCondition && approveCondition.length > 0) {
        approveCondition.forEach((item, index) => {
          tips.push(
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%'
              }}
              key={index}
            >
              {item.message}
            </div>
          )
        })
      }
    }

    if (flowId.state !== 'rejected' && flowId.state !== 'draft') {
      const details = getNodeValueByPath(flowId, 'form.details', [])
      details.find((item, index) => {
        if (item.feeTypeForm.invoiceForm && item.feeTypeForm.invoiceForm.type === 'wait') {
          return tips.push(
            <div
              key={`${index}-invoice`}
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%'
              }}
            >
              {i18n.get('有「待开发票」的消费')}
            </div>
          )
        }
      })
    }

    if (riskTip) {
      tips.push(
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%'
          }}
          key={tips.length}
        >
          {riskTip}
          {
            <div
              onClick={e => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              <a onClick={onOpenOwnerLoanList.bind(this, backlog)}>{i18n.get('点击查看')}</a>
            </div>
          }
        </div>
      )
    }

    return tips
  }

  getCarBonCopyBillFooter(backlog) {
    let { onFooterMarkedReadClick } = this.props
    let carbonCopyArr = []
    carbonCopyArr = [
      {
        action: FlowAction.Print,
        label: i18n.get('打印单据'),
        onClick: e => this.handlePrint(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-print'
        }
      },
      {
        action: FlowAction.Comment,
        label: i18n.get('评论'),
        onClick: e => this.handleComment(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-comment'
        }
      }
    ]
    if (this.state.showPrintBtn) {
      carbonCopyArr.splice(1, 0, {
        action: FlowAction.PrintInvoice,
        label: i18n.get('打印单据和发票'),
        onClick: e => this.handlePrintInvoice(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-printinvoice'
        }
      })
    }

    if (backlog.state === 'UNREAD') {
      carbonCopyArr.unshift({
        action: FlowAction.MarkedRead,
        label: i18n.get('标为已读'),
        onClick: e => onFooterMarkedReadClick(),
        buttonProps: {
          'data-testid': 'bill-footer-markedread'
        }
      })
    }
    if (backlog.scenesType && backlog.scenesType === 'EXPENSE_MANAGE') {
      if (backlog.state === 'paid' || backlog.state === 'archived') {
        carbonCopyArr.unshift({
          action: FlowAction.Withdraw,
          label: i18n.get('撤销支付'),
          onClick: e => this.handleWithdraw(backlog),
          buttonProps: {
            'data-testid': 'bill-footer-withdraw'
          }
        })
      }
    }
    return carbonCopyArr
  }

  getActionsFooter(actions, backlog) {
    let footer = []
    let actionsMap = keyBy(actions)
    if (actionsMap['freeflow.addnode']) {
      footer.push({
        action: FlowAction.ShiftNode,
        label: i18n.get('转交'),
        onClick: e => this.handleAddNode(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-shiftnode'
        }
      })
    }
    if (actionsMap['freeflow.back']) {
      footer.push({
        action: FlowAction.Back,
        label: i18n.get('回退'),
        onClick: e => this.handleBack(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-back'
        }
      })
    }
    if (actionsMap['freeflow.print']) {
      footer.push({
        action: FlowAction.Print,
        label: i18n.get('打印单据'),
        onClick: e => this.handlePrint(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-print'
        }
      })
    }
    if (actionsMap['freeflow.printInvoice'] && this.state.showPrintBtn) {
      footer.push({
        action: FlowAction.PrintInvoice,
        label: i18n.get('打印单据和发票'),
        onClick: e => this.handlePrintInvoice(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-printinvoice'
        }
      })
    }
    if (actionsMap['freeflow.comment']) {
      footer.push({
        action: FlowAction.Comment,
        label: i18n.get('评论'),
        onClick: e => this.handleComment(backlog),
        buttonProps: {
          'data-testid': 'bill-footer-comment'
        }
      })
    }
    if (actionsMap['freeflow.admin.addnode']) {
      const kaBillPrivilegeAdminAddNodeApprove = api.getState('@common').powers.KA_Bill_Privilege_Admin_Add_Node_Approve
      const isInApproving = ['paying', 'approving', 'sending', 'receiving', 'receivingExcep'].includes(
        backlog?.flowId?.state
      )
      if (kaBillPrivilegeAdminAddNodeApprove && isInApproving) {
        footer.push({
          action: FlowAction.AdminShiftNode,
          label: i18n.get('加签'),
          onClick: e => this.handleAdminOperateNode({ backlog, type: 'adminAddNode' }),
          buttonProps: {
            'data-testid': 'bill-footer-adminaddnode'
          }
        })
      }
    }
    if (actionsMap['freeflow.admin.skipnode']) {
      const addminSkipNodeToggle = api.getState()['@common'].toggleManage?.['tg_privilegeAdmin_skipNode']
      const isInApproving = ['approving', 'sending', 'receiving', 'receivingExcep'].includes(backlog?.flowId?.state)
      if (addminSkipNodeToggle && isInApproving) {
        footer.push({
          action: FlowAction.AdminSkipNode,
          label: i18n.get('跳过'),
          onClick: e => this.handleAdminOperateNode({ backlog, type: 'adminSkipNode' }),
          buttonProps: {
            'data-testid': 'bill-footer-adminskipnode'
          }
        })
      }
    }
    return footer
  }

  getFooter = footerParams => {
    const { staffMe } = this.props
    const { isCarbonCopy, backlog, currRoleForMine, flowId, bottomToolbar, params } = footerParams
    if (isCarbonCopy) {
      return this.getCarBonCopyBillFooter(backlog)
    }
    if (bottomToolbar?.length) {
      return this.getCustomFooter(bottomToolbar, backlog, flowId, params)
    }
    if (currRoleForMine?.actions?.length) {
      return this.getActionsFooter(currRoleForMine.actions, backlog)
    }
    return this.getFooterByState(flowId.state, backlog, params)
  }

  /**
   * 判断是否显示撤回按钮
   * 必须是审批中状态或者寄送状态
   * 是从已审批列表进入
   * 当前审批流允许撤回并且当前上一个审批的节点是非ebot节点或者非重算节点时
   * 或者上一个已审批的节点不是自动跳过或者自动同意节点 可以撤回
   * @param nodes  审批流
   * @param node  当前审批节点
   * @param __status  列表的审批状态（是否是从已审批列表进入的） 已审批列表APPROVED
   * @param footer  底部按钮数组
   * @param backlog  审批流
   * @param state  审批状态
   * */
  getWithdrawFooter = (nodes, node, __status, footer, backlog, state) => {
    const { staffMe } = this.props
    // 当前审批流中勾选了可以撤回审批多选框
    const isAllowWithdraw = get(backlog, 'flowId.plan.submitNode.isAllowWithdraw', false)
    //会签节点是否自己已经审批过  审批过才显示按钮
    const currentSigner = node?.counterSigners?.find(
      signer => signer?.signerId?.id === staffMe?.id && signer?.state === 'PROCESSED'
    )

    // 获取上一个节点 不能用preId的原因是如果是自动审批节点 那么preId指向的是前个节点而不是上个节点
    const index = nodes.findIndex(v => v.id === node.id)
    const prevNode = index > 0 ? nodes[index - 1] : {}
    // 当前审批流允许撤回并且上一个节点是非ebot节点和非重算节点时 可以撤回
    const notEbotNode =
      prevNode.type !== 'ebot' && prevNode.type !== 'recalculate' && prevNode.type !== 'invoicingApplication'
    // 是否是自动跳过
    const { agreeType = 'NO_AUTO_AGREE', skippedType = 'NO_SKIPPED', id } = prevNode
    const agreeTypeFlag = agreeType && agreeType !== 'NO_AUTO_AGREE'
    const autoPass = skippedType !== 'NO_SKIPPED' || agreeTypeFlag
    // 审批状态
    const approvingMap = {
      approving: 'approving',
      sending: 'sending',
      paying: 'paying'
    }
    const isApprovingStatus = !!approvingMap[state]
    let showApproveBtnCondition =
      isAllowWithdraw && notEbotNode && !autoPass && __status === 'APPROVED' && isApprovingStatus
    if (currentSigner) {
      showApproveBtnCondition = showApproveBtnCondition && currentSigner
    }
    if (showApproveBtnCondition) {
      footer.push({
        label: i18n.get('撤回审批'),
        onClick: () => {
          this.handleApproveWithdraw(id, backlog)
        },
        buttonProps: {
          'data-testid': 'bill-footer-withdraw'
        }
      })
    }
  }

  /**
   * 已审批详情点击撤回审批按钮弹出弹出提示
   * 点击确定时撤回
   * */
  handleApproveWithdraw = (prevNodeId, backlog) => {
    const { withdraw } = this.props
    Modal.confirm({
      title: i18n.get('确认发起撤回吗?'),
      content: '',
      onOk: async () => {
        const nextId = get(backlog, 'flowId.plan.taskId', '')
        const rejectTo = prevNodeId
        const id = get(backlog, 'flowId.plan.id', '')
        const name = 'freeflow.withdraw'
        const res = await withdraw(id, name, rejectTo, nextId)
        if (res.payload.code === '0') {
          Modal.success({
            title: res.payload.message
          })
        } else {
          Modal.error({
            title: res.payload.message
          })
        }

        this.handleBillUpdate()
        this.props.reload()
      }
    })
  }
  // 检测单子是否包含附件
  checkAttachment = () => {
    const backlog = this.state.backlog || this.props.backlog
    const form = get(backlog, 'flowId.form', {})
    const formComponents = get(backlog, 'flowId.form.specificationId.components', [])
    const hasAttachment = this.checkAttachmentFn(form, formComponents)
    return hasAttachment || this.checkDetailsAttachment()
  }
  // 检测 明细是否包含附件
  checkDetailsAttachment = () => {
    const backlog = this.state.backlog || this.props.backlog
    const details = get(backlog, 'flowId.form.details', [])
    let hasAttachment = false
    for (let i = 0; i < details.length; i++) {
      const el = details[i]
      const form = get(el, 'feeTypeForm', {})
      const components = get(el, 'specificationId.components', [])
      const hasAttachment2 = this.checkAttachmentFn(form, components)
      if (hasAttachment2) {
        hasAttachment = true
        break
      }
    }
    return hasAttachment
  }
  checkAttachmentFn = (form, components) => {
    const attachmentsArr = components.filter(it => it?.type === 'attachments' || it?.type === 'aiAttachments')
    let hasAttachment = false
    if (attachmentsArr.length > 0) {
      for (let i = 0; i < attachmentsArr.length; i++) {
        const el = attachmentsArr[i]
        const attachments = form?.[el?.field]
        if (attachments?.length > 0) {
          for (let j = 0; j < attachments.length; j++) {
            const item = attachments[j]
            // 必须要有非钉盘的文件才能导出,都是钉盘文件不到处
            if (!item?.key?.startsWith('DP:{')) {
              hasAttachment = true
              break
            }
          }
          if (hasAttachment) {
            break
          }
        }
      }
    }
    return hasAttachment
  }
  // 导出全部附件
  exportAttachment = async () => {
    const hasAttachment = this.checkAttachment()
    if (hasAttachment) {
      const val = await api.open('@layout:AsyncExportModal')
      const backlog = this.state.backlog || this.props.backlog
      const flowId = get(backlog, 'flowId.id')
      const { taskName } = val
      const res = await Fetch.GET(`/api/v2/easyexcel/annex/export/async?taskName=${taskName}&flowId=${flowId}`)
      showMessage.info(i18n.get('附件异步导出中,请稍后到导出管理中查看!'))
    } else {
      showMessage.info(i18n.get('无可导出附件或附件是钉盘上传'))
    }
  }

  getDiffs = async (type, curId, prevId) => {
    const privilegeId = get(this.props, 'privilegeId')
    const curVersion = await getBillHistoryVersionDetail(curId, privilegeId)
    const prevVersion = await getBillHistoryVersionDetail(prevId, privilegeId)
    const diffs = await getDiffsBetweenVersions(type, curVersion, prevVersion)
    return diffs
  }
  handleViewDiff = e => {
    this.showViewDiffModal(e)
  }

  /**
   *
   * @param e {React.MouseEvent}
   */
  showViewDiffModal = e => {
    this.setState({
      isShowDiffModal: true,
      initPosition: {
        x: e.clientX - 375,
        y: e.clientY
      }
    })
    this.forceUpdate()
  }

  hideViewDiffModal = () => {
    this.setState({
      isShowDiffModal: false,
      initPosition: undefined
    })
    this.forceUpdate()
  }

  getHistoryVersions = async data => {
    const privilegeId = get(this.props, 'privilegeId')
    const res = await getBillHistoryVersionList(data?.id, privilegeId)
    const hasDocumentType = await this.CheckHasDocumentType(res?.items?.[0]?.id)
    this.setState({
      historyVersions: res.items,
      hasDocumentType
    })
  }

  CheckHasDocumentType = async curId => {
    if (!curId) {
      return false
    }
    const privilegeId = get(this.props, 'privilegeId')
    const firstVersion = await getBillHistoryVersionDetail(curId, privilegeId)
    return !!firstVersion?.value?.form?.details
  }

  fileCanBeDownload = () => {
    return api.getState('@common').fileCanDownload
  }

  render() {
    let {
      isEditConfig,
      onOpenOwnerLoanList,
      backlogType,
      source,
      currRoleForMine,
      showExpressButton,
      privilegeId,
      showAllFeeType,
      size,
      offsetWidth,
      autoOpenDetailId,
      hasPopupTitle,
      canActivate = false,
      customStyle = {},
      bottomToolbar,
      hiddenFlowLinks = false,
      showFooter = true,
      isFromLoanManage = false,
      privilegeAcitons = [],
      staffMe,
      showFullScreenDrawer,
      showHeaderClose,
      layer,
      showBillMore,
      scene,
      needConfigButton = false
    } = this.props
    const { riskData, historyVersions, isShowDiffModal = false, initPosition, showWidget } = this.state
    let backlog = this.state.backlog || this.props.backlog
    //backlog.id=-1说明改backlog流程已经结束
    if (this.state.backlog && this.props.backlog && this.props.backlog.id === -1) {
      this.state.backlog.id = -1
    }
    let { flowId, ownerId } = backlog
    if (!flowId) {
      return false
    }
    const params = { canActivate }
    let isCarbonCopy = backlogType && backlogType === 'carbonCopy'
    let isSuppleInvoiceBtn = flowId.state !== 'rejected' && flowId.state !== 'draft'
    let footer = this.getFooter({ currRoleForMine, isCarbonCopy, flowId, backlog, bottomToolbar, params })
    flowId.countDownDuration = backlog.countDownDuration
    flowId.autoApproveType = backlog.autoApproveType
    //控制打印按钮的隐藏显示
    footer = isPrintShow({ selectedRowsData: backlog })
      ? footer
      : footer.filter(line => line.label !== '打印单据' && line.label !== '打印单据和发票' && line.label !== '打印提醒')
    // 轻共享MC里这三个按钮需要隐藏
    if (window.__PLANTFORM__ === 'MC') {
      footer = footer.filter(
        line => line.label !== '打印提醒' && line.label !== '转交审批' && line.label !== '加签审批'
      )
    }

    // 根据条件判断是否添加分享按钮
    fnPushDingTalkShareBtn(footer, get(backlog, 'flowId.id'))

    const dataSource = get(backlog, 'flowId.form')
    const nodes = get(backlog, 'flowId.plan.nodes', [])
    const taskId = get(backlog, 'flowId.plan.taskId', '')
    const node = nodes.find(v => v.id === taskId) || {}
    const fromDataLink = get(this.props, 'fromDataLink')

    this.getWithdrawFooter(nodes, node, this.props?.params?.__status, footer, backlog, flowId.state)

    if (!isFromLoanManage || !privilegeId || privilegeAcitons.includes('freeflow.export')) {
      const exportAttachment = {
        action: FlowAction.ExportAttachment,
        label: i18n.get('导出全部附件'),
        onClick: e => this.exportAttachment(),
        buttonProps: {
          'data-testid': 'bill-footer-exportattachment'
        }
      }
      if (!this.fileCanBeDownload()) {
        exportAttachment.disabled = true
        exportAttachment.toolTipText = i18n.get('您无权进行此操作，可联系管理员授权')
      }
      const submitNode = get(backlog, 'flowId.plan.submitNode')
      if (canDownAttachmentFLow(staffMe, node, submitNode)) {
        footer.unshift(exportAttachment)
      }
    }
    //是否使用配置按钮
    const useConfigButton = needConfigButton ? !isCarbonCopy && !bottomToolbar?.length : needConfigButton

    return (
      <div id="bill-detail" style={customStyle} className={style['bill-detail']} data-locale={i18n.currentLocale}>
        <BillInfoReadOnlyContainer
          showAllFeeType={showAllFeeType}
          onOpenOwnerLoanList={onOpenOwnerLoanList && onOpenOwnerLoanList.bind(this, backlog)}
          renderRiskTips={this.renderRiskTips()}
          bus={this.bus}
          showHeaderClose={showHeaderClose}
          showFullScreenDrawer={showFullScreenDrawer}
          layer={layer}
          showExpressButton={showExpressButton}
          dataSource={flowId}
          suppleInvoiceBtn={isSuppleInvoiceBtn}
          source={source}
          privilegeId={privilegeId}
          isEditConfig={isEditConfig}
          detailStack={autoOpenDetailId ? { id: autoOpenDetailId } : undefined}
          isShowWarningTips={isCarbonCopy}
          noRiskWarning={backlogType === 'carbonCopy'}
          riskData={riskData}
          offsetWidth={offsetWidth}
          size={size}
          isFlowEditable={node.allowModify}
          backLogOwnerId={ownerId}
          isBacklog={!!backlog}
          showWidget={showWidget}
          hiddenFlowLinks={hiddenFlowLinks}
          hasPopupTitle={hasPopupTitle}
          showBillMore={showBillMore}
          canDeleteComment={currRoleForMine?.actions?.includes('freeflow.deleteComment')}
          billFooter={
            showFooter && !fromDataLink && (
              <BillInfoFooter
                footer={footer}
                flowId={flowId?.id}
                scene={scene}
                privilegedId={privilegeId}
                needConfigButton={useConfigButton}
                actionMap={this.getActionMap(backlog)}
              />
            )
          }
        />
        <BillVersionDiffModal
          visible={isShowDiffModal}
          versions={historyVersions}
          initPosition={initPosition}
          riskData={getRiskReasonDataForVersionDiffModal(riskData)}
          dataSource={dataSource}
          getDiffs={this.getDiffs}
          onClose={this.hideViewDiffModal}
        />
      </div>
    )
  }
}
