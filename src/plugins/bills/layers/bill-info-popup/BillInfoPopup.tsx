
/**************************************************
 * Created by kaili on 2017/8/8 下午12:38.
 **************************************************/

import React, { Component } from 'react'
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager'
import BillDetail from './BillDetail'
import { observer } from 'mobx-react'
import { inject, provider } from '@ekuaibao/react-ioc'
import { PermissionVm } from '../../vms/Permission.vm'
import { connect } from '@ekuaibao/mobx-store'
import './BillinfoPopup.less'
import style from './BillDetail.module.less'
import { startLoadFlowPerformanceStatistics } from '../../../../lib/flowPerformanceStatistics'
import { billDrawerConfig, ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER } from '../../../../elements/configure/bill-drawer'

@EnhanceDrawer({
  showHeader: false,
  get width() {
    return billDrawerConfig.minWidth
  },
  get currentWidth() {
    return billDrawerConfig.width
  },
  className: `${style['bill-no-modal-header']} ${style['bill-no-modal-mask']}`,
})
@provider(['permission', PermissionVm])
@observer
@connect(store => ({ size: store.states['@layout'].size }))
export default class BillInfoPopup extends Component<any, any> {
  @inject('permission') permission: any
  constructor(props) {
    super(props)
    this.state = {}
    startLoadFlowPerformanceStatistics()
  }
  componentDidMount() {
    this.setState({})
    // 页面点击事件包含菜单可空白地方的点击,菜单的点击通过stopPropagation来阻止；弹框/抽屉页面中的点击不会监听
    const layout = document.getElementById('layout-wrapper')
    layout?.addEventListener?.('click', this.handleClick)
  }

  componentWillUnmount() {
    const layout = document.getElementById('layout-wrapper')
    layout?.removeEventListener?.('click', this.handleClick)
  }

  handleClick = (e: any) => {
    if (ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER.includes(e.target?.id)) {
      return
    }
    this.props.layer.emitCancel()
  }

  render() {
    const { backlog, bus, params, title, showFooter, ...others } = this.props
    const billDetail:HTMLDivElement = document.getElementById('bill-detail')
    const param = { ...others, backlog, bus, params, title, showFooter, hasPopupTitle: true }
    if (billDetail) {
      param.offsetWidth = billDetail.offsetWidth
    }
    return <BillDetail {...param} showFullScreenDrawer ={true} showHeaderClose = {true} />
  }
}
