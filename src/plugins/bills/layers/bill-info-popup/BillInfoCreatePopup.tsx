import React, { Component, createContext } from 'react'
import { EnhanceDrawer, Layer } from '@ekuaibao/enhance-layer-manager'
import RightPart from '../../parts/right-part/RightPart'
import type { MessageCenter } from '@ekuaibao/messagecenter'

export const BillInfoCreatePopupContext = createContext({ createByPopupDrawer: false })

type IBusOther = {
    reload: () => void
}
interface IBillInfoCreatePopupProps {
    bus: MessageCenter & IBusOther
    data: any
    layer: any
    line: any
}
@EnhanceDrawer({
    width: 750,
    mask:true,
    class: {
        left: 'drag-hover'
    }
})
export default class BillInfoCreatePopup extends Component<IBillInfoCreatePopupProps> {
    constructor(props) {
        super(props)
        props.overrideGetResult(this.getResult)
    }
    componentDidMount(): void {
        const { data, bus, line } = this.props
        bus.emit('list:line:click', {
            formType: data.type,
            state: 'new',
            currentSpecification: data,
            requisitionInfo: line?.requisitionInfo
        })
    }

    getResult = () => {
        const { bus } = this.props
        return bus.invoke('check:value:changed').then(() => {
            return {}
        })
    }
    handleSave(refresh?: boolean) {
        const { layer, bus } = this.props
        refresh && bus.reload()
        layer.emitCancel()
    }
    handleCancel() { }
    render(): any {
        const { bus, layer } = this.props
        const cancel = this.handleCancel.bind(this)
        const save = this.handleSave.bind(this)
        return (<BillInfoCreatePopupContext.Provider value={{ createByPopupDrawer: true }}>
            <RightPart bus={bus}
                       handleCreatePopupSave={save}
                       showFullScreenDrawer = {true}
                       showHeaderClose = {true}
                       layer = {layer}
                       closeDrawer={cancel}
                       scene={'OWNER'}
                       createByPopupDrawer={true}
                       showUpDown={false}
            />
        </BillInfoCreatePopupContext.Provider>)
    }
}
