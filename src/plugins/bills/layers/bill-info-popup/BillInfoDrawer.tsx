import React, { Component } from 'react'
import BillInfo from './BillInfo'
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager'
import style from './BillDetail.module.less'
import { billDrawerConfig, listenerToDrawerResize, ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER } from '../../../../elements/configure/bill-drawer'


@EnhanceDrawer({
  get width() {
    return billDrawerConfig.minWidth
  },
  get currentWidth() {
    return billDrawerConfig.width
  },
  className: `${style['bill-no-modal-header']} ${style['bill-no-modal-mask']}`
})
export default class BillInfoDrawer extends Component {
  _removeListenerToDrawerResize: () => void
  constructor(props) {
    super(props)
  }

  componentDidMount() {
    // 页面点击事件包含菜单可空白地方的点击,菜单的点击通过stopPropagation来阻止；弹框/抽屉页面中的点击不会监听
    const layout = document.getElementById('layout-wrapper')
    layout?.addEventListener?.('click', this.handleClick)

    this._removeListenerToDrawerResize = listenerToDrawerResize()
  }

  componentWillUnmount() {
    const layout = document.getElementById('layout-wrapper')
    layout?.removeEventListener?.('click', this.handleClick)

    this._removeListenerToDrawerResize?.()
  }

  handleClick = async e => {
    const { bus } = this.props

    // 单据详情的编辑态会有是否修改的验证
    if (bus.has('check:value:changed')) {
      try {
        await bus.invoke('check:value:changed')
        if (ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER.includes(e.target?.id)) {
          return
        }
        this.props.layer.emitCancel()
      } catch (error) {
        e.stopPropagation()
      }
    } else {
      if (ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER.includes(e.target?.id)) {
        return
      }
      this.props.layer.emitCancel()
    }
  }

  render() {
    return <BillInfo {...this.props} />
  }
}
