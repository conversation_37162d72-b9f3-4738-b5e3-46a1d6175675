/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/20.
 */
import React, { PureComponent, createElement } from 'react'
import styles from './RightBottomLine.module.less'
import Money from '../../../elements/puppet/Money'
import { app as api } from '@ekuaibao/whispered'
import MoneyNzh from '../../../elements/puppet/MoneyNzh'
import { map, cloneDeep, get } from 'lodash'
import classNames from 'classnames'
import { isNegat, getV } from '@ekuaibao/lib/lib/help'
import { Popover } from 'antd'
import { getStaffName } from '../../../elements/utilFn'
import { formattedMoneyForCrossCurrency } from './WrittenOffMoneyDetails'
import { Resource } from '@ekuaibao/fetch'
import { OutlinedDataLog } from '@hose/eui-icons'
import { NameCell } from '../../../elements/name-cell'
const { isDismissLocalCurrencyLabel } = api.require('@lib/featbit');

export function formatToChineseMoney(value, isNegative, isShowForeignCNY) {
  const valueC = isShowForeignCNY ? value?.foreign : value?.standard
  const money = typeof value === 'object' ? valueC : value
  const standardCurrency = api.getState()['@common'].standardCurrency
  const moneyNum = Number(money)
  const showForeignCNY = isShowForeignCNY && 'CNY' === value?.foreignStrCode
  if (
    'zh-CN' === i18n.currentLocale &&
    !isNaN(moneyNum) &&
    (('CNY' === standardCurrency.strCode && 'CNY' === value?.standardStrCode && !isShowForeignCNY) || showForeignCNY)
  ) {
    const valNum = isNegative && moneyNum !== 0 ? moneyNum * -1 : moneyNum
    return MoneyNzh.toMoney(valNum, { outSymbol: false })
  }
  return null
}

const wordAndMoney = function (amount, isSecond, isNegative, dimentionCurrency, notShowForeign = false, allowSelectionReceivingCurrency = false) {
  const foreignStrCode = getV(amount, 'foreignStrCode', '')
  const standardStrCode = getV(
    amount,
    'standardStrCode',
    getV(api.getState()['@common'], 'standardCurrency.strCode', '')
  )
  let strCode = dimentionCurrency && !allowSelectionReceivingCurrency ? dimentionCurrency.strCode : standardStrCode
  let isShowForeign = false
  if (isSecond) {
    isShowForeign = foreignStrCode ? true : false
  }
  if (isSecond) {
    strCode = foreignStrCode ? foreignStrCode : standardStrCode
  }
  const isForeign = foreignStrCode && isSecond
  const isBlod = isForeign || (!foreignStrCode && standardStrCode) || notShowForeign
  const dismissLocalCurrencyLabel = isDismissLocalCurrencyLabel()
  const text = !allowSelectionReceivingCurrency ? (dismissLocalCurrencyLabel ? '' : i18n.get('本位币')) : ''
  const standardPrefix = amount?.allowReceivingCurrency || (foreignStrCode && !isSecond && dismissLocalCurrencyLabel) ? i18n.get('折合') : text
  const foreignPrefix = amount?.allowReceivingCurrency ? '' : (dismissLocalCurrencyLabel ? '' : (dismissLocalCurrencyLabel ? '' : i18n.get('原币')))

  return (
    <>
      <span className="mr-4 prefix"> {isForeign ? foreignPrefix : standardPrefix}</span>
      <span className={classNames({ 'color-black-1': isBlod, 'f-fd mr-4': true, 'fs-16': !amount?.foreign && amount?.foreign !== 0 }, 'ignore-translate')}>{strCode}</span >
      <Money
        className={classNames({ 'color-black-1': isBlod, 'fs-16': !amount.foreign && amount?.foreign !== 0 })}
        currencySize={16}
        valueSize={16}
        value={amount}
        isNegative={isNegative}
        withoutStyle={true}
        showSymbol={false}
        isShowForeign={isShowForeign}
        showForeignNum={false}
      />
    </>
  )
}

const wordAndMoneyForReceipt = function (amount, isSecond, isNegative, dimentionCurrency) {
  const foreignStrCode = getV(amount, 'foreignStrCode', '')
  const standardStrCode = getV(
    amount,
    'standardStrCode',
    getV(api.getState()['@common'], 'standardCurrency.strCode', '')
  )
  let strCode = dimentionCurrency ? dimentionCurrency.strCode : standardStrCode
  let isShowForeign = false
  if (isSecond) {
    isShowForeign = foreignStrCode ? true : false
  }
  if (isSecond) {
    strCode = foreignStrCode ? foreignStrCode : standardStrCode
  }
  const isForeign = foreignStrCode && isSecond
  const isBlod = isForeign || (!foreignStrCode && standardStrCode)
  return (
    <>
      <span className="mr-5">
        {isDismissLocalCurrencyLabel() ? null : <span className="mr-5">{isForeign ? i18n.get('原币') : i18n.get('本位币')}</span>}
        <span className={classNames({ 'color-black-1': isBlod })}>{strCode}</span>
      </span>
      <Money
        className={classNames({ 'color-black-1': isBlod })}
        currencySize={16}
        valueSize={16}
        value={amount}
        isNegative={isNegative}
        withoutStyle={true}
        showSymbol={false}
        isShowForeign={isShowForeign}
        showForeignNum={false}
      />
    </>
  )
}

const formattedMoneyForReceipt = function (readOnly, key, label, money, isNegative, dimentionCurrency) {
  return (
    <div className="amount" key={key}>
      <div className="text-col">
        <div className={readOnly ? 'text' : 'text_no_after'}>{label}</div>
      </div>
      <div className="money-col">
        <div className="money">{wordAndMoneyForReceipt(money, false, isNegative, dimentionCurrency)}</div>
        <div className="chinese-money">{formatToChineseMoney(money, isNegative)}</div>
      </div>
    </div>
  )
}

const formattedMoney = function (
  readOnly,
  key,
  label,
  money,
  isNegative,
  writtenOffSummaryForMutiCurrency,
  dimentionCurrency,
  notShowForeign = false,
  allowSelectionReceivingCurrency = false
) {
  const showForeign = !notShowForeign
  return (
    <div className="amount" key={key}>
      <div className="text-col">
        <div className={readOnly ? 'text' : 'text_no_after'}>{label}</div>
      </div>
      <div className="money-col">
        {showForeign &&
          renderForeignMoney(key, money, isNegative, writtenOffSummaryForMutiCurrency, allowSelectionReceivingCurrency)}
        <div className="money">
          {wordAndMoney(money, false, isNegative, dimentionCurrency, !showForeign, allowSelectionReceivingCurrency)}
        </div>
        <div className="chinese-money">{formatToChineseMoney(money, isNegative)}</div>
        {key === 'expenseMoney' && renderBudgetMoney(key, money, isNegative, writtenOffSummaryForMutiCurrency)}
      </div>
    </div>
  )
}

const renderForeignLines = (dataArr = [], isNegative) => {
  const standardCurrency = api.getState()['@common'].standardCurrency

  return (
    <>
      {dataArr.map(el => {
        const money =
          el.currencyStrCode === standardCurrency.strCode
            ? { standard: el.moneySum, standardStrCode: el.currencyStrCode }
            : { foreign: el.moneySum, foreignStrCode: el.currencyStrCode }
        return <div className="foreign-money">{wordAndMoney(money, true, isNegative)}</div>
      })}
    </>
  )
}

const renderForeignMoneyByKey = (key, money, isNegative, writtenOffSummaryForMutiCurrency = {}) => {
  const {
    expenseMoneySumByCurrency,
    writtenOffSumByCurrency,
    payMoneySumByCurrency,
    writtenOffByDetails,
    profitAndLossSummary,
    payMoneyByDetails,
    companyRealPaySumByCurrency,
    writtenOffDataObj
  } = writtenOffSummaryForMutiCurrency
  if (!writtenOffDataObj) return null
  const { writtenOffTotalInStandard } = writtenOffDataObj

  switch (key) {
    case 'expenseMoney': {
      return renderForeignLines(expenseMoneySumByCurrency)
    }
    case 'writtenOffMoney': {
      return (
        <>
          {renderForeignLines(writtenOffSumByCurrency, isNegative)}
          {renderWrittenOffToolTip(writtenOffByDetails, profitAndLossSummary, writtenOffTotalInStandard, isNegative)}
        </>
      )
    }
    case 'paybackMoney': {
      return (
        <>
          {renderForeignLines(writtenOffSumByCurrency, isNegative)}
          {renderWrittenOffToolTip(writtenOffByDetails, profitAndLossSummary, writtenOffTotalInStandard, isNegative)}
        </>
      )
    }
    case 'payMoney': {
      return (
        <>
          {renderForeignLines(payMoneySumByCurrency)}
          {renderPayMoneyToolTip(payMoneyByDetails)}
        </>
      )
    }
    case 'companyPayMoney': {
      return renderForeignLines(companyRealPaySumByCurrency)
    }
  }
}

const renderPayMoneyToolTip = payMoneyByDetails => {
  if (!(payMoneyByDetails && payMoneyByDetails.length)) return null
  const standardCurrency = api.getState()['@common'].standardCurrency
  const payTotal = payMoneyByDetails
    .map(item => item.payMoneyModel.standard)
    .reduce((a, b) => {
      if (!isNaN(a) && !isNaN(b)) {
        return new Big(a).plus(b).valueOf()
      }
      return 0
    })
  const content = (
    <>
      <p className="payMoneyTitle">{`${i18n.get('支付金额：')}${standardCurrency.strCode} ${payTotal}`}</p>
      <hr />
      {payMoneyByDetails.map((el, idx) => {
        const { name, code, payMoneyModel } = el
        const text = payMoneyModel.foreignStrCode
          ? `${name}(${code}) ${payMoneyModel.foreignStrCode} ${payMoneyModel.foreign} x ${i18n.get('当前汇率')}${payMoneyModel.rate
          }`
          : `${name}(${code}) ${payMoneyModel.standardStrCode} ${payMoneyModel.standard}`
        return <p key={idx}>{text}</p>
      })}
    </>
  )
  return (
    <Popover content={content} trigger="hover" getPopupContainer={triggerNode => triggerNode.parentNode}>
      <span className="bottom-line-popover-btn">{i18n.get('查看明细')}</span>
    </Popover>
  )
}

const renderWrittenOffToolTip = (writtenOffByDetails, profitAndLossSummary, writtenOffTotalInStandard, isNegative) => {
  const standardCurrency = api.getState()['@common'].standardCurrency
  const { totalProfitAndLoss, profitAndLossByLoans } = profitAndLossSummary || {}

  const content = (
    <>
      <p className="writtenOffTitle">{`${i18n.get('核销金额：')}${standardCurrency.strCode} ${isNegative &&
        '-'} ${writtenOffTotalInStandard}`}</p>
      <hr />
      <>
        {writtenOffByDetails.map((el, idx) => {
          const { name, code, amount, writtenOffRateType, loanRate } = el
          const rateText = amount.foreign
            ? ` x ${writtenOffRateType === 'REAL_TIME'
              ? `${i18n.get('当前汇率')} ${amount.rate}`
              : `${window.IS_SMG ? i18n.get('预支汇率') : i18n.get('借款汇率')} ${loanRate}`
            }`
            : ''
          return (
            <p key={idx}>{`${name}(${code}) ${amount.foreignStrCode || amount.standardStrCode} ${amount.foreign ||
              amount.standard}${rateText}`}</p>
          )
        })}
      </>
      {profitAndLossSummary && (
        <>
          <p className="profitTitle">{`${i18n.get('汇兑损益：')} ${totalProfitAndLoss.currencyStrCode} ${totalProfitAndLoss.moneySum
            }`}</p>
          <hr />
          {profitAndLossByLoans.map(loan => {
            const { name, currencyStrCode, moneySum, profitAndLossByDetails, loanInfoId } = loan
            return (
              <>
                <p key={loanInfoId}>
                  {i18n.get(`借款包{__k0}，汇兑损益{__k1} {__k2}`, {
                    __k0: name,
                    __k1: currencyStrCode,
                    __k2: moneySum
                  })}
                </p>
                {profitAndLossByDetails.map((detail, idx) => {
                  const {
                    name,
                    code,
                    foreignStrCode,
                    foreignMoneyValue,
                    realTimeRate,
                    loanRate,
                    standardStrCode,
                    profitAndLossValue
                  } = detail
                  return (
                    <p key={idx}>
                      {`${name}(${code}) ${foreignStrCode} ${foreignMoneyValue} x (${i18n.get(
                        '当前汇率'
                      )}${realTimeRate} - ${i18n.get(
                        '借款汇率'
                      )}${loanRate}) = ${standardStrCode} ${profitAndLossValue}`}
                    </p>
                  )
                })}
              </>
            )
          })}
        </>
      )}
    </>
  )
  return (
    <Popover content={content} trigger="hover" getPopupContainer={triggerNode => triggerNode.parentNode}>
      <span className="bottom-line-popover-btn">{i18n.get('查看明细')}</span>
    </Popover>
  )
}

const renderForeignMoney = (key, money, isNegative, writtenOffSummaryForMutiCurrency, allowSelectionReceivingCurrency) => {
  const chargeMultCurrencyWrittenOffIsOpened = api.getState('@common').powers.MULTICURRENCYWRITTENOFF
  if (chargeMultCurrencyWrittenOffIsOpened && key !== 'settlements' && writtenOffSummaryForMutiCurrency && Number(writtenOffSummaryForMutiCurrency?.writtenOffDataObj?.writtenOffTotalInStandard || 0) !== 0) {
    return renderForeignMoneyByKey(key, money, isNegative, writtenOffSummaryForMutiCurrency)
  }

  return (
    <>
      {money && money.foreignStrCode && (allowSelectionReceivingCurrency || Number(money.standard) !== 0) ? (
        <div className="foreign-money">{wordAndMoney(money, true, isNegative, undefined, undefined, allowSelectionReceivingCurrency)}</div>
      ) : null}
    </>
  )
}

const renderBudgetMoney = (key, money, isNegative, writtenOffSummaryForMutiCurrency) => {
  return (
    <>
      {money && money.budgetStrCode && money.standardStrCode !== money.budgetStrCode ? (
        <div className="budget-money">
          <span className="mr-5">
            <span className="mr-5">{i18n.get('预算币')}</span>
            {money.budgetStrCode}
          </span>
          <Money
            currencySize={16}
            valueSize={16}
            value={money.budget}
            isNegative={isNegative}
            withoutStyle={true}
            showSymbol={false}
            isShowForeign={false}
            showForeignNum={false}
          />
        </div>
      ) : null}
    </>
  )
}

const moneyMap = {
  reimbursementMoney: ({ value, key, readOnly, writtenOffSummaryForMutiCurrency, dimentionCurrency, type }) => {
    // if (!readOnly) {
    //   const { label = i18n.get('报账金额'), money } = value
    //   return formattedMoney(key, label, money, undefined, writtenOffSummaryForMutiCurrency, dimentionCurrency)
    // }
    return value ? formattedMoney(readOnly, key, i18n.get('报账金额'), value) : null
  },
  expenseMoney: ({
    value,
    key,
    readOnly,
    writtenOffSummaryForMutiCurrency,
    dimentionCurrency,
    type,
    allowSelectionReceivingCurrency
  }) => {
    const text = type === 'corpPayment' ? i18n.get('付款金额') : i18n.get('报销金额')
    if (!readOnly) {
      const { label = i18n.get('付款金额'), money } = value
      let vv = typeof money === 'object' ? money.standard : money
      return (
        vv * 1 !== 0 &&
        formattedMoney(
          readOnly,
          key,
          label,
          money,
          undefined,
          writtenOffSummaryForMutiCurrency,
          dimentionCurrency,
          undefined,
          allowSelectionReceivingCurrency
        )
      )
    }
    return value
      ? formattedMoney(
        readOnly,
        key,
        text,
        value,
        undefined,
        undefined,
        undefined,
        undefined,
        allowSelectionReceivingCurrency
      )
      : null
  },
  receiptMoney: ({ value, key, readOnly, writtenOffSummaryForMutiCurrency, dimentionCurrency }) => {
    if (!readOnly) {
      const { label, money } = value
      let vv = typeof money === 'object' ? money.standard : money
      return (
        vv * 1 !== 0 &&
        formattedMoney(readOnly, key, label, money, undefined, writtenOffSummaryForMutiCurrency, dimentionCurrency)
      )
    }
    return value ? formattedMoney(readOnly, key, i18n.get('收款金额'), value) : null
  },
  settlementMoney: ({ value, key, readOnly }) => {
    // 结算单的结算金额
    if (!readOnly) {
      const { label, money } = value
      return value ? formattedMoney(readOnly, key, i18n.get('结算金额'), money) : null
    }
    return value ? formattedMoney(readOnly, key, i18n.get('结算金额'), value) : null
  },
  settlements: ({ value = [], key, readOnly }) => {
    //结算方式的结算金额
    return value.map((obj, index) => {
      return obj && settlementMoney(readOnly, obj, `${key}${index}`)
    })
  },
  writtenOffMoney: ({
    value,
    key,
    isMultiplePayee,
    writtenOffSummaryForMutiCurrency,
    dimentionCurrency,
    isReceiptTemplate,
    readOnly,
    writtenOffSummaryForCrossCurrency
  }) => {
    // 多收款人模式下不支持核销
    if (isMultiplePayee) return null
    let vv = typeof value === 'object' ? value.standard : value
    if (typeof value === 'object') {
      value.standard = Math.abs(value.standard)
    } else {
      value = Math.abs(value)
    }

    if (writtenOffSummaryForCrossCurrency) {
      return vv * 1 !== 0 && formattedMoneyForCrossCurrency({ readOnly, money: value, writtenOffSummaryForCrossCurrency })
    }

    return (
      vv * 1 !== 0 &&
      !isReceiptTemplate &&
      formattedMoney(
        readOnly,
        key,
        i18n.get('核销金额'),
        value,
        true,
        writtenOffSummaryForMutiCurrency,
        dimentionCurrency
      )
    )
  },
  paybackMoney: ({ value, key, readOnly, writtenOffSummaryForMutiCurrency, dimentionCurrency, isReceiptTemplate }) => {
    let vv = typeof value === 'object' ? value.standard : value
    if (isReceiptTemplate) {
      return (
        vv * 1 !== 0 &&
        formattedMoneyForReceipt(readOnly, key, i18n.get('还款金额'), value, undefined, dimentionCurrency)
      )
    }
    return (
      vv * 1 !== 0 &&
      formattedMoney(readOnly, key, i18n.get('还款金额'), value, undefined, writtenOffSummaryForMutiCurrency, undefined)
    )
  },
  companyPayMoney: ({ value, key, writtenOffSummaryForMutiCurrency, readOnly }) => {
    let vv = typeof value === 'object' ? value.standard : value
    return (
      vv * 1 !== 0 &&
      formattedMoney(readOnly, key, i18n.get('企业已付金额'), value, true, writtenOffSummaryForMutiCurrency)
    )
  },
  payMoney: ({ value, key, writtenOffSummaryForMutiCurrency, dimentionCurrency, readOnly, payMoneyNotShowForeign, allowSelectionReceivingCurrency, writtenOffSummaryForCrossCurrency }) => {
    const vv = typeof value === 'object' ? value.standard : value
    let isNe = isNegat(vv)
    const myValue = typeof value === 'object' ? cloneDeep(value) : value
    if (typeof myValue === 'object') {
      myValue.standard = Math.abs(myValue.standard)
    } else {
      value = Math.abs(value.standard)
    }
    const notShowForeign = payMoneyNotShowForeign
    return formattedMoney(
      readOnly,
      key,
      i18n.get('支付金额'),
      myValue,
      isNe,
      writtenOffSummaryForMutiCurrency,
      dimentionCurrency,
      notShowForeign,
      allowSelectionReceivingCurrency
    )
  },
  actuallyPaidMoney: ({ value, key, writtenOffSummaryForMutiCurrency, dimentionCurrency, payMoney, readOnly }) => {
    let vv = typeof value === 'object' ? value.standard : value
    const isNe = isNegat(vv)
    const myValue = typeof value === 'object' ? cloneDeep(value) : value
    if (typeof myValue === 'object') {
      const payMoneyStandard = get(payMoney, 'standard')
      const standardValue = Number(myValue.standard)
      if (standardValue === 0 || standardValue === Number(payMoneyStandard)) return null
      myValue.standard = Math.abs(myValue.standard)
    } else {
      return null
    }
    return formattedMoney(
      readOnly,
      key,
      i18n.get('实付金额'),
      myValue,
      isNe,
      writtenOffSummaryForMutiCurrency,
      dimentionCurrency
    )
  }
}


export default class RightBottomLineElement extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      showClassName: false,
      allowViewLoanList: false
    }
  }
  componentDidMount() {
    this.getShowGroupClassNames()
    this.judgeLoanViewAuth()
  }

  componentDidUpdate() {
    this.getShowGroupClassNames()
  }

  getShowGroupClassNames = () => {
    this.setState({ showClassName: this.amountRef?.childNodes?.length > 0 })
  }

  judgeLoanViewAuth = async () => {
    const configRule = new Resource('/api/v2/loan/config')
    const userInfo = await api.invokeService('@common:get:userinfo', {})
    const loanConfig = await configRule.GET('')

    const hasLoanManage = userInfo?.permissions?.includes("LOAN_MANAGE");
    const restrictAccessItem = loanConfig?.items?.find(item => item?.type === "RESTRICT_ACCESS")?.forbid

    if(userInfo?.staff?.id === this.props.submitter?.id){ // 自己看自己的单据
      this.setState({ allowViewLoanList: true })
    } else if (userInfo?.staff?.id === this.props.ownerId?.id) { // 委托人查看单据
      this.setState({ allowViewLoanList: true })
    } else{
      this.setState({ allowViewLoanList: !restrictAccessItem || hasLoanManage });
    }
  }

  render() {
    const {
      onOpenOwnerLoanList,
      canWrittenOff,
      submitter,
      type,
      readOnly,
      isMultiplePayee,
      payPlanMode,
      writtenOffSummaryForMutiCurrency,
      payMoney,
      dimentionCurrency,
      className,
      isReceiptTemplate,
      payMoneyNotShowForeign,
      allowSelectionReceivingCurrency,
      receivingAmount,
      writtenOffSummaryForCrossCurrency
    } = this.props
    const { showClassName, allowViewLoanList } = this.state
    const renderMoney = Object.keys(moneyMap).filter(key => !!this.props[key] || this.props[key] === 0)
    return (
      <div className={showClassName ? className : null}>
        <div className={classNames(styles['bottom-line'], !payPlanMode && styles['border-top-line'])}>
          {allowViewLoanList && onOpenOwnerLoanList &&
            canWrittenOff &&
            type !== 'custom' &&
            type !== 'payment' &&
            type !== 'settlement' &&
            type !== 'reconciliation' &&
            type !== 'receipt' ? (
            <div className="check-loan" onClick={onOpenOwnerLoanList}>
              <OutlinedDataLog style={{ marginRight: 4}} fontSize={16} />
              <span>
                {i18n.get('查看')}
                <NameCell type="user" id={submitter.id} name={getStaffName(submitter)} />
                {i18n.get('whoes-detail-verification')}
              </span>
            </div>
          ) : null}
          <div className="bottom-line-content">
            <div className="amount-wrapper" ref={ref => (this.amountRef = ref)}>
              {map(renderMoney, key => {
                return moneyMap[key]({
                  value: this.props[key],
                  key,
                  readOnly,
                  isMultiplePayee,
                  writtenOffSummaryForMutiCurrency,
                  payMoney,
                  dimentionCurrency,
                  isReceiptTemplate,
                  type,
                  allowSelectionReceivingCurrency,
                  receivingAmount,
                  payMoneyNotShowForeign,
                  writtenOffSummaryForCrossCurrency
                })
              })}
            </div>
          </div>
        </div>
      </div>
    )
  }
}

function settlementMoney(readOnly, obj, key) {
  let { value, label, showNegative } = obj
  let vv = typeof value === 'object' ? value.standard : value
  const isNe = vv * 1 > 0 && showNegative

  if (typeof value === 'object') {
    value.standard = Math.abs(value.standard)
  } else {
    value = Math.abs(value)
  }
  return vv * 1 !== 0 && formattedMoney(readOnly, key, label, value, isNe)
}
