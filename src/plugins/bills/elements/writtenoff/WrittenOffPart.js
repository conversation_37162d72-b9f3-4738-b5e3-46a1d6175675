/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/20.
 */
import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './WrittenOffPart.module.less'
import Money from '../../../../elements/puppet/Money'
import WrittenOffItem from '../../../../elements/puppet/writtenOff/WrittenOffItem'
import ListLayoutItemWrapper from 'ekbc-list-layout/lib/ListLayoutItemWrapper'
import ListLayout from 'ekbc-list-layout/lib/ListLayout'
import ActiveWrapper from '../../../../elements/ekbc-basic/active-wrapper/active-wrapper'
import { total } from '@ekuaibao/lib/lib/lib-util'
import { showModal, showMessage } from '@ekuaibao/show-util'
import { getloanpackageDetailInfo, getLoanPackageList, getLoanCurrencyField, getEffectiveCurrencyInfo } from '../../bills.action'
import { cloneDeep, get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { getMoney } from '../../../../lib/misc'
import { MoneyMath } from '@ekuaibao/money-math'
import { getDetailCalculateMoney, keepTwoDecimalFull } from '../../util/billUtils'
import { uuid, isString } from '@ekuaibao/helpers'
import { parseFormValueAsParam } from '../../util/parse'
import { summarySelectType } from '../../../../components/consts'
import { Button, Checkbox } from '@hose/eui'
import { OutlinedTipsAdd } from '@hose/eui-icons'
import { getWrittenOffRate, getReceiveAmountTotal, getMoneyObj } from './WrittenOffUtils'
import { enableFlowOptimization } from '../../../../lib/featbit'

@EnhanceConnect(
  state => ({
    standardCurrency: state['@common'].standardCurrency,
    allCurrencyRates: state['@common'].allCurrencyRates,
    multiplePayeesMode: state['@bills'].multiplePayeesMode,
    MULTICURRENCYWRITTENOFF: state['@common'].powers.MULTICURRENCYWRITTENOFF, //多币种核销charge
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo
  }),
  { getloanpackageDetailInfo, getLoanPackageList }
)
export default class WrittenOffPart extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = props.bus
    this.state = {
      details: props.details || [],
      currentIndex: 0,
      writtenOffItems: [],
      total: 0,
      writtenOffTotal: 0,
      submitter: props.submitter,
      selectedList: [],
      isAllChecked: false,
      isReceiptTemplate: props.isReceiptTemplate || false,
      initWrittenOffItems: []
    }
    this.writtenOffAmountIsChanged = true
    this.initMutiWrittenOffInfo()
  }

  async componentDidMount() {
    if (!this.props.allCurrencyRates) {
      await api.dataLoader('@common.allCurrencyRates').load()
    }
  }

  // 判断是否允许进行多币种核销
  initMutiWrittenOffInfo = (billSpecification = this.props.billSpecification) => {
    const chargeAgainst = billSpecification.configs.find(item => item.ability === 'chargeAgainst')
    const writtenOffAbility = billSpecification.configs.find(item => item.ability === 'writtenOff') || {}
    const isChargeAgainst = get(chargeAgainst, 'isChargeAgainst', false)

    const { writtenOffRateType, foreignCurrency, crossCurrencyWrittenOffRateType, crossCurrencyWrittenOff } = writtenOffAbility
    this.writtenOffRateType = writtenOffRateType
    this.crossCurrencyWrittenOffRateType = crossCurrencyWrittenOffRateType
    this.crossCurrencyWrittenOff = crossCurrencyWrittenOff
    this.allowSelectionReceivingCurrency = billSpecification?.configs?.find(v => v.ability === 'pay')?.allowSelectionReceivingCurrency
    this.canIUseMultCurrencyWrittenOff = this.props.MULTICURRENCYWRITTENOFF && foreignCurrency && !isChargeAgainst
  }
  componentWillMount() {
    this.bus.watch('get:writtenOff', this.getResult)
    this.bus.on('details:change', this.handleDetailsChange)
    this.bus.on('set:delegator', this.handleSubmitterChange)
    this.bus.on('import:writtenoff:fromApply', this.handleImportWrittenOffFromApply)
    this.bus.on('expenseLink:change', this.listenExpenseLinkChange)
    this.bus.on('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
    this.setInitValue()
  }

  async setInitValue() {
    let { bus, value = [], details = [], expenseLink, MULTICURRENCYWRITTENOFF } = this.props
    expenseLink = expenseLink ? (Array.isArray(expenseLink) ? expenseLink : [expenseLink]) : []
    this.detailTotal = this.fnCalculateDetail(details)
    value = value.filter(v => {

      if (v.loanInfoId && typeof v.loanInfoId === 'object' && !v.id) {
        //如果已经有maxWTAmount，就不用继续计算
        /**
         * item.amount: 单据被借款包核销的金额(不一定是占用金额，占不占用借款包金额由inCalc标识)，
         * item.inCalc: 单据是否占用借款包金额，true：占用，占用金额=核销金额(item.amount) false：不占用金额
         * item.loanInfoId.remain: 借款包剩余可核销金额
         *      item.inCalc=true: 借款包的最大可核销金额=item.amount+item.loanInfoId.remain
         *      item.inCalc=false: 借款包的最大可核销金额=item.loanInfoId.remain
         */
        let remainBig = new Big(v.loanInfoId.remain)
        v.maxWTAmount = remainBig.toFixed(2)
        let relatedIds = this.getRelatedIds(expenseLink)
        v.fromApply = v.loanInfoId.source === 'REQUISITION'
        v.hasImported = expenseLink.length ? relatedIds.includes(v.loanInfoId.flowId) : false
        if (v.inCalc) {
          v.maxWTAmount = new MoneyMath(v.amount).add(v.loanInfoId.remain).fixedValue
        }
        v.id = v.loanInfoId.id
      } else {
        //不确定是不是错误逻辑先加个限制，如果跨币种核销就不走这里
        if (!this.crossCurrencyWrittenOff) {
          v.id = v.loanInfoId
        }
        let remainBig = new Big(v.amount)
        v.maxWTAmount = remainBig.toFixed(2)
      }

      // 开户多币种核销charge的用户，切换模板时，不允许核销外币的模板需要过滤掉已选的外币借款包
      if (!this.crossCurrencyWrittenOff && MULTICURRENCYWRITTENOFF && !this.canIUseMultCurrencyWrittenOff) {
        return !get(v, 'loanInfoId.foreignCurrencyLoan')
      }
      return true
    })

    this.init = true
    if (value?.length && details?.length && this.crossCurrencyWrittenOff) {
      const [standardMoneyObj, receivingMoneyObj] = getMoneyObj(value, details)
      this.rateInfoMap = await getWrittenOffRate(value, receivingMoneyObj, standardMoneyObj)
    }

    // 开户多币种核销charge的用户，切换模板时，需要重新计算初始值
    if (!this.crossCurrencyWrittenOff && MULTICURRENCYWRITTENOFF && !this.canIUseMultCurrencyWrittenOff) {
      this.setWrittenOffRecord(this.detailTotal, value, details)
    }
    bus.emit('billInfoEditable:writtenOff:change', value)
    this.setState({ writtenOffItems: value, hasExpenseLink: !!expenseLink.length, initWrittenOffItems: value })
  }

  componentWillUnmount() {
    this.bus.un('get:writtenOff', this.getResult)
    this.bus.un('details:change', this.handleDetailsChange)
    this.bus.un('set:delegator', this.handleSubmitterChange)
    this.bus.un('import:writtenoff:fromApply', this.handleImportWrittenOffFromApply)
    this.bus.un('expenseLink:change', this.listenExpenseLinkChange)
    this.bus.un('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
    api.invokeService('@bills:clear:written:off:summary')
  }

  handleDimentionMultiCurrencyChange = () => {
    this.setState({
      writtenOffItems: [],
      selectedList: [],
      isAllChecked: false
    })
    this.setWrittenOffRecord(this.detailTotal, [])
  }

  listenExpenseLinkChange = ({ expenseLink, clearData }) => {
    expenseLink = expenseLink ? (Array.isArray(expenseLink) ? expenseLink : [expenseLink]) : []
    let writtenOffItems = this.state.writtenOffItems.slice(0)
    let self = this
    let callback = () => {
      let relatedIds = this.getRelatedIds(expenseLink)
      writtenOffItems.forEach(item => {
        item.hasImported = expenseLink.length ? relatedIds.includes(item.loanInfoId.flowId) : false
      })
      self.setState({ writtenOffItems, hasExpenseLink: !!expenseLink.length })
    }
    if (writtenOffItems.filter(item => item.hasImported).length && clearData) {
      showModal.confirm({
        content: i18n.get('当前核销的借款包与所关联的申请事项不一致，是否清空？'),
        onOk() {
          self.setState({ writtenOffItems: [] })
        },
        onCancel() {
          callback()
        }
      })
    } else {
      callback()
    }
  }

  getResult = (isCheck = false, isChangeSpecification = false, mustWrittenOff = false) => {
    let result = cloneDeep(this.state.writtenOffItems)
    //给外币借款包添加foreignAmount属性
    result = result.map(el => {
      const foreignWrittenOff = el.foreignAmount
      if (foreignWrittenOff) {
        el.amount = foreignWrittenOff.standard
        el.foreignAmount = foreignWrittenOff.foreign
      } else {
        delete el.foreignAmount
      }

      return el
    })
    if (isChangeSpecification) {
      return result.map(line => {
        if (typeof line.amount === 'object') {
          line.amount = line.amount.standard || 0
        }
        return line
      })
    }
    return this.showLoanModal(isCheck, mustWrittenOff)
      .then(_ => {
        result.forEach(line => {
          if (typeof line.loanInfoId === 'object') {
            line.loanInfoId = line.loanInfoId.id
          }
          if (typeof line.amount === 'object') {
            line.amount = line.amount.standard || 0
          }
          delete line.id
          delete line.maxWTAmount
          delete line.manualAmount
        })
        return result.filter(v => v.amount * 1 !== 0)
      })
      .catch(error => {
        return error
      })
  }

  handleSubmitterChange = submitter => {
    let prevSubmitter = this.state.submitter
    if (submitter.id !== prevSubmitter.id) {
      this.setState({ submitter, writtenOffItems: [] }, () => {
        this.bus.emit('writtenOff:changed', 0)
      })
    }
  }

  handleDetailsChange = async details => {
    const detailsArr = this.fnSetDetailsId(details)
    const detailTotal = this.fnCalculateDetail(details)
    this.writtenOffAmountIsChanged = this.detailTotal === detailTotal
    this.detailTotal = detailTotal
    this.initMutiWrittenOffInfo()
    if (this.crossCurrencyWrittenOff && this.state.writtenOffItems.length) {
      const [standardMoneyObj, receivingMoneyObj] = getMoneyObj(this.state.writtenOffItems, details)
      this.rateInfoMap = await getWrittenOffRate(this.state.writtenOffItems, receivingMoneyObj, standardMoneyObj)
    }
    this.setWrittenOffRecord(this.detailTotal, undefined, detailsArr)
  }

  handleListChange = data => {
    let writtenOffItems = data
    //切换模版
    if (Array.isArray(data) && data.filter(v => v === undefined).length > 0) {
      writtenOffItems = this.state.writtenOffItems
    }

    if (this.init && Array.isArray(data) && this.props.value && this.crossCurrencyWrittenOff) {
      const ids = data.map(i => i?.id)?.join(',')
      const prevIds = this.props.value?.map(i => i?.id)?.join(',')
      if (ids !== prevIds) {
        this.init = false
      }
    }
    this.setWrittenOffRecord(this.detailTotal, writtenOffItems, this.state.details, true)
  }

  handleItemRemove = dataSource => {
    this.init = false
    const { isReceiptTemplate, initWrittenOffItems } = this.state
    let data = isReceiptTemplate ? initWrittenOffItems.slice(0) : this.state.writtenOffItems.slice(0)
    let idx = data.findIndex(o => o.loanInfoId.id === dataSource.loanInfoId.id)
    data.splice(idx, 1)
    this.setWrittenOffRecord(this.detailTotal, data)
  }

  fnCalculateDetail(details) {
    let cloneDetails = enableFlowOptimization() ? details : cloneDeep(details)
    let { payDetail } = getDetailCalculateMoney(cloneDetails, 'expense')
    let detailAmount = payDetail.items.map(d => {
      let { feeTypeForm } = d
      if (feeTypeForm.companyRealPay) {
        const amount = feeTypeForm.amount ? feeTypeForm.amount.standard : 0
        return amount - feeTypeForm.companyRealPay.standard
      }
      return feeTypeForm.amount ? feeTypeForm.amount.standard : '0.00'
    })

    return total(detailAmount)
  }

  // 核销金额是否自己编辑过
  isEdited(list) {
    const index = list.findIndex(line => {
      let { maxWTAmount, manualAmount } = line
      return manualAmount && maxWTAmount !== manualAmount
    })
    return index > -1
  }

  setWrittenOffByCrossCurrency = (detailAmount, writtenOffItems, details = this.state.details) => {
    try {
      let data = cloneDeep(writtenOffItems)
      // 汇兑损益汇总
      const profitAndLossSummary = {
        totalProfitAndLoss: {
          currencyStrCode: '',
          moneySum: '0'
        },
        profitAndLossByLoans: []
      }
      const [standardMoneyObj, receivingMoneyObj] = getMoneyObj(writtenOffItems, details)

      const IS_REAL_TIME = this.crossCurrencyWrittenOffRateType === 'REAL_TIME'
      let amountBig = new Big(detailAmount)
      if (receivingMoneyObj) {
        amountBig = new Big(getReceiveAmountTotal(details))
      }

      const rateInfoMap = this.rateInfoMap
      data.forEach(line => {
        const { loanInfoId, manualAmount, maxWTAmount } = line
        const foreignMoneyObj = cloneDeep(loanInfoId?.foreignCurrencyLoan)
        const { loanRate, realRate, receiveRealStandardRate, receiveLoanStandardRate, receiveLoanRate, receiveRealRate } = rateInfoMap[loanInfoId?.id]
        this.receiveRealStandardRate = receiveRealStandardRate
        const calcRate = IS_REAL_TIME ? realRate : loanRate
        const receiveRate = IS_REAL_TIME ? receiveRealRate : receiveLoanRate
        const receiveStandardRate = IS_REAL_TIME ? receiveRealStandardRate : receiveLoanStandardRate

        const standardScale = Number(standardMoneyObj?.standardScale || 2)
        const foreignScale = Number(foreignMoneyObj?.foreignCurrencyLoan || 2)
        const receiveScale = Number(receivingMoneyObj?.foreignScale || 2)

        let loanAmountBig = new Big(0)
        if (this.init) {
          const foreign = line.foreignAmount?.foreign ?? line.foreignAmount
          loanAmountBig = line.foreignAmount ? new Big(foreign) : new Big(line.amount)
        } else if (!isNaN(Number(manualAmount))) {
          loanAmountBig = new Big(manualAmount)
        } else if (foreignMoneyObj && !isNaN(Number(loanInfoId?.foreignRemain))) {
          loanAmountBig = new Big(loanInfoId?.foreignRemain)
        } else if (!isNaN(Number(maxWTAmount))) {
          loanAmountBig = new Big(maxWTAmount)
        }


        if (foreignMoneyObj && !receivingMoneyObj) {
          loanAmountBig = loanAmountBig.times(calcRate)
        }
        //有收款金额的本位币借款包
        if (receivingMoneyObj && !foreignMoneyObj) {
          loanAmountBig = loanAmountBig.times(receiveStandardRate)
        }
        //有收款金额的外币借款包
        if (receivingMoneyObj && foreignMoneyObj) {
          loanAmountBig = loanAmountBig.times(receiveRate)
        }

        let writtenOffMoneyBig = new Big(0)
        if (amountBig.eq(0)) {
          writtenOffMoneyBig = new Big(0)
        } else if (loanAmountBig.gte(amountBig)) {
          writtenOffMoneyBig = amountBig
          amountBig = new Big(0)
        } else {
          writtenOffMoneyBig = loanAmountBig
          amountBig = amountBig.minus(loanAmountBig)
        }

        if (foreignMoneyObj && !receivingMoneyObj) {
          line.foreignAmount = {
            ...foreignMoneyObj,
            standard: writtenOffMoneyBig.toFixed(standardScale),
            foreign: writtenOffMoneyBig.div(calcRate).toFixed(foreignScale),
          }
          line.rate = calcRate
          line.amount = writtenOffMoneyBig.toFixed(standardScale)
        } else if (receivingMoneyObj) {
          line.receivingLoanAmount = writtenOffMoneyBig.toFixed(receiveScale)
          const stanardAmountBig = writtenOffMoneyBig.div(receiveStandardRate)
          line.amount = stanardAmountBig.toFixed(standardScale)
          line.rate = calcRate
          line.receivingLoanRate = receiveStandardRate
          if (foreignMoneyObj) {
            line.receivingLoanRate = receiveRate
            line.foreignAmount = {
              ...foreignMoneyObj,
              standard: line.amount,
              foreign: writtenOffMoneyBig.div(receiveRate).toFixed(foreignScale)
            }
          }
        } else {
          line.amount = writtenOffMoneyBig.toFixed(standardScale)
        }

        if (foreignMoneyObj && IS_REAL_TIME && Number(realRate) !== Number(loanRate)) {
          const rateDiff = new Big(realRate).minus(loanRate).valueOf()
          const lossMoney = new Big(line.foreignAmount.foreign).times(rateDiff).toFixed(standardScale)
          profitAndLossSummary.totalProfitAndLoss.currencyStrCode = standardMoneyObj.standardStrCode
          const lossProfitItem = {
            loanInfoId: loanInfoId.id,
            name: loanInfoId.title,
            moneySum: lossMoney,
            foreignStrCode: foreignMoneyObj.foreignStrCode,
            foreign: line.foreignAmount.foreign,
            rate: loanRate,
            realRate: realRate,
            profitAndLossByDetails: [],//后台需要没有用
            currencyStrCode: standardMoneyObj.standardStrCode
          }
          profitAndLossSummary.profitAndLossByLoans.push(lossProfitItem)
        }


        return line
      })

      if (profitAndLossSummary.profitAndLossByLoans.length) {
        profitAndLossSummary.totalProfitAndLoss.moneySum = profitAndLossSummary.profitAndLossByLoans.reduce((prev, next) => {
          return new Big(prev).plus(next.moneySum).toFixed(standardMoneyObj.standardScale || 2)
        }, 0)
      }

      const writtenOffTotal = total(data.map(v => getMoney(v.amount) || 0)).toString()
      const writtenOffReceivingTotal = total(data.map(v => getMoney(v.receivingLoanAmount) || 0)).toString()
      this.receivingMoneyObj = receivingMoneyObj
      this.bus.emit('billInfoEditable:writtenOff:change', writtenOffItems)
      this.setState({ writtenOffTotal, writtenOffReceivingTotal, writtenOffItems: data }, () => {
        let totalMoneyObj = {
          ...standardMoneyObj,
          standard: new Big(writtenOffTotal).toFixed(standardMoneyObj.standardScale || 2),
        }

        if (receivingMoneyObj) {
          totalMoneyObj = {
            ...totalMoneyObj,
            foreign: new Big(writtenOffReceivingTotal).toFixed(receivingMoneyObj.standardScale || 2),
            rate: this.receiveRealStandardRate,
            foreignStrCode: receivingMoneyObj.standardStrCode,
            foreignNumCode: receivingMoneyObj.standardNumCode,
            foreignScale: receivingMoneyObj.standardScale,
            foreignSymbol: receivingMoneyObj.standardSymbol,
            foreignUnit: receivingMoneyObj.standardUnit
          }
        }
        const params = {
          writtenOffItems: data,
          writtenOffTotal: totalMoneyObj,
          profitAndLossSummary
        }
        this.bus.emit('writtenOff:changed', writtenOffTotal, null, params)
        api.invokeService('@bills:save:cross:written:off:summary', params)
      })
    } catch (error) {
      console.log(error)
    }
  }

  // 多币种核销
  setWrittenOffRecordInMutiCurrency = (detailAmount, writtenOffItems, details) => {
    const writtenOffRateType = this.writtenOffRateType
    let { standardCurrency, dimentionCurrencyInfo, billSpecification, bus, allCurrencyRates } = this.props
    const currencyStandard = dimentionCurrencyInfo?.currency || standardCurrency
    const isEdited = this.isEdited(writtenOffItems)
    let data = cloneDeep(writtenOffItems)
    const receiptAbility = get(billSpecification, 'configs', []).find(v => v.ability === 'receipt')
    const hasForeign = data.find(i => i.loanInfoId.foreignCurrencyLoan)
    // 费用明细中所包含的各币种总金额汇总
    const currencyAmountObj = {}
    // 多币种已核销金额汇总
    const writtenOffSummaryObj = {}
    // 企业已付金额汇总
    const companyRealPaySumByCurrencyObj = {}
    // 核销金额合计（本位币）
    let writtenOffTotalInStandard = new MoneyMath(0)
    // 处理费用明细
    let detailsClone = cloneDeep(details)

    // 汇兑损益汇总
    const profitAndLossSummary = {
      totalProfitAndLoss: {
        currencyStrCode: currencyStandard.strCode,
        moneySum: '0'
      },
      profitAndLossByLoans: []
    }

    // 计算报销金额中每个币种的合计金额
    detailsClone.forEach(el => {
      const amount = get(el, 'feeTypeForm.amount', {})
      const companyRealPay = get(el, 'feeTypeForm.companyRealPay')
      if (amount.foreignStrCode) {
        const foreignMoney = get(currencyAmountObj, amount.foreignStrCode, new MoneyMath(0))
        currencyAmountObj[amount.foreignStrCode] = foreignMoney.add(amount.foreign)
        writtenOffSummaryObj[amount.foreignStrCode] = new MoneyMath(0) //为排序占位
        if (companyRealPay) {
          const companyRealPayMoney = get(companyRealPaySumByCurrencyObj, amount.foreignStrCode, new MoneyMath(0))
          companyRealPaySumByCurrencyObj[amount.foreignStrCode] = companyRealPayMoney.add(companyRealPay.foreign)
        }
      } else {
        const standardMoney = get(currencyAmountObj, currencyStandard.strCode, new MoneyMath(0))
        currencyAmountObj[currencyStandard.strCode] = standardMoney.add(amount.standard)
        writtenOffSummaryObj[currencyStandard.strCode] = new MoneyMath(0) //为排序占位
        if (companyRealPay) {
          const companyRealPayMoney = get(companyRealPaySumByCurrencyObj, currencyStandard.strCode, new MoneyMath(0))
          companyRealPaySumByCurrencyObj[currencyStandard.strCode] = companyRealPayMoney.add(companyRealPay.standard)
        }
      }
    })

    // 各币种报销金额汇总
    const expenseMoneySumByCurrency = []
    const currencyAmountObjKeys = Object.keys(currencyAmountObj)
    currencyAmountObjKeys.forEach(key => {
      if (Number(currencyAmountObj[key].value) !== 0) {
        expenseMoneySumByCurrency.push({
          currencyStrCode: key,
          moneySum: currencyAmountObj[key].value
        })
      }
    })

    // 从费用明细中汇总各币种可核销金额
    const writtenOffCurrencyAmountObj = {}
    // 不参与计算的结算方式中的企业已付金额合计（本位币）
    let uncalculateCompanyRealPayAmountBig = new MoneyMath(0)
    // 过滤结算方式，留下没有结算方式和结算方式为随单支付的费用明细做计算，并将明细金额中的企业已付金额刨去
    detailsClone = detailsClone.filter(el => {
      // 根据结算方式取值
      const settlementType = get(el, 'feeTypeForm.settlement.opportunity')
      const needCalculate = !settlementType || settlementType === 'SINGLEPAYMENT'
      if (needCalculate) {
        // 刨去费用明细金额中的企业已付金额
        const amount = get(el, 'feeTypeForm.amount', {})
        if (amount.foreignStrCode) {
          if (el.feeTypeForm.companyRealPay) {
            if (Number(el.feeTypeForm.amount.foreign) > Number(el.feeTypeForm.companyRealPay.foreign)) {
              // 后面有计算standard, 这里不处理
              el.feeTypeForm.amount.foreign = new MoneyMath(el.feeTypeForm.amount.foreign).minus(
                el.feeTypeForm.companyRealPay.foreign
              ).value
            } else {
              el.feeTypeForm.amount.foreign = ''
              el.feeTypeForm.amount.standard = ''
            }
          }
          const foreignMoney = get(writtenOffCurrencyAmountObj, amount.foreignStrCode, new MoneyMath(0))
          writtenOffCurrencyAmountObj[amount.foreignStrCode] = foreignMoney.add(el.feeTypeForm.amount.foreign)
        } else {
          if (el.feeTypeForm.companyRealPay) {
            if (Number(el.feeTypeForm.amount.standard) > Number(el.feeTypeForm.companyRealPay.standard)) {
              el.feeTypeForm.amount.standard = new MoneyMath(el.feeTypeForm.amount.standard).minus(
                el.feeTypeForm.companyRealPay.standard
              ).value
            } else {
              el.feeTypeForm.amount.standard = ''
            }
          }
          const standardMoney = get(writtenOffCurrencyAmountObj, currencyStandard.strCode, new MoneyMath(0))
          writtenOffCurrencyAmountObj[amount.standardStrCode] = standardMoney.add(amount.standard)
        }
      } else {
        if (el.feeTypeForm.companyRealPay) {
          uncalculateCompanyRealPayAmountBig = uncalculateCompanyRealPayAmountBig.add(
            el.feeTypeForm.companyRealPay.standard
          )
        }
      }
      return needCalculate
    })

    // 本位币核销金额
    let amountBig = get(writtenOffCurrencyAmountObj, currencyStandard.strCode, new MoneyMath(0))

    //先遍历一遍外币借款包，获取外币费用中没能核销的部分
    let result = []
    // 收款单还款
    if (receiptAbility?.summarySelect === summarySelectType.repay && hasForeign) {
      result = data.map(line => {
        const foreignCurrencyLoan = get(line, 'loanInfoId.foreignCurrencyLoan')
        // 核销外币借款包时，将本位币填到借款包的amount和standard上，原币填到foreign上
        const shouldCalculateRate = writtenOffRateType === 'REAL_TIME'
        const currentCurrencyRates =
          allCurrencyRates.find(
            item =>
              item.numCode === foreignCurrencyLoan.foreignNumCode &&
              item.originalId === foreignCurrencyLoan.standardNumCode
          ) || {}
        const realTimeRate = get(currentCurrencyRates, 'rate', 1)
        const { amount, manualAmount, maxWTAmount, foreignAmount, id, title } = line
        // 外币
        const foreignAmountStr = foreignAmount?.foreign || foreignAmount
        const foreign =
          Number(foreignAmountStr || 0) || Number(manualAmount || 0) || Number(amount || 0) || Number(maxWTAmount || 0)
        if (foreignCurrencyLoan) {
          line.foreignAmount = cloneDeep(foreignCurrencyLoan)
          const loanRate = get(line.foreignAmount, 'rate', 1)
          const foreignScale = get(line.foreignAmount, 'foreignScale', 2)
          const foreignStrCode = get(line.foreignAmount, 'foreignStrCode')
          const standardStrCode = get(line.foreignAmount, 'standardStrCode')
          // 外币换算
          line.foreignAmount.standard = new Big(Number(foreign))
            .times(Number(shouldCalculateRate ? realTimeRate : loanRate))
            .toFixed(Number(foreignScale))
          line.foreignAmount.foreign = new Big(Number(foreign)).toFixed(Number(foreignScale))

          const writtenOffProfitAndLossByLoan = {
            loanInfoId: id,
            name: title,
            currencyStrCode: currencyStandard.strCode,
            moneySum: 0,
            profitAndLossByDetails: []
          }
          // 会产生汇兑损益时，进入计算
          if (shouldCalculateRate && realTimeRate !== loanRate) {
            // 汇兑损益计算
            //  let profitAndLoss = new MoneyMath(foreign).times(
            //   new MoneyMath(realTimeRate).minus(loanRate).value
            // ).value
            // profitAndLoss = keepTwoDecimalFull(Number(profitAndLoss), currencyStandard.scale)
            // writtenOffProfitAndLossByLoan.moneySum = profitAndLoss
            const diffRate = Number(realTimeRate) - Number(loanRate)
            writtenOffProfitAndLossByLoan.moneySum = new Big(Number(foreign))
              .times(Number(diffRate))
              .toFixed(Number(currencyStandard.scale))
            if (diffRate !== 0) {
              writtenOffProfitAndLossByLoan.profitAndLossByDetails.push({
                detailId: '',
                name: '',
                code: '',
                loanRate,
                realTimeRate,
                foreignStrCode,
                standardStrCode,
                foreignMoneyValue: new Big(Number(foreign)).toFixed(Number(foreignScale)),
                // profitAndLossValue: profitAndLoss
                profitAndLossValue: new Big(Number(foreign))
                  .times(Number(diffRate))
                  .toFixed(Number(currencyStandard.scale))
              })
              line.foreignAmount.rate = realTimeRate
              line.foreignAmount.sysRate = realTimeRate
            }
          }
          if (writtenOffProfitAndLossByLoan.profitAndLossByDetails.length) {
            profitAndLossSummary.profitAndLossByLoans.push(writtenOffProfitAndLossByLoan)
          }
        }
        return line
      })
    } else {
      result = data.map(line => {
        /**
         * ===================================================== 外币计算逻辑
         * */
        // 借款包的外币金额
        const foreignCurrencyLoan = get(line, 'loanInfoId.foreignCurrencyLoan')
        //foreignCurrencyLoan有值时，进入外币借款包计算
        if (foreignCurrencyLoan) {
          // 回显的借款包可核销数line.foreignAmount不会是对象，有非对象的值时作为最大可核销数
          const loanForeignWrittenOff = typeof line.foreignAmount !== 'object' ? line.foreignAmount : null
          line.foreignAmount = cloneDeep(foreignCurrencyLoan)
          line.foreignAmount.standard = 0
          // 核销外币借款包时，将本位币填到借款包的amount和standard上，原币填到foreign上
          let shouldCalculateRate = writtenOffRateType === 'REAL_TIME'
          // 获取借款包借时汇率
          const loanRate = get(line.foreignAmount, 'rate', 1)
          const lineId = typeof line.id === 'object' ? line.id.id : line.id
          //当前借款包的损益信息
          let writtenOffProfitAndLossByLoan = {
            loanInfoId: lineId,
            name: line.title,
            currencyStrCode: currencyStandard.strCode,
            moneySum: 0,
            profitAndLossByDetails: []
          }
          // 当前外币的报销总额
          let foreignExpenseBig = writtenOffCurrencyAmountObj[line.foreignAmount.foreignStrCode]
          // 当前外币借款包的最大可核销金额
          const foreignAmount = loanForeignWrittenOff || line.loanInfoId.foreignRemain
          // 手动填写的核销金额 || 借款包的总核销金额
          let writtenOffAmount = new MoneyMath(line.manualAmount || foreignAmount || 0)

          if (!foreignExpenseBig || foreignExpenseBig.equal(0)) {
            // 场景1：费用明细切换币种时，会造成外币金额无法找到
            // 场景2：报销金额已被核销清零时
            line.foreignAmount.foreign = line.amount = '0.00'
          } else if (writtenOffAmount.gte(foreignExpenseBig)) {
            // 核销金额 大于等于 报销金额
            line.amount = line.foreignAmount.foreign = foreignExpenseBig.value
            // line.foreignAmount.standard = foreignExpenseBig.times(loanRate).value
            foreignExpenseBig = new MoneyMath(0)
          } else {
            // 核销金额 小于 报销金额
            const writtenOffAmountValue = writtenOffAmount.value
            line.amount = line.foreignAmount.foreign = writtenOffAmountValue
            // line.foreignAmount.standard = new MoneyMath(writtenOffAmountValue).times(loanRate).value
            foreignExpenseBig = foreignExpenseBig.minus(writtenOffAmountValue)
          }
          // 更新当前外币在可核销总额
          writtenOffCurrencyAmountObj[line.foreignAmount.foreignStrCode] = foreignExpenseBig

          let foreignWrittenOffTotal = line.amount
          // 核销汇总数据计算
          detailsClone = detailsClone.map(el => {
            // 返回场景：费用明细没有金额时；不是外币借款包时；币种不一致时；当前借款包的核销金额已用尽；
            if (
              !el.feeTypeForm.amount.foreign ||
              !el.feeTypeForm.amount.foreignStrCode ||
              el.feeTypeForm.amount.foreignStrCode !== line.foreignAmount.foreignStrCode ||
              !foreignWrittenOffTotal
            )
              return el

            // 要发出去的数据
            let detailAmountData = cloneDeep(el.feeTypeForm.amount)

            if (Number(el.feeTypeForm.amount.foreign) > Number(foreignWrittenOffTotal)) {
              detailAmountData.foreign = foreignWrittenOffTotal
              el.feeTypeForm.amount.foreign = new MoneyMath(el.feeTypeForm.amount.foreign).minus(
                foreignWrittenOffTotal
              ).value
              foreignWrittenOffTotal = 0
            } else if (Number(el.feeTypeForm.amount.foreign) === Number(foreignWrittenOffTotal)) {
              el.feeTypeForm.amount.foreign = ''
              el.feeTypeForm.amount.standard = ''
              foreignWrittenOffTotal = 0
            } else {
              // detail amount < writtenOff amount
              foreignWrittenOffTotal = new MoneyMath(foreignWrittenOffTotal).minus(el.feeTypeForm.amount.foreign).value
              el.feeTypeForm.amount.foreign = ''
              el.feeTypeForm.amount.standard = ''
            }
            detailAmountData.rate = shouldCalculateRate ? el.feeTypeForm.amount.rate : loanRate
            const foreignDetailStandardValue = new MoneyMath(detailAmountData.foreign).times(detailAmountData.rate).value
            detailAmountData.standard = Number(foreignDetailStandardValue).toFixed(detailAmountData.standardScale)
            writtenOffTotalInStandard = writtenOffTotalInStandard.add(foreignDetailStandardValue)
            line.foreignAmount.standard = new MoneyMath(line.foreignAmount.standard).add(
              detailAmountData.standard
            ).value
            if (el.feeTypeForm.amount.standard)
              el.feeTypeForm.amount.standard = Number(
                new MoneyMath(el.feeTypeForm.amount.foreign).times(el.feeTypeForm.amount.rate).value
              ).toFixed(el.feeTypeForm.amount.standardScale)

            // 会产生汇兑损益时，进入计算
            if (shouldCalculateRate && el.feeTypeForm.amount.rate !== loanRate) {
              // 汇兑损益计算
              let profitAndLoss = new MoneyMath(detailAmountData.foreign).times(
                new MoneyMath(el.feeTypeForm.amount.rate).minus(loanRate).value
              ).value
              // toFixed方法遇到这个数字时有问题-55.885932
              profitAndLoss = keepTwoDecimalFull(Number(profitAndLoss), currencyStandard.scale)
              // 总汇兑损益计算
              profitAndLossSummary.totalProfitAndLoss.moneySum = Number(
                new MoneyMath(profitAndLossSummary.totalProfitAndLoss.moneySum).add(profitAndLoss).value
              )
              // 当前借款包的汇兑损益计算
              writtenOffProfitAndLossByLoan.moneySum = Number(
                new MoneyMath(writtenOffProfitAndLossByLoan.moneySum).add(profitAndLoss).value
              )
              // 当前借款包中的汇总损益明细收集
              if (Number(profitAndLoss) !== 0) {
                writtenOffProfitAndLossByLoan.profitAndLossByDetails.push({
                  detailId: el.feeTypeForm.detailId,
                  name: el.feeTypeId.name,
                  code: el.feeTypeId.code,
                  foreignStrCode: line.foreignAmount.foreignStrCode,
                  foreignMoneyValue: detailAmountData.foreign,
                  loanRate,
                  realTimeRate: el.feeTypeForm.amount.rate,
                  standardStrCode: currencyStandard.strCode,
                  profitAndLossValue: profitAndLoss
                })
              }
            }

            // 一致的币种记入汇总中，金额
            let { writtenOffByDetailsArr = [] } = el
            const lineId = typeof line.id === 'object' ? line.id.id : line.id
            writtenOffByDetailsArr.push({
              loanInfoId: lineId,
              detailId: el.feeTypeForm.detailId,
              name: el.feeTypeId.name,
              code: el.feeTypeId.code,
              writtenOffRateType,
              loanRate,
              amount: detailAmountData
            })
            el.writtenOffByDetailsArr = writtenOffByDetailsArr
            return el
          })
          // 如果当前借款包中的损益明细中有记录，需要把借款包的损益信息收集到 汇兑损益汇总 中
          if (writtenOffProfitAndLossByLoan.profitAndLossByDetails.length) {
            profitAndLossSummary.profitAndLossByLoans.push(writtenOffProfitAndLossByLoan)
          }
          return line
        }

        /**
         * ===================================================== 本位币计算逻辑
         * */
        // 最多可核销
        let maxWTAmountBig = new MoneyMath(line.maxWTAmount || 0)
        // 可核销金额
        let loanAmount = new MoneyMath(line.manualAmount || line.amount || 0)
        // 核销金额是否调整过
        if (isEdited || this.writtenOffAmountIsChanged) {
          loanAmount = new MoneyMath(line.manualAmount || line.amount || line.maxWTAmount || 0)
        } else {
          loanAmount = new MoneyMath(line.maxWTAmount || 0)
        }

        // 最多可核销金额是否小于实际核销金额
        if (!maxWTAmountBig.gte(loanAmount)) {
          loanAmount = maxWTAmountBig
        }

        if (amountBig.equal(0)) {
          line.amount = '0.00'
        } else if (loanAmount.gte(amountBig)) {
          // 核销金额 大于等于 报销金额
          line.amount = amountBig.value
          amountBig = new MoneyMath(0)
        } else {
          // 核销金额 小于 报销金额
          line.amount = loanAmount.value
          amountBig = amountBig.minus(loanAmount)
        }
        // writtenOffSummaryObj[standardCurrency.strCode] = (writtenOffSummaryObj[standardCurrency.strCode] || new MoneyMath(0)).add(line.amount)
        // writtenOffTotalInStandard = writtenOffTotalInStandard.add(line.amount)

        let standardWrittenOffTotal = line.amount

        // 核销汇总数据计算
        detailsClone = detailsClone.map(el => {
          if (!el.feeTypeForm.amount.standard || el.feeTypeForm.amount.foreignStrCode) return el
          // 要发出去的数据
          let detailAmountData = cloneDeep(el.feeTypeForm.amount)

          // writtenOffTotalInStandard: 核销总金额
          if (Number(el.feeTypeForm.amount.standard) > Number(standardWrittenOffTotal)) {
            detailAmountData.standard = standardWrittenOffTotal
            el.feeTypeForm.amount.standard = new MoneyMath(el.feeTypeForm.amount.standard).minus(
              standardWrittenOffTotal
            ).value
            writtenOffTotalInStandard = writtenOffTotalInStandard.add(standardWrittenOffTotal)
            standardWrittenOffTotal = 0
          } else if (el.feeTypeForm.amount.standard === standardWrittenOffTotal) {
            writtenOffTotalInStandard = writtenOffTotalInStandard.add(standardWrittenOffTotal)
            el.feeTypeForm.amount.standard = ''
            standardWrittenOffTotal = 0
          } else {
            // detail amount < writtenOff amount
            standardWrittenOffTotal = new MoneyMath(standardWrittenOffTotal).minus(el.feeTypeForm.amount.standard).value
            writtenOffTotalInStandard = writtenOffTotalInStandard.add(el.feeTypeForm.amount.standard)
            el.feeTypeForm.amount.standard = ''
          }
          const standardValue = detailAmountData.standard
          detailAmountData.standard = Number(standardValue).toFixed(detailAmountData.standardScale)

          let { writtenOffByDetailsArr = [] } = el
          writtenOffByDetailsArr.push({
            loanInfoId: line.id,
            detailId: el.feeTypeForm.detailId,
            name: el.feeTypeId.name,
            code: el.feeTypeId.code,
            writtenOffRateType,
            amount: detailAmountData
          })
          el.writtenOffByDetailsArr = writtenOffByDetailsArr

          return el
        })
        return line
      })
    }
    // 企业已付金额汇总
    const companyRealPaySumByCurrency = []
    Object.keys(companyRealPaySumByCurrencyObj).forEach(key => {
      // if (companyRealPaySumByCurrencyObj[key])
      const companyRealPayMoney = companyRealPaySumByCurrencyObj[key].value
      if (companyRealPayMoney > 0) {
        companyRealPaySumByCurrency.push({
          currencyStrCode: key,
          moneySum: companyRealPayMoney
        })
      }
    })

    // 核销明细汇总
    let writtenOffByDetails = []
    // 各币种支付金额计算
    let payMoneySumByCurrencyObj = {}
    let payMoneySumByCurrency = []
    // 各币种支付明细汇总
    let payMoneyByDetails = []
    let payMoneyTotal = new MoneyMath(0)

    // 收款单还款
    if (receiptAbility?.summarySelect === summarySelectType.repay && hasForeign) {
      result.forEach(item => {
        const { id, foreignAmount, loanInfoId } = item
        writtenOffByDetails.push({
          name: '',
          code: '',
          detailId: '',
          loanInfoId: id,
          writtenOffRateType,
          loanRate: loanInfoId?.foreignCurrencyLoan?.rate || 1,
          amount: foreignAmount
        })
      })
    } else {
      detailsClone.forEach(el => {
        const amount = get(el, 'feeTypeForm.amount', {})
        if (amount.standard && amount.foreign) {
          const foreignMoney = get(payMoneySumByCurrencyObj, amount.foreignStrCode, new MoneyMath(0))
          payMoneySumByCurrencyObj[amount.foreignStrCode] = foreignMoney.add(amount.foreign)
          // 这里的amount受汇兑损益影响，需要重新计算支付金额中的standard字段(standard = foreign * rate)
          const standardWithoutProfitAndLoss = Number(new MoneyMath(amount.foreign).times(amount.rate).value).toFixed(
            amount.standardScale
          )
          payMoneyTotal = payMoneyTotal.add(standardWithoutProfitAndLoss)
          payMoneyByDetails.push({
            detailId: el.feeTypeForm.detailId,
            name: el.feeTypeId.name,
            code: el.feeTypeId.code,
            payMoneyModel: { ...amount, standard: standardWithoutProfitAndLoss }
          })
        } else if (amount.standard) {
          const standardMoney = get(payMoneySumByCurrencyObj, amount.standardStrCode, new MoneyMath(0))
          payMoneySumByCurrencyObj[amount.standardStrCode] = standardMoney.add(amount.standard)
          payMoneyTotal = payMoneyTotal.add(amount.standard)
          payMoneyByDetails.push({
            detailId: el.feeTypeForm.detailId,
            name: el.feeTypeId.name,
            code: el.feeTypeId.code,
            payMoneyModel: amount
          })
        }
        if (el.writtenOffByDetailsArr && el.writtenOffByDetailsArr.length)
          writtenOffByDetails = writtenOffByDetails.concat(el.writtenOffByDetailsArr)
      })

      writtenOffByDetails.forEach(el => {
        if (el.amount.foreignStrCode) {
          let foreignWrittenOffBig = get(writtenOffSummaryObj, el.amount.foreignStrCode, new MoneyMath(0))
          writtenOffSummaryObj[el.amount.foreignStrCode] = foreignWrittenOffBig.add(el.amount.foreign)
        } else {
          let standardWrittenOffBig = get(writtenOffSummaryObj, el.amount.standardStrCode, new MoneyMath(0))
          writtenOffSummaryObj[el.amount.standardStrCode] = standardWrittenOffBig.add(el.amount.standard)
        }
      })
    }
    //核销各币种总额
    const writtenOffSumByCurrency = []
    // 收款单还款
    if (receiptAbility?.summarySelect === summarySelectType.repay && hasForeign) {
      const currencyStrCode = writtenOffByDetails[0]?.amount?.foreignStrCode
      const moneySum = writtenOffByDetails.reduce((pre, curr) => {
        pre = pre + Number(curr.amount.foreign)
        return pre
      }, 0)
      writtenOffSumByCurrency.push({ currencyStrCode, moneySum })
    } else {
      Object.keys(writtenOffSummaryObj).forEach(key => {
        if (writtenOffSummaryObj[key].value > 0) {
          writtenOffSumByCurrency.push({
            currencyStrCode: key,
            moneySum: writtenOffSummaryObj[key].value
          })
        }
      })
    }

    Object.keys(payMoneySumByCurrencyObj).forEach(key => {
      if (Number(payMoneySumByCurrencyObj[key].value) !== 0) {
        payMoneySumByCurrency.push({
          currencyStrCode: key,
          moneySum: payMoneySumByCurrencyObj[key].value
        })
      }
    })

    const writtenOffTotalInStandardValue = Number(writtenOffTotalInStandard.value).toFixed(currencyStandard.scale)

    let params = {
      expenseMoneySumByCurrency,
      writtenOffDataObj: {
        payMoneyTotal: Number(payMoneyTotal.minus(uncalculateCompanyRealPayAmountBig.value).value).toFixed(
          currencyStandard.scale
        ),
        writtenOffTotalInStandard: writtenOffTotalInStandardValue
      }
    }

    if (payMoneySumByCurrency.length > 0) params.payMoneySumByCurrency = payMoneySumByCurrency
    if (payMoneyByDetails.length > 0) params.payMoneyByDetails = payMoneyByDetails

    // 收款单还款
    if (receiptAbility) {
      if (hasForeign) {
        params = { ...params, writtenOffByDetails, writtenOffSumByCurrency }
      }
      delete params.expenseMoneySumByCurrency
    } else {
      if (Number(writtenOffTotalInStandardValue) > 0) {
        params = { ...params, writtenOffByDetails, writtenOffSumByCurrency }
      }
    }
    if (receiptAbility?.summarySelect === summarySelectType.repay && hasForeign) {
      if (profitAndLossSummary.profitAndLossByLoans.length > 0) {
        profitAndLossSummary.totalProfitAndLoss.moneySum = profitAndLossSummary.profitAndLossByLoans.reduce((c, n) => {
          c = (Number(c) + Number(n.moneySum)).toFixed(2)
          return c
        }, 0)
        params.profitAndLossSummary = profitAndLossSummary
      }
    } else {
      if (profitAndLossSummary.profitAndLossByLoans.length > 0) {
        params.profitAndLossSummary = profitAndLossSummary
      }
    }
    if (companyRealPaySumByCurrency.length > 0) {
      params.companyRealPaySumByCurrency = companyRealPaySumByCurrency
    }
    params.hasWrittenOffRecords = result?.length > 0
    api.invokeService('@bills:save:written:off:summary', params)
    if (receiptAbility?.summarySelect === summarySelectType.repay && hasForeign) {
      bus.emit('writtenOff:value:change', result, true)
    }
    this.setState({ writtenOffItems: result })
    this.calculToTalAmountForForeign(result, writtenOffTotalInStandardValue, params)
  }

  // 单币种核销（原核销逻辑）
  setWrittenOffRecordInStandardCurrency = (detailAmount, writtenOffItems) => {
    // 以下部分为原逻辑
    const isEdited = this.isEdited(writtenOffItems)
    let data = cloneDeep(writtenOffItems)
    let amountBig = new MoneyMath(detailAmount)
    let result = data.map(line => {
      let maxWTAmountBig = new MoneyMath(line.maxWTAmount || 0)
      let loanAmount = new MoneyMath(line.manualAmount || line.amount || 0)
      if (isEdited || this.writtenOffAmountIsChanged) {
        loanAmount = new MoneyMath(line.manualAmount || line.amount || line.maxWTAmount || 0)
      } else {
        loanAmount = new MoneyMath(line.maxWTAmount || 0)
      }

      if (!maxWTAmountBig.gte(loanAmount)) {
        loanAmount = maxWTAmountBig
      }

      if (amountBig.equal(0)) {
        line.amount = '0.00'
      } else if (loanAmount.gte(amountBig)) {
        line.amount = amountBig.value
        amountBig = new MoneyMath(0)
      } else {
        line.amount = loanAmount.value
        amountBig = amountBig.minus(loanAmount)
      }
      return line
    })
    api.invokeService('@bills:clear:written:off:summary')
    this.setState({ writtenOffItems: result })
    this.calculToTalAmount(result)
  }

  setWrittenOffRecord = (detailAmount, writtenOffItems = this.state.writtenOffItems, details = this.state.details) => {
    let { bus } = this.props
    this.receivingMoneyObj = null
    bus.emit('writtenOff:value:change', writtenOffItems)
    bus.emit('billInfoEditable:writtenOff:change', writtenOffItems)
    this.setState({ writtenOffItems, initWrittenOffItems: cloneDeep(writtenOffItems) }, () => {
      if (this.crossCurrencyWrittenOff) {
        this.setWrittenOffByCrossCurrency(detailAmount, writtenOffItems, details)
      } else if (this.canIUseMultCurrencyWrittenOff) {
        api.invokeService('@bills:clear:cross:written:off:summary')
        this.setWrittenOffRecordInMutiCurrency(detailAmount, writtenOffItems, details)
      } else {
        api.invokeService('@bills:clear:cross:written:off:summary')
        this.setWrittenOffRecordInStandardCurrency(detailAmount, writtenOffItems)
      }
    })
  }

  handleImportWrittenOffFromApply = async data => {
    const { billSpecification, MULTICURRENCYWRITTENOFF } = this.props
    const writtenOffAbility = get(billSpecification, 'configs', []).find(v => v.ability === 'writtenOff')
    let writtenOffItems = this.state.writtenOffItems.slice(0)
    let hasImported = []
    let hasZero = []
    let hasForeign = []
    data.forEach(item => {
      if (item.remain === 0) {
        hasZero.push(item)
      }
      if (!!item.foreignCurrencyLoan) {
        hasForeign.push(item)
      }
      if (writtenOffItems.find(v => item.id === v.id)) {
        hasImported.push(item)
      }
      if (!writtenOffItems.find(v => item.id === v.id) && item.remain !== 0) {
        writtenOffItems.push({
          id: item.id,
          loanInfoId: item,
          maxWTAmount: item.remain,
          title: item.title,
          repaymentDate: item.repaymentDate,
          fromApply: true,
          flowId: item.flowId,
          hasImported: true
        })
      }
    })

    if (hasImported.length && hasImported.length === data.length) {
      let allZero = !hasImported.map(v => v.remain).reduce((a, b) => a + b)
      let text = allZero ? i18n.get('剩余借款为0的核销不支持导入！') : i18n.get('相关核销已全部被导入！')
      showModal.info({ content: text })
    } else if (hasZero.length) {
      showModal.info({ content: i18n.get('剩余借款为0的核销不支持导入！') })
    } else if (MULTICURRENCYWRITTENOFF && !writtenOffAbility?.crossCurrencyWrittenOff && hasForeign.length) {
      showModal.info({ content: i18n.get('暂不支持复制外币借款包') })
      return
    }
    this.setWrittenOffRecord(this.detailTotal, writtenOffItems)
  }

  calculToTalAmountForForeign(writtenOffItems = [], writtenOffTotalInStandard, writtenOffSummaryForMutiCurrency) {
    const writtenOffTotal = total(writtenOffItems.map(v => getMoney(v.amount) || 0)).toString()
    this.bus.emit('writtenOff:changed', writtenOffTotalInStandard, writtenOffSummaryForMutiCurrency)
    this.bus.emit('writtenOff:items:change', writtenOffItems)
    this.setState({ writtenOffTotal: writtenOffTotalInStandard })
  }

  calculToTalAmount(writtenOffItems = []) {
    let writtenOffTotal = total(writtenOffItems.map(v => getMoney(v.amount) || 0)).toString()
    this.bus.emit('writtenOff:changed', writtenOffTotal)
    this.bus.emit('writtenOff:items:change', writtenOffItems)
    this.setState({ writtenOffTotal })
  }

  openStackModal(param) {
    let { bus } = this.props
    api.open('@bills:BillStackerModal', param).then(data => {
      bus.emit('writtenOff:value:change', data)
      bus.getValue().then(async value => {
        let expenseLink = value.expenseLink || value.expenseLinks
        expenseLink = expenseLink ? (Array.isArray(expenseLink) ? expenseLink : [expenseLink]) : []
        let relatedIds = this.getRelatedIds(expenseLink)
        data.forEach(item => {
          item.hasImported = expenseLink.length ? relatedIds.includes(item.loanInfoId.flowId) : false
        })

        if (this.crossCurrencyWrittenOff && data?.length) {
          const [standardMoneyObj, receivingMoneyObj] = getMoneyObj(data, this.state.details)
          this.rateInfoMap = await getWrittenOffRate(data, receivingMoneyObj, standardMoneyObj)
        }
        param.viewKey === 'WrittenOffListView' && this.setWrittenOffRecord(this.detailTotal, data)
      })
    })
  }

  getRelatedIds = expenseLink => {
    const relatedIds = []
    expenseLink.forEach(v => {
      v &&
        v.related &&
        v.related.forEach(item => {
          relatedIds.push(item.id)
        })
    })
    return relatedIds
  }

  handleAllChecked = e => {
    let { writtenOffItems } = this.state
    let checked = e.target.checked
    let selectedList = checked ? writtenOffItems.map(o => o.loanInfoId.id) : []
    this.setState({ selectedList, isAllChecked: checked })
  }

  handleRemoveWrittenOff = () => {
    let { writtenOffItems, selectedList, initWrittenOffItems, isReceiptTemplate } = this.state
    if (isReceiptTemplate) {
      writtenOffItems = initWrittenOffItems
    }
    if (selectedList.length === 0) {
      return showMessage.warning(i18n.get('请选择核销记录'))
    }
    let items = writtenOffItems.filter(item => {
      return !~selectedList.findIndex(o => o === (item.loanInfoId.id || item.loanInfoId))
    })
    this.setState({
      selectedList: [],
      isAllChecked: false,
      initWrittenOffItems: items
    })
    this.setWrittenOffRecord(this.detailTotal, items)
  }

  // 为实现借款包核销计算，需要给没有id的明细加上id
  fnSetDetailsId = detailsArr => {
    let details = cloneDeep(detailsArr)
    let shouldSetDetailsId = false
    details = details.map(el => {
      const detailId = get(el, 'feeTypeForm.detailId')
      if (!detailId) {
        //判断如果是自动生成的费用明细，那刚生成还没有费用金额的时候，不给他添加detailId
        let amount
        if (typeof el?.feeTypeForm?.amount === 'string') {
          amount = Number(el?.feeTypeForm?.amount)
        } else {
          amount = Number(el?.feeTypeForm?.amount?.standard)
        }
        if (el?.feeTypeForm?.isAutoDetail && amount === 0) {
          shouldSetDetailsId = false
        } else {
          shouldSetDetailsId = true
          el.feeTypeForm.detailId = uuid(14)
        }
      }
      return el
    })
    if (shouldSetDetailsId) {
      this.setState({ details }, () =>
        setTimeout(() => {
          this.bus.setFieldsValue({ details })
          this.bus.emit('details:change', details)
        })
      )
    } else {
      this.setState({ details })
    }
    return details
  }

  handleAddWrittenOff = async () => {
    const { billSpecification, dataSource, baseDataProperties, getLoanPackageList } = this.props
    this.init = false
    if (this.detailTotal < 0) {
      return showModal.error({
        title: i18n.get('无法核销'),
        content: i18n.get('支付金额小于0不能添加核销')
      })
    }
    if (this.allowSelectionReceivingCurrency && !this.crossCurrencyWrittenOff) {
      return showModal.error({
        title: i18n.get('无法核销'),
        content: i18n.get('需要联系系统管理员开通跨币种核销功能')
      })
    }
    let { writtenOffItems, submitter, isReceiptTemplate } = this.state
    let { initValue, isModify, flowId, scopeIds } = this.props
    const billData = await this.props.bus?.getValue()
    const { form } = parseFormValueAsParam(billData, billSpecification, dataSource, baseDataProperties)
    if (isReceiptTemplate) {
      //如果是收款单
      const receiptAbility = get(billSpecification, 'configs', []).find(v => v.ability === 'receipt')
      let loanFetch = getLoanPackageList({ state: 'REPAID' })
      if (receiptAbility.summarySelect === summarySelectType.repay) {
        loanFetch = api.invokeService('@bills:get:bill:loan:list', { flowId, billData: form })
      }
      loanFetch.then(data => {
        const writtenOffAbility = get(billSpecification, 'configs', []).find(v => v.ability === 'writtenOff')
        let loanPackageList = data?.payload?.items || data?.items || []
        if (loanPackageList.length > 0) {
          loanPackageList = loanPackageList.filter(item => item.infoType !== 'EBUSSCARD')
        }
        let param = {
          viewKey: 'WrittenOffListView',
          loanPackageList,
          checkedValues: writtenOffItems,
          initValue: initValue,
          flowId,
          billData: form,
          writtenOffAbility,
          submitterId: submitter.id,
          isModify,
          scopeIds,
          isReceiptTemplate,
          receiptAbility
        }
        this.openStackModal(param)
      })
    } else {
      api.invokeService('@bills:get:bill:loan:list', { flowId, billData: form }).then(data => {
        const writtenOffAbility = get(billSpecification, 'configs', []).find(v => v.ability === 'writtenOff')
        // const isEBussCard = get(billSpecification, 'configs', []).find(v => v.isEBussCard)
        let loanPackageList = data.items || []
        // if (isEBussCard) {
        //   loanPackageList = loanPackageList.filter(item => item.infoType === 'EBUSSCARD')
        // } else {
        //   loanPackageList = loanPackageList.filter(item => item.infoType !== 'EBUSSCARD')
        // }
        let param = {
          viewKey: 'WrittenOffListView',
          loanPackageList,
          checkedValues: writtenOffItems,
          initValue: initValue,
          flowId,
          billData: form,
          writtenOffAbility,
          submitterId: submitter.id,
          isModify,
          scopeIds
        }
        this.openStackModal(param)
      })
    }
  }

  handleChecked = (item, e) => {
    let selectedList = this.state.selectedList.slice()
    const id = item.loanInfoId.id || item.loanInfoId
    if (e.target.checked) {
      selectedList.push(id)
    } else {
      selectedList = selectedList.filter(o => o !== id)
    }
    this.setState({ selectedList })
  }

  handleDetailClick = (data, e) => {
    e.stopPropagation()
    e.preventDefault()
    let { isModal, stackerManager, getloanpackageDetailInfo, isModify, flowId } = this.props
    let id = typeof data.loanInfoId === 'object' ? data.loanInfoId.id : data.loanInfoId
    let param = isModify ? { id, flowId } : { id: data.loanInfoId.id }
    getloanpackageDetailInfo(param).then(action => {
      if (action.error) return
      let data = action.payload
      if (isModal && stackerManager) {
        let openViewList = stackerManager.values()
        let layer = openViewList.find(o => o.key == 'LoanPackageDetailView')
        !layer &&
          stackerManager.push('LoanPackageDetailView', {
            loanDetail: data.value
          })
      } else {
        api.open('@bills:BillStackerModal', {
          viewKey: 'LoanPackageDetailView',
          loanDetail: data.value
        })
      }
    })
  }

  fnBuildItems = (data, i) => {
    let { writtenOffItems, selectedList, hasExpenseLink, isReceiptTemplate } = this.state
    let id = typeof data.loanInfoId === 'string' ? data.loanInfoId : data.loanInfoId.id
    const { billSpecification } = this.props
    const showErrorWhenLink = get(billSpecification, 'components', [])?.find(v => v.field === 'expenseLinks')
      ?.showErrorWhenLink
    return (
      <ListLayoutItemWrapper key={id} data-grid={{ h: 7.5 }}>
        <ActiveWrapper key={id} isEditable={true} data={data} onRemove={this.handleItemRemove}>
          <WrittenOffItem
            item={data}
            isEdit={true}
            isChecked={!!~selectedList.findIndex(o => o === id)}
            isShowBorder={i + 1 !== writtenOffItems.length}
            onClick={this.handleDetailClick}
            onChange={this.handleChecked}
            hasExpenseLink={hasExpenseLink}
            isReceiptTemplate={isReceiptTemplate}
            showErrorWhenLink={showErrorWhenLink}
            isCross={this.crossCurrencyWrittenOff}
          />
        </ActiveWrapper>
      </ListLayoutItemWrapper>
    )
  }

  renderWrittenOffPart() {
    let { writtenOffItems } = this.state
    let children = writtenOffItems.map(this.fnBuildItems)
    return (
      <ListLayout
        style={{ height: writtenOffItems.length * 75 }}
        rowKey="id"
        rowHeight={10}
        margin={[0, 0]}
        width={10000}
        dataSource={writtenOffItems}
        onListChange={this.handleListChange}
      >
        {children}
      </ListLayout>
    )
  }

  renderActionView() {
    const { writtenOffItems, isReceiptTemplate } = this.state
    return (
      <div className="writtenOff-action">
        <div>
          <Button category="secondary" theme="default" onClick={this.handleAddWrittenOff} size='small' icon={<OutlinedTipsAdd />} data-testid="bill-writtenoff-add">
            {isReceiptTemplate ? i18n.get('添加借款') : /*添加核销*/ i18n.get('选择')}
          </Button>
        </div>
        {writtenOffItems.length > 0 && <span className="sep-line" />}
        {writtenOffItems.length > 0 && (
          <Button category="secondary" theme="default" onClick={this.handleRemoveWrittenOff} size='small' data-testid="bill-writtenoff-remove">
            {i18n.get('移除核销')}
          </Button>
        )}
      </div>
    )
  }

  showLoanModal = async (flag = false, mustWrittenOff = false) => {
    let { flowId, isModify, multiplePayeesMode, billSpecification, dataSource, baseDataProperties } = this.props
    let { writtenOffItems, details, hasExpenseLink } = this.state
    const billData = await this.props.bus?.getValue()
    const { form } = parseFormValueAsParam(billData, billSpecification, dataSource, baseDataProperties)
    if (flag) {
      const loanWrittenOffMoney = writtenOffItems.reduce((sum, item) => sum + (item.amount * 1 || 0), 0)
      const {
        payDetail: { value },
        companyPayMoney
      } = getDetailCalculateMoney(details || [], 'expense')
      if (!multiplePayeesMode) {
        const payMoney = new MoneyMath(value).minus(loanWrittenOffMoney).minus(companyPayMoney).value
        return new Promise((resolve, reject) => {
          const receiptAbility = get(billSpecification, 'configs', []).find(v => v.ability === 'receipt')
          if (!writtenOffItems.length && receiptAbility?.summarySelect === summarySelectType.repay) {
            return showModal.error({
              title: i18n.get('收款单还款'),
              content: i18n.get('收款单还款核销必填'),
              okText: i18n.get('知道了'),
              onOk: function () {
                return reject('cancel')
              }
            })
          }
          api.invokeService('@bills:get:bill:loan:list', { flowId, billData: form }).then(data => {
            let list = data.items
            const filterBillSpecification =
              this.filterBillSpecification(
                billSpecification.configs.filter(item => item?.ability === 'writtenOff')?.[0]?.loanList,
                list
              ) <= 0
            if (
              this.isHasWriteOff(list) &&
              !writtenOffItems.length &&
              Number(getMoney(payMoney) > 0 && mustWrittenOff)
            ) {
              if (window.IS_SZJL && filterBillSpecification) {
                return resolve()
              }
              return showModal.error({
                title: window.IS_SMG ? i18n.get('未核销预支') : i18n.get('未核销借款'),
                content: window.IS_SMG
                  ? i18n.get('此单据未核销预支，无法提交')
                  : i18n.get('此单据未核销借款，无法提交'),
                onOk: function () {
                  return reject('cancel')
                },
                okText: i18n.get('知道了')
              })
            }
            if (this.isHasWriteOff(list) && !writtenOffItems.length && !isModify && Number(getMoney(payMoney) > 0)) {
              if (window.IS_SZJL && filterBillSpecification) {
                return resolve()
              }
              return showModal.confirm({
                title: window.IS_SMG ? i18n.get('未核销预支') : i18n.get('未核销借款'),
                content: window.IS_SMG
                  ? i18n.get('你有预支未核销，是否继续提交单据？')
                  : i18n.get('你有借款未核销，是否继续提交单据?'),
                onOk: function () {
                  return resolve()
                },
                onCancel: function () {
                  return reject('cancel')
                },
                okText: i18n.get('继续'),
                cancelText: i18n.get('取消')
              })
            }
            if (writtenOffItems.filter(data => data.fromApply && !data.hasImported).length) {
              const isForbidSubmit = this.fnIsForbidSubmit()
              const showErrorWhenLink = get(billSpecification, 'components', [])?.find(v => v.field === 'expenseLinks')
                ?.showErrorWhenLink
              let text = hasExpenseLink
                ? isForbidSubmit
                  ? i18n.get('单据中的申请事项与借款包不一致,禁止提交')
                  : showErrorWhenLink
                    ? ''
                    : i18n.get('单据中的申请事项与借款包不一致,是否继续？')
                : showErrorWhenLink
                  ? ''
                  : i18n.get('该借款相关的申请事项未被当前单据关联')
              if (!text) return resolve()
              if (isForbidSubmit) {
                return showModal.warning({
                  title: i18n.get(text),
                  onOk: function () {
                    return reject('cancel')
                  },
                  okText: i18n.get('确定')
                })
              }
              return showModal.confirm({
                title: i18n.get(text),
                onOk: function () {
                  return resolve()
                },
                onCancel: function () {
                  return reject('cancel')
                },
                okText: i18n.get('继续'),
                cancelText: i18n.get('取消')
              })
            }
            return resolve()
          })
        })
      }
    }
    return Promise.resolve()
  }
  // 过滤单据模板
  filterBillSpecification(loanList = {}, list = []) {
    const { ids = [], isAll = true } = loanList || {}
    if (isAll) {
      return 1
    }
    return list.filter(item => ids?.indexOf(item.flowSpecificationId) != -1).length
  }
  fnIsForbidSubmit = () => {
    const { billSpecification } = this.props
    const configs = get(billSpecification, 'configs', [])
    const apply = configs.find(line => line.ability === 'apply')
    return apply && apply.canApply && apply.isForbidSubmit
  }

  isHasWriteOff(loanPackageList) {
    if (loanPackageList && loanPackageList.length > 0) {
      for (let i = 0; i < loanPackageList.length; i++) {
        let line = loanPackageList[i]
        let amount = line.loanAmount || line.remain
        if (amount && amount * 1 > 0) return true
      }
    } else {
      return false
    }
  }

  render() {
    let { writtenOffItems, writtenOffTotal, isReceiptTemplate, initWrittenOffItems, writtenOffReceivingTotal, isAllChecked } = this.state
    const { billSpecification } = this.props
    let writtenLeng = writtenOffItems.length
    const item = writtenLeng > 0 && writtenOffItems[0]
    const standardStrCode = get(item, 'loanInfoId.totalMoneyNode.standardStrCode')
    const title = IS_STANDALONE
      ? window.IS_SMG
        ? i18n.get('核销预支/预付款')
        : i18n.get('核销借款/预付款')
      : isReceiptTemplate
        ? i18n.get('选择借款')
        : i18n.get('核销借款')
    //处理收款单模板中借款包模块的展示数据
    if (isReceiptTemplate) {
      let moneyTotal = 0
      const hasForeign = writtenOffItems.find(i => i.loanInfoId.foreignCurrencyLoan)
      const receiptAbility = get(billSpecification, 'configs', []).find(v => v.ability === 'receipt')
      writtenOffItems.forEach((el, i) => {
        if (hasForeign && receiptAbility.summarySelect === summarySelectType.repay) {
          moneyTotal += Number(el.foreignAmount?.standard)
        } else {
          if (!el.manualAmount) {
            initWrittenOffItems.forEach(k => {
              if (k.title == el.title) {
                if (!el.amount) {
                  el.amount = k.maxWTAmount
                  el.writeoffMoney = k.maxWTAmount
                } else {
                  el.amount = k.amount
                  el.writeoffMoney = k.amount
                }
              }
            })
            moneyTotal += Number(el.amount)
          } else if (el.manualAmount) {
            el.amount = el.manualAmount
            el.writeoffMoney = el.manualAmount
            moneyTotal += Number(el.manualAmount)
          }
        }
      })
      writtenOffTotal = moneyTotal
    }
    return (
      <div className={styles['WrittenOff-part']}>
        <div className="written-header">
          <div className="written-title">
            <span>{title}</span>
            {!!writtenLeng && <span>{`（${writtenLeng}）`}</span>}
          </div>
          {this.renderActionView()}
          {writtenLeng > 0 ? <>
            <div className="select-all">
              {writtenOffItems.length !== 0 ? <Checkbox checked={isAllChecked} onChange={this.handleAllChecked}>
                {i18n.get('全选')}
              </Checkbox> : <div/>}
              <div className="amount">
                <span className="text">{isReceiptTemplate ? i18n.get('还款总额') : i18n.get('核销总额')}:</span>
                <div className="money-text">
                  <Money
                    currencySize={16}
                    valueSize={16}
                    color="var(--eui-function-warning-600)"
                    value={this.receivingMoneyObj ? writtenOffReceivingTotal : writtenOffTotal}
                    showSymbol={false}
                    showStrCode={true}
                    currencyStrCode={this.receivingMoneyObj ? this.receivingMoneyObj?.standardStrCode : standardStrCode}
                  />
                </div>
              </div>
            </div>
            <div className="writtenoff-wrapper">{this.renderWrittenOffPart()}</div>
          </> : null}
        </div>
      </div>
    )
  }
}
