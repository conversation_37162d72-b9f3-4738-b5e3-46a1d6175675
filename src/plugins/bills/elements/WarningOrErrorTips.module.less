.bill-err-bg {
  display: flex;
  border-radius: 6px;
  font: var(--eui-font-body-r1);
  color: var(--eui-text-titie);
  margin: 0 -8px;
  padding: 10px 16px;
  flex-direction: column;
  background-color:var(--eui-function-danger-50);
  &:not(:first-child) {
    margin-top: 16px;
  }
  :global {
    .header-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      .eui-icon {
        color: var(--eui-function-danger-500);
      }
      .icon.error{
        color: var(--eui-function-danger-500);
      }
      .header-child {
        display: flex;
        flex: 1;
        margin-left: 5px;
        justify-content: space-between;
      }
      .header-right {
        display: flex;
        max-width: 80px;
        align-items: center;
        justify-content: flex-end;
      }
    }
    .content-wrapper {
      padding-left: 19px;
      padding-right: 80px;
      display: flex;
      flex-direction: column;
      .content-item-child {
        //height: 38px;
        line-height: 38px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
      }
    }
  }
}

.bill-warning-bg {
  display: flex;
  border-radius: 6px;
  font: var(--eui-font-body-r1);
  color: var(--eui-text-titie);
  padding: 10px 16px;
  flex-direction: column;
  background-color: var(--eui-function-warning-50);
  &:first-child {
    margin-top: 0;
  }
  margin: 0 -8px;
  &:not(:first-child) {
    margin-top: 16px;
  }
  :global {
    .header-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      .eui-icon {
        color: var(--eui-function-warning-500);
      }
      .icon.warning{
        color: var(--eui-function-warning-500);
      }
      .header-child {
        display: flex;
        flex: 1;
        margin-left: 5px;
        width: calc(100% - 40px);
      }
      .header-right {
        display: flex;
        max-width: 80px;
        align-items: center;
        justify-content: flex-end;
        > .attachment-wrapper-fix {
          margin: 0;
        }
      }
    }
    .content-wrapper {
      padding-left: 19px;
      padding-right: 80px;
      display: flex;
      flex-direction: column;
      .content-item-child {
        //height: 38px;
        line-height: 38px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
      }
    }
  }
}
