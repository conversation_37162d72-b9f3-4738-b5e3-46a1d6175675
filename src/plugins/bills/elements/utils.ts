/**************************************
 * Created By LinK On 2020/8/31 11:09.
 **************************************/
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'

// 整理外币核销数据
export const initForeignCurrencyProps = (props: any) => {
  const form = get(props, 'dataSource.form')
  if (!form || !props?.writtenOffRecords?.length) return undefined
  const {
    expenseMoneySumByCurrency,
    writtenOffSumByCurrency,
    payMoneySumByCurrency,
    writtenOffByDetails,
    profitAndLossSummary,
    payMoneyByDetails,
    writtenOffMoney = {}
  } = form
  if (!expenseMoneySumByCurrency) return undefined

  return {
    expenseMoneySumByCurrency,
    writtenOffSumByCurrency,
    payMoneySumByCurrency,
    writtenOffByDetails,
    profitAndLossSummary,
    payMoneyByDetails,
    writtenOffDataObj: {
      writtenOffTotalInStandard: writtenOffMoney.standard
    },
    hasWrittenOffRecords: true
  }
}

// 整理外币核销数据
export const initForeignCurrencyForCrossProps = (props: any) => {
  const writtenOff = props.dataSource?.writtenOff?.records || []
  const writtenOffAbility =
    props.dataSource?.form?.specificationId?.configs?.find(item => item.ability === 'writtenOff') || {}

  if (!writtenOffAbility?.crossCurrencyWrittenOff) return null
  const { profitAndLossSummary } = props.dataSource.form
  const writtenOffItems = writtenOff.map(line => {
    let foreignAmount
    if (line?.loanInfoId?.foreignCurrencyLoan) {
      foreignAmount = {
        ...line?.loanInfoId?.foreignCurrencyLoan,
        foreign: line.foreignAmount
      }
    }
    return {
      ...line,
      amount: line.amount,
      foreignAmount
    }
  })
  return { profitAndLossSummary, writtenOffItems, writtenOffTotal: props.dataSource?.form?.writtenOffMoney }
}

export const transformPayMoney = (value: any) => {
  if (value?.foreignNumCode && value?.standardNumCode) {
    return {
      foreign: value?.standard,
      foreignNumCode: value?.standardNumCode,
      foreignSymbol: value?.standardSymbol,
      foreignUnit: value?.standardUnit,
      foreignScale: value?.standardScale,
      foreignStrCode: value?.standardStrCode,
      standard: value?.foreign,
      standardStrCode: value?.foreignStrCode,
      standardNumCode: value?.foreignNumCode,
      standardSymbol: value?.foreignSymbol,
      standardUnit: value?.foreignUnit,
      standardScale: value?.foreignScale,
      rate: value?.rate,
      allowReceivingCurrency: true
    }
  }
  return {
    ...value,
    allowReceivingCurrency: value?.foreignNumCode
  }
}
