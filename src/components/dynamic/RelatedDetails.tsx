/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/3/9 14:33.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import './RelatedDetails.less'
import { app as api } from '@ekuaibao/whispered'
import FeeDetailViewItem from '../../elements/feeDetailViewList/feeDetailViewItem'
import { related } from '../../elements/feeDetailViewList/Related'
import { Button } from '@hose/eui'
import { get, cloneDeep, reduce } from 'lodash'
import RelateTitleView from '../../elements/feeDetailViewList/relateTitleView'
import { MessageCenter } from '@ekuaibao/messagecenter'
import { MoneyMath } from '@ekuaibao/money-math'
import { EnhanceConnect } from '@ekuaibao/store'
import { standardValueMoney } from '../../lib/misc'
import Big from 'big.js'

interface Props {
  value: any
  onChange: Function
  form: any
  field: any
  applicationListDetails?: any[]
  bus?: any
  template: any
  billTemplate: any
  dataFromOrder: any
}

interface State {
  selectData: any
}

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'linkDetailEntities'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback()
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => {
  return {
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo
  }
})
export default class RelatedDetails extends PureComponent<Props, State> {
  bus = new MessageCenter()
  constructor(props: Props) {
    super(props)
    const { value } = props
    const data = this.fnValue(value)
    this.state = {
      selectData: data
    }
  }

  preGetValue() {
    const { value } = this.props
    const _tempConsumId = this.fnGetDetailId()
    if (!value) {
      related.removeByConsumeId(_tempConsumId)
      return
    }
    return value
  }

  componentDidMount() {
    this.bus.on('get:modify:money', this.getModifyMoney)
    this.getFlowDetail()
    const { value } = this.props
    const _tempConsumId = this.fnGetDetailId()
    const cValue = value ? value : []
    related.setRelatedItemMap({ id: _tempConsumId, value: cValue })
    related.setTempConsumId(_tempConsumId)
    this.props.bus.watch('dynamic:value:blur', this.handleAmountBlur)
  }

  handleAmountBlur = changeValues => {
    const { selectData = [] } = this.state
    const dataList = get(selectData, '[0].dataList', [])
    if (selectData?.length === 1 && dataList?.length === 1) {
      // 只有单个关联的时候做赋值,其他不做
      // 单据模板配置了同步修改、关联单条明细、修改的费用金额小于可用申请金额时，联动更改本次报销金额
      const keepEqualIsChecked = this.checkIskeepEqual()
      if (keepEqualIsChecked && dataList && dataList.length === 1) {
        const amount = get(changeValues, 'amount.standard')
        const money = get(dataList[0], 'unwrittenOffAmount.standard')
        if (amount && money && amount * 1 <= money * 1) {
          this.getModifyMoney(changeValues.amount, dataList[0], true)
        }
      }
    }
  }
  //单据模板关联申请字段是否配置了：费用金额发生变更时同步本次报销金额
  checkIskeepEqual = () => {
    const { billTemplate } = this.props
    const expenseLinksField = billTemplate.find(el => el.field === 'expenseLinks')
    return get(expenseLinksField, 'keepEqualIsChecked')
  }
  getFlowDetail = () => {
    const { value, onChange } = this.props
    if (!value || (value && !value.length)) {
      return
    }
    const ids = value.map((line: any) => line.flowId)
    api.invokeService('@bills:get:ExpenseLink:list', ids).then((result: any) => {
      const flows: any = {}
      let flowList = []
      result?.items?.forEach((item: any) => {
        if (item?.details?.length) {
          flowList = flowList.concat(item.details)
        }
      })
      flowList.forEach(item => (flows[item.id] = item))
      const cValue = value.map((line: any) => {
        // const flowObj: any = flows[line.flowId]
        const flowObj: any = flows[line.dataList[0].linkId]
        const {
          form: { code, title, requisitionMoney, specificationId, submitterId }
        } = flowObj
        return { ...line, title, code, money: requisitionMoney, ownerId: submitterId, specificationId }
      })
      onChange && onChange(cValue)
    })
  }

  componentWillReceiveProps(nextProps: Readonly<Props>, nextContext: any): void {
    if (this.props.value !== nextProps.value) {
      const val = this.fnValue(nextProps.value)
      const data = cloneDeep(val)
      data.map(line => this.fnAddUseMoney(line))
      this.setState({ selectData: data })
    }
  }

  componentWillUnmount() {
    this.bus.un('get:modify:money', this.getModifyMoney)
    this.props.bus.un('dynamic:value:blur', this.handleAmountBlur)
  }

  getModifyMoney = (modifyMoney, value, noUpdateAmount) => {
    const { onChange } = this.props
    const { selectData } = this.state
    const data = cloneDeep(selectData)
    const cData = data.map(v => {
      const dataList = v.dataList.map(item => {
        if (item.id === value.id) {
          item.modifyValue = modifyMoney
        }
        return item
      })
      return { ...v, dataList }
    })
    cData.map(line => this.fnAddUseMoney(line))
    const _tempConsumId = this.fnGetDetailId()
    related.updateRelatedMap(_tempConsumId, value.id, modifyMoney)
    this.setState({ selectData: cData }, () => {
      // noUpdateAmount 是否不更新amount 不更新为true
      if (!noUpdateAmount) {
        this.fnUpdateAmount()
      }
    })
    onChange && onChange(cData)
    this.forceUpdate()
  }

  onAddRelateList = () => {
    const { selectData } = this.state
    const { applicationListDetails, dataFromOrder } = this.props
    const _tempConsumId = this.fnGetDetailId()
    const ids = []
    let list = []
    selectData.length &&
      selectData.forEach(v => {
        list = list.concat(v.dataList)
      })
    list.forEach(v => ids.push(v.id))
    api
      .open('@bills:FeeDetailViewApplyList', {
        ids,
        _tempConsumId,
        from: 'FEEDETAIL',
        dataSource: applicationListDetails,
        dataFromOrder,
      })
      .then((data: any) => {
        // 已修改的数据就不去动他
        const newSelectData = cloneDeep(data)
        const { selectData } = this.state
        const sData = newSelectData.map(line => {
          const selectedLine = selectData.find(it => it?.code === line?.code)
          if (!!selectedLine?.dataList?.length && !!line.dataList?.length) {
            selectedLine.dataList = line.dataList.map(item => {
              const selectedItem = selectedLine.dataList.find(el => el.id === item.id)
              return selectedItem || item
            })
          }
          return selectedLine || line
        })
        sData.map(line => this.fnAddUseMoney(line))
        this.setState({ selectData: sData }, () => {
          this.fnUpdateAmount()
        })
        const { onChange } = this.props
        onChange && onChange(sData)
        this.forceUpdate()
      })
  }

  onDelete = item => {
    const selectData = this.fnGetData(item)
    this.setState({ selectData }, () => {
      this.fnUpdateAmount()
    })
    const _tempConsumId = this.fnGetDetailId()
    related.deleteRelateItem(_tempConsumId, item.id)
    const { onChange } = this.props
    onChange && onChange(selectData)
    this.forceUpdate()
  }

  fnUpdateAmount = () => {
    const { form, bus, template, dataFromOrder, dimentionCurrencyInfo } = this.props
    const { selectData } = this.state
    const amountField = template.find(el => el.name === 'amount')
    const defType = get(amountField, 'defaultValue.type')
    const isFormulaAmount = defType === 'formula' || defType === 'costStandard'
    // 成本归属单传入订单的企业已付金额时，不处理费用明细的金额赋值
    if ((isFormulaAmount && !amountField.editable) || dataFromOrder?.companyRealPayFromOrder) {
      return
    }
    let amount = reduce(
      selectData,
      function(sum, n) {
        return new MoneyMath(sum).add(n.useTotalMoney).value
      },
      0
    ) as any
    if (dimentionCurrencyInfo && dimentionCurrencyInfo?.currency?.numCode !== amount?.numCode) {
      const standardAmount = standardValueMoney(amount.standard, dimentionCurrencyInfo?.currency)
      amount = { ...amount, ...standardAmount }
    }
    if (amount?.budgetStrCode && amount?.standard && amount?.budgetRate) {
      amount.budget = new Big(amount.standard).div(amount.budgetRate).toFixed(Number(amount.budgetScale))
    }
    if (amount.foreignStrCode && amount?.standard && amount?.rate) {
      amount.foreign = new Big(amount.standard).div(amount.rate).toFixed(Number(amount.foreignScale))
    }
    form.setFieldsValue({ amount })
    if (bus && bus.has('amount:changed')) {
      bus.invoke('amount:changed', amount).catch(e => {})
    }
  }

  fnAddUseMoney = (data: any) => {
    const moneys = data.dataList.map((item: any) => item.modifyValue)
    data.useTotalMoney = reduce(
      moneys,
      function(sum, n) {
        return new MoneyMath(sum).add(n).value
      },
      0
    )
  }

  fnValue = (value: any) => {
    return !value ? [] : value
  }

  fnGetDetailId = () => {
    const { field, value } = this.props
    const val = this.fnValue(value)
    const dataList = val.length > 0 ? val[0].dataList : []
    const detailId = dataList && dataList.length > 0 && dataList[0]._tempConsumId
    return detailId || get(field, '_tempConsumId')
  }

  fnGetData = item => {
    const { selectData } = this.state
    const data = cloneDeep(selectData)
    const cData = data.map(v => {
      const dataList = v.dataList.filter(line => line.id !== item.id)
      return { ...v, dataList }
    })
    const oData = cData.filter(v => !!v.dataList.length)
    oData.map(line => this.fnAddUseMoney(line))
    return oData
  }

  render() {
    const { selectData } = this.state
    const { dataFromOrder } = this.props
    const hideAmount = dataFromOrder?.companyRealPayFromOrder
    return (
      <div className="relatelist-wrapper">
        <div className="title">{i18n.get('关联明细')}</div>
        <div className="relatelist">
          <div className="list-wrapper">
            {!!selectData.length &&
              selectData.map((line, index) => {
                const { dataList } = line
                const requisitionConfig = get(line, 'specificationId.configs', []).find(
                  (line: any) => line.ability === 'requisition'
                )
                const applyContentRule = get(requisitionConfig, 'applyContentRule', '')
                const notShowDelete = applyContentRule === 'manual' || applyContentRule === 'amountAndTrip'
                return (
                  <div key={index} className="item-header">
                    <RelateTitleView bus={this.bus}
                                     item={line}
                                     onDelete={this.onDelete}
                                     hideAmount={hideAmount}
                                     isShowDelete={notShowDelete} />
                    {!notShowDelete &&
                      dataList.map((item, index) => {
                        return (
                          <div className="list-item" key={index}>
                            <FeeDetailViewItem bus={this.bus}
                                               data={item}
                                               hideAmount={hideAmount}
                                               onDelete={this.onDelete} />
                          </div>
                        )
                      })}
                  </div>
                )
              })}
          </div>
          <Button category="secondary" theme="highlight" onClick={this.onAddRelateList}>
            {i18n.get('添加关联明细')}
          </Button>
        </div>
      </div>
    )
  }
}
