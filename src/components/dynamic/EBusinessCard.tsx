import React from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { Button, Dropdown } from '@hose/eui'
import { OutlinedTipsWarning, OutlinedTipsAdd, TwoToneLogoHoseCircular } from '@hose/eui-icons'
import { app } from '@ekuaibao/whispered'
import { StaffIF } from '@ekuaibao/ekuaibao_types'
import EBusinessCardComponent from '../../elements/e-card/ECardElement'
import { Fetch } from '@ekuaibao/fetch'
import styles from './EBusinessCard.module.less'

const CARD_TYPES = {
  ALIPAY: 'ALIPAY',
  LL: 'LL',
  LL_PHYSICAL_CARD: 'LL_PHYSICAL_CARD'
} as const

type CardType = keyof typeof CARD_TYPES

interface ICorporateExpenseCard {
  offlineAuthorized: boolean
  id?: string | null
  cardNo?: string | null
  cardType?: string | null
  cardTypeName?: string | null
}

interface IECardOptionalRange {
  domestic?: {
    alipay?: boolean
  }
  international?: {
    ll?: {
      checked?: boolean
    }
    ll_physical_card?: boolean
  }
}

interface IProps {
  value?: ICorporateExpenseCard
  submitterId: StaffIF | string
  onChange: (value: ICorporateExpenseCard) => void
  field?: {
    eCardOptionalRange?: IECardOptionalRange
  }
}

export interface IECardItem {
  id: string
  cardType: CardType
  cardTypeName: string
  cardNo: string
}

interface IState {
  selectedItem: IECardItem | null
  cardList: IECardItem[]
}

@((EnhanceField as any)({
  descriptor: {
    type: 'corporateExpenseCard'
  },
  wrapper: wrapper()
}))
export default class EBusinessCard extends React.Component<IProps, IState> {
  constructor(props) {
    super(props)
    this.state = {
      cardList: [],
      selectedItem: props?.value || null
    }
  }

  async componentDidMount() {
    const { field, submitterId } = this.props
    const { eCardOptionalRange } = field || {}
    const map = this.getCardTypeAvailability(eCardOptionalRange)
    const cardType = Object.keys(map).filter(key => map[key as CardType])
    const ownerIdStr = typeof submitterId === 'string' ? submitterId : submitterId?.id
    const { data } = await app.invokeService('@bills:check:ecard:type:state', {
      mustAvailable: 'true',
      openUserId: ownerIdStr,
      openCorpId: Fetch.ekbCorpId,
      cardType
    })

    this.setState({
      cardList: data || []
    })
  }

  private getCardTypeAvailability = (eCardOptionalRange?: IECardOptionalRange) => {
    return {
      [CARD_TYPES.ALIPAY]: eCardOptionalRange?.domestic?.alipay ?? false,
      [CARD_TYPES.LL]: eCardOptionalRange?.international?.ll?.checked ?? false,
      [CARD_TYPES.LL_PHYSICAL_CARD]: eCardOptionalRange?.international?.ll_physical_card ?? false
    }
  }

  private createEmptyCardData = (): ICorporateExpenseCard => ({
    offlineAuthorized: false,
    id: null,
    cardNo: null,
    cardType: null,
    cardTypeName: null
  })

  handleClick = async (item: IECardItem): Promise<void> => {
    const { onChange } = this.props
    const { id, cardNo, cardType, cardTypeName } = item

    this.setState({
      selectedItem: item
    })
    onChange({
      offlineAuthorized: true,
      id,
      cardNo,
      cardType,
      cardTypeName
    })
  }

  handleDel = (): void => {
    const { onChange } = this.props
    onChange(this.createEmptyCardData())
  }

  private renderECardType = () => {
    const { cardList } = this.state

    const renderLabel = (item: IECardItem) => {
      return (
        <div className={styles.cardItem} onClick={() => this.handleClick(item)}>
          <TwoToneLogoHoseCircular fontSize={32} />
          <div className="content">
            <div className="title">{item?.cardTypeName}</div>
            <div className="cardNo">{item?.cardNo}</div>
          </div>
        </div>
      )
    }

    const items = cardList.map(item => ({
      key: item.id,
      label: renderLabel(item)
    }))

    if (cardList.length) {
      return (
        <Dropdown menu={{ items }} placement="topLeft">
          <Button category="secondary" theme="highlight" size="small" icon={<OutlinedTipsAdd />}>
            {i18n.get('添加易商卡')}
          </Button>
        </Dropdown>
      )
    }

    // 都不可用时显示禁用状态
    return (
      <>
        <Button disabled category="secondary" theme="highlight" size="small" icon={<OutlinedTipsAdd />}>
          {i18n.get('添加易商卡')}
        </Button>
        <div
          style={{
            color: 'var(--eui-text-caption)',
            font: 'var(--eui-font-body-r1)',
            marginTop: 4
          }}
        >
          <OutlinedTipsWarning style={{ marginRight: 4 }} />
          <span>{i18n.get('请联系管理员配置卡片类型')}</span>
        </div>
      </>
    )
  }

  render() {
    const { value } = this.props
    const { selectedItem } = this.state

    return value?.offlineAuthorized ? (
      <EBusinessCardComponent selectedItem={selectedItem} onDel={this.handleDel} />
    ) : (
      this.renderECardType()
    )
  }
}
