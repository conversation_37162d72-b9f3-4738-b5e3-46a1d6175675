/**
 * 出行人组件
 * <AUTHOR>
 * @date    2018-12-21
 */

import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { get, isArray, castArray, isObject } from 'lodash'
import { constantValue, lastSelectValue } from '../utils/fnInitalValue'
import { isDisable } from '../utils/fnDisableComponent'
import styles from './MutilStaff.module.less'
import { FetchRefList } from '../utils/FetchRefList'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { getRecordLink } from './helpers/getRecordLink'
import { clearableTripDataLink } from '../../lib/lib-util'
import { getStaffShowValue } from '../../elements/utilFn'
import { Modal } from 'antd'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import classNames from 'classnames'
import TagSelector from '../../elements/tag-selector'
import { checkStaffDataRange } from '../utils/fnCheckStaffDataRange'
import { getBoolVariation } from '../../lib/featbit'
import { mutilStaffHandleDenpence, useNewAutomaticAssignment } from '../utils/fnAutoDependence'

@EnhanceField({
  descriptor: {
    type: 'list:ref:organization.Staff'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      callback()
    }
    // if (value && editable && !allowExternalStaff) {
    //   if (value.find(o => o.external)) {
    //     const externalRoot = api.getState()['@common'].externalDepartment?.data
    //     callback(
    //       i18n.get(`{label}的取值规则不允许选择`, { label }) +
    //         (externalRoot ? externalRoot[0].name : i18n.get('外部人员'))
    //     )
    //   }
    // }
    if (rule.level === 3) callback()
    callback(required(field, value))
  },
  initialValue(props) {
    let { field, value, submitterId, lastChoice } = props

    if (!value) {
      let constVal = constantValue(field)
      if (constVal) return typeof constVal === 'string' ? [constVal] : constVal
      let lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) return lastVal
    }
    let { defaultValue } = field
    let type = get(field, 'defaultValue.type', '')
    if (!value && type === 'predefine' && defaultValue.value === 'submitter') {
      value = [submitterId.id]
    }
    return value
  },
  wrapper: wrapper()
})
@EnhanceConnect(
  state => ({
    staffs: state['@common'].staffs,
    staffDisplayConfig: state['@common'].organizationConfig.staffDisplayConfig,
    travelManagementConfig: state['@tpp-v2'].travelManagementConfig
  }),
  {
    getRecordLink: getRecordLink
  }
)
@FetchRefList
export default class MutilStaff extends PureComponent {
  constructor(props) {
    super(props)
    let { field } = props
    let { dependence } = field
    let isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    this.state = {
      dependenceList: [],
      isDependence,
      dependenceMap
    }
  }

  async componentWillMount() {
    let { bus, staffs, field } = this.props
    bus.on('on:dependence:change', this.handleDependenceChange)
    if (!staffs?.length) {
      await api.dataLoader('@common.staffs').load()
    }
    if (!this.state.isDependence && staffs?.length === 1 && useNewAutomaticAssignment() && field?.rangeOnlyOneAutomaticAssignment) {
      const result = checkStaffDataRange(field, staffs[0])
      result && this.onChange([result])
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('on:dependence:change', this.handleDependenceChange)
  }

  async componentDidMount() {
    if (getBoolVariation('mfrd-3800-staff-default-value-visible')) {
      let { value, billState, onChange, bus } = this.props
      if (value?.length) {
        const configVisibilityStaffs = await api.dataLoader('@contactManagement.configVisibilityStaffs').load();
        if (billState === 'new' && !configVisibilityStaffs.fullVisible) {
          const newValue = value.filter(staff => configVisibilityStaffs.staffIds.some(id => id === isObject(staff) ? staff?.id : staff))
          if (value.length !== newValue.length) {
            bus.setValidateLevel(1)
            onChange(newValue)
          }
        }
      }
    }
  }

  currentIdx = null

  handleDependenceChange = async ({ key, id, dependenceFeeType = false }, options = {}) => {
    if (useNewAutomaticAssignment()) {
      const result = await mutilStaffHandleDenpence({ key, id, dependenceFeeType }, options, this.state, this.props)
      result && this.setState(result)
      return
    }
    let { getRecordLink, field, onChange, bus, billState, form, detailId, isDetail } = this.props
    let { dependence, dataType, defaultValue, dependenceCondition } = field
    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据

    if (dependence && dependence?.length) {
      let isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (!!isNeedFetch) {
        this.currentIdx = id //标明promise的执行顺序
        const { dependenceMap } = this.state
        let list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        getRecordLink({
          recordSearch: list,
          entity: dataType.elemType.entity,
          dependenceFeeType,
          dependenceCondition
        }).then(action => {
          const reqIdx = id // 闭包记录id
          let { items } = action.payload
          let newValue = undefined
          if (this.currentIdx === reqIdx) {
            const currentStaffs = this.props.value
            if (items.length === 1) {
              bus.setValidateLevel(3)
              newValue = items
              const changeValue = checkStaffDataRange(field, items)
              onChange(changeValue)
            } else if (Array.isArray(currentStaffs)) {
              let isValid = true
              currentStaffs.forEach(staff => {
                if (items.findIndex(item => item.id === staff.id) === -1) {
                  isValid = false
                  return
                }
              })
              if (isInitLoad) {
                const fieldsValue = form.getFieldsValue()
                // 初始化进来 当前value 值赋值
                newValue = fieldsValue[field?.field] ?? undefined
                newValue = checkStaffDataRange(field, newValue)
                onChange(newValue)
              } else if (items.length === 1) {
                bus.setValidateLevel(3)
                newValue = items
                const changeValue = checkStaffDataRange(field, newValue)
                onChange(changeValue)
              } else if (!isValid) {
                bus.setValidateLevel(3)
                onChange(undefined)
              }
            }

            // TODO: 档案关系埋点
            let { billData, billSpecification, feeType, dataSource } = this.props
            const oldValue = this.props?.value
            let newBillData = billData
            let message = '单据上的档案关系赋值'
            if (feeType) {
              message = '明细上的档案关系赋值'
            } else {
              newBillData = dataSource
            }
            api?.logger?.info(message, {
              specificationId: billSpecification?.id,
              specificationName: billSpecification?.name,
              flowId: newBillData?.flowId || newBillData?.id,
              code: newBillData?.code || newBillData?.form?.code,
              sceneName: '档案关系',
              feeTypeId: feeType?.id,
              feeTypeName: feeType?.name,
              field: field?.field,
              dependField: key,
              oldValue,
              newValue
            })
            this.setState({
              dependenceList: items
            })
          }
        })
      }
    }
  }


  handleClick = async () => {
    console.log('多选')
    let { bus, onChange, value = [], field, travelManagementConfig, form, submitterId } = this.props
    let { dependenceList, isDependence } = this.state
    const { allowExternalStaff, allowCancelDependence, optional } = field
    const newValue = castArray(value).map(item => (typeof item === 'string' ? item : item?.id))
    if (isDependence && !dependenceList.length) {
      if (allowCancelDependence) {
        const shouldSelectAllUser = await new Promise((resolve, reject) => {
          Modal.confirm({
            title: i18n.get('您没有可以选择的员工'),
            okText: i18n.get('知道了'),
            cancelText: i18n.get('选择所有员工'),
            onCancel: () => resolve(true),
            onOk: () => resolve(false)
          })
        })
        if (!shouldSelectAllUser) {
          return
        }
        // 取消依赖
        dependenceList = undefined
      } else {
        return showModal.error({
          title: i18n.get('您没有可以选择的员工')
        })
      }
    }
    let staffRangeRule = false
    if (field.valueRangeFilter && field.valueRangeFilter !== 'false') {
      //后端定义这个“false”表示取消限制
      staffRangeRule = field.valueRangeFilter
    }
    // 填单场景需要跟进通讯录范围走
    const followContactRules = true
    if (allowExternalStaff) {
      let checkedList = []
      const internalStaffIds = value.filter(staff => !staff.external).map(line => line.id)
      if (field?.allowInteriorStaff === undefined || field?.allowInteriorStaff) {
        checkedList.push({
          type: 'department-member',
          multiple: true,
          checkedKeys: internalStaffIds
        })
      }
      const externalStaffIds = value.filter(staff => staff.external).map(line => line.id)
      checkedList.push({
        type: 'external',
        multiple: true,
        checkedKeys: externalStaffIds
      })
      bus
        .invoke('element:ref:select:staffs', {
          checkedList,
          allowExternalStaff,
          staffRangeRule,
          required: !optional,
          followContactRules,
          dataSource: isDependence ? dependenceList : undefined
        })
        .then(data => {
          let staffs = []
          data.checkedList.forEach(checked => (staffs = staffs.concat(checked.checkedData || [])))
          onChange(staffs)
          bus.emit('set:tripdatalink:traveler:change')
        })
    } else {
      bus
        .invoke('element:ref:select:staffs', {
          checkedKeys: newValue,
          multiple: true,
          dataSource: isDependence ? dependenceList : undefined,
          allowExternalStaff,
          staffRangeRule,
          required: !optional,
          followContactRules
        })
        .then(async data => {
          const checkStaffs = () => {
            if (data.length > 100) {
              showMessage.error(field.label + i18n.get('不能超过100人'))
              return false
            }
            onChange(data)
            return true
          }
          bus.emit('set:tripdatalink:traveler:change')
          // 存在[出行人][行程规划]且行程管理配置黑名单时，切换出行人时需校验提示
          if (
            field.field === 'travelers' &&
            travelManagementConfig?.contextDetail?.length &&
            form.getFieldValue('u_行程规划')?.length // @i18n-ignore
          ) {
            const { clearable, travelerId } = clearableTripDataLink(data, value, submitterId)
            if (clearable) {
              showModal.confirm({
                title: i18n.get('提示'),
                content: i18n.get('根据行程配置，切换出行人时，您的行程明细将被清空，是否继续？'),
                cancelText: i18n.get('取消'),
                okText: i18n.get('确定'),
                onOk() {
                  if (checkStaffs()) {
                    bus.emit('set:tripdatalink:value')
                    bus.emit('set:tripdatalink:traveler:id', travelerId)
                  }
                }
              })
            } else {
              if (checkStaffs()) {
                bus.emit('set:tripdatalink:traveler:id', travelerId)
              }
            }
          } else {
            checkStaffs()
          }
        })
    }
  }

  handleRemove = staffs => {
    const { onChange, field, form, bus } = this.props
    onChange(staffs)
    if (field.field === 'travelers') {
      bus.emit('set:tripdatalink:traveler:change')
    }
  }

  render() {
    let { value = [], field, staffDisplayConfig = [] } = this.props,
      editable = !isDisable(this.props),
      className1 = editable ? styles.wrapper : styles.wrapper_disabled,
      txt
    let users = []
    if (staffDisplayConfig.length < 2) {
      users = isArray(value) ? value : [value]
    } else {
      users = isArray(value) ? value.filter(item => !!item) : [value]
    }
    const placeholder = getPlaceholder(field)
    return (
      <TagSelector
        value={users}
        onClick={this.handleClick}
        showAvatar={true}
        placeholder={placeholder ?? i18n.get('请选择人员')}
        editable={editable}
        onChange={this.handleRemove}
        id={field.field}
      />
    )
    // let users = []
    // if (staffDisplayConfig.length < 2) {
    //   users =
    //     isArray(value) && value?.length > 0 ? value.filter(item => !!item).map(item => item.name) : [value?.name || '']
    // } else {
    //   users =
    //     isArray(value) && value?.length > 0
    //       ? value
    //         .filter(item => !!item)
    //         .map(line => {
    //           const showValue = getStaffShowValue(line, staffDisplayConfig)
    //           return `${line.name}${showValue}`
    //         })
    //       : [value?.name || '']
    // }
    // txt = users.join(',')
    // let id = 'travelers_tooltip' + field.name.replace(' ', '')
    // const placeholder = getPlaceholder(field)
    // return (
    //   <div>
    //     <div
    //       className={classNames('mutil-staff', `${className1} `)}
    //       disabled={!editable}
    //       onClick={e => (editable ? this.handleClick(e) : false)}
    //       placeholder={placeholder}
    //       data-for={id}
    //       data-tip="dataTip"
    //     >
    //       {txt}
    //     </div>
    //   </div>
    // )
  }
}
