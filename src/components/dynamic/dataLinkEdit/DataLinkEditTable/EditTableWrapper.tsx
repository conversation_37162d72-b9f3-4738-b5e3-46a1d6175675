import React, { Component, createContext, useEffect, useState } from 'react'
import { Form } from 'antd'
import { FormComponentProps } from 'antd/es/form'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './DataLinkEditTable.module.less'
import { includes, isEqual, cloneDeep, debounce } from 'lodash'
import { resetDataLinEdits, formatCellValue } from './utils'
import EKBIcon from '../../../../elements/ekbIcon'
import { ENUM_TYPES } from '../../../consts'
import DataLinksCell from './DataLinksCell'
import RefCell from './RefCell'
import AttachmentsCell from './AttachmentsCell'
import SimpleCell from './SimpleCell'
import CityCell from './CityCell'
import SwitchCell from './SwitchCell'
import MoneyCell from './MoneyCell'
import DateRangeCell from './DateRangeCell'
import DateCell from './DateCell'
import ListRefOrganizationStaffCell from './ListRefOrganizationStaffCell'
import RefOrganizationStaffCell from './RefOrganizationStaffCell'
import PayeeInfoCell from './PayeeInfoCell'
import EnumCell from './EnumCell'
import SimpleEditCell from './SimpleEditCell'
import NumberEditCell from './NumberEditCell'
import SwitchEditCell from './SwitchEditCell'
import DateEditCell from './DateEditCell'
import DateRangeEditCell from './DateRangeEditCell'
import EnumEditCell from './EnumEditCell'
import CityEditCell from './CityEditCell'
import PayeeInfoEditCell from './PayeeInfoEditCell'
import RefOrganizationStaffEditCell from './RefOrganizationStaffEditCell'
import ListRefOrganizationStaffEditCell from './ListRefOrganizationStaffEditCell'
import RefDepartmentEditCell from './RefDepartmentEditCell'
import RefEditCell from './RefEditCell'
import DataLinkEditCell from './DataLinkEditCell'
import DataLinkListEditCell from './DataLinkListEditCell'
import MoneyEditCell from './MoneyEditCell'
import AttachmentsEditCell from './AttachmentsEditCell'
import NumberCell from './NumberCell'
import MessageCenter from '@ekuaibao/messagecenter'
import { standardValueMoney } from '../../../../lib/misc'
import CurrencyMoney from '../../../../elements/currency/currency-money'
import { app } from '@ekuaibao/whispered'
import Big from 'big.js'
import {
  OutlinedEditBatchYes,
  OutlinedTipsAdd,
  OutlinedDirectionDownload,
  OutlinedDirectionUpload,
  OutlinedDirectionWindowMax,
  OutlinedDirectionWindowMini,
  OutlinedDirectionUp,
  OutlinedDirectionDown,
} from '@hose/eui-icons'
import { Fetch } from '@ekuaibao/fetch'
import { getLinkNodeElement, triggerClick } from '@ekuaibao/sdk-bridge/sdk/utils'
import moment from 'moment'
import { Space, Table, Popconfirm, InputNumber, Alert, Progress, Tooltip, Button } from '@hose/eui'
import { realName } from '../../../../elements/util'
import { getBoolVariation } from '../../../../lib/featbit'
import { operatorsMap } from '../../../utils/utilFunctionsParams'



export const getNumValue = (value) => {
  if (!value || (typeof value === 'object' && !value?.standard)) return 0;
  if (typeof value === 'object') {
    return value?.standard
  }
  return value
}

export const formater = (value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

interface IProps extends FormComponentProps {
  isEdit?: boolean
  field?: any
  dataSource?: any[]
  onSelect?: () => void
  onGetData?: (data: any, index?: number | string) => void
  onApportionFormChange?: (data: any) => void
  onFieldEdit?: (data: any, index: any) => void
  editing?: boolean
  authStaffStaffMap?: any
  bus?: any
  flowFormFieldsValue?: any
  flow?: any
  columns?: any[]
  components?: any[]
  noTransColumns?: boolean
  canCreate?: boolean
  editingKey?: string | number
  onGetActiveDataIndex: (editingKey: string | number) => void
  rowSelection?: {
    type?: string
    onChange?: (selectedRowKeys: React.Key[], selectedRows: any[]) => void
  }
  type?: 'settlementChecking' | undefined
  entityId: string
  isHabSetValue?: boolean
  pagination?: any
  onJumpLastPage?: (total: number) => void
  onRowClick?: (record: any, index: number) => void
  apportionConfigs?: {
    field: string;
    label: string;
    type: string;
    defaultApportionField?: string;
    limitConfig?: {
      field?: string;
      operator?: string
    }
  }[]
}

interface IState {
  dataSource?: any[]
  editingKey?: string | number
  needCalculateUpdateFields: Array<string> | null
  isFullScreen: boolean
  apportionForm?: any
  apportionErrors?: string[]
  fieldsValue?: any
}
const EditableContext = createContext(null)

class EditableCell extends Component<IProps> {
  renderCellItem = props => {
    const {
      field: { type }
    } = props
    if (type === 'textarea' || type === 'text') {
      return <SimpleEditCell {...props} />
    } else if (type === 'number') {
      return <NumberEditCell {...props} />
    } else if (type === 'switcher') {
      return <SwitchEditCell {...props} />
    } else if (type === 'date') {
      return <DateEditCell {...props} />
    } else if (type === 'dateRange') {
      return <DateRangeEditCell {...props} />
    } else if (includes(ENUM_TYPES, type) || type?.startsWith('basedata.Enum')) {
      return <EnumEditCell {...props} />
    } else if (type === 'city') {
      return <CityEditCell {...props} />
    } else if (type === 'payeeInfo') {
      return <PayeeInfoEditCell {...props} />
    } else if (type === 'ref:organization.Staff') {
      return <RefOrganizationStaffEditCell {...props} />
    } else if (type === 'list:ref:organization.Staff') {
      return <ListRefOrganizationStaffEditCell {...props} />
    } else if (type === 'ref:organization.Department') {
      return <RefDepartmentEditCell {...props} useEUI />
    } else if (
      type.startsWith('ref') &&
      type !== 'ref:organization.Staff' &&
      type !== 'ref:organization.Department' &&
      type !== 'ref:basedata.Enum.currency' &&
      !includes(ENUM_TYPES, type)
    ) {
      return <RefEditCell {...props} />
    } else if (type === 'dataLink') {
      return <DataLinkEditCell {...props} />
    } else if (type === 'dataLinks') {
      return <DataLinkListEditCell {...props} />
    } else if (type === 'money') {
      return <MoneyEditCell {...props} />
    } else if (type === 'attachments' || type === 'aiAttachments') {
      return <AttachmentsEditCell {...props} />
    } else {
      return '未知类型'
    }
  }

  renderCell = ({ getFieldDecorator }) => {
    const { editing, children, ...restProps } = this.props
    return <td {...restProps}>{editing ? this.renderCellItem({ getFieldDecorator, ...this.props }) : children}</td>
  }

  render() {
    return <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
  }
}

@EnhanceConnect(state => ({
  authStaffStaffMap: state['@common'].authStaffStaffMap
}))
class EditTableWrapper extends Component<IProps, IState> {
  constructor(props) {
    super(props)
    this.state = {
      dataSource: props.dataSource,
      editingKey: props.editingKey || '',
      needCalculateUpdateFields: null,
      isFullScreen: false,
      apportionForm: {},
      apportionErrors: [],
      fieldsValue: {}
    }
  }
  componentWillMount(): void {
    const { bus = new MessageCenter() } = this.props
    bus.watch('get:edit:table:values', this.handleGetEditTableValues)
  }

  componentWillUnmount(): void {
    let { bus = new MessageCenter() } = this.props
    bus.un('get:edit:table:values', this.handleGetEditTableValues)
    // 清理全屏相关的事件监听器和样式
    document.removeEventListener('keydown', this.handleEscKey)
    document.body.style.overflow = 'auto'
  }

  handleGetEditTableValues = () => {
    return this.state.dataSource || []
  }

  componentDidMount(): void {
    this.fnDataSource(this.props)
    // 获取分摊错误

  }
  componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
    if (!isEqual(nextProps.dataSource, this.props.dataSource)) {
      this.fnDataSource(nextProps)
    }
  }
  fnDataSource = props => {
    const { dataSource = [], editingKey = '' } = props
    this.setState({ dataSource, editingKey })
  }

  async handleSave(form, key) {
    // await this.getFormulaValues()
    const { components, onGetData, type } = this.props
    form.validateFields((error, row) => {
      if (error) {
        return
      }
      row = formatCellValue(row, components || [], type)
      // @ts-ignore
      const newData = [...this.state.dataSource]
      const index = newData.findIndex(item => key === item.index)
      if (index > -1) {
        const item = newData[index]
        newData.splice(index, 1, {
          ...item,
          ...row
        })
        this.setState({ dataSource: newData, editingKey: '' })
      } else {
        newData.push({ ...row })
        this.setState({ dataSource: newData, editingKey: '' })
      }

      onGetData?.(newData, '')
    })
  }

  handleCreate = () => {
    const { components, onGetActiveDataIndex, onJumpLastPage } = this.props
    const { dataSource, editingKey } = this.state
    if (editingKey) {
      typeof onJumpLastPage === 'function' && onJumpLastPage(dataSource.length)
      return
    }
    const index = dataSource.length
    const newData = { index }
    components?.forEach(el => {
      newData[el?.name] = el.type === 'switcher' ? false : ''
    })
    const data = [...dataSource, newData]
    this.setState({
      // @ts-ignore
      dataSource: data,
      editingKey: index
    })
    typeof onJumpLastPage === 'function' && onJumpLastPage(data.length)
    onGetActiveDataIndex?.(index)
    this.debouncedGetFormulaValues()
  }

  handleSelect = () => {
    const { onSelect } = this.props
    const { editingKey } = this.state

    if (editingKey) return
    onSelect?.()
  }

  handleCancel = () => {
    this.setState({ editingKey: '' })
  }

  handleEdit = (record, key) => {
    this.setState({ editingKey: key })
  }

  handleDelete = (key, e) => {
    e.stopPropagation()
    e.preventDefault()
    const { onGetData } = this.props
    // @ts-ignore
    const newData = [...this.state.dataSource]
    const dataFilter = newData.filter(item => {
      return item.index !== key
    })
    this.setState({ dataSource: dataFilter })
    onGetData?.(dataFilter)
  }

  getColumns = (editingKey) => {
    const {
      authStaffStaffMap,
      components,
      apportionConfigs,
      noTransColumns,
      isEdit,
      rowSelection,
      onFieldEdit,
      field,
      isHabSetValue,
      onApportionFormChange,
      field: _field
    } = this.props
    const { apportionForm } = this.state;
    const minWidth = editingKey === '' ? 150 : 200
    if (noTransColumns) {
      return components
    }
    let columns =
      components?.map((item, index) => {
        const { field, customData, type } = item
        const editable = this.props.type === 'settlementChecking' ? item.editable : true

        return {
          title: realName({ name: item.label, enName: item.enLabel }),
          dataIndex: field,
          key: field,
          field: item,
          editable,
          width: index !== components.length - 1 ? minWidth : components.length > 5 ? minWidth : undefined,
          render: (text, record) => {
            if (type === 'textarea' || type === 'text') {
              return <SimpleCell text={text} name={item.name} />
            } else if (type === 'number') {
              return <NumberCell text={text} field={item} />
            } else if (type === 'city') {
              return <CityCell text={text} />
            } else if (type === 'switcher') {
              return <SwitchCell text={text} />
            } else if (type === 'money') {
              return <MoneyCell text={text} />
            } else if (type === 'dateRange') {
              return <DateRangeCell text={text} field={item} />
            } else if (type === 'date') {
              return <DateCell text={text} field={item} />
            } else if (type === 'list:ref:organization.Staff') {
              return <ListRefOrganizationStaffCell text={text} authStaffStaffMap={authStaffStaffMap} />
            } else if (type === 'ref:organization.Staff') {
              return <RefOrganizationStaffCell text={text} />
            } else if (type === 'payeeInfo') {
              return <PayeeInfoCell text={text} />
            } else if (includes(ENUM_TYPES, type) || type?.startsWith('basedata.Enum')) {
              return <EnumCell text={text} customData={customData} />
            } else if (
              type?.startsWith?.('ref') &&
              type !== 'ref:organization.Staff' &&
              !includes(ENUM_TYPES, type) &&
              type !== 'ref:basedata.Enum.currency'
            ) {
              // 包含部门
              return <RefCell text={text} />
            } else if (type === 'dataLink' || type === 'dataLinks') {
              return <DataLinksCell text={text} isHabSetValue={isHabSetValue} />
            } else if (type === 'attachments' || type === 'aiAttachments') {
              return <AttachmentsCell value={text} />
            } else {
              return '未知类型'
            }
          }
        }
      }) || ([] as any[])

    if (getBoolVariation('aprd-5665-datalink') && apportionConfigs.length > 0 && 'REF' === _field?.behaviour) {
      const allCurrency = app.getState()['@common'].allCurrencyRates
      const standardCurrency = app.getState()['@common'].standardCurrency
      const apportionColumns = apportionConfigs.map(item => ({
        title: item.label,
        dataIndex: item.field,
        key: item.field,
        field: item,
        columnType: 'apportionColumn',
        width: 160,
        render: (text, record) => {
          if (item.type === 'MONEY' && typeof text !== 'object') {
            text = standardValueMoney(text || "0")
          } else if (item.type === 'NUMBER') {
            text = getNumValue(text)
          }
          const unit = item.type === 'MONEY' ? 'CNY' : ''
          if (isEdit) {
            return item.type === 'MONEY' ?
              <div onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
              }}>
                <CurrencyMoney
                  value={text}
                  valueChange={(value) => {
                    if (!value?.isValidator) return
                    const val = {
                      [item.field]: value.data
                    }
                    apportionForm[record.index] = apportionForm[record.index] ? {
                      ...apportionForm[record.index],
                      ...val,
                    } : val
                    this.setState({
                      apportionForm
                    }, () => {
                      onApportionFormChange(apportionForm)
                    })
                  }}
                  standardCurrency={standardCurrency}
                  currencySelAble
                  useEUI={true}
                  allCurrencyRates={allCurrency}
                  noPopupContainer={true}
                /></div> : <InputNumber
                formatter={value => `${unit} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\CNY\s?|(,*)/g, '')}
                style={{ width: '100%' }}
                onClick={(e) => {
                  e.stopPropagation()
                }}
                placeholder={i18n.get('请输入')}
                value={text}
                onChange={(value) => {
                  const val = {
                    [item.field]: value
                  }
                  apportionForm[record.index] = apportionForm[record.index] ? {
                    ...apportionForm[record.index],
                    ...val,
                  } : val
                  this.setState({
                    apportionForm
                  }, () => {
                    onApportionFormChange(apportionForm)
                  })
                }}
              />
          } else {
            return formater(`${unit} ${text}`)
          }

        }
      }))
      columns = columns.concat(apportionColumns)
    }
    if (isEdit) {
      columns.push({
        title: i18n.get('操作'),
        dataIndex: 'operation',
        key: 'operation',
        width: 96,
        fixed: 'right',
        render: (text, record) => {
          const { editingKey } = this.state
          const editable = this.isEditing(record)
          return editable ? (
            <div className="btn-group">
              <EditableContext.Consumer>
                {form => (
                  <EKBIcon
                    className="icon-save icon-style stand-20-icon"
                    name="#EDico-zf-save"
                    onClick={() => this.handleSave(form, record.index)}
                  />
                )}
              </EditableContext.Consumer>
              <Popconfirm title="确定取消?" onConfirm={() => this.handleCancel()}>
                <EKBIcon className="icon-cancel icon-style stand-20-icon" name="#EDico-zf-off" />
              </Popconfirm>
            </div>
          ) : (
            <div className="btn-group">
              {record.disabledByState ? null : (
                // @ts-ignore
                <a disabled={editingKey !== ''}>
                  {field?.behaviour !== 'REF' && (
                    <EKBIcon
                      className="icon-style stand-20-icon"
                      name="#EDico-zf-edit"
                      onClick={() => this.handleEdit(record, record.index)}
                    />
                  )}
                  <EKBIcon
                    className="icon-style stand-20-icon"
                    name="#EDico-zf-delete"
                    onClick={e => this.handleDelete(record.index, e)}
                  />
                </a>
              )}
            </div>
          )
        }
      })
    }
    if (!rowSelection) {
      columns.unshift({
        title: '',
        dataIndex: 'index',
        key: 'index',
        fixed: 'left',
        className: 'index-column',
        // width: 40,
        render: (text, record) => {
          return (
            <div className="edit-number-wrapper">
              {/* <SimpleCell text={text + 1} name={undefined} /> */}
              <span>{text + 1}</span>
              {onFieldEdit && (
                <EKBIcon
                  className="icon-style stand-20-icon show"
                  name="#EDico-zoom"
                  onClick={() => onFieldEdit(record, record.index)}
                />
              )}
            </div>
          )
        }
      })
    }
    return columns
  }
  isEditing = record => record.index === this.state.editingKey

  renderReminder = () => {
    return (
      <div style={{ color: 'var(--eui-function-danger-500)' }}>
        <p>{i18n.get('注意')}</p>
        <p>{i18n.get('1、如果要新增数据请下载空白模版')}</p>
        <p>{i18n.get('2、导入后单据表格中原有数据将清空，并将Excel中的数据展示在表格中')}</p>
      </div>
    )
  }

  handleImport = () => {
    const { entityId, onGetData, field } = this.props
    app
      .open('@bills:ImportDetailByExcel', {
        type: 'datalink4flow',
        flag: {
          id: entityId,
          fieldName: field?.label
        },
        renderReminder: this.renderReminder
      })
      .then(data => {
        const flattenData = data?.map((item, index) => ({
          ...item.dataLinkForm,
          dataLinkTemplateId: item.dataLinkTemplateId,
          dataLinkId: item.dataLinkId,
          index
        }))
        this.setState({ dataSource: flattenData })

        onGetData?.(flattenData)
      })
  }
  handleExport = async () => {
    const { field, type } = this.props
    const { staff } = await app.open('@third-party-manage:ExportOption', {
      showStaff: true,
      hideColumnOption: true
    })
    const newData = cloneDeep(this.state.dataSource).map(item => {
      const dataLinkId = item.dataLinkId || null
      const dataLinkTemplateId = item.dataLinkTemplateId
      delete item.disabledByState
      delete item.dataLinkId
      delete item.dataLinkTemplateId
      delete item.index
      return {
        dataLinkId,
        dataLinkTemplateId: dataLinkTemplateId,
        dataLinkForm: resetDataLinEdits(item)
      }
    })
    const body = {
      items: newData,
      form: {
        staffType: staff
      }
    }
    Fetch.POST(
      `/api/v1/datalink/excel/export/datalink4flow/$${this.props.entityId}`,
      { corpId: encodeURIComponent(Fetch.ekbCorpId) },
      {
        body,
        headers: { accept: '*' },
        isBlob: true
      }
    )
      .then(blob => {
        const el = getLinkNodeElement()
        const url = window.URL.createObjectURL(blob)
        const l = type === 'settlementChecking' ? '账单调整' : field?.label
        const filename = `${l}_${moment().format('YYYYMMDD_HHmmss')}.xlsx`
        const name = decodeURIComponent(filename as string)
        el.setAttribute('href', url)
        el.setAttribute('download', name)
        triggerClick(el)
      })
      .catch(err => { })
  }
  getNeedCalculateUpdateFields = () => {
    const { components } = this.props
    const { needCalculateUpdateFields } = this.state
    if (needCalculateUpdateFields) {
      return needCalculateUpdateFields
    }
    const calculateUpdateFields = []
    components?.forEach((item) => {
      if (item.formula) {
        calculateUpdateFields.push(item.name)
      }
    })
    this.setState({
      needCalculateUpdateFields: calculateUpdateFields
    })
    return calculateUpdateFields
  }

  getFormulaValues = async () => {
    if (!getBoolVariation('ao-2-datalink-show-calculate')) return
    try {
      const { form, entityId, dataSource } = this.props
      const { editingKey } = this.state
      // 数字类型值转换为字符串
      const formValue = Object.entries(form.getFieldsValue()).reduce((acc, [key, value]) => {
        acc[key] = typeof value === 'number' ? String(value) : value
        return acc
      }, {})
      const params = {
        dataLinkFormList: [{
          form: formValue,
          dataLinkId: dataSource[editingKey]?.dataLinkId,
        }],
        dataLinkEntityId: entityId
      }
      const result = await app.invokeService('@bills:get:getDataLinkEditCalculate', params)
      const newValues = {}
      const needCalculateUpdateFields = this.getNeedCalculateUpdateFields()
      Object.keys(result[0].form).forEach((key) => {
        if (needCalculateUpdateFields.includes(key)) {
          newValues[key] = result[0].form[key]
        }
      })
      form.setFieldsValue(newValues)
    } catch (error) {
      console.log(error)
    }
  }

  // 自动计算
  debouncedGetFormulaValues = debounce(this.getFormulaValues, 500)

  onChange = (field) => {
    const isNeedFormula = Boolean(field?.calculation?.dependenciesBy?.length)
    if (isNeedFormula) {
      this.debouncedGetFormulaValues()
    }
  }

  handleFullScreen = () => {
    const { isFullScreen } = this.state
    this.setState({ isFullScreen: !isFullScreen }, () => {
      // 全屏时防止背景滚动
      if (!isFullScreen) {
        document.body.style.overflow = 'hidden'
        document.addEventListener('keydown', this.handleEscKey)
      } else {
        document.body.style.overflow = 'auto'
        document.removeEventListener('keydown', this.handleEscKey)
      }
    })
  }

  handleEscKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && this.state.isFullScreen) {
      this.handleFullScreen()
    }
  }

  render() {
    const { dataSource, editingKey, isFullScreen, apportionErrors } = this.state
    const { flowFormFieldsValue, bus, form, canCreate, rowSelection, onSelect, isEdit, field, onRowClick, type, pagination = false } = this.props
    let columns = this.getColumns(editingKey)
    const summaryIndex = columns.findIndex(item => item?.columnType === 'apportionColumn')

    columns = columns.map(col => {
      const newCol = {
        ...col,
        dataIndex: typeof col.dataIndex === 'string'
          ? col.dataIndex.split('.')
          : col.dataIndex,
      }
      if (!newCol.editable) {
        return newCol
      }
      return {
        ...newCol,
        onCell: record => ({
          record,
          bus,
          dataIndex: newCol.dataIndex,
          title: newCol.title,
          field: newCol.field,
          editing: this.isEditing(record),
          form,
          onChange: () => this.onChange(col.field)
        })
      }
    })

    if (!columns.length) {
      return null
    }
    let otherProps: any = rowSelection ? { rowSelection } : {}
    if (summaryIndex >= 0) {
      otherProps.summary = () => <Summary
        flowFormFieldsValue={flowFormFieldsValue}
        summaryIndex={summaryIndex}
        columns={columns}
        dataSource={dataSource}
      />
    }

    const wrapperClassName = isFullScreen ? styles.fullScreenWrapper : styles.dataLinkEditTableWrapper

    const scroll = isFullScreen ? { x: 300, y: 'calc(100vh - 200px)' } : { x: 300 }
    return (
      <EditableContext.Provider value={form}>
        <AlertApportionError
          columns={columns}
          flowFormFieldsValue={flowFormFieldsValue}
          dataSource={dataSource}
        />
        {apportionErrors.length > 0 && <Alert
          type="error"
          message={`当前表格存在 ${apportionErrors.length} 个问题，请修改后再提交单据`}
          className='overflow-description'
          description={
            <div>
              {apportionErrors.map((item, index) => <div key={index}>${item}</div>)}
            </div>
          }
        />}
        <div className={`${styles.editableWrapper} ${wrapperClassName}`}>
          <div className={styles.dataLinkEditTableHeader}>
            <Space>
              {onSelect && (
                <span onClick={this.handleSelect} className={styles.itemCreateBtn}>
                  <OutlinedEditBatchYes /> {i18n.get('选择')}
                </span>
              )}
              {canCreate && (
                <span onClick={this.handleCreate} className={styles.itemCreateBtn}>
                  <OutlinedTipsAdd /> {i18n.get('点击添加')}
                </span>
              )}
              {(canCreate || onSelect) && field?.behaviour !== 'REF' && (
                <span onClick={this.handleImport} className={styles.itemCreateBtn}>
                  <OutlinedDirectionDownload /> {i18n.get('导入')}
                </span>
              )}
              {type !== 'settlementChecking' && isEdit && field?.behaviour !== 'REF' && (
                <span onClick={this.handleExport} className={styles.itemCreateBtn}>
                  <OutlinedDirectionUpload /> {i18n.get('导出')}
                </span>
              )}
            </Space>
            <div>
              {isFullScreen ? (
                <span onClick={this.handleFullScreen} className={styles.itemCreateBtn}>
                  <OutlinedDirectionWindowMini /> {i18n.get('退出全屏')}
                </span>
              ) : (
                <span onClick={this.handleFullScreen} className={styles.itemCreateBtn}>
                  <OutlinedDirectionWindowMax /> {i18n.get('全屏')}
                </span>
              )}
            </div>
          </div>
          {/* @ts-ignore */}
          <Table
            key={`datalink-table-${editingKey}`}
            className={styles.dataLinkEditTable}
            components={{
              body: {
                cell: EditableCell
              }
            }}
            bordered
            dataSource={dataSource}
            columns={columns}
            pagination={pagination}
            size="middle"
            scroll={scroll}
            onRow={(record, index) => ({
              onClick: () => onRowClick?.(record, index)
            })}
            {...otherProps}
          />
        </div>
      </EditableContext.Provider>
    )
  }
}

export default Form.create()(EditTableWrapper)



export const Summary = (props) => {
  const { flowFormFieldsValue, summaryIndex, columns, dataSource } = props
  const baseDataPropertiesMap = app.getState()['@common'].globalFields.baseDataPropertiesMap
  if (dataSource.length > 0) {
    return (
      <Table.Summary fixed>
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}></Table.Summary.Cell>
          <Table.Summary.Cell index={1} colSpan={summaryIndex - 1}></Table.Summary.Cell>
          {
            columns.map((item, index) => {
              if (item?.columnType === 'apportionColumn') {
                const curColumnTotalValue = dataSource.reduce((cur, next) => {
                  const sum = new Big(cur).plus(getNumValue(next[item.dataIndex])).valueOf()
                  return Number(sum)
                }, 0)

                let targetValue = getNumValue(flowFormFieldsValue?.[item.field?.limitConfig?.field])
                if (item.field?.limitConfig?.field === 'expenseMoney' && flowFormFieldsValue?.[item.field?.limitConfig?.field] === undefined) {
                  const expenseMoney = flowFormFieldsValue?.details?.reduce((cur, next) => {
                    const sum = new Big(cur).plus(getNumValue(next?.feeTypeForm?.amount)).valueOf()
                    return Number(sum)
                  }, 0)
                  targetValue = expenseMoney
                }
                const operator = operatorsMap[item.field?.limitConfig?.operator]
                let strokeColor
                if (operator === '<') {
                  strokeColor = curColumnTotalValue < targetValue ? 'var(--eui-function-info-500, #3491FA)' : 'var(--eui-function-danger-500, #F53F3F)'
                } else if (operator === '≤') {
                  strokeColor = curColumnTotalValue <= targetValue ? 'var(--eui-function-info-500, #3491FA)' : 'var(--eui-function-danger-500, #F53F3F)'
                } else if (operator === '=') {
                  strokeColor = curColumnTotalValue == targetValue ? 'var(--eui-function-info-500, #3491FA)' : 'var(--eui-function-danger-500, #F53F3F)'
                }
                const justResultContent = <div>
                  汇总：
                  <div>{formater(curColumnTotalValue)}</div>
                </div>

                const content = item.field?.limitConfig && typeof flowFormFieldsValue === 'object' && item.field?.limitConfig?.field in flowFormFieldsValue || (item.field?.limitConfig?.field === 'expenseMoney' && targetValue) ? <div>
                  汇总进度：
                  <div>{formater(curColumnTotalValue)}/{formater(targetValue)}</div>
                  <Tooltip title={strokeColor !== 'var(--eui-function-info-500, #3491FA)' && `${item.title}必须 ${operator} 单据的${baseDataPropertiesMap[item.field?.limitConfig?.field]?.label}：${formater(targetValue)}`}>
                    <Progress percent={curColumnTotalValue / targetValue * 100} strokeColor={strokeColor} showInfo={false} />
                  </Tooltip>
                </div> : justResultContent
                return <Table.Summary.Cell index={index}>
                  {content}
                </Table.Summary.Cell>
              } else if (item.fixed == 'right') {
                return <Table.Summary.Cell index={index}></Table.Summary.Cell>
              }
              return null
            })
          }

        </Table.Summary.Row>
      </Table.Summary>
    )
  }
  return null
}

export const AlertApportionError = (props) => {
  const { columns, flowFormFieldsValue, dataSource } = props
  const [errors, setErrors] = useState([])
  const [collapsed, setCollapsed] = useState(true)
  useEffect(() => {
    const baseDataPropertiesMap = app.getState()['@common'].globalFields.baseDataPropertiesMap
    const _errors = columns.map((item, index) => {
      if (item?.columnType === 'apportionColumn' && item.field?.limitConfig) {
        if (item?.field?.limitConfig?.field !== 'expenseMoney' && !(typeof flowFormFieldsValue === 'object' && item.field?.limitConfig?.field in flowFormFieldsValue)) {
          return null
        }
        if (item?.field?.limitConfig?.field === 'expenseMoney' && !flowFormFieldsValue?.details?.length) {
          return null
        }
        const curColumnTotalValue = dataSource.reduce((cur, next) => {
          const sum = new Big(cur).plus(getNumValue(next[item.dataIndex])).valueOf()
          return Number(sum)
        }, 0)
        let targetValue = getNumValue(flowFormFieldsValue?.[item.field?.limitConfig?.field])
        if (item.field?.limitConfig?.field === 'expenseMoney' && flowFormFieldsValue?.[item.field?.limitConfig?.field] === undefined) {
          const expenseMoney = flowFormFieldsValue?.details?.reduce((cur: any, next: any) => {
            const sum = new Big(cur).plus(getNumValue(next?.feeTypeForm?.amount)).valueOf()
            return Number(sum)
          }, 0)
          targetValue = expenseMoney
        }
        const operator = operatorsMap[item.field?.limitConfig?.operator]
        let errorTip = `${item.title}必须 ${operator} 单据的${baseDataPropertiesMap[item.field?.limitConfig?.field]?.label}：${formater(targetValue)}`
        if (operator === '<') {
          errorTip = curColumnTotalValue < targetValue ? '' : errorTip
        } else if (operator === '≤') {
          errorTip = curColumnTotalValue <= targetValue ? '' : errorTip
        } else if (operator === '=') {
          errorTip = curColumnTotalValue == targetValue ? '' : errorTip
        }
        return errorTip;
      }
      return null
    }).filter(Boolean)
    setErrors(_errors);
  }, [columns, dataSource, flowFormFieldsValue])
  if (errors.length > 0 && dataSource.length > 0) {
    return (<Alert
      type="error"
      message={`当前表格存在 ${errors.length} 个问题，请修改后再提交单据`}
      className='overflow-description'
      action={
        <Button
          size="small"
          category="text"
          theme="highlight"
          onClick={() => {
            setCollapsed((prev) => !prev)
          }}
          icon={collapsed ? <OutlinedDirectionDown /> : <OutlinedDirectionUp />}>
          {collapsed ? i18n.get('展开') : i18n.get('收起')}
        </Button>
      }
      description={
        collapsed ? null : <div>
          {errors.map((item, index) => <div key={index}>{index + 1}. {item}{index === errors.length - 1 ? '。' : '；'}</div>)}
        </div>
      }
    />)
  }
  return null
}