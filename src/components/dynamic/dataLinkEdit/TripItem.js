import React from 'react'
import styles from './TripItem.module.less'
import { Tooltip, Tag } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
const EKBIcon = api.require('@elements/ekbIcon')
import { useEffect, useState } from 'react'
import { Resource } from '@ekuaibao/fetch'
const cityFetch = new Resource('/api/v2/basedata/city/')
const cityIanaFetch = new Resource('/api/v2/basedata/city/iana/')
import { getBoolVariation } from '../../../lib/featbit'

const TripItem = ({
  tripType,
  dateString,
  cityStr,
  readonly,
  editable,
  showButton,
  money,
  type,
  onOpen,
  onDeleteTrip,
  onShowDetail,
  customField,
  tooltipStr,
  startCity,
  endCity,
  originStartDate,
  originEndDate,
  referenceStartTime,
  referenceEndTime
}) => {
  const [startCityEnhance, setStartCityEnhance] = useState([])
  const [endCityEnhance, setEndCityEnhance] = useState([])

  const [startCityStr, setStartCityStr] = useState([])
  const [endCityStr, setEndCityStr] = useState([])
  
  // 时区相关状态
  const [localUTC, setLocalUTC] = useState()
  const [startDateUTCs, setStartDateUTCs] = useState([])
  const [endDateUTCs, setEndDateUTCs] = useState([])
  const [allStartCityWithTimezone, setAllStartCityWithTimezone] = useState('')
  const [allEndCityWithTimezone, setAllEndCityWithTimezone] = useState('')

  const formatCity = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray
    return cityArray.map(city => city.label || city.name || '').join(', ')
  }

  const formatCityOrigin = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray
    
    if (cityArray.length === 1) {
      return cityArray[0].label || cityArray[0].name || ''
    }
    
    if (cityArray.length > 1) {
      const firstCity = cityArray[0].label || cityArray[0].name || ''
      return `${firstCity}(+${cityArray.length - 1})`
    }
    
    return ''
  }

  // 计算UTC偏移量的函数
  const getUtcOffset = (timestamp, timeZone) => {
    if (!timeZone) {
      return ''
    }
    try {
      const dtf = new Intl.DateTimeFormat('en-US', {
        timeZone,
        timeZoneName: 'shortOffset'
      })
      const parts = dtf.formatToParts(new Date(timestamp))
      const offsetPart = parts.find(p => p.type === 'timeZoneName')
      return offsetPart ? offsetPart.value.replace('GMT', 'UTC') : null
    } catch (error) {
      console.warn('该浏览器不支持设备时区详细信息获取，可能导致UTC显示异常，建议更换浏览器使用', error)
      return ''
    }
  }

  // 计算时区集合的函数
  const calculateTimeZoneOffsets = (cities, timestamp) => {
    if (!Array.isArray(cities) || cities.length === 0) return []
    
    return cities
      .map(city => {
        if (city?.iana) {
          const utcOffset = getUtcOffset(timestamp, city.iana)
          // 过滤掉与本地UTC相同的值
          return utcOffset && utcOffset !== localUTC ? utcOffset : null
        }
        return null
      })
      .filter(Boolean) // 过滤掉null值
  }

  // 生成带时区信息的城市显示文本
  const generateCityWithTimezone = (cities, startTimestamp, endTimestamp) => {
    if (!Array.isArray(cities) || cities.length === 0) {
      return ''
    }

    const cityLabels = cities.map(city => {
        let cityLabel = city?.label || city?.name || ''
        
        if (city?.iana && startTimestamp && endTimestamp) {
          const startUTC = getUtcOffset(startTimestamp, city.iana)
          const endUTC = getUtcOffset(endTimestamp, city.iana)
          
          if (startUTC && endUTC) {
            // const filteredStartUTC = startUTC === localUTC ? null : startUTC
            const filteredStartUTC = startUTC
            // const filteredEndUTC = endUTC === localUTC ? null : endUTC
            const filteredEndUTC = endUTC
            
            if (filteredStartUTC && filteredEndUTC) {
              if (filteredStartUTC === filteredEndUTC) {
                cityLabel = `${cityLabel}<${filteredStartUTC}>`
              } else {
                cityLabel = `${cityLabel}<${filteredStartUTC},${filteredEndUTC}>`
              }
            } else if (filteredStartUTC) {
              cityLabel = `${cityLabel}<${filteredStartUTC}>`
            } else if (filteredEndUTC) {
              cityLabel = `${cityLabel}<${filteredEndUTC}>`
            }
          }
        }

        if(city.type === 'cityGroup') {
          cityLabel = `${cityLabel}<UTC+8>`
        }
        return cityLabel
      })

      if (cityLabels.length > 0 && cityLabels.every(utc => utc.includes(localUTC))) {
        return null
      }else{
        return cityLabels.join('、')
      }
  }

  // 初始化本地UTC
  useEffect(() => {
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const localUTC = getUtcOffset(new Date().getTime(), timeZone)
    setLocalUTC(localUTC)
  }, [])

  useEffect(() => {
    console.log('bbbbbbbbbbb+useEffect——getBoolVariation', getBoolVariation('fkrd-5600_trip_UTC')) 
    if(!getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')) return
    const fetchData = async () => {
      // 1. 先获取城市详情信息
      const { enhanceFromCityData, enhanceToCityData } = await fetchAndProcessCityDetails(startCity, endCity)
      
      // 2. 再获取IANA时区信息
      const { enhanceFromCityData: ianaFromCityData, enhanceToCityData: ianaToCityData } = await fetchAndProcessCityIana(startCity, endCity)
      
      // 3. 合并城市详情和IANA时区信息
      const mergeAllData = (cityData, ianaData) => {
        if (!Array.isArray(cityData) || !Array.isArray(ianaData)) {
          return cityData
        }
        
        return cityData.map(city => {
          const ianaCity = ianaData.find(iana => iana.key === city.key)
          return {
            ...city,
            ...(ianaCity?.iana && { iana: ianaCity.iana })
          }
        })
      }
      
      const finalFromCityData = mergeAllData(enhanceFromCityData, ianaFromCityData)
      const finalToCityData = mergeAllData(enhanceToCityData, ianaToCityData)
      
      setStartCityStr(formatCityOrigin(finalFromCityData))
      setEndCityStr(formatCityOrigin(finalToCityData))
      setStartCityEnhance(formatCity(finalFromCityData))
      setEndCityEnhance(formatCity(finalToCityData))
      
      // 4. 计算时区信息 - 使用传入的时间戳参数
      if (originStartDate) {
        const startTimestamp = originStartDate
        const endTimestamp = originEndDate || startTimestamp
        
        // 计算出发地时区偏移量
        const startUTCs = calculateTimeZoneOffsets(finalFromCityData, startTimestamp)
        let uniqueStartUTCs = Array.from(new Set(startUTCs))
        if(finalFromCityData?.length > 0 && finalFromCityData?.[0]?.type === 'cityGroup' && localUTC !== 'UTC+8') {
          uniqueStartUTCs = ["UTC+8"]
        }
        setStartDateUTCs(uniqueStartUTCs)
        
        // 计算目的地时区偏移量（结束时间结合出发地城市）
        const endUTCs = calculateTimeZoneOffsets(finalFromCityData, endTimestamp)
        let uniqueEndUTCs = Array.from(new Set(endUTCs))
        if(finalFromCityData?.length > 0 && finalFromCityData?.[0]?.type === 'cityGroup' && localUTC !== 'UTC+8') {
          uniqueEndUTCs = ["UTC+8"]
        }
        setEndDateUTCs(uniqueEndUTCs)
        
        // 生成带时区信息的城市显示文本
        if(getBoolVariation('fkrd-5600_trip_UTC')) {
          const allStartCityWithTimezone = generateCityWithTimezone(finalFromCityData, startTimestamp, endTimestamp)
          const allEndCityWithTimezone = generateCityWithTimezone(finalToCityData?.length ? finalToCityData : finalFromCityData, startTimestamp, endTimestamp)
          setAllStartCityWithTimezone(allStartCityWithTimezone)
          setAllEndCityWithTimezone(allEndCityWithTimezone)
        }
      }
    }
    setTimeout(() => {
      fetchData()
    }, 500);
  }, [startCity, endCity, originStartDate, originEndDate, localUTC])

  const fetchAndProcessCityDetails = async (travelFromCity, travelToCity) => {
    const fromCityData = travelFromCity?.length > 0 ? (travelFromCity && JSON.parse(travelFromCity)) || [] : []
    const toCityData = travelToCity?.length > 0 ? (travelToCity && JSON.parse(travelToCity)) || [] : []
    const foreignCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        foreignCityKeys.push(city.key)
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        foreignCityKeys.push(city.key)
      }
    }

    if (foreignCityKeys.length > 0) {
      try {
        const res = await cityFetch.GET('[ids]', { ids: foreignCityKeys.join(',') })

        if (res && res.items && Array.isArray(res.items)) {
          const processCityDetails = (cityData, cityDetails) => {
            if (!cityData || !cityDetails || !Array.isArray(cityData) || !Array.isArray(cityDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))

            cityDetails.forEach(cityDetail => {
              if (cityDetail.fullName && cityDetail.countryCode !== 'CN') {
                const countryName = cityDetail.fullName.split(',')[0]
                newCityData.forEach(city => {
                  if (city.key === cityDetail.id && !city.label.includes(`(${countryName})`)) {
                    city.label = `${city.label}(${countryName})`
                  }
                })
              }
            })

            return newCityData
          }

          const enhanceFromCityData = processCityDetails(fromCityData, res.items)
          const enhanceToCityData = processCityDetails(toCityData, res.items)

          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市详情失败:', error)
      }
    }

    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  // 获取IANA时区信息
  const fetchAndProcessCityIana = async (travelFromCity, travelToCity) => {
    const fromCityData = travelFromCity?.length > 0 ? (travelFromCity && JSON.parse(travelFromCity)) || [] : []
    const toCityData = travelToCity?.length > 0 ? (travelToCity && JSON.parse(travelToCity)) || [] : []
    
    // 收集所有城市的 ID
    const allCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        if (city?.key) {
          allCityKeys.push(city.key)
        }
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        if (city?.key) {
          allCityKeys.push(city.key)
        }
      }
    }

    if (allCityKeys.length > 0) {
      try {
        const res = await cityIanaFetch.POST('', { ids: allCityKeys })
        
        if (res && res.items && Array.isArray(res.items)) {
          const processCityIana = (cityData, ianaDetails) => {
            if (!cityData || !ianaDetails || !Array.isArray(cityData) || !Array.isArray(ianaDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))
            
            ianaDetails.forEach(ianaDetail => {
              if (ianaDetail.iana) {
                newCityData.forEach(city => {
                  if (city.key === ianaDetail.id) {
                    city.iana = ianaDetail.iana
                  }
                })
              }
            })
            
            return newCityData
          }

          const enhanceFromCityData = processCityIana(fromCityData, res.items)
          const enhanceToCityData = processCityIana(toCityData, res.items)
          
          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市IANA信息失败:', error)
      }
    }
    
    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  const handleGetPrice = e => {
    onOpen && onOpen(e)
  }
  const handleDeleteTrip = e => {
    onDeleteTrip && onDeleteTrip(e)
  }
  const handleShowDetail = () => {
    onShowDetail && onShowDetail()
  }

  // 格式化时间显示
  const formatTimeDisplay = (timestamp) => {
    if (!timestamp) return ''
    
    const date = new Date(timestamp)
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${month}月${day}日`
  }

  // 创建带Tag的时间显示组件
  const renderTimeWithTimezone = () => {
    if (!getBoolVariation('fkrd-5600_trip_UTC')) {
      return <div className="date">{dateString}</div>
    }

    if (!originStartDate) {
      return <div className="date">{dateString}</div>
    }

    try {
      const startTimestamp = originStartDate
      const endTimestamp = originEndDate || startTimestamp
      // 格式化时间显示
      const startTimeStr = formatTimeDisplay(startTimestamp)
      const endTimeStr = originEndDate ? formatTimeDisplay(endTimestamp) : null
      return (
        <div className="date">
          <span>{referenceStartTime || startTimeStr}</span>
          {startDateUTCs.length > 0 && (
            <Tag className="ml-4" color="neu" size="small">
              {startDateUTCs.length > 1 ? i18n.get('多时区') : startDateUTCs[0]}
            </Tag>
          )}

          {endTimeStr && (
            <>
              <span className="mx-2">-</span>
              <span>{referenceEndTime || endTimeStr}</span>
              {endDateUTCs.length > 0 && (
                <Tag className="ml-4" color="neu" size="small">
                  {endDateUTCs.length > 1 ? i18n.get('多时区') : endDateUTCs[0]}
                </Tag>
              )}
            </>
          )}
        </div>
      )
    } catch (error) {
      return <div className="date">{dateString}</div>
    }
  }
  return (
    <section className={styles['trip-item']} onClick={() => handleShowDetail()}>
      <div className="infoTop">
        <img className="trip-type-icon" src={tripType.icon} style={{ background: tripType.color }} />
        <div className="content-wrapper">
          {renderTimeWithTimezone()}
          {getBoolVariation('fkrd-5582-travel-planning-show-foreign-country') && startCityStr ? <>
            <Tooltip title={allStartCityWithTimezone || startCityEnhance || ''}>
                <span className="city">{startCityStr}</span>
            </Tooltip>
            {endCity && (
            <Tooltip title={allEndCityWithTimezone || endCityEnhance || ''}>
                <span className="city">&nbsp;-&nbsp;{endCityStr}</span>
            </Tooltip>
          )}
          </> : 
            <Tooltip title={tooltipStr || ''}>
                <span className="city">{cityStr}</span>
            </Tooltip>
           }
        </div>
        {!readonly && editable && (
          <div onClick={e => handleDeleteTrip(e)}>
            <EKBIcon name="#ico-7-a-Group87" className="close-icon" />
          </div>
        )}
      </div>
      <div className="price-wrapper">
        {showButton && (
          <div className="content-money">
            <span onClick={e => handleGetPrice(e)}>{money ? '重新获取 > ' : '获取参考报价 > '}</span>
          </div>
        )}
        {money ? (
          <div className="content-money">
            <div>
              {i18n.get('参考报价:{money}元/', { money })}
              {i18n.get(type == 'HOTEL' ? '晚' : '张')}
            </div>
          </div>
        ) : null}
      </div>
      <div className="custom-field-wrapper">{customField}</div>
    </section>
  )
}

export default TripItem
