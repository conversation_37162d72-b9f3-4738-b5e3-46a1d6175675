import React, { PureComponent } from 'react'
import classNames from 'classnames'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { required } from '../validator/validator'
import { wrapper } from '../layout/FormWrapper'
import { Col, Row } from 'antd'
import { Select as EUISelect, Button } from '@hose/eui'
import styles from './InvoiceSelect.module.less'
import RefInvoice from '../../elements/invoice-form/RefInvoice'
import { EnhanceConnect } from '@ekuaibao/store'
import { QuerySelect } from 'ekbc-query-builder'
import { app as api } from '@ekuaibao/whispered'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { isDisable, detailIsDisable } from '../utils/fnDisableComponent'
import { getInvoiceOptions } from '../../lib/third-data'
import { isFunction } from '@ekuaibao/helpers'
import { get, cloneDeep } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'
import CardListView from '../../elements/InvoiceCard/InvoiceItem'
import { invoiceItemOptions, parseAsShowValue, fnGetInvoiceManage } from '../../elements/invoice-form/utils/config'
import { invoiceOptions, resetOverseasMasterInvoice, otherInvoiceByDimensionMoneyFormat } from '../../lib/InvoiceUtil'
import { getV } from '@ekuaibao/lib/lib/help'
import InvoiceMappingValue from '../../lib/InvoiceMappingValue'
import InvoiceTaxInfo from '../../elements/CarouselInvoiceReviewer/utils/InvoiceTaxInfo'
import { standardValueMoney, isZero } from '../../lib/misc'
import { fnSortInvoice } from '../utils/fnInvoiceSelectUtil'
import { SortSelector } from '../utils/SortViewUtil'
import { updateInvoiceDeduction } from '../../lib/InvoicePriceTaxSeparated'
import { getCheckInvoiceAgain } from '../../plugins/bills/bills.action'
import { showMessage } from '@ekuaibao/show-util'
import { getBoolVariation } from '../../lib/featbit'
import { calculateAble } from '../utils/fnCurrencyObj'
import Big from 'big.js'
import moment from 'moment'

const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')

@EnhanceField({
  descriptor: {
    type: 'invoice'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    let { invoiceType, optional } = field
    let { type, invoiceCorporationId, attachments, invoices, invoiceConfirm } = value
    if (type === 'unify' && (!invoiceCorporationId || invoiceCorporationId === 'defaults')) {
      return callback(i18n.get('请添加开票方'))
    }
    let amountObj = props?.form?.getFieldValue('amount')
    // let isZeroAmount = isZero(amountObj?.standard)
    if (type === 'noExist' && invoiceType?.noExistConfig) {
      const { currentNode: { flowPlanConfigId, configNodeId } = {} } = props
      const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
      const options = invoiceOptions(invoiceType, currentFlowNode)
      if (!options?.some(i => i.type === type)) {
        return callback(i18n.get('请添加发票'))
      }
    }
    if (
      invoiceType.isRequired &&
      type === 'exist' &&
      (!attachments || !attachments.length) &&
      (!invoices || !invoices.length) &&
      !invoiceConfirm &&
      // !isZeroAmount &&
      !optional
    ) {
      return callback(i18n.get('请添加发票'))
    }
    callback(required(field, value))
  },
  initialValue(props) {
    let { field, value, currentNode: { flowPlanConfigId, configNodeId } = {} } = props
    const { invoiceType, defaultValue, editable } = field
    const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
    let fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode)
    if (!value || (!!value && value.type === 'noWrite')) {
      const fnvalue = editable
        ? { type: invoiceType?.defaultInvoiceType || (!!fnInvoiceType.length && fnInvoiceType[0].type) }
        : { type: defaultValue.value ? defaultValue.value : 'noWrite' }
      return fnvalue
    } else if (value && value.type && value.type === 'unify' && !invoiceType.unify.choose && editable) {
      value = getInvoiceOptions(invoiceType)
      return value[0]
    }
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  feeTypes: state['@common'].feetypes.data,
  OCRPower: state['@common'].powers.OCR,
  OCRMedical: state['@common'].powers.OCRMedical,
  KA_INVOICE: state['@common'].powers.KA_INVOICE,
  DIGITAL_ORIGINAL_FILE: state['@common'].powers.DIGITAL_ORIGINAL_FILE,
  KA_PREVIEW_PDF_SHOW_MODAL: state['@common'].powers.KA_PREVIEW_PDF_SHOW_MODAL,
  userInfo: state['@common'].userinfo.data,
  corporationList: state['@invoice-manage'].corporationList
}))
export default class InvoiceSelect extends PureComponent {
  constructor(props) {
    super(props)
    let { invoiceType, importMode } = this.props.field
    const { flowPlanConfigId, configNodeId } = this.props.currentNode || {}
    const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
    let fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode)
    if (props.value) {
      const invoices = get(props.value, 'invoices') || []
      props.value.invoices = invoices.filter(v => v.master)
    }
    this.state = {
      type: undefined,
      corporationList: [],
      invoiceType: fnInvoiceType || [],
      imageVisible: false,
      importMode,
      fileList: get(props, 'value.attachments', []),
      invoiceImageList: [],
      canClick: true,
      isSort: true,
      waitingBatchInvoice: null,
      curSortType: '2' // 默认按发票时间排序
    }
    this.invoiceMappingValue = new InvoiceMappingValue()
  }

  invoiceTaxInfo = new InvoiceTaxInfo() //TODO

  componentDidMount() {
    const { field, onChange, bus, value, riskData } = this.props
    const { invoiceType, isSort, curSortType, importMode } = this.state
    const { editable } = field
    if (value && value.type === 'exist' && field?.invoiceType?.isRequired) {
      bus.emit('update:calculate:template', { invoiceForm: { optional: false } })
    }
    if (importMode?.setVisible) {
      api.invokeService('@bills:get:feetype:invoice:importMode', importMode).then(res => {
        this.setState({ importMode: res.items })
      })
    }
    if (value && value.invoices) {
      const invoiceIds = value.invoices.map(o => o.master.id)
      if (invoiceIds.length) {
        api.invokeService('@bills:get:invoice:image:by:ids', invoiceIds).then(rep => {
          this.setState({ invoiceImageList: rep.items })
        })
        api.invokeService('@bills:get:invoice:info:by:ids', invoiceIds).then(items => {
          items?.forEach(oo => {
            const item = value.invoices.find(v => v.master.id === oo.master.id)
            item.master = resetOverseasMasterInvoice(oo).master
            item.details =
              item.details?.map(v => {
                if (v.id === oo.details?.find(d => d.id == v.id)?.id) {
                  return v
                }
              }) || []
            item.details = item.details?.filter(line => !!line)
            item.originalData = oo
          })
          value.invoices = fnSortInvoice(value.invoices, isSort, curSortType, riskData)
          onChange(value)
        })
      }
    }
    bus.on('invoice-Attachment-Upload', this.handleAttachment)
    bus.on('continue:add:detail:invoiceForm', this.handleContinueAdd)
    bus.watch('fee:detail:feetype:change', this.handelFeeTypeChange)

    let ids = field.invoiceType.unify.limit ? field.invoiceType.unify.invoiceCorporation : undefined
    if ((ids && ids.length) || !field.invoiceType.unify.limit) {
      api.invokeService('@bills:get:invoice:corporation', ids).then(data => {
        this.setState({
          corporationList: data.items
        })
      })
    }
    if (value?.supplementInvoiceBatchId) {
      this.loadSupplementInvoiceBatch(value?.supplementInvoiceBatchId)
    }
    let ishas = invoiceType?.find(v => v.type === value?.type)
    if (!ishas && editable) {
      this.handleChange(!!invoiceType.length && invoiceType[0].value)
    }
  }

  loadSupplementInvoiceBatch = async supplementInvoiceBatchId => {
    if (!supplementInvoiceBatchId) {
      return
    }
    const query = new QuerySelect().filterBy(
      `batchNo.containsIgnoreCase("${supplementInvoiceBatchId}")||id.containsIgnoreCase("${supplementInvoiceBatchId}")`
    )
    const res = await api.invokeService('@invoice-manage:search:WaitingBatchInvoice', query.value(), {
      join$1: `detailForm.details.feeTypeId,feeTypeId,/v1/form/feeTypes`,
      join$2: `detailForm.flowId,flow,/flow/v1/flows`,
      join$3: 'invoices.invoiceId,master,/v2/invoice'
    })
    const waitingBatchInvoice = res?.items[0]
    this.setState({ waitingBatchInvoice })
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus.un('invoice-Attachment-Upload', this.handleAttachment)
    bus.un('continue:add:detail:invoiceForm', this.handleContinueAdd)
    bus.un('fee:detail:feetype:change', this.handelFeeTypeChange)
    if (window.changeTimeInvoice) {
      clearTimeout(window.changeTimeInvoice)
      window.changeTimeInvoice = undefined
    }
    this.setState = (state, callback) => {
      return
    }
  }

  fnChangeTimeFieldValue = (formValue = {}) => {
    // 如果配置的有日期口径，同时变化的数据有日期口径，就根据日期去请求汇率
    const { timeField, bus, form } = this.props
    if (timeField) {
      const timeFieldValue = formValue[timeField]
      const currentTimeFieldValue = form.getFieldValue(timeField)
      if (timeFieldValue && timeFieldValue !== currentTimeFieldValue) {
        const endOfDay = moment(timeFieldValue)
          .endOf('day')
          .valueOf()
        bus.emit('timeField:change', { date: endOfDay })
      }
    }
  }

  handleContinueAdd = () => {
    //特殊处理再记一笔
    const {
      field: { invoiceType, defaultValue, editable },
      onChange,
      currentNode: { flowPlanConfigId, configNodeId } = {}
    } = this.props
    const currentFlowNode = { flowPlanConfigId, nodeId: configNodeId }
    let fnInvoiceType = invoiceOptions(invoiceType, currentFlowNode)
    this.setState({
      fileList: []
    })
    const type = invoiceType?.defaultInvoiceType || fnInvoiceType[0].type
    const fnvalue = editable ? { type } : { type: defaultValue.value }
    onChange && onChange(fnvalue)
  }

  handelFeeTypeChange = async feeType => {
    const {
      bus,
      template,
      value,
      value: { invoices },
      billSpecification,
      billData
    } = this.props
    if (invoices?.length) {
      const isUpdate = await updateInvoiceDeduction([feeType], invoices, true)
      if (isUpdate) {
        const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
        let invoiceForm = { ...value }
        if (values.invoiceForm) {
          invoiceForm = { ...invoiceForm, ...values.invoiceForm }
        }
        const formValue = { ...values, invoiceForm }
        api?.logger?.info('价税分离之后赋值', {
          specificationId: billSpecification?.id,
          specificationName: billSpecification?.name,
          flowId: billData?.flowId,
          code: billData?.code,
          sceneName: '发票',
          feeTypeId: feeType?.id,
          feeTypeName: feeType?.name,
          operateType: '编辑',
          resultForm: formValue
        })
        this.fnChangeTimeFieldValue(formValue)
        bus.setFieldsValue(formValue)
      }
    }
    return invoices
  }

  handleInvoiceDelete = () => {
    const { onChange } = this.props
    onChange && onChange({ type: 'exist' })
  }

  handleAttachment = upFileList => {
    const {
      onChange,
      value: { type, invoices, invoiceConfirm }
    } = this.props
    const { fileList } = this.state
    let fileListData = upFileList.concat(fileList)
    this.setState({ fileList: fileListData })
    onChange && onChange({ type: 'exist', attachments: fileListData, invoices: invoices, invoiceConfirm })
  }

  handleRemoveAttachment = line => {
    const {
      onChange,
      value: { type, invoices, attachments, invoiceConfirm },
      field: { editable }
    } = this.props
    let fileLists = attachments.length > 0 ? attachments : [line]
    let files = fnFormatAttachment(fileLists)
    let cloneList = files.slice(0)
    let imgIndex = cloneList.findIndex(v => v.key === line.key)
    cloneList.splice(imgIndex, 1)
    this.setState({ fileList: cloneList })
    let invoiceType = type
    if (!editable && cloneList && !cloneList.length && invoices && !invoices.length) {
      invoiceType = 'noWrite'
    }
    onChange && onChange({ type: invoiceType, attachments: cloneList, invoices, invoiceConfirm })
  }

  handleFilePreview = line => {
    const { fileList } = this.state
    const fileLists = fileList.length > 0 ? fileList : [line] //审批中修改单据fileList为[]
    preview()({ value: fileLists, line })
  }

  handleFileDownload = line => {
    const url = getV(line, 'url', '')
    const fileName = getV(line, 'fileName', '')
    api.emit('@vendor:download', url, fileName)
  }

  handleChange = async value => {
    const { invoiceType, corporationList } = this.state
    const { onChange, field, bus, template, value: invoices } = this.props
    const list = invoiceType.filter(v => v.value === value)
    const type = !!list.length && list[0].type
    if (field?.invoiceType?.isRequired) {
      bus.emit('update:calculate:template', { invoiceForm: { optional: type !== 'exist' } })
    }
    // 上一个类型是已有发票，但是切换类型到别的类型，则重新走赋值逻辑
    if (type !== 'exist' && !!invoices?.invoices?.length) {
      const invoiceSumField = {}
      const summaryFields = template.filter(line => get(line, 'defaultValue.type') === 'invoiceSum')
      summaryFields.forEach(({ field }) => {
        invoiceSumField[field] = undefined
      })
      bus.emit('savebtn:state:change', { disabled: true })
      let currentFeeTypeForm
      if (isFunction(bus?.getFieldsValue)) {
        currentFeeTypeForm = await bus.getFieldsValue()
      }
      let values = await this.invoiceMappingValue.invoice2FeeTypeForm([], template, currentFeeTypeForm)
      values = this.deleteAutoFieldValue(values, true)

      const fieldValue = { ...invoiceSumField, ...values }
      this.fnChangeTimeFieldValue(fieldValue)
      bus.setFieldsValue(fieldValue).then(() => {
        if (bus && bus.has('amount:changed')) {
          bus.invoke('amount:changed', values.amount)
        }
      })
      bus.emit('savebtn:state:change', { disabled: false })
    }
    this.setState({ type: type })

    const fnvalue =
      type === 'unify'
        ? {
            type: type,
            invoiceCorporationId: corporationList[0] && corporationList[0].id,
            invoiceCorporation: corporationList[0]
          }
        : { type: type }
    onChange && onChange(fnvalue)

    if (Array.isArray(template) && (!invoices || !invoices.length)) {
      const taxs = template.filter(line => get(line, 'defaultValue.type', '') === 'invoiceSum')

      if (taxs.length) {
        bus.emit('money:tax:update:value')
      }
    }
  }

  handleChange2 = value => {
    const { corporationList } = this.state
    let corporation = corporationList.filter(v => v.name === value)
    const fnvalue = {
      type: 'unify',
      invoiceCorporationId: corporation && corporation[0].id,
      invoiceCorporation: corporation[0]
    }
    let { onChange, invoiceType, bus } = this.props

    onChange && onChange(fnvalue)
  }

  handleImportInvoiceClick = () => {
    const {
      feeTypes,
      bus,
      isDetail,
      value: { invoices }
    } = this.props
    const isBill = !isDetail
    bus.invoke('element:details:import:click', { visibilityFeeTypes: feeTypes, invoices, isBill }).then(data => {
      if (isBill) {
        this.setBillInvoiceValue({ billForm: data })
      } else {
        this.setInvoiceValue({ data, isOCR: true })
      }
    })
  }

  handleImportInputInvoiceClick = () => {
    const {
      feeTypes,
      bus,
      details,
      isDetail,
      value: { invoices }
    } = this.props
    const billDetails = details || []
    const isBill = !isDetail
    bus
      .invoke('element:details:input:import:click', { visibilityFeeTypes: feeTypes, billDetails, invoices, isBill })
      .then(data => {
        if (isBill) {
          this.setBillInvoiceValue({ billForm: data })
        } else {
          this.setInvoiceValue({ data })
        }
      })
  }

  handleImportAliPayInvoiceClick = async () => {
    const {
      feeTypes,
      bus,
      isDetail,
      value: { invoices }
    } = this.props
    const isAuth = await api.invokeService('@bills:check:aliPayCard:auth')
    if (!isAuth) return
    const isBill = !isDetail
    bus.invoke('element:details:aliPay:import:click', { feeTypes, invoiceList: invoices, isBill }).then(data => {
      if (isBill) {
        this.setBillInvoiceValue({ billForm: data })
      } else {
        this.setInvoiceValue({ data })
      }
    })
  }

  handleImportAifaPiaoInvoiceClick = async () => {
    const isAuth = await api.invokeService('@bills:check:aifapiao:auth', this.importAiFaPiaoH5Callback)
    if (!isAuth) return null
    this.importAiFaPiaoH5Callback()
  }

  handleSelectInvoiceFromPool = async () => {
    const { billSpecification, submitterId, value } = this.props
    const data = await api.invokeService('@bills:import:invoice:from:pool', {
      billSpecification,
      submitterId,
      invoices: value.invoices
    })
    this.setBillInvoiceValue({ billForm: data })
  }

  importAiFaPiaoH5Callback = async () => {
    const url = await api.invokeService('@bills:get:aifapiao:public:url')
    const res = await api.open('@bills:IframeModal', {
      src: url,
      handleListener: this.handleImportAifapiao
    })
  }

  handleImportAifapiao = async data => {
    const { ids, type } = data
    const {
      feeTypes,
      bus,
      submitterId,
      isDetail,
      value: { invoices }
    } = this.props
    if (type !== 'importInvoice') return
    const { items } = await api.invokeService('@bills:import:invoice:from:aifapiao', {
      ids,
      staffId: submitterId?.id
    })
    const isBill = !isDetail
    bus
      .invoke('element:details:import:afp:click', { feeTypes, invoiceList: invoices, data: items, isBill })
      .then(data => {
        if (isBill) {
          this.setBillInvoiceValue({ billForm: data })
        } else {
          this.setInvoiceValue({ data })
        }
      })
  }

  handleImportOCRMedicalClick = () => {
    this.handleImportOCRClick({ isMedical: true })
  }

  handleOverseasImportOCRClick = () => {
    this.handleImportOCRClick({ isOverseas: true })
  }

  handleImportOCRClick = ({ isMedical, isOverseas }) => {
    const {
      feeTypes,
      bus,
      isDetail,
      value: { invoices }
    } = this.props
    const isBill = !isDetail
    bus
      .invoke('element:details:import:ocr:click', {
        visibilityFeeTypes: feeTypes,
        states: [],
        invoices,
        isMedical,
        isOverseas,
        isBill
      })
      .then(data => {
        if (isBill) {
          this.setBillInvoiceValue({ billForm: data, isOCR: true })
        } else {
          this.setInvoiceValue({ data, isOCR: true })
        }
      })
  }

  deleteAutoFieldValue(values, isDelete = false) {
    // 自动计算和差旅标准赋值不能受导入发票影响
    const { autoCalFields, template } = this.props
    this.amountKeys.forEach(key => {
      // 删除的时候不计税金额需要更新字段值，所以忽略此字段
      const isTaxAmountValue = key !== 'noTaxAmount' && !isDelete
      // 发票汇总是由前端计算，后端系统计算未返回，故系统计算字段排查
      const taxAmount = template.find(v => v.name === key && v.defaultValue && v.defaultValue.type === 'invoiceSum')
      if (
        (autoCalFields?.onFields && autoCalFields.onFields.includes(key) && isTaxAmountValue && !taxAmount)  ||
        template.find(v => v.name === key && v.defaultValue && v.defaultValue.type === 'costStandard')
      ) {
        delete values[key]
      }
    })
    return values
  }

  setBillInvoiceValue = ({ billForm, isOCR }) => {
    const {
      bus,
      value: { attachments, invoiceConfirm }
    } = this.props
    if (attachments && attachments.length) {
      billForm.invoiceForm.attachments = attachments
    }
    bus.setFieldsValue(billForm).then(() => {
      this.handleExternal()
      this.refreshInvoiceValue({ invoices: billForm.invoiceForm.invoices, isOCR })
    })
  }

  setInvoiceValue = ({ data, isOCR }) => {
    const {
      bus,
      value: { attachments, invoiceConfirm }
    } = this.props
    if (!data?.length) return
    const { feeTypeForm } = data[0]
    if (attachments && attachments.length) {
      feeTypeForm.invoiceForm.attachments = attachments
    }
    if (invoiceConfirm) {
      feeTypeForm.invoiceForm.invoiceConfirm = invoiceConfirm
    }
    if (!isOCR) {
      this.deleteAutoFieldValue(feeTypeForm)
    }
    this.fnChangeTimeFieldValue(feeTypeForm)
    bus.setFieldsValue(feeTypeForm).then(() => {
      this.handleExternal()
      this.refreshInvoiceValue({ invoices: feeTypeForm.invoiceForm.invoices, isOCR })
      if (bus && bus.has('amount:changed')) {
        bus.invoke('amount:changed', { field: 'amount', amount: feeTypeForm.amount })
      }
    })
  }

  refreshInvoiceValue = ({ invoices, isOCR }) => {
    const { value, DIGITAL_ORIGINAL_FILE } = this.props
    const ids = invoices.map(item => item.master.id)
    if (ids.length && isOCR) {
      api.invokeService('@bills:get:invoice:image:by:ids', ids).then(rep => {
        this.setState({ invoiceImageList: rep.items })
      })
    }
    const digitalInvoices = invoices.filter(item => {
      const type = get(item, 'master.form.E_system_发票主体_发票类别')
      const isFullDigital = [
        'FULL_DIGITAl_SPECIAL',
        'FULL_DIGITAl_NORMAL',
        'ELECTRONIC_TRAIN_INVOICE',
        'ELECTRONIC_AIRCRAFT_INVOICE',
        'ELECTRONIC_PAPER_CAR'
      ].includes(type)
      return isFullDigital
    })
    if (DIGITAL_ORIGINAL_FILE && digitalInvoices.length) {
      const invoiceIds = digitalInvoices.map(v => v.master.id)
      api.invokeService('@bills:get:invoice:info:by:ids', invoiceIds).then(items => {
        items.forEach(oo => {
          const item = digitalInvoices.find(v => v.master.id === oo.master.id)
          item.master = oo.master
          item.originalData = oo
        })
        this.handleOnchange({ ...value, invoices })
      })
    }
  }

  amountKeys = ['amount', 'taxAmount', 'noTaxAmount', 'taxRate']

  handleExternal = () => {
    const { external } = this.props
    if (external) {
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    }
  }

  handleOnchange = data => {
    const { onChange } = this.props
    this.handleExternal()
    onChange && onChange(data)
  }

  handleInvoiceEdit = (dataSource, item, i, attachment) => {
    let index = i
    const {
      value: { invoices },
      bus,
      value,
      template,
      feeDetailId,
      feeTypes,
      feeType
    } = this.props
    const flatFeeTypes = depthFirstTraversal(cloneDeep(feeTypes))
    let currentFeeType = flatFeeTypes.find(item => item?.feeType?.id === feeDetailId)
    if (feeType) {
      currentFeeType = feeType
    }
    const regions = dataSource.filter(line => line.id !== item.id).map(line => line.region)
    let invoiceList = cloneDeep(invoices) || []
    index = invoiceList.findIndex(v => v.master.id === item.master.id)
    const {invoice, isForeignCurrencyEdit = false} = otherInvoiceByDimensionMoneyFormat(cloneDeep(item))
    api.open('@bills:EditInvoice', { invoiceInfo: invoice, attachment, regions, isForeignCurrencyEdit }).then(async data => {
      invoices[index].approveAmount && !invoices[index].comment && delete invoices[index].approveAmount
      delete invoices[index].taxAmount
      delete invoices[index].taxRate
      invoices[index].master.form = { ...invoices[index].master.form, ...data.master.form }
      bus.emit('savebtn:state:change', { disabled: true })

      // 可抵扣税额需要重新调用 updateInvoiceDeduction 获取相关值
      if (currentFeeType) {
        await updateInvoiceDeduction([currentFeeType], invoices, true)
      }

      const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
      const fieldValue = this.deleteAutoFieldValue(values)
      // TODO: 删除费用上发票的埋点
      const { billData, billSpecification, feeType } = this?.props
      const invoiceIds = item?.details?.map(it => {
        return it?.masterId
      })
      api?.logger?.info('在费用上编辑发票', {
        specificationId: billSpecification?.id,
        specificationName: billSpecification?.name,
        flowId: billData?.flowId,
        code: billData?.code,
        sceneName: '发票',
        feeTypeId: feeType?.id,
        feeTypeName: feeType?.name,
        operateType: '编辑',
        invoiceIds: invoiceIds,
        resultForm: fieldValue
      })
      this.fnChangeTimeFieldValue(fieldValue)
      bus.setFieldsValue(fieldValue)
      this.handleOnchange({ ...value, invoices })
      bus.emit('savebtn:state:change', { disabled: false })
    })
  }

  handleDeleteInvoice = async line => {
    let {
      value: { type, invoices, attachments, invoiceConfirm },
      field: { editable },
      value,
      bus,
      template
    } = this.props
    let invoiceList = cloneDeep(invoices) || [],
      invoiceSumField = {}
    let index = invoiceList.findIndex(v => v.master.id === line.master.id)
    invoiceList.splice(index, 1)

    if (!editable && (!attachments || !attachments.length) && (!invoiceList || !invoiceList.length)) {
      type = 'noWrite'
    }
    // 删除最后一张发票时，清除发票汇总上所有的金额价税合计上的金额数据
    if (!invoiceList?.length) {
      const summaryFields = template.filter(line => get(line, 'defaultValue.type') === 'invoiceSum')
      summaryFields.forEach(({ field }) => {
        invoiceSumField[field] = undefined
      })
    }
    bus.emit('savebtn:state:change', { disabled: true })

    let currentFeeTypeForm
    if (isFunction(bus?.getFieldsValue)) {
      currentFeeTypeForm = await bus.getFieldsValue()
    }

    let values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoiceList, template, currentFeeTypeForm)
    values = this.deleteAutoFieldValue(values, true)

    const fieldValue = { ...invoiceSumField, ...values }
    // TODO: 删除费用上发票的埋点
    const { billData, billSpecification, feeType } = this?.props
    const invoiceIds = line?.details?.map(it => {
      return it?.masterId
    })
    api?.logger?.info('在费用上删除发票', {
      specificationId: billSpecification?.id,
      specificationName: billSpecification?.name,
      flowId: billData?.flowId,
      code: billData?.code,
      sceneName: '发票',
      feeTypeId: feeType?.id,
      feeTypeName: feeType?.name,
      operateType: '删除',
      invoiceIds: invoiceIds,
      resultForm: fieldValue
    })

    this.fnChangeTimeFieldValue(fieldValue)
    bus.setFieldsValue(fieldValue).then(() => {
      if (bus && bus.has('amount:changed')) {
        bus.invoke('amount:changed', values.amount)
      }
    })
    this.handleOnchange({ type, attachments, invoices: invoiceList, invoiceConfirm })
    bus.emit('savebtn:state:change', { disabled: false })
  }

  handleImgPreview = imageList => {
    // api.emit('@vendor:preview:images', [imageList], imageList)
    preview()({ value: [imageList], line: imageList })
  }

  fnGetcorporationById = (id, invoiceCorporation) => {
    const { corporationList } = this.state
    const { onChange } = this.props
    let corporation = corporationList.filter(v => v.id === id)
    if (!corporation.length && !corporationList.length) return
    if (!corporation.length && corporationList.length) {
      if (id === 'defaults') return //关联申请单 第三方公务卡导入 默认不赋值id== 'defaults'
      if (invoiceCorporation && id === invoiceCorporation.id) return invoiceCorporation.name
      onChange &&
        onChange({ type: 'unify', invoiceCorporationId: corporationList[0].id, invoiceCorporation: corporationList[0] })
      return corporationList[0].name
    }
    return corporation[0].name
  }

  handleEditTax = item => {
    const {
      value: { invoices },
      value,
      flowId,
      bus,
      template
    } = this.props
    const {
      master: { entityId, id, form },
      itemIds
    } = item
    const invoiceId = item.invoiceId || id
    const params = {
      flowId,
      invoiceId,
      itemIds
    }
    const taxAmount = this.invoiceTaxInfo.getTaxAmount(item)
    const taxRate = this.invoiceTaxInfo.getTaxRate(item)
    api
      .open('@bills:InvoiceEditTaxModal', { entityId, taxAmount, taxRate, params, invoiceForm: form })
      .then(async data => {
        const deductibleInvoice = invoices.find(line => line.master.id === item.master.id)
        if(item?.taxAmount?.foreignNumCode === '156' && calculateAble([data.taxAmount,item.taxAmount?.rate,item.taxAmount?.standardScale])){
          const standard = new Big(data.taxAmount).times(item.taxAmount?.rate).toFixed(Number(item.taxAmount?.standardScale))
          deductibleInvoice.taxAmount = {...deductibleInvoice.taxAmount, standard:standard, foreign: data.taxAmount}
        }else{
          deductibleInvoice.taxAmount = standardValueMoney(data.taxAmount)
        }
        deductibleInvoice.taxRate = data.taxRate
        bus.emit('savebtn:state:change', { disabled: true })
        const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
        this.fnChangeTimeFieldValue(values)
        bus.setFieldsValue({ ...values })
        this.handleOnchange({ ...value, invoices })
        bus.emit('savebtn:state:change', { disabled: false })

        api.invokeService('@bills:set:taxAmount:change:log', {
          dataId: invoiceId,
          dataBefore: { taxAmount },
          dataAfter: { taxAmount: Number(data.taxAmount) }
        })
      })
  }

  handleEditReviewAmount = item => {
    const {
      value: { invoices },
      value,
      flowId,
      bus,
      template
    } = this.props
    const {
      master: { entityId, form, id },
      itemIds,
      comment
    } = item
    let { approveAmount } = item
    const invoiceId = item.invoiceId || id
    const params = {
      flowId,
      invoiceId,
      itemIds
    }
    const totalMoney =
      entityId === i18n.get('system_发票主体')
        ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })]?.standard
        : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })]?.standard ||
          form[i18n.get(`E_{__k0}_金额合计`, { __k0: entityId })]?.standard
    !approveAmount && (approveAmount = totalMoney)
    api.open('@bills:InvoiceApproveAmountModal', { params, approveAmount, comment, totalMoney }).then(async data => {
      const deductibleInvoice = invoices.find(line => line.master.id === item.master.id)
      deductibleInvoice.approveAmount = standardValueMoney(data.approveAmount)
      deductibleInvoice.comment = data.comment
      const amount = invoices.reduce(
        (total, oo) => {
          const {
            master: { entityId, form }
          } = oo
          //新上传的发票 提交人修改时还没有核发金额
          const approveamount =
            entityId === i18n.get('system_发票主体')
              ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })]
              : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })]
          return new MoneyMath(total).add(oo.approveAmount || approveamount || 0).__value
        },
        { standard: '0' }
      )
      bus.emit('savebtn:state:change', { disabled: true })
      const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
      this.fnChangeTimeFieldValue(values)
      bus.setFieldsValue({ ...values })
      this.handleOnchange({ ...value, invoices })
      if (Number(amount.standard) !== 0) {
        bus.setFieldsValue({ amount })
      }
      bus.emit('savebtn:state:change', { disabled: false })
    })
  }

  handleRetryCheckerInvoiceClick = async (i, item) => {
    let index = i
    const {
      value: { invoices },
      bus,
      value,
      template
    } = this.props
    const { canClick } = this.state
    if (!canClick) return
    this.setState({ canClick: false })
    try {
      bus.emit('savebtn:state:change', { disabled: true })
      const invoiceId = item.master.id
      let invoiceList = cloneDeep(invoices) || []
      index = invoiceList.findIndex(v => v.master.id === item.master.id)
      const data = await api.invokeService('@bills:retry:checker:invoice', invoiceId)
      invoices[index].details = data.value.details
      invoices[index].master = data.value.master
      invoices[index].originalData = data.value
      const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
      this.fnChangeTimeFieldValue(values)
      bus.setFieldsValue(this.deleteAutoFieldValue(values))
      this.handleOnchange({ ...value, invoices })
      this.setState({ canClick: true })
      bus.emit('savebtn:state:change', { disabled: false })
    } catch (e) {
      this.setState({ canClick: true })
      bus.emit('savebtn:state:change', { disabled: false })
    }
  }

  handleCheckInvoiceAgain = (index, item) => {
    const invoiceId = item.master.id
    const fpdm = item.master.form['E_system_发票主体_发票代码']
    const fphm = item.master.form['E_system_发票主体_发票号码']
    const {
      bus,
      value: { invoices },
      template
    } = this.props
    getCheckInvoiceAgain({ invoiceId, fpdm, fphm })
      .then(async res => {
        if (res.value) {
          showMessage.success('查验成功')
          bus.emit('savebtn:state:change', { disabled: true })
          const values = await this.invoiceMappingValue.invoice2FeeTypeForm(invoices, template)
          this.fnChangeTimeFieldValue(values)
          bus.setFieldsValue({ ...values })
          this.handleUploadInvoiceMetaile()
        } else {
          showMessage.error('查验失败')
        }
        bus.emit('savebtn:state:change', { disabled: false })
      })
      .catch(e => {
        bus.emit('savebtn:state:change', { disabled: false })
        showMessage.error(e.message || e.msg)
      })
  }
  handleInvoiceStatusChange = (index, invoice) => {
    const {
      value: { invoices },
      value
    } = this.props
    let invoiceList = cloneDeep(invoices) || []
    invoiceList[index] = invoice
    this.handleOnchange({ ...value, invoices: invoiceList })
  }
  handleUploadInvoiceMetaile = (index, invoice) => {
    const {
      value: { invoices },
      value
    } = this.props
    let invoiceList = cloneDeep(invoices) || []
    invoiceList[index] = invoice
    const ids = invoices.map(item => item.master.id)
    api.invokeService('@bills:get:invoice:image:by:ids', ids).then(rep => {
      this.setState({ invoiceImageList: rep.items })
    })
    this.handleOnchange({ ...value, invoices: invoiceList })
  }

  handleSortInvoice = () => {
    const {
      value: { invoices = [] },
      value,
      riskData
    } = this.props
    const { isSort, curSortType } = this.state
    if (!(invoices && invoices.length)) return
    const invoiceArr = fnSortInvoice(invoices, !isSort, curSortType, riskData)
    this.handleOnchange({ ...value, invoices: invoiceArr })
    this.setState({ isSort: !isSort })
  }

  // 切换排序类型
  handleSortChange = curValue => {
    const {
      value: { invoices = [] },
      value,
      riskData
    } = this.props
    if (!invoices.length) return
    this.setState(
      {
        curSortType: curValue,
        isSort: true
      },
      () => {
        const invoiceArr = fnSortInvoice(invoices, true, curValue, riskData)
        this.handleOnchange({ ...value, invoices: invoiceArr })
      }
    )
  }
  handleSupplementInvoiceBatch = () => {
    const { waitingBatchInvoice } = this.state
    if (waitingBatchInvoice) {
      api
        .open('@invoice-manage:BatchInvoiceDetailModal', {
          batchData: waitingBatchInvoice,
          isEdit: true,
          onConfirmCheck: this.getConfirmCheck
        })
        .then(() => {
          this.refreshSupplementInvoiceBatch()
        })
    } else {
      showMessage.info(i18n.get('暂无数据'))
    }
  }

  refreshSupplementInvoiceBatch = () => {
    const { value } = this.props
    this.loadSupplementInvoiceBatch(value?.supplementInvoiceBatchId)
  }

  render() {
    const {
      invoiceType,
      corporationList,
      invoiceImageList,
      isSort,
      curSortType,
      importMode,
      waitingBatchInvoice
    } = this.state
    let {
      field,
      value,
      userInfo: { permissions },
      field: { editable },
      submitterId,
      OCRPower,
      OCRMedical,
      bus,
      onChange,
      corporationList: allcorporationList,
      billState,
      modifyApproveMoney,
      isRecordExpends,
      isQuickExpends,
      isDetail,
      isModify
    } = this.props

    if (waitingBatchInvoice?.invoices?.length) {
      return <Button onClick={this.handleSupplementInvoiceBatch}> {i18n.get('查看批量补充发票')} </Button>
    }

    let didiObj
    if (allcorporationList && allcorporationList.length) {
      didiObj = allcorporationList.find(item => item.channel == 'DIDI')
    }
    if (field.canResetUnify && didiObj) {
      value = {
        type: 'unify',
        invoiceCorporationId: didiObj.id,
        invoiceCorporation: didiObj
      }
      if (window.changeTimeInvoice) {
        clearTimeout(window.changeTimeInvoice)
        window.changeTimeInvoice = undefined
      }
      window.changeTimeInvoice = setTimeout(_ => {
        try {
          onChange && onChange(value)
        } catch (error) {}
      }, 200)
    }
    let type = value && value.type
    let disabled = isDisable(this.props)
    const modifyDisabled = detailIsDisable(this.props)
    let InvoiceValue
    if (!field.invoiceType.unify.limit) {
      InvoiceValue = (value && type !== 'noWrite' && invoiceItemOptions(value)) || ''
    }
    InvoiceValue = (value && invoiceItemOptions(value)) || ''
    const corporation =
      type === 'unify' && this.fnGetcorporationById(value.invoiceCorporationId, value.invoiceCorporation)
    const xs = corporation || type === 'unify' ? { span: 12 } : { span: 24 }
    const invoiceDetailList = (value && value.invoices) || []
    const fileList = value && value.attachments
    const showValue = parseAsShowValue(fileList, invoiceDetailList)
    const isInvoiceManagePermissions = !!fnGetInvoiceManage(permissions)
    const invoices = (value && value.invoices) || []
    const needRerequestInvoiceAttachment = getBoolVariation('cyxq-73109-fee-detail-load-optimization') ? false : true
    return (
      <div
        className={classNames(styles['invoice-select-wrapper'], {
          'invoice-select-wrapper-disabled': modifyDisabled
        })}
      >
        <Row>
          <Col xs={{ ...xs }}>
            {InvoiceValue ? (
              <EUISelect
                value={InvoiceValue}
                style={{ width: '100%' }}
                disabled={!editable || disabled}
                onChange={this.handleChange}
              >
                {invoiceType.map((v, index) => {
                  return (
                    <EUISelect.Option key={index} value={v.value}>
                      {v.label}
                    </EUISelect.Option>
                  )
                })}
              </EUISelect>
            ) : (
              <EUISelect value={i18n.get('无需填写')} disabled style={{ width: '100%' }} />
            )}
          </Col>

          {corporation || type === 'unify' ? (
            <Col xs={{ span: 11, offset: 1 }}>
              <EUISelect
                value={corporation}
                style={{ width: '100%' }}
                disabled={!editable}
                onChange={this.handleChange2}
              >
                {corporationList.map((v, index) => {
                  return (
                    <EUISelect.Option key={index} value={v.name}>
                      {v.name}
                    </EUISelect.Option>
                  )
                })}
              </EUISelect>
            </Col>
          ) : null}
        </Row>

        {type && (type === 'exist' || type === 'noWrite') && (
          <div className="invoice-content">
            <RefInvoice
              value={fileList}
              bus={bus}
              OCRPower={OCRPower}
              OCRMedical={OCRMedical}
              importMode={importMode}
              importWay={'addInvoice'}
              disabled={modifyDisabled}
              onImportInvoiceClick={this.handleImportInvoiceClick}
              onImportInputInvoiceClick={this.handleImportInputInvoiceClick}
              onImportOCRClick={this.handleImportOCRClick}
              onImportOverseasInvoiceClick={this.handleOverseasImportOCRClick}
              onImportOCRMedicalClick={this.handleImportOCRMedicalClick}
              onImportAliPayInvoiceClick={this.handleImportAliPayInvoiceClick}
              onImportAifaPiaoInvoiceClick={this.handleImportAifaPiaoInvoiceClick}
              onSelectInvoiceFromPool={this.handleSelectInvoiceFromPool}
            />
            {!!invoices.length && (
              <SortSelector
                onTypeChange={this.handleSortChange}
                value={curSortType}
                onSortChange={this.handleSortInvoice}
                isSort={isSort}
              />
            )}
          </div>
        )}
        <CardListView
          bus={bus}
          dataSource={showValue || []}
          isEdit={!modifyDisabled}
          isModify={isModify}
          modifyApproveMoney={modifyApproveMoney}
          billState={billState}
          submitterId={submitterId}
          isHover={!isDetail}
          invoiceImgList={invoiceImageList}
          isRecordExpends={isRecordExpends}
          isQuickExpends={isQuickExpends}
          onDeleteItem={this.handleDeleteInvoice}
          handleImgPreview={this.handleImgPreview}
          onOpenEditInvoice={this.handleInvoiceEdit}
          isInvoiceManagePermissions={isInvoiceManagePermissions}
          onFileDownload={this.handleFileDownload}
          onFilePreview={this.handleFilePreview}
          onRemoveAttachment={this.handleRemoveAttachment}
          onOpenEditModal={this.handleEditTax}
          onOpenEditReviewAmount={this.handleEditReviewAmount}
          onRetryCheckerInvoiceClick={this.handleRetryCheckerInvoiceClick}
          onUploadInvoiceMetaile={this.handleUploadInvoiceMetaile}
          riskData={this.props.riskData}
          checkInvoiceAgain={this.handleCheckInvoiceAgain}
          showCheckAginButton={true}
          onInvoiceStatusChange={this.handleInvoiceStatusChange}
          needRerequestInvoiceAttachment={needRerequestInvoiceAttachment}
        />
      </div>
    )
  }
}

export function depthFirstTraversal(root, arr = []) {
  if (!root) {
    return arr
  }
  let stack = root

  let current
  while ((current = stack.pop())) {
    arr.push(current)

    const children = current.children
    if (!children || !children.length) {
      continue
    }
    children.length == 1 ? stack.push(children[0]) : (stack = stack.concat(children))
  }
  return arr
}
