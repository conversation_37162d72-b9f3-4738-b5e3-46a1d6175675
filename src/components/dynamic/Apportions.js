/**
 * Created by <PERSON><PERSON> on 2017/9/11.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { UIContainer as Container } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import ApportionsHeader from '../../elements/puppet/ApportionsHeader'
import { showModal, showMessage } from '@ekuaibao/show-util'
import { MoneyMath } from '@ekuaibao/money-math'
import Big from 'big.js'
import { getMoney, fnDefineIsFormula, standardValueMoney } from '../../lib/misc'
import { isDisable } from '../utils/fnDisableComponent'
import { app as api } from '@ekuaibao/whispered'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { wrapper } from '../layout/FormWrapper'
import { formatApportionDateTime, recordApportionMoneyData } from '../utils/fnDetailsApportion'
import { ApportionContext } from './types'
import { fnHideFieldsNote, isHiddenFieldsInclude } from '../utils/fnHideFields'
import { fnCheckApportionOtherConfig, fnValidateMoneyItemEqual } from '../validator/validator'

const fnDefineIsFromThirdParty = template => {
  return template.filter(t => t.editable === false && t.isFromThirdParty).length > 0
}

@EnhanceField({
  descriptor: {
    type: 'apportions'
  },
  initialValue() {
    return undefined
  },
  wrapper: wrapper()
})
export default class Apportions extends PureComponent {
  // 当前组件的值，通过 formBus 拿到
  currentValue = undefined
  constructor(props) {
    super(props)
    const { value, isModify, showAllFeeType, cannotEditAmountField, feeAmount } = props
    const checked = value ? !!value.length : false
    let disable = isDisable(props)
    let isEdit = true
    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    if (isModify && !showAllFeeType && !disable && cannotEditAmountField) {
      disable = true
      isEdit = false
    }
    this.state = {
      feeAmount: feeAmount,
      apportionFieldError: '',
      checked,
      checkedSource: 'manual', // check 值的来源
      disable,
      isEdit,
      apportionTemplateAmountField: 'amount',
      apportionfLebal: '',
      apportionMoneyFieldMap: {}
    }
    this.formBus = new MessageCenter()
  }

  _contextValue = undefined
  get contextValue() {
    const { form } = this.props
    const { _contextValue } = this

    if (_contextValue === form) {
      return this._contextValue
    } else {
      const result = { form }
      this._contextValue = result
      return result
    }
  }

  //框架提供的
  preGetValue = validate => {
    if (this.formBus.has('get:apportions:values')) {
      return this.formBus.invoke('get:apportions:values', validate)
    }
    return undefined
  }

  componentWillMount() {
    let { bus } = this.props
    bus.on('@form:did:mount', this.setMoney)
    bus.watch('amount:changed', this.handleFeeAmountChanged)
    bus.watch('update:feeType:amount:save', this.handleFeeAmountUpdated)
    bus.on('continue:add:detail', this.handleContinueAdd)
    bus.on('exist:apportion:empty:item', this.checkEmptyItem)
    this.formBus.watch('update:feeType:amount', this.handleUpdateFeeTypeChanged)
    this.formBus.watch('apportion:template:change', this.apportionTemplateChange)
    this.formBus.on('apportion:update:error:msg', this.handleUpdateErrorMsg)
    this.formBus.on('apportion:update:apportions', this.handleFeeApportionsUpdated)
  }

  componentDidMount() {
    const { value } = this.props
    const checked = value ? !!value.length : false
    this.setState({ checked })
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('amount:changed', this.handleFeeAmountChanged)
    bus.un('update:feeType:amount:save', this.handleFeeAmountUpdated)
    bus.un('@form:did:mount', this.setMoney)
    bus.un('continue:add:detail', this.handleContinueAdd)
    bus.un('exist:apportion:empty:item', this.checkEmptyItem)
    this.formBus.un('update:feeType:amount', this.handleUpdateFeeTypeChanged)
    this.formBus.un('apportion:update:error:msg', this.handleUpdateErrorMsg)
    this.formBus.un('apportion:update:apportions', this.handleFeeApportionsUpdated)
  }

  componentWillReceiveProps(nextProps, nextContext) {
    const { field, bus, hiddenFields } = nextProps
    const { field: prevField } = this.props
    const { checked, checkedSource } = this.state
    const { currentValue = [] } = this
    const isAutoChecked = checkedSource === 'auto'
    const hasValue = currentValue.length > 0

    // 自动计算中是否含有隐藏字段
    const isHideHasCalc =
      field?.configs?.find?.(item => item.ability === 'caculate' && item.property === 'hide') ?? false
    let isHide = false
    if (isHideHasCalc) {
      // attributeHide 是自动计算出来的隐藏逻辑，如果自动计算没有值，那么默认 hide 是 false，等待自动计算完成才进入清理值的逻辑
      if (field?.attributeHide) {
        isHide = field?.attributeHide && isHiddenFieldsInclude(hiddenFields, field)
      }
    } else {
      isHide = isHiddenFieldsInclude(hiddenFields, field)
    }
    /**
     * 隐藏的时候需要清除对应的值
     * 但是 setFieldsValue 会引发这个生命周期，所以需要判断 checked 为 true 的情况下
     */
    if (isHide && !field?.open && checked) {
      bus.setFieldsValue({ apportions: undefined })
      api.emit('external:APPORTION:empty')
      this.currentValue = []
      this.setState({ checked: false })
    }

    if (prevField?.open !== field.open && !field.open && isAutoChecked && !hasValue) {
      // 只有在自动勾选的情况下，才在自动计算后清除对应的 checked 状态
      // 而且一定要是没值的情况
      this.currentValue = []
      this.setState({ checked: false })
    } else if (field.open) {
      this.setState({ checked: true, checkedSource: 'auto' })
    }
  }

  handleFeeApportionsUpdated = values => {
    const { bus } = this.props
    this.currentValue = values?.apportions ?? []
    bus.emit('dynamic:value:changed', values)
  }

  checkEmptyItem = () => {
    this.formBus.emit('exist:apportion:empty:item')
  }

  setMoney = field => {
    let { bus } = this.props
    bus.getFieldsValue().then(value => {
      field && this.setState({ apportionTemplateAmountField: field })
      if (field && !(field in value)) {
        this.setState({
          feeAmount: field ? value[field] : value['amount'],
          apportionFieldError: '分摊模版中指定金额不存在'
        })
        return
      }
      this.setState({ feeAmount: field ? value[field] : value['amount'], apportionFieldError: '' })
    })
  }

  /**
   * 费用类型金额改变
   * @param amount
   */
  handleFeeAmountChanged = (res = {}) => {
    const { field, amount } = res
    const { apportionTemplateAmountField, apportionMoneyFieldMap } = this.state
    if (!amount) {
      return
    }

    if (apportionTemplateAmountField === field) {
      this.setState({ feeAmount: amount }, () => {
        this.formBus.emit('total:amount:changed', amount)
      })
    }

    if (apportionMoneyFieldMap[field]) {
      this.formBus.emit('other:apportion:total:amount:changed', {
        fieldKey: `otherApportionMoney_${field}`,
        value: amount
      })
    }
  }

  handleFeeAmountUpdated = res => {
    const { apportionTemplateAmountField } = this.state
    if (!this.state.isEdit) return Promise.resolve(res)
    let { apportions } = res
    const amount = res[apportionTemplateAmountField]
    let apportionMoneyTotal = apportions?.reduce((sum, line) => {
      return new MoneyMath(sum).add(line.apportionForm.apportionMoney).value
    }, 0)
    let apportionTotalPercent = apportions?.reduce((sum, line) => {
      return new Big(sum).plus(line.apportionForm.apportionPercent || 0)
    }, 0)
    let { isEqual, title } = this.fnValidateEqual(apportionMoneyTotal, apportionTotalPercent, amount, apportions)
    if (!isEqual) {
      showMessage.error(title)
      this.setState({ errorMsg: title })
      return Promise.reject()
    }
    res.apportions = formatApportionDateTime(res.apportions)
    recordApportionMoneyData(res.apportions, this.props)
    return Promise.resolve(res)
  }

  handleContinueAdd = () => {
    this.setState({ feeAmount: standardValueMoney(0) })
    this.formBus.emit('clear:apportion:data')
  }

  fnValidateEqual = (apportionTotalMoney, apportionTotalPercent, amount, apportions) => {
    let title = '',
      isEqual = false
    const {
      form,
      field: { rule }
    } = this.props
    const { apportionfLebal } = this.state
    if (amount && amount.foreign) {
      isEqual = new Big(getMoney(amount))
        .minus(new Big(getMoney(apportionTotalMoney)))
        .abs()
        .lte(new Big(0.05))
    } else {
      isEqual = new Big(getMoney(apportionTotalMoney)).eq(new Big(getMoney(amount)))
    }

    if (!isEqual) {
      title = i18n.get('费用金额 ≠ 总分摊金额，请修改后重新提交', { lable: apportionfLebal })
      return { title, isEqual }
    }

    isEqual = new Big(apportionTotalPercent.toFixed(2)).eq(100)
    if (!isEqual) {
      title = i18n.get('分摊比例 ≠ 100%，请修改后重新提交')
      return { title, isEqual }
    }
    const itemEqual = fnValidateMoneyItemEqual(apportions, getMoney(apportionTotalMoney))
    if (!itemEqual.zeroPass || !itemEqual.percentMoneyPass || !itemEqual.apportionMoneyZeroPass) {
      if (!itemEqual.apportionMoneyZeroPass) {
        title = i18n.get('存在分摊金额为0的数据，请修改')
      } else if (!itemEqual.zeroPass) {
        title = i18n.get('存在分摊金额和分摊比例都为0的数据,请修改')
      } else {
        title = i18n.get('存在费用金额 * 分摊比例 ≠ 分摊金额的数据,请修改')
      }
      return { title, isEqual: false }
    }
    const length = apportions.length
    //检查分摊模板其它配置
    const hasOtherConfigErr = fnCheckApportionOtherConfig(apportions)
    if (hasOtherConfigErr) {
      title = hasOtherConfigErr
      return { title }
    }
    rule &&
      rule !== 'PERCENTAGE' &&
      apportions.some((apportion, index) => {
        if (index < length - 2) {
          //最后两行不校验
          const { apportionMoney, apportionPercent } = apportion.apportionForm
          const { standard } = apportionMoney
          const total = getMoney(amount)
          const percent = Number(total) === 0 ? new Big(total) : new Big(standard).div(total)
          isEqual = percent
            .minus(new Big(apportionPercent).div(100))
            .abs()
            .lt(0.01)
          if (!isEqual) {
            title = i18n.get('存在费用金额 * 分摊比例 ≠ 分摊金额的数据,请修改', { lable: apportionfLebal })
            return true
          }
        }
      })

    // 校验其他金额字段
    const apportionSpecification = apportions[0]?.specificationId
    const otherMoneyFields = this.getOtherMoneyFieldsFromSpecification(apportionSpecification, form)
    if (otherMoneyFields?.length) {
      apportions.forEach((apportion, idx) => {
        otherMoneyFields.forEach(el => {
          el.totalAmountCounted = new Big(getMoney(apportion.apportionForm[el.name]))
            .plus(el.totalAmountCounted)
            .toFixed(2)
          if (length - 1 === idx) {
            if (!new Big(getMoney(el.totalAmount)).eq(el.totalAmountCounted)) {
              isEqual = false
              title = `${el.label} ≠ ${el.label}的总分摊金额，请更新后重新提交`
            }
          }
        })
      })
    }
    return { title, isEqual }
  }

  // 取其他金额字段对应的总金额
  getOtherMoneyFieldsFromSpecification = (apportionSpecification, form) => {
    const otherMoneyFields = []
    const apportionComponents = apportionSpecification?.components || []
    apportionComponents.forEach(el => {
      const compConfigs = el?.configs || []
      const otherMoneyField = compConfigs.find(config => config.sourceField === 'otherApportionMoney')
      if (otherMoneyField && form) {
        const obj = {}
        obj.totalAmount = form.getFieldValue(otherMoneyField.targetField)
        obj.name = el.field
        obj.label = el.label
        obj.totalAmountCounted = 0
        otherMoneyFields.push(obj)
      }
    })
    return otherMoneyFields
  }

  handleUpdateFeeTypeChanged = amount => {
    const { template, autoCalFields } = this.props
    const { apportionTemplateAmountField } = this.state
    const component = (template || []).find(element => element.field === apportionTemplateAmountField)

    const isAmountCalculate = fnDefineIsFormula(component.field, autoCalFields)
    const isFromThirdParty = fnDefineIsFromThirdParty(template)
    if (isAmountCalculate || isFromThirdParty) {
      return this.updateApportionsMoney()
    }
    this.setState({ feeAmount: amount })
    let { bus } = this.props
    let obj = {}
    obj[apportionTemplateAmountField] = amount
    bus.setFieldsValue({ ...obj })
  }

  handleUpdateErrorMsg = () => {
    this.setState({ errorMsg: '' })
  }
  updateApportionsMoney = () => {
    showMessage.error(i18n.get('费用金额不可修改，请修改分摊明细'))
  }

  handleCheckedChanged = checked => {
    let { bus } = this.props
    if (checked) {
      // 开启费用分摊时  从费用金额取值
      this.setMoney()
      this.setState({ checked, checkedSource: 'manual' })
    } else {
      showModal.confirm({
        title: i18n.get('关闭费用分摊'),
        content: i18n.get('关闭费用分摊填写的分摊明细会被清空，是否关闭？'),
        cancelText: i18n.get('取消'),
        okText: i18n.get('确定'),
        onOk: () => {
          bus.setFieldsValue({ apportions: undefined })
          api.emit('external:APPORTION:empty')
          this.currentValue = []
          this.setState({ checked })
        }
      })
    }
  }

  render() {
    //cannotEditAmountField 不可编辑金额相关字段；仅在审批中修改时的费用明细中有取值为true的可能
    let {
      bus,
      value,
      layout,
      external,
      billSpecification,
      isForbid,
      field: apportionField = {},
      submitterId = {},
      feeDetailId = '',
      showAllFeeType,
      billTemplate,
      template,
      apportionVisibleList,
      riskInfo,
      billState,
      dataFromOrder,
      billData,
      detalsAllCurrencyRates,
      specificationId
    } = this.props
    let { feeAmount, errorMsg, checked, disable, isEdit, apportionfLebal, apportionFieldError } = this.state
    let style = disable ? { pointerEvents: 'none', opacity: isEdit ? 0.4 : 1 } : null
    let headerStyle = disable && !isEdit ? { opacity: 0.4 } : {}

    const contextValue = this.contextValue

    let risks = {}
    if (external) {
      risks = { ...external }
    }
    const ShowRiskInfoBillStates = ['draft', 'rejected']
    if (riskInfo && ShowRiskInfoBillStates.includes(billState)) {
      const tg_show_apportion_risk_in_edit = api.getState()['@common'].toggleManage?.['tg_show_apportion_risk_in_edit']
      if (tg_show_apportion_risk_in_edit) {
        const values = Object.keys(riskInfo).reduce((result, key) => {
          result[key] = riskInfo[key].apportions
          return result
        }, {})
        risks = { ...risks, ...values }
      }
    }
    return (
      <ApportionContext.Provider value={contextValue}>
        {
          <div style={style}>
            {!apportionField.open && (
              <ApportionsHeader
                style={headerStyle}
                checked={checked}
                onCheckedValueChanged={this.handleCheckedChanged}
                label={fnGetFieldLabel(apportionField)}
              />
            )}
            {checked && (
              <Container
                name="@apportion:Apportion"
                value={value}
                dataFromOrder={dataFromOrder}
                billTemplate={billTemplate}
                apportionfLebal={apportionfLebal}
                feetypeTemplate={template}
                bus={this.formBus}
                layout={layout}
                feeAmount={feeAmount}
                external={risks}
                isForbid={isForbid}
                apportionField={apportionField}
                isEdit={isEdit}
                errorMsg={errorMsg}
                apportionFieldError={apportionFieldError}
                billSpecificationId={billSpecification.id}
                submitterId={submitterId.id}
                feeDetailId={feeDetailId}
                apportionVisibleList={apportionVisibleList}
                showAllFeeType={showAllFeeType}
                billBus={bus}
                billData={billData}
                detalsAllCurrencyRates={detalsAllCurrencyRates}
                feetypeSpecificationId={specificationId}
                billSpecification={billSpecification}
              />
            )}
          </div>
        }
      </ApportionContext.Provider>
    )
  }

  apportionTemplateChange = template => {
    const { baseDataProperties, dataFromOrder } = this.props
    const { configs = [], components = [] } = template
    const config = configs.find(v => v.ability === 'apportion')
    // 取一下指定金额的 label 记下来
    let fLabel = ''
    if (dataFromOrder?.companyRealPayFromOrder) {
      // 如果从商城过来的成本归属单需要改文案如下
      fLabel = i18n.get('订单企业支付金额')
    } else {
      fLabel = baseDataProperties.find(f => f.name === config?.apportionMoneyField)?.label
    }
    let apportionMoneyFieldMap = {}
    if (config?.otherApportionMoneyFields && config?.otherApportionMoneyFields?.length) {
      apportionMoneyFieldMap = config.otherApportionMoneyFields.reduce((result, item) => {
        result[item] = item
        return result
      }, {})
    }
    this.setState({ apportionfLebal: fLabel, apportionMoneyFieldMap })
    this.setMoney(config && config.apportionMoneyField)
  }
}
