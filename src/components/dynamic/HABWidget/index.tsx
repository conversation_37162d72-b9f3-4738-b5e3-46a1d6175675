import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { isEqual, get } from 'lodash'
import { HABAsyncComponent, ErrorBoundaries } from './HABAsyncComponent'
import './HABWidget.less'
import 'github-markdown-css/github-markdown-light.css'

// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'widget'
  }
})
export default class HABWidget extends PureComponent<
  { bus: any; field: any; dataSource: any },
  { hasError: boolean; data: any; assignmentRule: any[]; isEdit: boolean, habLastData: any, habFormInstances: any, currentFlow }
> {

  habDataSource: any

  constructor(props) {
    super(props)
    const { isEdit, isModify, field, billState, dataSource } = props
    const isCanEdit = isEdit || isModify || (['rejected', 'draft', 'new'].includes(billState) && typeof isEdit === 'undefined')
    this.state = {
      hasError: false,
      data: {},
      habLastData: {},
      assignmentRule: field?.isLinkageAssignment ? field?.assignmentRule?.fields || [] : [],
      isEdit: isCanEdit,
      habFormInstances: {}, //HAB组件内部表单
      currentFlow: dataSource || {}
    }
    this.habDataSource = {}
    //只读态单据
    if (!isCanEdit) {
      setTimeout(() => {
        this.fnBillToHabData({}, true)
      }, 0)
    }
  }
  componentWillReceiveProps(np) {
    this.fnBillToHabData()
  }
  componentWillMount() {
    const { bus } = this.props
    bus.watch('bill:value:changed:forhab', this.fnBillToHabData)
    bus.on('dynamic:value:changed', this.handleValueChanged)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('bill:value:changed:forhab', this.fnBillToHabData)
    bus.un('dynamic:value:changed', this.handleValueChanged)
  }

  handleValueChanged = () => {
    const { bus } = this.props
    const { currentFlow } = this.state
    bus.getValue().then(form => {
      this.setState({
        currentFlow: { ...currentFlow, form }
      })
    })

  }
  //回显HAB组件时将 bill数据 转成 HAB需要的data
  fnBillToHabData = async (changeValues?: any, forceUpdate?: boolean) => {
    const { assignmentRule, isEdit } = this.state
    let { bus, dataSource: { form = {} } = {} } = this.props
    let valueMap = {}
    const formValue = isEdit ? { ...(await bus.getValue()), ...changeValues } : form
    assignmentRule.forEach(rule => {
      const { sourceField, targetField } = rule
      valueMap[sourceField] = formValue[targetField]
    })

    const shouldForceUpdate = changeValues && Object.keys(changeValues).length && !isEqual(this.habDataSource, { ...this.habDataSource, ...valueMap })
    this.habDataSource = { ...this.habDataSource, ...valueMap }

    if (forceUpdate || shouldForceUpdate) {
      this.forceUpdate()
    }
  }

  //HAB onChange后 转成 单据需要的字段
  fnHabToBillData = data => {
    const { assignmentRule } = this.state
    let valueMap = {}
    const fields = Object.keys(data)
    assignmentRule.forEach(rule => {
      const { sourceField, targetField } = rule
      if (fields.includes(sourceField)) {
        valueMap[targetField] = data[sourceField]
      }
    })
    return valueMap
  }
  //HAB组件内部值变化
  handleChange = value => {
    const { bus } = this.props
    if (!value || !Object.keys(value).length) return
    const result = this.fnHabToBillData(value)
    this.habDataSource = { ...this.habDataSource, ...value }
    if (Object.keys(result).length) {
      bus?.emit('HAB:value:changed', result)
    }
  }

  setHabFormInstances = (habFormInstances) => {
    const { bus } = this.props
    bus.euiHabForms = bus.euiHabForms ? { ...bus.euiHabForms, ...habFormInstances } : habFormInstances
    this.setState({
      habFormInstances
    })
  }

  // 当组件内部抛出错误时，将被调用
  static getDerivedStateFromError() {
    return { hasError: true }
  }

  // 此生命周期方法用于记录错误信息
  componentDidCatch(error, errorInfo) {
    // 你也可以将错误日志上报给服务器
    console.error('ErrorBoundary caught an error', error, errorInfo)
  }
  render() {
    const { field } = this.props
    const { hasError, isEdit, habFormInstances, currentFlow } = this.state
    const habExtraProps = { disabled: !isEdit, setHabFormInstances: this.setHabFormInstances, habFormInstances, flow: currentFlow }
    if (hasError) {
      return <ErrorBoundaries />
    }
    return (
      <div className='hab-widget' >
        <HABAsyncComponent
          field={field}
          habDataSource={this.habDataSource}
          onHabChange={isEdit ? this.handleChange : null}
          habExtraProps={habExtraProps}
        />
      </div>
    )
  }
}
