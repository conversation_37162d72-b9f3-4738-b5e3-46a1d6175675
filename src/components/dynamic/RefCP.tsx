import React from 'react'
import { isObject, isString } from '@ekuaibao/helpers'
import { shouldUpdateValue } from '../utils/DependenceUtil'
import { addFullPath, handleGetDataById, isAllowCancelDependenceClearValue } from '../utils/fnInitalValue'
import { app as api } from '@ekuaibao/whispered'
import { treeDataToMap } from '@ekuaibao/lib/lib/fnTreeDataToMap'
import { Fetch } from '@ekuaibao/fetch'
import { isDisable } from '../utils/fnDisableComponent'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import RefView from '../../elements/puppet/Ref'
import { EnhanceConnect } from '@ekuaibao/store'
import { getRecordLink } from './helpers/getRecordLink'
import { refHandleDenpence, useNewAutomaticAssignment } from '../utils/fnAutoDependence'
import { getBoolVariation } from '../../lib/featbit'
@EnhanceConnect(
  state => ({
    userInfo: state['@common'].userinfo,
    allStandardCurrency: state['@common'].allStandardCurrency,
    legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency
  }),
  {
    getRecordLink: getRecordLink
  }
)
export default class RefCP extends React.Component<any, any> {
  checkedId = []
  __value = undefined
  hasEmitHandleDependenceChange = false

  constructor(props) {
    super(props)
    const { field } = props
    const { dependence } = field
    const isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    const fieldType = field?.type || ''
    const useTreeSelectRC = fieldType.startsWith('ref:basedata.Dimension')
    this.state = {
      isDependence,
      dependenceList: [],
      dimensionList: undefined,
      dependenceListOnLoading: false,
      useTreeSelectRC,
      dependenceMap
    }
  }

  componentWillMount() {
    const { bus } = this.props
    if (bus) {
      bus.on('on:dependence:change', this.handleDependenceChange)
      bus.watch('fee:detail:feetype:change', this.handleFeeTypeChange)
      bus.on('update:dimention:currency:by:dimension:value', this.fnHandleDimentionCurrenncy)
    }
  }

  componentDidMount() {
    const { field, onComponentLoadFinished, isModify, value } = this.props
    this.fnSetLastValue(value)
    setTimeout(() => {
      const { value, bus, field } = this.props
      const id = isObject(value) ? value.id : value
      if (bus) {
        bus.emit('on:dependence:change', { key: field.name, id }, { isInit: true })
      }
      this.fnHandleDimentionCurrenncy(value, true)
    }, 200)
    if (onComponentLoadFinished && !this.hasEmitHandleDependenceChange) {
      onComponentLoadFinished(field)
    }
  }

  componentWillReceiveProps(np) {
    if (this.props.value !== np.value) {
      shouldUpdateValue.call(this, this.props, np, true)
      const { value, field } = np
      let id
      if (isString(value)) {
        id = value
      } else if (isObject(value) && value.id && !value.name) {
        id = value.id
      }
      if (id && this.props.value?.id !== id) {
        const promise = handleGetDataById(field, id)
        promise &&
          promise.then(result => {
            !!result ? this.onChange(result) : this.onChange()
            this.fnSetLastValue(result)
            this.fnHandleDimentionCurrenncy(result)
          })
      } else if (isObject(np.value) && this.props.value?.id !== np.value?.id) {
        this.fnHandleDimentionCurrenncy(np.value)
        this.fnSetLastValue(np.value)
      }
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    this.__value = undefined
    if (bus) {
      bus.un('on:dependence:change', this.handleDependenceChange)
      bus.un('fee:detail:feetype:change', this.handleFeeTypeChange)
      bus.un('update:dimention:currency:by:dimension:value', this.fnHandleDimentionCurrenncy)
    }
  }

  fnSetLastValue = (value: any) => {
    if (!getBoolVariation('cyxq-78463')) {
      return
    }
    if (value) {
      this.__value = value
    }
  }

  fnHandleDimentionCurrenncy = async (value: any, flag?: boolean) => {
    const { bus, field, allStandardCurrency = [], value: data, legalEntityCurrencyPower } = this.props
    let baseCurrencyId = value?.form?.baseCurrencyId
    const dimensionId = isObject(value) ? value.id : value
    const isCurrencyChange = data?.form?.baseCurrencyId !== baseCurrencyId 
    const same = dimensionId === data?.id
    if (field?.name === 'legalEntityMultiCurrency' && !flag && !same) {
      bus.emit('dimention:multi:currency:change', value)
    }

    if (
      !baseCurrencyId &&
      field?.name === 'legalEntityMultiCurrency' &&
      legalEntityCurrencyPower &&
      dimensionId?.length
    ) {
      const result = await handleGetDataById(field, dimensionId)
      baseCurrencyId = result?.form?.baseCurrencyId
    }
    if (field?.name === 'legalEntityMultiCurrency' && baseCurrencyId) {
      const { items: rates = [] } = await api.invokeService('@currency-manage:get:currency:rates:by:Id', baseCurrencyId)
      const currency = allStandardCurrency.find(item => item.id === baseCurrencyId)
      if (flag) {
        bus.emit('dimention:currency:init', { currency, rates })
      } else {
        bus.emit('dimention:currency:change', { currency, rates}, isCurrencyChange)
        bus.emit('dimension:currency:change:payPlan:change', currency)
      }
      api.invokeService('@bills:update:currencyRatesList', {
        items: (rates || []).concat({ ...currency, rate: currency.rate || 1 })
      })
      api.invokeService('@bills:update:dimention:currency', { currency, rates, dimention: value })
    }
  }

  currentIdx = null
  handleDependenceChange = async ({ key, id, dependenceFeeType = false }, options: { isInit: boolean }) => {
    const realOldValue = this.props.value
    this.hasEmitHandleDependenceChange = true
    if (useNewAutomaticAssignment()) {
      const result = await refHandleDenpence(
        { key, id, dependenceFeeType, __value: this.__value },
        options,
        this.state,
        this.props
      )
      result && this.setState(result)
      return
    }
    const { getRecordLink, field, value, bus, flowId, isModify, billState, form, detailId, isDetail } = this.props
    const { dependence, dataType, selectRange, dependenceCondition, isShowFullPath, allowCancelDependence } = field
    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据

    if (dependence && dependence?.length) {
      const isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (!!isNeedFetch) {
        const { dependenceMap } = this.state
        const list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        this.currentIdx = id // 标明promise的执行顺序
        this.setState({ dependenceListOnLoading: true }, () => {
          getRecordLink({
            recordSearch: list,
            entity: dataType.entity,
            defaultValue: isObject(value) ? value.id : value,
            range: selectRange,
            dependenceFeeType,
            flowId: isModify ? flowId : '',
            dependenceCondition
          }).then(async action => {
            const reqIdx = id // 闭包记录id
            const { matchDefaultValue, items, leafItems } = action.payload
            let newValue = value
            let newLeafItems = leafItems
            if (!items?.length) newLeafItems = null
            if (this.currentIdx === reqIdx) {
              if (isInitLoad && !isModify) {
                const fieldsValue = form.getFieldsValue()
                let updateValue = fieldsValue[field?.field] ?? undefined
                if (!!updateValue && items?.length) {
                  const id = isObject(updateValue) ? updateValue.id : updateValue
                  const map = treeDataToMap(items)
                  updateValue = map[id] ?? newLeafItems
                }
                // 初始化进来 当前value 值赋值
                this.onChange(updateValue, isInitLoad)
              } else if (!matchDefaultValue) {
                bus.setValidateLevel(1)
                if (items.length && !newLeafItems && this.__value) {
                  const id = isObject(this.__value) ? this.__value.id : this.__value
                  const map = treeDataToMap(items)
                  const vv = map[id]
                  newValue = vv
                  this.onChange(vv)
                } else {
                  newValue = newLeafItems ? newLeafItems : undefined
                  if (isAllowCancelDependenceClearValue(allowCancelDependence, newValue, value)) {
                    this.onChange(newValue)
                  }
                }
              }

              // TODO: 档案关系埋点
              const { billData, billSpecification, feeType, dataSource } = this.props
              const oldValue = realOldValue
              let newBillData = billData
              let message = '单据上的档案关系赋值'
              if (feeType) {
                message = '明细上的档案关系赋值'
              } else {
                newBillData = dataSource
              }
              api?.logger?.info(message, {
                specificationId: billSpecification?.id,
                specificationName: billSpecification?.name,
                flowId: newBillData?.flowId || newBillData?.id,
                code: newBillData?.code || newBillData?.form?.code,
                sceneName: '档案关系',
                feeTypeId: feeType?.id,
                feeTypeName: feeType?.name,
                field: field?.field,
                dependField: key,
                isInitLoad,
                oldValue,
                newValue
              })

              if (field?.name === 'legalEntityMultiCurrency' && !!items?.length) {
                if (items.length === 1) {
                  const item0 = items[0]
                  item0?.id !== value?.id && bus.emit('dimention:multi:currency:change')
                } else if (value?.id !== newValue?.id) {
                  bus.emit('dimention:multi:currency:change')
                }
              }
              isShowFullPath && addFullPath(items, '')
              this.setState({
                dependenceListOnLoading: false,
                dependenceList: items
              })
            }
          })
        })
      }
    }
  }

  filterLastChoice = (list, preValue, nextValue) => {
    const { bus } = this.props
    if (nextValue && nextValue.id && (!nextValue.code || nextValue.needCheckValue)) {
      const map = treeDataToMap(list)
      const vv = map[nextValue.id]
      if (!vv || !vv.active) {
        if (bus) {
          bus.setValidateLevel(1)
        }
        preValue ? this.onChange(preValue) : this.onChange()
        this.__value = nextValue
      } else {
        this.onChange(vv)
      }
    }
  }

  onChange = (value?: any, isInitLoad?: boolean) => {
    const { onChange } = this.props
    this.fnSetLastValue(value)
    onChange(value)
    if (!isInitLoad) {
      if (value?.id !== this.props.value?.id) {
        this.fnHandleDimentionCurrenncy(value)
      }
    }
  }

  handleDimensionList = dimensionList => {
    let { value } = this.props
    value = this.getValue(value)
    this.filterLastChoice(dimensionList, undefined, value)
    this.setState({ dimensionList })
  }

  fnFindIdInDimensionList = (list, id) => {
    return list.find(item => {
      if (item.id === id) {
        return true
      }
      if (item.children?.length > 0) {
        return this.fnFindIdInDimensionList(item.children, id)
      }
      return false
    })
  }

  getValue = value => {
    value = value || {}
    if (typeof value === 'string') {
      value = {
        id: value
      }
    } else if (value instanceof Array) {
      const arr = []
      value.length < 1
        ? (this.checkedId = [])
        : value.forEach(v => {
            arr.push(v.id || v)
            this.checkedId = arr
          })
    }
    return value
  }

  handleFeeTypeChange = () => {
    // 天阳 bnB3nHI6Fb3qzw XSG-13395 与原有逻辑不符合，特殊处理
    const availableIds = ['bnB3nHI6Fb3qzw']
    if (!availableIds.includes(Fetch.ekbCorpId)) {
      return
    }
    const { onChange, field } = this.props
    if (field?.type?.startsWith('ref:basedata.Dimension') && field?.defaultValue?.value) {
      // 重新render默认值
      onChange(null)
    }
    return
  }

  render() {
    const {
      field,
      isModify,
      flowId,
      getExpenseStandardItemsLength,
      fromSupplier,
      submitterId,
      form,
      isChangePosition,
      dropdownMatchSelectWidth,
      useEUI
    } = this.props
    let { value } = this.props
    const { dimensionList = [], isDependence, dependenceList, dependenceListOnLoading, useTreeSelectRC } = this.state
    value = this.getValue(value)

    if (value.active === false) {
      const text = value?.deleted
        ? i18n.get('deleted', { name: value.name, code: value.code })
        : i18n.get('terminated', { name: value.name, code: value.code })
      value.label = value.label || text
      if (!~dimensionList.indexOf(value)) {
        dimensionList.unshift(value)
      }
    }
    const disabled = isDisable(this.props)
    const { optional, selectRange, Component, multiple, defaultPlaceholder, allMatchList } = field
    let placeholder = getPlaceholder(field)
    if (optional) {
      placeholder = defaultPlaceholder ? i18n.get(placeholder) : i18n.get('（选填）') + i18n.get(placeholder)
    }

    const data = {
      id: value.id ? value.id : this.checkedId, // 此处判空，将不显示placeholder
      placeholder: placeholder,
      disabled: disabled,
      optional: optional,
      onChange: this.onChange,
      Component,
      multiple,
      onClick: this.handleClick,
      getExpenseStandardItemsLength,
      allMatchList,
      hideCode: field.hideCode,
      isShowFullPath: field.isShowFullPath,
      isChangePosition,
      dropdownMatchSelectWidth
    }
    const { dataType = {}, allowCancelDependence, isCanViewAllDataWithResultBlank, defaultValue } = field
    const { entity = '' } = dataType
    const param = isModify ? { name: entity, flowId } : { name: entity }

    return (
      <RefView
        useEUI={useEUI}
        useTreeSelectRC={!useEUI && useTreeSelectRC}
        submitterId={submitterId?.id}
        fromSupplier={fromSupplier}
        data={data}
        allowCancelDependence={allowCancelDependence}
        dimensionList={dimensionList}
        param={param}
        onlyLeafCanBeSelected={'all' !== selectRange}
        onDimensionChange={this.handleDimensionList}
        isDependence={isDependence}
        dependenceList={dependenceList}
        dimensionType={this.props.id}
        type={field.type}
        isCanViewAllDataWithResultBlank={isCanViewAllDataWithResultBlank}
        defaultValue={defaultValue}
        dependenceListOnLoading={dependenceListOnLoading}
        form={form}
        field={field}
      />
    )
  }
}
