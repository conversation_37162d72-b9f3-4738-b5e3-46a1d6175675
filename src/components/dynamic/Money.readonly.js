/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { Tooltip } from 'antd'
import styles from './Money.readonly.module.less'
import SVG_CURRENCY from '../../images/currency.svg'
import { isEqual } from 'lodash'
import { hasForeign, hasBudget } from '../utils/fnCurrencyObj'
import Money from '../../elements/puppet/Money'
import { getV } from '@ekuaibao/lib/lib/help'

function thousandBitSeparator(num, fixed = 0) {
  num = String(Number(num).toFixed(fixed))
  const re = /(-?\d+)(\d{3})/g
  while (re.test(num)) {
    num = num.replace(re, '$1,$2')
  }
  return num
}

@EnhanceField({
  descriptor: {
    type: 'money'
  },
  wrapper: wrapper(true)
})
export default class MoneyReadonly extends PureComponent {
  renderStandardCurrency = value => {
    return <Money value={value} className="no-money-style" withoutStyle={true} isShowThousandsSeparator={this.isShowThousandsSeparator()} />
  }

  isHiddenRate = _ => {
    const { field } = this.props
    return getV(field, 'hiddenRate', false)
  }

  isShowThousandsSeparator = _ => {
    const { field } = this.props
    return getV(field, 'isShowThousandsSeparator', false)
  }

  renderForeignStr = _ => {
    const { value = {}, field } = this.props
    let { foreign, foreignStrCode, rate, foreignScale } = value
    const isHiddenRate = this.isHiddenRate(field)
    if (this.isShowThousandsSeparator()) {
      foreign = thousandBitSeparator(foreign, foreignScale)
    }
    const complex = isHiddenRate
      ? i18n.get('noRate-complex', { foreignStrCode, foreign })
      : i18n.get('rate-complex', {
          foreignStrCode,
          foreign,
          rate,
          rateLabel: `，${i18n.get('核算汇率')}`
        })
    return i18n.get('原币') + ' ' + complex
  }

  renderBudgetStr = _ => {
    const { value = {}, field } = this.props
    let { budget, budgetStrCode, budgetRate, budgetScale } = value
    const isHiddenRate = this.isHiddenRate(field)
    if (this.isShowThousandsSeparator()) {
      budget = thousandBitSeparator(budget, budgetScale)
    }
    const complex = isHiddenRate
      ? i18n.get('noRate-complex', { foreignStrCode: budgetStrCode, foreign: budget })
      : i18n.get('rate-complex', {
          foreignStrCode: budgetStrCode,
          foreign: budget,
          rate: budgetRate,
          rateLabel: i18n.get('预算汇率')
        })
    return i18n.get('预算币') + ' ' + complex
  }

  renderForeignCurrency = value => {
    let { standardSymbol, standard, standardScale, rate, sysRate, budgetStrCode, foreignStrCode } = value
    if (this.isShowThousandsSeparator()) {
      standard = thousandBitSeparator(standard, standardScale)
    }
    return (
      <div className="foreign-currency">
        <div>
          <span className="mr-4 symbol">{standardSymbol}</span>
          <span className='standard'>{standard}</span>
        </div>
        {foreignStrCode && <span className="currency-info">
          {this.renderForeignStr()}
          {!this.isHiddenRate() && !isEqual(rate, sysRate) && (
            <Tooltip title={i18n.get('该汇率不是默认汇率')}>
              <img className="img" src={SVG_CURRENCY} />
            </Tooltip>
          )}
        </span>}
        {budgetStrCode && <span className="currency-info">{this.renderBudgetStr()}</span>}
      </div>
    )
  }

  render() {
    let { value = {} } = this.props
    if (value && value.hasOwnProperty('label')) {
      value = value.label
    }
    return (
      <div className={styles['element-wrapper']}>
        <div className="money-wrapper">
          {hasForeign(value) || hasBudget(value)
            ? this.renderForeignCurrency(value)
            : this.renderStandardCurrency(value)}
        </div>
      </div>
    )
  }
}
