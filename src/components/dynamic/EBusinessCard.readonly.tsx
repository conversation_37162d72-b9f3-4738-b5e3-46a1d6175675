import React from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { StaffIF } from '@ekuaibao/ekuaibao_types'

type ICorporateExpenseCard = {
  offlineAuthorized: boolean
  cardType?: string
}

interface IProps {
  isReadOnly?: boolean
  value?: ICorporateExpenseCard
  submitterId: StaffIF | string
  onChange: (value: ICorporateExpenseCard) => void
}

const tipMap = {
  ALIPAY: i18n.get('该申请事项已授权国内易商卡，可去手机端合思 APP 消费使用'),
  LL: i18n.get('该申请事项已授权国际易商卡，可去手机端合思 APP 消费使用'),
  LL_PHYSICAL_CARD: i18n.get('该申请事项已授权国际易商卡，可去手机端合思 APP 消费使用')
}

@((EnhanceField as any)({
  descriptor: {
    type: 'corporateExpenseCard'
  },
  wrapper: wrapper(true)
}))
export default class EBusinessCard extends React.Component<IProps> {
  render() {
    const { submitterId, value } = this.props
    if (!value || !value?.offlineAuthorized) {
      return i18n.get('无')
    }

    return tipMap?.[value?.cardType] || i18n.get('该申请事项已授权易商卡，可去手机端合思 APP 消费使用')
  }
}
