/**************************************************
 * Created by nanyuantingfeng on 30/06/2017 15:34.
 **************************************************/
import React, { Component } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Input as AntdInput } from 'antd'
import { Input as EUIInput } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
import { required, checkedEmail, checkedAddress } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'
import { changeAutoComputerDisplayValue } from '../utils/fnDefineIsFormula'
import { debounce } from 'lodash'
import styles from './Text.module.less'
import classNames from 'classnames'
import isDependency from './helpers/is-dependency'
import { getPlaceholder } from '../utils/fnGetFieldLabel'

/**
 * 参考https://developer.mozilla.org/zh-CN/docs/Web/API/CompositionEvent
 * demo参见:https://github.com/liyuan-meng/practice-example/blob/master/src/inputAndComposition/input.js
 */

@EnhanceField({
  descriptor: {
    type: 'text'
  },
  validator: field => (rule, value, callback) => {
    const { label, maxLength, minLength, name, editable, validator } = field
    if (!editable && value && value.length > 1000) {
      return callback(i18n.get('cannot-exceed-words', { label, maxLength: 1000, length: value.length }))
    }
    if (editable && value && value.length > maxLength) {
      return callback(i18n.get('cannot-exceed-words', { label, maxLength, length: value.length }))
    }
    if (editable && value && value.length < minLength) {
      return callback(i18n.get('cannot-less-words', { label, minLength }))
    }
    if (name === 'receiver_email' || name === 'email') {
      return callback(checkedEmail(value))
    }
    if (name === 'receiver_address') {
      return callback(checkedAddress(value))
    }
    if (!!value && !!validator?.(value)) {
      return callback(validator(value))
    }
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  initialValue(props) {
    return ''
  },
  wrapper: wrapper()
})
export default class Text extends Component {
  isOnComposition = false
  isFocus = false

  constructor(props) {
    super(props)
    const { value } = props
    const disabled = isDisable(props)
    this.state = { value, disabled }
  }

  componentWillReceiveProps(nextProps, nextContext) {
    const { value } = nextProps
    const disabled = isDisable(nextProps)
    if (value !== this.state.value || (disabled !== undefined && disabled !== this.state.value)) {
      if (!this.isFocus) {
        // 如果是当前正在输入的内容，不进行赋值，因为本身state上的value就是最新的
        this.state.value = value
      }
      this.state.disabled = disabled
    }
  }

  handleComposition = evt => {
    if (evt.type === 'compositionend') {
      this.isOnComposition = false

      // 谷歌浏览器：compositionstart onChange compositionend
      // 火狐浏览器：compositionstart compositionend onChange
      if (window.navigator.userAgent.indexOf('Chrome') > -1) {
        this.onChange(evt)
      }

      return
    }

    this.isOnComposition = true
  }

  onChange = e => {
    const { onChange, canRealTime } = this.props
    if (canRealTime) {
      // 实时的更新到表单上
      onChange && onChange(e.target.value)
    }
    this.setState({ value: e.target.value })
  }

  debounceChange = debounce(e => {
    const { onChange } = this.props
    onChange && onChange(e)
  }, 400)

  handelOnFocus = () => {
    const { bus, autoCalFields, field } = this.props
    this.isFocus = true
    if (isDependency(autoCalFields, field)) {
      bus && bus.emit('savebtn:state:change', { disabled: true })
    }
  }

  onBlur = e => {
    this.isFocus = false
    const { onChange, getExpenseStandardItemsLength, onEditBlur, field, bus } = this.props
    const { value } = e.target
    onChange && onChange(value)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    bus && bus.emit('dynamic:value:blur', { [field.field]: value })
    bus && bus.emit('savebtn:state:change', { disabled: false })

    if (onEditBlur) {
      onEditBlur(e, field)
    }
  }

  render() {
    const { field, isEdit, fromSupplier, useEUI } = this.props
    let { value, disabled } = this.state
    let { optional, newStyle = false, name, maxLength } = field
    let placeholder = getPlaceholder(field)
    if (optional) placeholder = i18n.get('（选填）') + placeholder
    value = changeAutoComputerDisplayValue(value, this.props)
    const commonProps = {
      onChange: this.onChange,
      onCompositionStart: this.handleComposition,
      onCompositionUpdate: this.handleComposition,
      onCompositionEnd: this.handleComposition
    }
    const Input = useEUI ? EUIInput : AntdInput
    const size = useEUI ? 'default' : 'large'
    return (
      <>
        <Input
          value={value}
          size={size}
          maxLength={maxLength}
          data-testid={`field-text-${name}`}
          showCount
          allowClear
          disabled={disabled}
          placeholder={placeholder}
          data-testid={`filed-text-${name}`}
          onFocus={this.handelOnFocus}
          onBlur={this.onBlur}
          className={classNames({ [styles['large-text']]: newStyle }, { [styles['text-auto-disabled']]: disabled })}
          {...commonProps}
        />
        {!isEdit && fromSupplier && /code$/.test(name) && (
          <div style={{ color: 'rgba(250,150,42,1)' }}>{i18n.get('创建后不可修改')}</div>
        )}
      </>
    )
  }
}
