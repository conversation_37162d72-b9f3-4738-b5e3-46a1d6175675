import { Fetch } from '@ekuaibao/fetch'
import { useEffect } from 'react'
import { InvoiceData } from '@hose/invoice-components/lib/es/types'
import { addRumAction } from '../../../hosting/dataflux-rum'

export const getAuthInfo = () => {
  // 区分本地模式/线上模式
  const URL = process.env.NODE_ENV === 'development' ? process.env.PROXY_URL : location.host

  return {
    corpId: Fetch?.ekbCorpId, // 企业id
    token: Fetch?.accessToken, // 用户token
    businessSource: 'APP', // 业务来源
    domainUrl: URL, // 费控域名
    staffId: Fetch?.accessToken // 用户ID
  }
}

// 性能测量hook逻辑
export const usePerformanceMeasurement = () => {
  useEffect(() => {
    performance.mark('CorporateInvoice-render-start')

    // 使用 requestAnimationFrame 确保在下一个渲染帧后测量
    const measureRenderTime = () => {
      performance.mark('CorporateInvoice-render-end')
      performance.measure(
        'CorporateInvoice-render-duration',
        'CorporateInvoice-render-start',
        'CorporateInvoice-render-end'
      )

      // 获取测量结果
      const measures = performance.getEntriesByName('CorporateInvoice-render-duration')
      if (measures.length > 0) {
        const renderTime = measures[measures.length - 1].duration
        addRumAction('corporate_invoice_render_time', {
          title: '对公发票组件渲染时间',
          loadedDuration: renderTime,
          hashPath: location.hash
        })
        console.log(`%c 对公发票组件渲染时间:${renderTime.toFixed(2)}ms`, 'color:red')
        // 清理性能标记
        performance.clearMarks('CorporateInvoice-render-start')
        performance.clearMarks('CorporateInvoice-render-end')
        performance.clearMeasures('CorporateInvoice-render-duration')
      }
    }

    requestAnimationFrame(measureRenderTime)
    return () => {
      // 清理函数
      performance.clearMarks()
      performance.clearMeasures()
    }
  }, [])
}

// 测试用的发票数据
export const mockInvoiceData: InvoiceData = {
  master: {
    pipeline: 1,
    grayver: '*******-prd',
    dbVersion: 2,
    threadId: '66',
    version: 4,
    active: true,
    createTime: 1756292074834,
    updateTime: 1758627988970,
    name: '25519146543002622348',
    nameSpell: '25519146543002622348',
    code: ':25519146543002622348',
    corporationId: 'ID01uYloBwGlkz',
    sourceCorporationId: null,
    dataCorporationId: null,
    id: 'ID01uYloBwGlkz::25519146543002622348',
    form: {
      E_system_收票状态: 'NORECEIVE',
      E_system_票据渠道: 'MATHEMATICS',
      E_system_发票主体_PDF: null,
      E_system_发票主体_code: '',
      E_system_发票主体_name: '25519146543002622348',
      E_system_发票主体_cardId: null,
      E_system_发票主体_图片: 'z9zKMvwCYNik_-1756292071950-924.pdf',
      E_system_发票主体_备注: '',
      // @ts-ignore
      E_system_发票主体_席位: '10F号',
      E_system_发票主体_方向: 0,
      E_system_发票主体_来源: 'OCR',
      E_system_发票主体_税额: {
        standard: '9.83',
        standardUnit: '元',
        standardScale: 2,
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_车厢: '04车',
      E_system_发票主体_车次: 'C5581',
      E_system_发票主体_验真: true,
      E_system_发票主体_复核人: '',
      E_system_发票主体_开票人: '',
      E_system_发票主体_收款人: '',
      E_system_发票主体_encryptCode: null,
      E_system_发票主体_上车车站: '成都东',
      E_system_发票主体_下车车站: '泸州',
      E_system_发票主体_业务类型: '售',
      E_system_发票主体_乘车时间: 1752475380000,
      E_system_发票主体_价税合计: {
        standard: '119.00',
        standardUnit: '元',
        standardScale: 2,
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_发票代码: '',
      E_system_发票主体_发票号码: '25519146543002622348',
      E_system_发票主体_发票日期: 1753804800000,
      E_system_发票主体_发票状态: 'NORMAL_BLUE',
      E_system_发票主体_发票类别: 'ELECTRONIC_TRAIN_INVOICE',
      E_system_发票主体_发票金额: {
        standard: '109.17',
        standardUnit: '元',
        standardScale: 2,
        standardSymbol: '¥',
        standardNumCode: '156',
        standardStrCode: 'CNY'
      },
      E_system_发票主体_座位类型: '二等座',
      E_system_发票主体_消费类型: '交通',
      E_system_发票主体_空调特性: '',
      E_system_发票主体_识别范围: [0, 0, 595, 396],
      E_system_发票主体_身份证号: '1426351986****1323',
      E_system_票据来源_sourceEntityId: 'ID01LZMQWBEtKn',
      E_system_发票主体_乘车人姓名: '辛艳琴',
      E_system_发票主体_购买方名称: '湖南恒昌医药集团股份有限公司',
      E_system_发票主体_进销项标识: 'OUTPUT_INVOICE_RECEIPT',
      E_system_发票主体_销售方名称: '中国铁路成都局集团有限公司成都车站',
      E_system_发票主体_全电发票号码: '25519146543002622348',
      E_system_发票主体_发票印刷号码: '',
      E_system_发票主体_电子客票号码: '4654375086071290946352025',
      E_system_发票主体_购买方地址电话: '',
      E_system_发票主体_销售方地址电话: '',
      E_system_发票主体_购买方开户行及账号: '',
      E_system_发票主体_购买方纳税人识别号: '914301053293486436',
      E_system_发票主体_销售方开户行及账号: '',
      E_system_发票主体_销售方纳税人识别号: '91510100X2160097XM'
    },
    ledgerAmount: null,
    ledgerAmountModel: null,
    totalCount: 1,
    useCount: 0,
    // @ts-ignore
    entityId: 'system_发票主体',
    platformId: 'system_发票平台',
    source: 'WRITE',
    masterId: null,
    index: 0,
    visibility: {
      fullVisible: true,
      staffs: [],
      roles: [],
      departments: [],
      departmentsIncludeChildren: true
    },
    visible: true,
    ownerId: '',
    operatorId: 'ID01uYloBwGlkz:gRYa6glxK82c00',
    sourceId: '',
    selfPlannedConfigs: null,
    rigidControlPassed: true,
    controlCalcVersion: 0,
    topDate: 0,
    topFlag: 0,
    flowCounts: {},
    sourceMessage: ''
  },
  details: [],
  // @ts-ignore
  state: 'SUCCESS',
  messageList: [
    '该发票已存在，不允许重复上传',
    '购方信息与企业开票信息不符，未找到<br>企业名称：湖南恒昌医药集团股份有限公司<br>纳税人识别号：914301053293486436'
  ],
  tags: ['DUPLICATE', 'HEADER'],
  message:
    '该发票已存在，不允许重复上传<br>购方信息与企业开票信息不符，未找到<br>企业名称：湖南恒昌医药集团股份有限公司<br>纳税人识别号：914301053293486436',
  id: 'Dihiobbpewu7'
}
