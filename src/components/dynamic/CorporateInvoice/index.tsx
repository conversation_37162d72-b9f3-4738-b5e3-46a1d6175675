import React, { useEffect, useState } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../../layout/FormWrapper'
import { setGlobalAuthInfo } from '@hose/invoice-components/lib/es/web'
import {
  DetailsMasterIF,
  ImportType,
  InvoiceData,
  InvoiceStatus,
  Master,
  MoneyIF
} from '@hose/invoice-components/lib/es/types'
import InvoiceUploadOCR from '@hose/invoice-components/lib/es/web/InvoiceUploadOCR'
import InvoiceSelector from '@hose/invoice-components/lib/es/web/InvoiceBatch/InvoiceBatchSelector'
import InvoiceCardList from '@hose/invoice-components/lib/es/web/InvoiceCard/InvoiceCardList'
import InvoiceSort from '@hose/invoice-components/lib/es/web/InvoiceCard/InvoiceSort'
import InvoiceBusiness from '@hose/invoice-components/lib/es/web/InvoiceBusiness'
import { getAuthInfo, usePerformanceMeasurement, mockInvoiceData } from './utils'
import { Tag } from '@hose/eui'
import { OutlinedEditGlobalLink } from '@hose/eui-icons'
import { BatchInfo } from '@hose/invoice-components/lib/es/service/type'
import './index.less'

function repeatArray<T>(arr: T[], n: number): T[] {
  return (
    Array(n)
      .fill(arr)
      // @ts-ignore
      .flat()
  )
}

const CorporateInvoice = props => {
  const importWays = [{ type: ImportType.OCR }]
  const [sortValue, setSortValue] = useState(['time', 'descending'])
  const [batchId, setBatchId] = useState('')
  const [invoiceData, setInvoiceData] = useState<InvoiceData[]>([])
  const [batchInvoiceData, setBatchInvoiceData] = useState<InvoiceData[]>([])
  const [filterByString] = useState<string>('')
  // 使用性能测量hook
  usePerformanceMeasurement()

  // 发票组件需要的全局信息
  useEffect(() => {
    const authInfo = getAuthInfo()
    console.log('CorporateInvoice-authInfo:', authInfo)
    setGlobalAuthInfo(authInfo)
  }, [])

  // 上传发票按钮操作
  const onUploadSuccess = (data: InvoiceData[], totalAmount: MoneyIF, batchId?: string) => {
    console.log('CorporateInvoice-onUploadSuccess:', data, totalAmount, batchId)
    setInvoiceData(data.concat(invoiceData))
  }

  // 关联发票按钮操作
  const onBatchSelectionChange = (invoiceIds: string[], batchInfo: BatchInfo) => {
    console.log('CorporateInvoice-onBatchSelectionChange:', invoiceIds, batchInfo)
    setBatchId(batchInfo.batchId)
  }

  // 排序操作
  const onSortChange = (sort: string[]) => {
    console.log('CorporateInvoice-onSortChange:', sort)
    setSortValue(sort)
  }

  // 上传卡片操作
  const handleDelete = (invoiceId: Master, itemIds: DetailsMasterIF[], invoiceAmount?: MoneyIF) => {
    console.log('CorporateInvoice-handleDelete:', invoiceId, itemIds, invoiceAmount)
    setInvoiceData(invoiceData.filter(item => item.id !== invoiceId.id))
  }

  // 批次卡片操作
  const handleGroupDelete = (groupId: string, invoiceIds: string[], groupAmount?: MoneyIF) => {
    console.log('CorporateInvoice-handleGroupDelete:', groupId, invoiceIds, groupAmount)
  }

  // 发票完整组件
  // return <InvoiceBusiness data={repeatArray([invoiceData], 50)} />

  // 分散组件
  return (
    <div className="corporate-invoice-wrapper">
      <div className="header">
        <div className="header-left">
          {/* 上传发票 */}
          <InvoiceUploadOCR importWays={importWays} bindSuccess={onUploadSuccess} disableInvoiceList={invoiceData} />
          {/* 关联发票 */}
          <InvoiceSelector filterBy={filterByString} onSelectionChange={onBatchSelectionChange} batchId={batchId} />
        </div>
        <div className="header-right">
          {/* 发票排序组件 */}
          <InvoiceSort onSortChange={onSortChange} />
        </div>
      </div>
      {/* 发票卡片-上传 */}
      {invoiceData.length > 0 && (
        <InvoiceCardList
          groupTitle={i18n.get('上传发票')}
          // dataSource={(repeatArray([invoiceData], 5) as unknown) as InvoiceData[]}
          dataSource={invoiceData}
          showDelete={true}
          sortValue={sortValue}
          handleDelete={handleDelete}
        />
      )}

      {/* 发票卡片-关联 */}
      {/* <InvoiceCardList
        groupTitle={i18n.get('关联发票')}
        dataSource={(repeatArray([invoiceData], 5) as unknown) as InvoiceData[]}
        batchId={batchId}
        handleGroupDelete={handleGroupDelete}
        customTag={
          <Tag color="pri" icon={<OutlinedEditGlobalLink />}>
            {i18n.get('关联发票')}
          </Tag>
        }
        sortValue={sortValue}
      /> */}
    </div>
  )
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'corporateInvoice'
  },
  wrapper: wrapper()
})(CorporateInvoice)
