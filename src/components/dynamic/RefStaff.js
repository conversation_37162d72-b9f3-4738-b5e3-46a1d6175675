/**************************************************
 * Created by nanyuanting<PERSON> on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { Modal } from 'antd'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { isEmpty } from '@ekuaibao/helpers'
import { app as api } from '@ekuaibao/whispered'
import { constantValue, lastSelectValue, isAllowCancelDependenceClearValue } from '../utils/fnInitalValue'
import { isDisable } from '../utils/fnDisableComponent'
import { showModal } from '@ekuaibao/show-util'
import { isObject, isString } from '@ekuaibao/helpers'
import { shouldUpdateValue } from '../utils/DependenceUtil'
import { getRecordLink } from './helpers/getRecordLink'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import TagSelector from '../../elements/tag-selector'
import { getDisplayName, getStaffShowByConfig } from '../../elements/utilFn'
import { checkStaffDataRange } from '../utils/fnCheckStaffDataRange'
import { getBoolVariation } from '../../lib/featbit'
import { refStaffHandleDenpence, useNewAutomaticAssignment } from '../utils/fnAutoDependence'
@EnhanceField({
  descriptor: {
    type: 'ref:organization.Staff'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  initialValue(props) {
    let { field, value, submitterId, lastChoice, isModify, isDetail, detailId } = props
    const { defaultValue } = field
    const notUseDefaultValue = isModify && isDetail && !detailId ? false : isModify
    if (!value && !notUseDefaultValue) {
      let constVal = constantValue(field)
      if (constVal) return constVal
      let lastVal = lastSelectValue(field, lastChoice)
      if (lastVal) return lastVal
    }
    if (!value && defaultValue?.type === 'predefine' && defaultValue?.value === 'submitter' && !isModify) {
      value = submitterId
    }
    return value
  },
  wrapper: wrapper()
})
@EnhanceConnect(
  state => ({
    staffs: state['@common'].staffs,
    delegatorList: state['@common'].delegators,
    userInfo: state['@common'].userinfo.data,
    multiplePayeesMode: state['@bills'].multiplePayeesMode
  }),
  {
    getRecordLink: getRecordLink
  }
)
export default class RefStaff extends PureComponent {
  constructor(props) {
    super(props)
    let { field } = props
    let { dependence } = field
    let isDependence = dependence && !!dependence.length
    let dependenceMap = []
    if (isDependence) {
      dependenceMap =
        dependence.map(v => {
          const { direction, roleDefId } = v
          return { direction, roleDefId, dependenceId: '' }
        }) || []
    }
    this.state = {
      isDependence,
      dependenceList: [],
      staffMap: {},
      dependenceMap
    }
  }

  async componentWillMount() {
    const { bus, staffs, field, value } = this.props
    bus.on('on:dependence:change', this.handleDependenceChange)
    bus.on('on:submitterId:change', this.handleSubmitterIdChange)
    if (!staffs?.length) {
      await api.dataLoader('@common.staffs').reload()
    }
    if (
      !this.state.isDependence &&
      staffs?.length === 1 &&
      useNewAutomaticAssignment() &&
      field?.rangeOnlyOneAutomaticAssignment &&
      value?.id !== staffs[0].id
    ) {
      const result = checkStaffDataRange(field, staffs[0])
      result && this.onChange(result)
    }
  }
  fnGetCanAssignment = () => {
    let { detailId, isDetail, flowId } = this.props
    let canFirstAssignment = false
    if (isDetail) {
      canFirstAssignment = !detailId
    } else {
      canFirstAssignment = !flowId
    }
    return canFirstAssignment
  }

  isVisibilityStaff = (configVisibilityStaffs, staffId) => {
    const visible = configVisibilityStaffs.fullVisible || configVisibilityStaffs.staffIds.some(id => id === staffId)
    return visible
  }

  async componentDidMount() {
    let { value, bus, field, billState } = this.props
    let staffMap = {}
    let staff = null
    bus.setValidateLevel(1)
    console.log(value, 'value')
    if (typeof value === 'string' && value) {
      const configVisibilityStaffs = await api.dataLoader('@contactManagement.configVisibilityStaffs').load()
      if (getBoolVariation('mfrd-3800-staff-default-value-visible')) {
        if (billState !== 'new' || this.isVisibilityStaff(configVisibilityStaffs, value)) {
          staff = await api.invokeService('@bills:get:StaffById', value)
          staffMap[value] = staff
        }
      } else {
        staff = await api.invokeService('@bills:get:StaffById', value)
        staffMap[value] = staff
      }
      this.onChange(staff)
      this.setState({ staffMap })
    }
    const id = isObject(value) ? value.id : value
    this.fnGetCanAssignment() && setTimeout(this.fnUpdateForm.bind(this, staff || value, true), 1000)
    setTimeout(() => bus.emit('on:dependence:change', { key: field.name, id }, { isInit: true }), 800)
  }

  fnProxyGetValue = value => {
    const handler = {
      get: (target, name) => {
        if (name in target) {
          return target[name]
        }
        if (!!~name.indexOf('.')) {
          const arr = name.split('.')
          let i = 0
          let curValue = target
          try {
            while (i < arr.length) {
              if (arr[i] in curValue) {
                curValue = curValue[arr[i]]
                i++
              } else {
                break
              }
            }
          } catch (error) {
            console.log(error)
          }
          return i === arr.length ? curValue : ''
        } else {
          return null
        }
      }
    }
    return new Proxy(value, handler)
  }

  //判断是否存在部门联动规则，部门字段存在联动，优先部门设置
  getDeptAssignRuleFields = (source, target) => {
    const { template } = this.props
    const deptObj = template.find(
      v => v?.isLinkageAssignment && v?.assignmentRule && v?.dataType?.entity === 'organization.Department'
    )
    const fields = deptObj?.assignmentRule?.fields
    if (fields && fields.length && source && target) {
      return fields.some(v => v?.sourceField === source && v?.targetField === target)
    }
    return false
  }

  fnUpdateForm = async (value, init) => {
    const {
      bus,
      template,
      form,
      submitterId,
      field: { assignmentRule, isLinkageAssignment, referenceData },
      isCopy,
      lastChoice,
      billSpecification,
      dataSource
    } = this.props
    let staffValue = value
    let staffCustomForm = staffValue?.staffCustomForm ?? []
    if (!staffValue?.staffCustomForm) {
      if (staffValue?.id) {
        const staff = await api.invokeService('@bills:get:StaffById', staffValue?.id)
        staffCustomForm = staff?.staffCustomForm ?? []
        staffValue = staff
      }
    }
    const fields = assignmentRule?.fields ?? []
    let valueMap = {}
    const formValue = await bus.getValue()
    !!isLinkageAssignment &&
      !isEmpty(staffCustomForm) &&
      fields.forEach(item => {
        template.forEach(v => {
          if (item.targetField === v.field) {
            if (staffValue !== undefined) {
              let cur
              const data = this.fnProxyGetValue(staffValue)
              if (!!~item.sourceField.indexOf('.')) {
                //非首次进入、初始化进来且不存在部门联动等取人员字段联动规则
                if (init) {
                  const hasDeptRuleFields = this.getDeptAssignRuleFields(item.sourceField, item.targetField)
                  if (!hasDeptRuleFields) {
                    cur = data?.[item.sourceField]
                  }
                } else {
                  // 成本中心或者法人实体，绑定在部门上
                  cur = data?.[item.sourceField]
                }
              } else if (item.sourceField === 'defaultDepartment') {
                // 人员默认部门
                const lastChoiceValue = lastChoice.find(item => item.fieldName === item.sourceField)?.lastChoiceValue
                if (isCopy && lastChoiceValue !== data?.[item.sourceField] && init) {
                  return
                }
                cur = data?.[item.sourceField]
              } else {
                cur = staffCustomForm?.[item.sourceField]
              }
              if (v.type === 'number' && cur != void 0) {
                valueMap[v.name] = `${cur * 1}`
              } else if (!!cur) {
                const currentValue = formValue?.[v.name]
                if (currentValue && currentValue?.id === cur && isObject(currentValue) && isString(cur)) {
                  valueMap[v.name] = currentValue
                } else {
                  valueMap[v.name] = cur
                }
              }
            }
          }
        })
      })
    if (Object.keys(valueMap).length) {
      !!form && form.setFieldsValue(valueMap)
      api?.logger?.info('人员联动赋值', {
        specificationId: billSpecification?.id,
        specificationName: billSpecification?.name,
        flowId: dataSource?.id,
        code: dataSource?.form?.code,
        sceneName: '人员联动赋值',
        submitterId,
        currentStaff: value,
        resultForm: valueMap
      })
    }
  }
  async componentWillReceiveProps(np) {
    const { staffMap } = this.state
    if (typeof np.value === 'string' && np.value && np.value !== this.props.value) {
      api.invokeService('@bills:get:StaffById', np.value).then(staff => {
        staffMap[np.value] = staff
        this.onChange(staff)
        this.setState({ staffMap: { ...staffMap } })
      })
    }
    if (!this.props.value && np.value && isObject(np.value)) {
      this.fnUpdateForm(np.value)
    }
    if (
      this.props.value &&
      np.value &&
      isObject(np.value) &&
      isObject(this.props.value) &&
      this.props.value.id !== np.value.id
    ) {
      this.fnUpdateForm(np.value)
    }
    shouldUpdateValue(this.props, np)
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus.un('on:submitterId:change', this.handleSubmitterIdChange)
    bus.un('on:dependence:change', this.handleDependenceChange)
  }

  onChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }

  currentIdx = null
  handleDependenceChange = async ({ key, id, dependenceFeeType = false }, options = {}) => {
    if (useNewAutomaticAssignment()) {
      const result = await refStaffHandleDenpence({ key, id, dependenceFeeType }, options, this.state, this.props)
      result && this.setState(result)
      return
    }
    let { getRecordLink, field, bus, billState, form, detailId, isDetail } = this.props
    let { dependence, dataType, dependenceCondition, allowCancelDependence } = field
    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据

    // if (dependence && dependence?.length && dependence.dependenceId === key) {
    if (dependence && dependence?.length) {
      let isNeedFetch = dependence?.find(v => v.dependenceId === key)
      if (!!isNeedFetch) {
        this.currentIdx = id //标明promise的执行顺序
        const { dependenceMap } = this.state
        let list = dependenceMap.map((v, i) => {
          // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
          const dependenceId = dependence[i]?.dependenceId
          if (dependenceId === key) {
            v.dependenceId = id
          }
          return v
        })
        this.setState({ dependenceMap: list })
        getRecordLink({
          recordSearch: list,
          entity: dataType.entity,
          dependenceFeeType,
          dependenceCondition
        }).then(action => {
          const reqIdx = id // 闭包记录id
          let data = action.payload
          const { items } = data
          let newValue = undefined
          if (this.currentIdx === reqIdx) {
            const value = this.props.value
            let staffId = isObject(value) ? value.id : value
            let index = items.findIndex(v => v.id === staffId)
            if (isInitLoad) {
              const fieldsValue = form.getFieldsValue()
              // 初始化进来 当前value 值赋值
              newValue = fieldsValue[field?.field] ?? undefined
              newValue = checkStaffDataRange(field, newValue)
              this.onChange(newValue)
            } else if (index === -1) {
              let result = items.length === 1 ? items[0] : undefined
              bus.setValidateLevel(1)
              result = checkStaffDataRange(field, result)
              newValue = result
              if (isAllowCancelDependenceClearValue(allowCancelDependence, result, value)) {
                this.onChange(result)
              }
            }

            // TODO: 档案关系埋点
            let { billData, billSpecification, feeType, dataSource } = this.props
            const oldValue = this.props?.value
            let newBillData = billData
            let message = '单据上的档案关系赋值'
            if (feeType) {
              message = '明细上的档案关系赋值'
            } else {
              newBillData = dataSource
            }
            api?.logger?.info(message, {
              specificationId: billSpecification?.id,
              specificationName: billSpecification?.name,
              flowId: newBillData?.flowId || newBillData?.id,
              code: newBillData?.code || newBillData?.form?.code,
              sceneName: '档案关系',
              feeTypeId: feeType?.id,
              feeTypeName: feeType?.name,
              field: field?.field,
              dependField: key,
              oldValue,
              newValue
            })

            this.setState({
              dependenceList: items
            })
          }
        })
      }
    }
  }

  // 单据中的提交人，会走这个方法
  handleClick = async () => {
    let { value = {}, field, submitterId, userInfo, delegatorList, billSpecification = {} } = this.props
    let { dependence, allowCancelDependence } = field
    let isSubmitter = field.name === 'submitterId'
    let delegatorCopyList = isSubmitter
      ? delegatorList.filter(de => {
          return (
            de.delegateType === billSpecification.type &&
            (!de.specIds ||
              de.specIds.length === 0 ||
              de.specIds.indexOf(billSpecification?.originalId?.id || billSpecification?.originalId) > -1)
          )
        })
      : delegatorList.filter(item => item.delegateType === billSpecification.type)
    let obj = {}
    delegatorCopyList = delegatorCopyList.reduce(function(item, next) {
      if (!obj[next.id]) {
        obj[next.id] = true
        item.push(next)
      }
      return item
    }, [])

    delegatorCopyList.push(userInfo.staff)
    let ds = void 0
    let checkedKeys = void 0
    let disabledKeys = void 0
    if (isSubmitter) {
      value = value || submitterId
      ds = delegatorCopyList
    }

    if (isObject(value)) {
      checkedKeys = value.id ? [value.id] : []
    }

    if (typeof value === 'string' && value) {
      checkedKeys = [value]
    }

    if (ds && ds.length === 1) {
      disabledKeys = checkedKeys
    }

    if (dependence && dependence.length) {
      ds = this.state.dependenceList
    }

    if (ds && !ds.length) {
      if (allowCancelDependence) {
        const shouldSelectAllUser = await new Promise((resolve, reject) => {
          Modal.confirm({
            title: i18n.get('您没有可以选择的员工'),
            okText: i18n.get('知道了'),
            cancelText: i18n.get('选择所有员工'),
            onCancel: () => resolve(true),
            onOk: () => resolve(false)
          })
        })
        if (!shouldSelectAllUser) {
          return
        }
        // 取消依赖
        ds = undefined
      } else {
        return showModal.error({
          title: i18n.get('您没有可以选择的员工')
        })
      }
    }

    this.openStaffModal(ds, checkedKeys, isSubmitter, disabledKeys)
  }
  handleSubmitterIdChange = (newSubmitter, oldSubmitter, params) => {
    const { bus, field } = this.props
    if (field?.field === params?.fieldName) {
      bus.emit('set:LinkRequisitionInfo', newSubmitter, oldSubmitter)
      bus.emit('set:delegator', newSubmitter)
      bus.emit('update:blockUI', newSubmitter)
      this.onChange(newSubmitter)
    }
  }

  // 单选
  openStaffModal = (ds, checkedKeys, isSubmitter, disabledKeys) => {
    console.log('单选')

    const { bus, field, value, billSpecification, userInfo, fromDataLinkEdit } = this.props

    console.log(this.props)

    const formType = billSpecification?.type
    const { name, allowExternal, optional } = field
    let staffRangeRule = false
    if (field.valueRangeFilter && field.valueRangeFilter !== 'false') {
      //后端定义这个“false”表示取消限制
      staffRangeRule = field.valueRangeFilter
    }

    if (name === 'directorLeader' || name === 'branchLeader') {
      let checkedList = [
        {
          type: allowExternal ? 'external' : 'department-member',
          multiple: false,
          checkedKeys: value ? [value?.id] : []
        }
      ]
      return bus
        .invoke('element:ref:select:staff', {
          checkedList,
          allowExternalStaff: allowExternal,
          staffRangeRule,
          required: !optional
        })
        .then(data => {
          this.onChange(data?.checkedList?.[0]?.checkedData?.[0])
        })
    }

    if (fromDataLinkEdit) {
      return bus.invoke('element:ref:select:staff', { value, field }).then(data => {
        this.onChange(data)
      })
    }

    // 这里需要修改填单场景需要跟进组织范围走
    // 通过调研发现到这里已经是填单的打开选人组件的地方，这里做处理
    // 除了提单人，别的人员字段都需要跟进组织范围 by bsp
    bus
      .invoke('element:ref:select:staff', {
        field,
        dataSource: ds,
        checkedKeys,
        closeable: isSubmitter,
        disabledKeys,
        staffRangeRule,
        required: !optional,
        followContactRules: !isSubmitter
      })
      .then(async data => {
        // data[0] 单选,但是的组件返回的是一个多选的组件、
        if (!data || !data.length) {
          field.name !== 'submitterId' && this.onChange()
          return
        }
        let checkPayeeConfig = false
        if (field.name === 'submitterId' && data[0]?.id !== value?.id) {
          const res = await api.invokeService('@bills:get:delegate:config', data[0]?.id)
          checkPayeeConfig = res?.value?.[`${formType}Config`]?.applyPayeeAccount
        }
        if (checkPayeeConfig) {
          showModal.confirm({
            content: (
              <div>{i18n.get('切换提交人后，收款信息将展示切换后的人员可见的收款账户，已填写的收款账户将会清空')}</div>
            ),
            onCancel: () => {
              this.fnChangeValue(value)
            },
            onOk: async () => {
              this.fnClearPayeeId(data[0], data[0]?.id)
            }
          })
        } else {
          const id = data[0]?.id !== value?.id ? userInfo?.staff?.id : data[0]?.id
          field.name === 'submitterId' ? this.fnClearPayeeId(data[0], id) : this.fnChangeValue(data[0])
        }
      })
  }

  fnChangeValue = data => {
    const { bus, field, value } = this.props
    if (field.name === 'submitterId') {
      bus.emit('set:LinkRequisitionInfo', data, value)
      bus.emit('set:delegator', data)
      bus.emit('update:blockUI', data)
      api.invokeService('@bills:set:submitter:data', data)
    }
    this.fnUpdateForm(data)
    this.onChange(data)
  }

  fnClearPayeeId = async (data, submitterId) => {
    const { bus, multiplePayeesMode, PayPlanStore, billSpecification } = this.props
    const formType = billSpecification?.type
    if (multiplePayeesMode) {
      const result = await this.props.bus.getFieldsValue()
      let details = result?.details || []
      details = details.map(v => {
        if (v?.feeTypeForm?.feeDetailPayeeId) {
          v.feeTypeForm.feeDetailPayeeId = {}
        }
        return v
      })
      bus.setFieldsValue({ details })
      PayPlanStore?.clearObjs([])
      this.fnChangeValue(data)
    } else {
      const res = await api.invokeService('@bills:get:default:payee', {
        formType,
        submitterId
      })
      bus.setFieldsValue({ payeeId: res?.value || {} })
      this.fnChangeValue(data)
    }
  }

  handleRemove = e => {
    e?.stopPropagation?.()
    e?.preventDefault?.()
    this.onChange(undefined)
  }

  showTags(value) {
    if (!value) return []
    const { field, ownerId } = this.props
    const fieldValue = field.field
    return [formatTagLabel(value, ownerId, fieldValue)]
  }

  render() {
    let { value, field, isModify } = this.props
    let { optional } = field
    let placeholder = getPlaceholder(field)
    let { staffMap } = this.state
    let fieldValue = field.field
    if (optional) placeholder = i18n.get('（选填）') + i18n.get(placeholder)
    let editable = !isDisable(this.props)
    if (isModify && fieldValue === 'submitterId') {
      editable = false
    }

    if (typeof value === 'string') {
      value = staffMap[value]
    }
    return (
      <TagSelector
        value={this.showTags(value)}
        onClick={this.handleClick}
        showAvatar={true}
        placeholder={placeholder ?? i18n.get('请选择人员')}
        editable={editable}
        onChange={this.handleRemove}
        id={fieldValue}
      />
    )
  }
}

const formatTagLabel = (value, ownerId, fieldValue) => {
  if (!!value && typeof ownerId === 'object' && value?.name !== ownerId?.name && fieldValue === 'submitterId') {
    const ownerName = getDisplayName(ownerId)
    const name = getStaffShowByConfig(value)
    return {
      ...value,
      label: i18n.get('generation-submit', { __k0: name, __k1: ownerName })
    }
  }
  return value
}
