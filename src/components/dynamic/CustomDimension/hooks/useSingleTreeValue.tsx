import { useRef, useCallback } from 'react'
import { isObject, isString } from '@ekuaibao/helpers'

interface UseSingleTreeValueOptions {
  field: any
  props: any
  currencyHook?: any
  getValue: (item: any, triggerChange?: boolean) => Promise<any>
  findNodeById: (id: string) => any
}

interface UseSingleTreeValueReturn {
  handleChange: (newValue: any) => void
  handleValueChange: () => void
  previousValueRef: React.MutableRefObject<any>
  previousPropsRef: React.MutableRefObject<any>
}

/**
 * 单选TreeSelect值处理Hook - 处理RefCPV2组件的单选逻辑
 */
export function useSingleTreeValue({ 
  field, 
  props, 
  currencyHook,
  getValue,
  findNodeById,
}: UseSingleTreeValueOptions): UseSingleTreeValueReturn {
  const { value, onChange, bus } = props
  const previousValueRef = useRef(value)
  const previousPropsRef = useRef({ value, field, bus })

  const handleChange = (newValue: any): void => {
    if(typeof newValue === 'string') {
      newValue = findNodeById(newValue) || newValue
    }

    onChange(newValue)
  }

  // 处理值变化的复杂逻辑（原 componentWillReceiveProps + shouldUpdateValue 逻辑）
  const handleValueChange = useCallback((): void => {
    const prevProps = previousPropsRef.current
    const nextProps = { value, field, bus }

    if (prevProps.value !== nextProps.value) {
      // === 集成 shouldUpdateValue 的功能 ===
      let nextValue = isObject(nextProps.value) ? nextProps.value.id : nextProps.value
      let prevValue = isObject(prevProps.value) ? prevProps.value.id : prevProps.value
      nextValue = nextValue || ''
      prevValue = prevValue || ''

      if (nextValue !== prevValue) {
        if (bus) {
          bus.emit('on:dependence:change', { key: field.name, id: nextValue })
        }
      }

      // 原 componentWillReceiveProps 的其他逻辑
      let id
      if (isString(value)) {
        id = value
      } else if (isObject(value) && value.id && !value.name) {
        id = value.id
      }

      if (id && prevProps.value?.id !== id) {
        getValue(id).then(result => {
          !!result ? handleChange(result) : handleChange(undefined)
          // 处理货币维度变化
          if (currencyHook && result) {
            currencyHook.fnHandleDimentionCurrenncy(result)
          }
        })
      } else if (isObject(nextProps.value) && prevProps.value?.id !== nextProps.value?.id) {
        // 处理货币维度变化
        if (currencyHook) {
          currencyHook.fnHandleDimentionCurrenncy(nextProps.value)
        }
      }

      // 更新 refs
      previousValueRef.current = value
      previousPropsRef.current = nextProps
    }
  }, [value, field, bus, handleChange])

  return {
    handleChange,
    handleValueChange,
    previousValueRef,
    previousPropsRef
  }
} 