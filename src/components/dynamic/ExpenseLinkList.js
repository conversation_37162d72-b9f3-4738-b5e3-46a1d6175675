import React, { Fragment, PureComponent } from 'react'
import styles from './ExpenseLink.module.less'
import classNames from 'classnames'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { Button } from '@hose/eui'
import { get, flatten, cloneDeep, compact } from 'lodash'
import { isDisable } from '../utils/fnDisableComponent'
import RequisitionInfo from '../../elements/puppet/RequisitionInfo'
import { related } from '../../elements/feeDetailViewList/Related'
import { showModal } from '@ekuaibao/show-util'
import { getV } from '@ekuaibao/lib/lib/help'
import { uuid } from '@ekuaibao/helpers'
import * as actions from '../utils/fnExpenseLinkUtil'
import { isZero } from '../../lib/misc'
import AssociatedTrip from '../../elements/puppet/RelevantApply'
import { fnCheckLimitTripOrder, limitTripOrderFilter } from '../utils/fnExpenseLinkHelper'
import { OutlinedTipsAdd } from '@hose/eui-icons'

@EnhanceField({
  descriptor: {
    type: 'expenseLinks'
  },
  validator: field => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => {
  return {
    requisitionInfo: state['@bills'].requisitionInfo,
    loanPackage: state['@common'].loanPackage,
    currentVisibleFeeTypes: state['@bills'].currentVisibleFeeTypes
  }
})
export default class ExpenseLinkList extends PureComponent {
  expenseLinkMap = {}
  constructor(props) {
    super(props)
    this.state = {
      hasWrittenOff: false,
      existDetails: false,
      relatedApplyId: [],
      relatedRequisitionData: [],
      entityInfo: undefined,
      showImportTripOrderButton: false,
      limitTripOrders: [],
      hasOrderList: []
    }
    this.expenseLinkList = []
    this.expenseLinkTypeMap = {}
  }

  componentDidMount() {
    let {
      value,
      bus,
      flowId,
      submitterId,
      isModify,
      billState,
      field,
      field: { allowMultipleRequests },
      billSpecification
    } = this.props
    related.setExpenseLink(value)
    related.setExpenseSpecification(field)
    if (value && value.length && ['new', 'draft'].includes(billState)) {
      this.getExpenseLinkType(value)
    }
    //防止无缘无故刷新单据值
    if (value && value.length && ['new', 'draft', 'rejected', 'modify'].includes(billState)) {
      const ids = value.map(v => v.id)
      this.getExpenseLinkDetails(ids)
      if (!allowMultipleRequests) {
        this.fnHandleExpenseLinkValueChanged(ids[0])
      }
    }
    let param = isModify ? { id: submitterId.id, status: 'REPAID', flowId } : { id: submitterId.id, status: 'REPAID' }
    api.invokeService('@common:get:submitter:loan:list', param).then(_ => {
      this.setState({
        hasWrittenOff: this.checkWrittenOff(value)
      })
    })
    bus.on('set:delegator', this.fnSetDelegator)
    bus.on('details:change', this.fnDetailsChange)
    bus.on('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
    const limitTripOrders = fnCheckLimitTripOrder(billSpecification)
    this.setState({ limitTripOrders })
    setTimeout(this.getInitRelatedApplyId, 0)
  }

  componentWillReceiveProps(np) {
    const { field } = np
    related.setExpenseSpecification(field)
    if (this.props.value !== np.value && ['new', 'draft'].includes(np.billState)) {
      this.getExpenseLinkType(np.value)
    }
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('set:delegator', this.fnSetDelegator)
    bus.un('details:change', this.fnDetailsChange)
    bus.un('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
  }

  handleDimentionMultiCurrencyChange = dimention => {
    if (dimention?.id !== this.legalEntityMultiCurrency || !dimention?.id) {
      this.handleClear()
    }
  }

  getInitRelatedApplyId = () => {
    this.props.bus.getFieldsValue().then(res => {
      res?.details && this.fnDetailsChange(res.details)
      this.initedRelatedApplyId = true
    })
  }

  getExpenseLinkType = value => {
    this.expenseLinkTypeMap = {}
    if (!value) {
      return
    }
    value.forEach(v => {
      const config = getV(v, 'specificationId.configs', []).find(v => v.ability === 'requisition') || {}
      this.expenseLinkTypeMap[v.id] = config.applyContentRule
    })
  }

  // 获取被报销明细关联过的申请事项id
  fnDetailsChange = formDetails => {
    if (!formDetails || !formDetails.length || !['new', 'draft'].includes(this.props.billState)) {
      this.state.relatedApplyId.length && this.setState({ relatedApplyId: [] })
      return
    }
    let relatedApplyId = []
    formDetails.forEach(v => {
      const linkDetailEntities = getV(v, 'feeTypeForm.linkDetailEntities', [])
      if (linkDetailEntities.length) {
        linkDetailEntities.forEach(line => {
          const flowId = get(line, 'flowId') || get(line, 'linkDetailEntityId.linkId')
          relatedApplyId.push(flowId)
        })
      }
    })
    relatedApplyId = Array.from(new Set(relatedApplyId))
    this.setState({ relatedApplyId })
  }

  fnSetDelegator = () => {
    let { onChange, template = [], form } = this.props
    let temp = template.filter(
      el => el.defaultValue && el.defaultValue.type === 'predefine' && el.defaultValue.value === 'submit.requisition'
    )
    if (temp) {
      let valueMap = {}
      temp.forEach(el => {
        valueMap[el.field] = undefined
      })

      form.setFieldsValue(valueMap)
    }
    onChange && onChange(undefined)
  }

  fnChange = async (ids, selectedData) => {
    let {
      onChange,
      requisitionInfo = [],
      bus,
      value,
      form,
      field: { allowMultipleRequests, automaticImport }
    } = this.props
    if (!Array.isArray(ids)) {
      ids = [ids]
    }
    if (selectedData && !Array.isArray(selectedData)) {
      selectedData = [selectedData]
    }
    let res = selectedData ?? requisitionInfo.filter(v => ids.includes(v.id))
    const diffArr = value && Array.isArray(value) && value.filter(item => !ids.includes(item.flowId))
    if (diffArr && diffArr.length) {
      const flowIds = this.getRelatedIds(diffArr)
      this.fnDeleteDetails(flowIds, false)
    }
    const data = await form.getFieldsValue()
    const applicationListDetails = (await bus.invoke('update:select:expenselink', { ...data, expenseLinks: res })) || []
    related.setExpenseLink(res)
    this.getExpenseLinkDetails(ids)
    if (automaticImport) {
      // 自动导入费用明细
      this.setAutoImportDetails(applicationListDetails)
    }
    if (!allowMultipleRequests) {
      this.fnHandleExpenseLinkValueChanged(ids[0])
    }

    onChange && onChange(res)
    bus.emit('expenseLink:change', { expenseLink: res })
    this.setState({ hasWrittenOff: this.checkWrittenOff(res) })
  }

  // 自动导入明细
  setAutoImportDetails(applicationListDetails) {
    const { submitterId, bus } = this.props
    applicationListDetails = actions.formatImportApplyDetail(applicationListDetails, null)
    let feeDetails = compact(
      flatten(
        applicationListDetails.map(v =>
          v?.dataList.map(details => {
            if (details?.feeTypeId && !isZero(details.modifyMoney.standard)) {
              // 设置导入明细弹框里面的明细置灰数据
              const _tempConsumId = uuid()
              let relateDetail = { consumeAmount: details.amount, relateId: details.id }
              related.setRelatedMap({ id: _tempConsumId, value: [relateDetail] })
              details._tempConsumId = _tempConsumId
              const filterData = v?.dataList.filter(fl => fl.id == details.id)
              details.linkDetailEntities = [actions.fnAddUseMoney(cloneDeep({ ...v, dataList: filterData }))] // 明细中把关联的申请对象赋值，申请单提示未核销使用
              return details
            }
          })
        )
      )
    )

    // 延迟加载 feeDetails 数据变更 自动导入明细逻辑
    setTimeout(async () => {
      if (!feeDetails.length) return
      const importDetails = await actions.autoImportApplyDetail(cloneDeep(feeDetails), submitterId, false)
      if (importDetails?.length) {
        const flag = api.getState()['@common'].toggleManage?.['tempo_requisition_import_cal']
        bus.emit('import:expenseLink', importDetails, flag)
      }
    }, 1000)
  }

  handleImportApplyDetail = async () => {
    const { bus, form, submitterId, currentVisibleFeeTypes } = this.props
    const dataSource = await form.getFieldsValue()
    const applicationListDetails = (await bus.invoke('update:select:expenselink', { ...dataSource })) || []
    actions.handleImportApplyDetail({ currentVisibleFeeTypes, applicationListDetails, bus, submitterId })
  }

  handleImportWrittenOff = _ => {
    if (this.writtenoff) {
      if (this.writtenoff[0].writtenOffRemainTimes === 0) {
        showModal.info({
          title: i18n.get('可核销次数为0')
        })
        return
      }
      let { bus } = this.props
      bus.emit('import:writtenoff:fromApply', this.writtenoff)
    }
  }

  isImportDetails = () => {
    const { value, billSpecification } = this.props
    return this.state.existDetails && value && billSpecification.type !== 'loan'
  }

  getRelatedIds = expenseLink => {
    const relatedIds = []
    expenseLink.forEach(v => {
      v &&
        v.related &&
        v.related.forEach(item => {
          relatedIds.push(item.id)
        })
    })
    return relatedIds
  }

  getExpenseLinkDetails = async ids => {
    const result = await api.invokeService('@bills:get:ExpenseLink:list', ids)
    const items = get(result, 'items', [])
    if (!items.length) return
    this.expenseLinkList = flatten(
      items.map(v => {
        this.expenseLinkMap[v.infoId] = v
        return v.details
      })
    )
    this.setState({ relatedRequisitionData: this.expenseLinkList || [] }, () => {
      this.getTripOrderListByExpenseCode()
    })
    const existDetails = !!this.expenseLinkList.find(v => get(v, 'form'))
    if (existDetails) {
      // 告知单据详情关联申请的明细
      const expenseLinkListDetails = this.expenseLinkList.reduce((result, currentValue) => {
        currentValue.form?.details && result.splice(-1, 0, ...currentValue.form.details)
        return result
      }, [])
      this.props.bus.emit('billInfoEditable:expenseLinkDetails:change', {
        expenseLinkDetails: expenseLinkListDetails
      })
    }
    this.setState({ existDetails })
  }

  fnHandleExpenseLinkValueChanged = async id => {
    const { template, form } = this.props
    if (!id || !template) return
    const resp = await api.invokeService('@bills:get:apply-event-detail-list', id)
    if (!resp?.items) return
    const flow = resp.items
    let temp = template.filter(
      el => el.defaultValue && el.defaultValue.type === 'predefine' && el.defaultValue.value === 'submit.requisition'
    )
    if (temp) {
      let valueMap = {}
      const flowForm = flow.find(v => v.form && !v.form.linkRequisitionInfo)
      if (flowForm && flowForm.form) {
        temp.forEach(el => {
          valueMap[el.field] = flowForm.form[el.field]
        })
        this.legalEntityMultiCurrency = flowForm?.form?.legalEntityMultiCurrency
        form.setFieldsValue(valueMap)
      }
    }
  }

  checkWrittenOff = value => {
    let {
      loanPackage: { items = [] }
    } = this.props
    if (value && value.length) {
      let relatedIds = this.getRelatedIds(value)
      let writtenOff = items.filter(el => relatedIds.includes(el.flowId))
      this.writtenoff = writtenOff
      return writtenOff.length > 0
    }
    return false
  }

  fnClearRelateMoney = (deleteLinkDetailEntities, detailId) => {
    let list = []
    deleteLinkDetailEntities.forEach(line => {
      const dataList = get(line, 'dataList')
      list = dataList ? list.concat(dataList) : list.concat([line.linkDetailEntityId])
    })
    list.forEach(item => {
      related.deleteRelateItem(item._tempConsumId || detailId, item.id)
    })
  }

  fnDeleteDetails = (flowIds, isClearAll = false) => {
    const { form } = this.props
    const details = form.getFieldValue('details', []) || []
    const _details = details.map(line => {
      if (isClearAll) {
        delete line.feeTypeForm.linkDetailEntities
        return line
      } else {
        const { feeTypeForm, ...others } = line
        let linkDetailEntities = get(line, 'feeTypeForm.linkDetailEntities', []) || []
        const detailId = get(line, 'feeTypeForm.detailId', '')
        let saveLinkDetailEntities = linkDetailEntities
        if (linkDetailEntities.length > 0) {
          saveLinkDetailEntities = linkDetailEntities.filter(item => {
            const id = item.flowId ? item.flowId : get(item, 'linkDetailEntityId.linkId')
            return flowIds.indexOf(id) < 0
          })
          const deleteLinkDetailEntities = linkDetailEntities.filter(item => {
            const id = item.flowId ? item.flowId : get(item, 'linkDetailEntityId.linkId')
            return flowIds.indexOf(id) >= 0
          })

          this.fnClearRelateMoney(deleteLinkDetailEntities, detailId)
        }
        return { ...others, feeTypeForm: { ...feeTypeForm, linkDetailEntities: saveLinkDetailEntities } }
      }
    })
    form.setFieldsValue({ details: _details })
    isClearAll && related.clearRelatedData()
  }

  updateApplyFromExpense = params => {
    const { bus } = this.props
    bus && bus.emit('update:apply:from:Expense', params)
  }

  handleAddExpenseLink = async () => {
    let {
      value,
      field: { allowMultipleRequests }
    } = this.props
    const refValue = {}
    const idList = value ? value.map(v => {
      refValue[v.id] = v
      return v.id
    }) : []
    const { id, selectedData } = await api.open('@bills:BillStackerModal', {
      viewKey: 'ExpenseLinkListView',
      updateApplyFromExpense: this.updateApplyFromExpense,
      value: allowMultipleRequests ? idList : idList[0],
      isMultipleExpenseLink: allowMultipleRequests,
      refValue: refValue
    })
    this.setState({ showImportTripOrderButton: false }, () => {
      this.fnChange(id, selectedData)
    })
  }

  getTripOrderListByExpenseCode = () => {
    //@i18n-ignore
    api.invokeService('@third-party-manage:get:entity:by:platform:name', '行程管理').then(result => {
      const entityInfo = result?.items?.[0]
      if (entityInfo) {
        const entityId = entityInfo.id
        const data = this?.state?.relatedRequisitionData || []
        //@i18n-ignore
        const filterCode = data
          ?.map(item => `form.E_${entityId}_申请单编号.containsIgnoreCase("${item?.form?.code}")`)
          .join(' || ')
        const filter = filterCode ? `(active==true) && (${filterCode})` : '(active==true)'
        const params = {
          entityId,
          type: 'LIST',
          query: { limit: { start: 0, count: 10 }, filterBy: filter },
          params: { type: 'order' },
          submitterId: this.props.submitterId?.id
        }
        api.invokeService('@third-party-manage:search:datalink:by:entityId', params).then(res => {
          const itemLength = res?.items?.data?.length
          if (itemLength) {
            const { limitTripOrders } = this.state
            const [first] = limitTripOrders
            let showImportTripOrderButton = true
            if (first === 'ALL') {
              // 三种限制方式都有了，就不可以导入订单了
              showImportTripOrderButton = false
            }
            this.setState({ showImportTripOrderButton, entityInfo })
          } else {
            this.setState({ showImportTripOrderButton: false, entityInfo })
          }
        })
      }
    })
  }

  handleClear = () => {
    this.setState({ relatedRequisitionData: [] })
    this.fnChangeExpenseLink(this._handleClear, { isClearAll: true })
  }

  _handleClear = () => {
    const { onChange, bus } = this.props
    onChange()
    related.setExpenseLink([])
    bus.invoke('clear:realted:application:details')
    bus.emit('billInfoEditable:expenseLinkDetails:change', { expenseLinkDetails: [] })
    bus.emit('expenseLink:change', {})
    this.setState({ hasWrittenOff: this.checkWrittenOff(), existDetails: false })
  }

  handleRemove = line => {
    this.fnChangeExpenseLink(this._handleRemove.bind(this, line.id), { line })
  }

  _handleRemove = async id => {
    const { bus, form, value, onChange } = this.props
    const data = cloneDeep(value).filter(v => v.id !== id)
    if (!data || !data.length) {
      related.clearRelatedData()
    }
    // 删除关联申请单, 告知单据详情关联申请的明细
    const newExpenseLinkListFlowIdArr = data.map(item => item.flowId)
    const expenseLinkListDetails = this.expenseLinkList
      .filter(expenseLinkItem => newExpenseLinkListFlowIdArr.includes(expenseLinkItem.id))
      .reduce((result, currentValue) => {
        currentValue.form?.details && result.splice(-1, 0, ...currentValue.form.details)
        return result
      }, [])
    bus.emit('billInfoEditable:expenseLinkDetails:change', { expenseLinkDetails: expenseLinkListDetails })
    onChange(data)
    this.setState({ showImportTripOrderButton: false }, () => {
      if (data && data.length) {
        const ids = data.map(v => v.id)
        this.getExpenseLinkDetails(ids)
      }
    })
    const dataSource = await form.getFieldsValue()
    bus.invoke('update:select:expenselink', { ...dataSource, expenseLinks: data })
    related.setExpenseLink(data)
    bus.emit('expenseLink:change', { expenseLink: data })
    this.setState({
      hasWrittenOff: this.checkWrittenOff(data),
      existDetails: !!data.length,
      entityInfo: undefined
    })
  }

  fnFilterRelatedDetail = () => {
    const { form } = this.props
    const details = form.getFieldValue('details', []) || []
    let list = []
    details.forEach(line => {
      const linkDetailEntities = get(line, 'feeTypeForm.linkDetailEntities', []) || []
      if (linkDetailEntities.length) {
        list = list.concat(linkDetailEntities)
      }
    })
    return list
  }

  fnChangeExpenseLink(fn, { line = {}, isClearAll = false }) {
    const flowIds = this.getRelatedIds([line])
    const list = this.fnFilterRelatedDetail()
    const arr = isClearAll
      ? list
      : list.filter(item => flowIds.includes(item.flowId) || flowIds.includes(get(item, 'linkDetailEntityId.linkId')))
    if (arr.length) {
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get('申请明细已被关联，若删除关联申请，将同步删除明细中的关联明细'),
        cancelText: i18n.get('取消'),
        okText: i18n.get('确定'),
        onOk: this.handleOk.bind(this, { fn, isClearAll, flowIds })
      })
    } else {
      fn()
    }
  }

  handleOk = ({ fn, isClearAll, flowIds }) => {
    this.fnDeleteDetails(flowIds, isClearAll)
    fn()
  }

  handleDetailClick = line => {
    let { bus, flowId } = this.props
    bus && bus.emit('check:requisition:detail', { detail: line, flowId })
  }

  fnCheckId = line => {
    if (!this.state.relatedApplyId.length) {
      return false
    }
    let includesId = this.state.relatedApplyId.includes(line.id)
    if (includesId) {
      return includesId
    }
    const related = line.related || []
    includesId = !!related.find(
      v => v.formType === 'requisition' && v.id !== line.id && this.state.relatedApplyId.includes(v.id)
    )
    return includesId
  }
  handleImportTripOrder = () => {
    const { value = [], bus } = this.props
    const { entityInfo, relatedRequisitionData = [], limitTripOrders } = this.state
    let codeList = value.map(item => item.code)
    if (relatedRequisitionData.length > 0) {
      codeList = relatedRequisitionData?.map(item => item?.form?.code)
    }
    let otherParams = undefined
    if (limitTripOrders.length) {
      const filters = limitTripOrderFilter(limitTripOrders, entityInfo)
      otherParams = { filters }
    }
    bus && bus.emit('open:trip:order:datalink:modal', entityInfo, codeList, undefined, otherParams)
  }
  getDescription = line => {
    const { field } = this.props
    const descHidden =
      !this.initedRelatedApplyId ||
      this.expenseLinkTypeMap[line.id] === 'trip' ||
      !['new', 'draft'].includes(this.props.billState) ||
      this.fnCheckId(line)
    return descHidden || field?.showErrorWhenLink
      ? null
      : {
        content: i18n.get('未关联申请明细，不会核减申请金额')
      }
  }
  setHasOrder = (id) => {
    const { hasOrderList } = this.state
    const newOrderList = hasOrderList.slice(0)
    newOrderList.push(id)
    this.setState({
      hasOrderList: newOrderList
    })
  }
  render() {
    const { value, submitterId, billSpecification } = this.props
    const { hasWrittenOff, showImportTripOrderButton, hasOrderList } = this.state
    const canWrittenOff = !!billSpecification?.configs?.find(c => c.ability === 'writtenOff' && !c.writeOffTurnOff)
    const tripOrderInfo = billSpecification?.configs?.find(c => c.ability === 'apply')?.tripOrderInfo
    const disabled = isDisable(this.props)
    const isTrip = Array.isArray(value) && value.every(v => this.expenseLinkTypeMap[v.id] === 'trip')
    const { id, name } = submitterId
    return (
      <div
        id="multiple-expense-link-select"
        className={classNames(styles['expenselink-wrapper'], { 'expenselink-wrapper-disabled': disabled })}
      >
        <Button
          category="secondary"
          theme="highlight"
          onClick={disabled ? undefined : this.handleAddExpenseLink}
          disabled={disabled}
          icon={<OutlinedTipsAdd />}
          data-testid="field-expenseLink-select"
        >
          {i18n.get('关联申请')}
        </Button>
        {value && !!value.length && (
          <div className="import-select">
            {value.map((line, index) => (
              <div className='new-requisition-wrapper' key={line.id}>
                <RequisitionInfo
                  index={index}
                  editable={!disabled}
                  value={line}
                  onRemove={this.handleRemove.bind(this, line)}
                  onDetailClick={this.handleDetailClick.bind(this, line)}
                  description={this.getDescription(line)}
                  hasOrder={hasOrderList.includes(line.id)}
                />
                {tripOrderInfo && <AssociatedTrip fromExpenseLink submitterId={id} requisitionId={line.id} submitterName={name} setHasOrder={this.setHasOrder} />}
              </div>
            ))}
          </div>
        )}
        {this.isImportDetails() && !disabled && (
          <span className="import-font ml-5 mr-10" onClick={this.handleClear}>
            {i18n.get('清空选择')}
          </span>
        )}
        {this.isImportDetails() && !isTrip && (
          <span className="import-font mr-10" onClick={this.handleImportApplyDetail}>
            {i18n.get('导入明细')}
          </span>
        )}
        {hasWrittenOff && canWrittenOff && (
          <span className="import-font mr-10" onClick={this.handleImportWrittenOff}>
            {i18n.get('复制核销')}
          </span>
        )}
        {showImportTripOrderButton && !!value && (
          <span className="import-font mr-10" onClick={this.handleImportTripOrder}>
            {i18n.get('导入差旅订单')}
          </span>
        )}
      </div>
    )
  }
}
