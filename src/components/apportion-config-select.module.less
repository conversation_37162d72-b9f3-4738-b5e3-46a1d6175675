.apportion-config-comp-wrapper {
  background-color: var(--eui-bg-float-overlay);
  padding: 16px;
  margin-top: 8px;
  border-radius: 8px;

  :global {
    .eui-image {
      display: none;
    }

    .eui-form-item-explain {
      margin-bottom: 0;
    }

    .eui-space-horizontal {
      align-items: flex-start;

      .eui-input-number {
        height: 32px;
      }
    }

    .apportion-config-item {
      .eui-form-item {
        margin-bottom: 0;
      }

      .eui-input-number-group>.eui-input-number:first-child,
      .eui-input-number-group-addon:first-child {
        border-radius: 6px 0 0 6px;
      }
    }

    .flow-template-width {
      width: 160px;
    }

    .apportion-rule-width {
      width: 160px;
      display: flex;
      gap: 8px;

      >.eui-form-item,
      >.eui-select {
        flex: 1;
        overflow: hidden;
      }
    }

    .apportion-limit-width {
      width: 210px;
      display: flex;
      gap: 8px;

      >.eui-form-item,
      >.eui-select {
        flex: 1;
        overflow: hidden;
      }
    }

    .apportion-label-width {
      width: 160px;
    }



    .form-list-label {
      display: flex;
      align-items: center;
      color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
      font: var(--eui-font-body-r1);
      margin-top: 8px;
      margin-bottom: 8px;

      .apportion-label-width,
      .apportion-limit-width {
        width: 160px;
      }

      .required {
        color: var(--eui-function-danger-500, #F53F3F);
        font: var(--eui-font-body-b1);
        margin-left: 2px;
      }
    }
  }
}

.apportion-last-width {
  flex: 1
}


.image-preview {
  width: 100% !important;
}