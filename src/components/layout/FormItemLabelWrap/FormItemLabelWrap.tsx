import React from 'react'
import styles from '../ExceedStandardRiskForField.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Tooltip, Popover } from '@hose/eui'
import classnames from 'classnames'
import NoteView from './NoteView'
import { EnhanceConnect } from '@ekuaibao/store'
import EKBIcon from '../../../elements/ekbIcon'
import { observer } from 'mobx-react'
import { inject } from '@ekuaibao/react-ioc'
import RiskTag from '../../../plugins/bills/riskWarning/RiskTag'

interface Props {
  external: any
  isForbid: boolean
  isEdit: boolean
  noColon: boolean
  style: any
  placement: any
  stopPropagation: boolean
  flowId: string
  field: any
  isDetail: boolean
  billNotes: any[]
  detailReadable: boolean
  detailId?: string
  canEditNote?: boolean
  authorRemovable: boolean
  submitterId: any
  billNotesInHistory: any[]
  showBillNotesInHistory: boolean
  isAlign?: boolean
  formFontSize: 'small' | 'standard' | 'big'
  layout: string,
  isShowRiskLabel:boolean
}

@EnhanceConnect(state => ({
  billNotes: state['@bills'].billNotes,
  detailReadable: state['@bills'].detailReadable,
  authorRemovable: state['@bills'].authorRemovable,
  billNotesInHistory: state['@bills'].billNotesInHistory,
  showBillNotesInHistory: state['@bills'].showBillNotesInHistory
}))
@observer
export default class FormItemLabelWrap extends React.Component<Props> {
  @inject('permission') permission: any
  state = { noteArr: [] }

  componentWillReceiveProps(np) {
    const { billNotes, showBillNotesInHistory, billNotesInHistory } = this.props
    if (showBillNotesInHistory) {
      if (np.billNotesInHistory !== billNotesInHistory) {
        this.fnRefreshNoteArr(np.billNotesInHistory)
      }
    } else {
      if (np.billNotes !== billNotes) {
        this.fnRefreshNoteArr(np.billNotes)
      }
    }

  }

  componentDidMount() {
    const { billNotes, showBillNotesInHistory, billNotesInHistory } = this.props
    const arr = showBillNotesInHistory ? billNotesInHistory : billNotes
    this.fnRefreshNoteArr(arr)
  }

  fnRefreshNoteArr = (billNotes) => {
    const { isDetail, detailId, field, flowId } = this.props
    // 处理回显批注
    const noteArr = billNotes.filter(note => {
      const { subDataType, subDataId, onField } = note
      if (isDetail) { // 在明细中的label
        if (subDataType === 'FLOW') {
          return false
        } else {
          return subDataId === detailId && field.field === onField
        }
      } else { // 在表头的label
        if (subDataType === 'FLOW') {
          return subDataId === flowId && field.field === onField
        } else {
          return false
        }
      }
    })
    this.setState({ noteArr })
  }

  addNote = () => {
    const { flowId, detailId, isDetail, field } = this.props
    // @ts-ignore
    api.open('@bills:NoteEditModal', { page: 'add', flowId, detailId, isDetail, field })
  }

  render() {
    const {
      children,
      external,
      isEdit,
      noColon = false,
      style,
      placement = 'rightTop',
      stopPropagation = true,
      flowId,
      field,
      isDetail,
      detailId,
      detailReadable,
      authorRemovable,
      canEditNote,
      layout,
      showBillNotesInHistory,
      formFontSize,
      isAlign = false,
      isShowRiskLabel = false
    } = this.props

    const { noteArr } = this.state
    const nodeId = 'risk-warning-for-field' + Math.random()

    const hasNote = !!noteArr.length
    const noteIcon = hasNote ? <div data-testid="bill-form-labelnote" className="risk-view-note-icon"><EKBIcon name={'#EDico-comment1'} /></div> : null

    // 有风险的时候才会展示 NoteView 组件，没有发现其他逻辑，这块暂时不删试试
    const ContentComponent = (<NoteView external={external}
      noteArr={noteArr}
      flowId={flowId}
      field={field}
      canEditNote={canEditNote}
      isDetail={isDetail}
      detailId={detailId}
      detailReadable={detailReadable}
      authorRemovable={authorRemovable}
      showBillNotesInHistory={showBillNotesInHistory}
    />)

    const AddNote =()=><Tooltip title={i18n.get('添加批注')} overlayClassName={toolTipOverlayClassName}>
        <div data-testid="bill-form-labeladdnote" className="addNoteBtn risk-view-note-icon" onClick={this.addNote}>
          <EKBIcon name={'#EDico-comment1'} />
        </div>
      </Tooltip>

    const showAddBtn = canEditNote && !showBillNotesInHistory && !hasNote && !this.permission?.isMCDisabled()
    const toolTipOverlayClassName = formFontSize === 'big' ? 'toolTipFontSizeLarge' : ''

    return (
      <span
        className={styles['risk-warning-for-field-wrapper']}
        id={nodeId}
        onClick={e => {
          if (stopPropagation) {
            e.stopPropagation()
            e.preventDefault()
            e.nativeEvent.stopImmediatePropagation()
          }
        }}>
        <span
          className={classnames('form-item-label-wrapper', {
            'is-align': isAlign,
            'risk-warning-horizontal': layout !== 'vertical' && !hasNote,
            'dis-f': layout !== 'vertical',
            'risk-exist':external,
             edit: isEdit
          })}
          style={style}>
          {noteIcon
            ? (<Popover
              overlayClassName={styles['note-view-popover-wrap']}
              trigger="hover"
              arrowPointAtCenter
              autoAdjustOverflow
              placement={placement}
              content={ContentComponent}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
              {noteIcon}
            </Popover>)
            : (showAddBtn
              ? ( <AddNote />)
              : null)
          }
          {
            isShowRiskLabel && external && <RiskTag external = {external} />
          }
          <span className={classnames({ "risk-warning-label": layout !== 'vertical' })}>{children}</span>
          {isEdit || noColon ? null : <span style={{ paddingLeft: 2 }}>：</span>}
        </span>
      </span>
    )
  }
}

