/**************************************************
 * Created by kaili on 2017/7/24 下午5:15.
 **************************************************/
import styles from './FormWrapper.module.less'
import React, { Children } from 'react'
import classnames from 'classnames'
import { Form } from 'antd'
import { Tooltip } from '@hose/eui'
import { FormItem, getAiFilledFields } from './FormItem'
import FormItemReadonly from './FormItem4Readonly'
import IMAGE_TIP from '../../images/custom-plan-tip.svg'
import fnGetFieldLabel from '../utils/fnGetFieldLabel'
import { fnHideFields, fnFlowHideFields, isHiddenFieldsInclude } from '../utils/fnHideFields'
import { isIE } from '../../lib/misc'
import { fnFlowShowFields } from '../utils/fnShowFields'
import { OutlinedTipsMaybe, FilledGeneralIntelligentService } from '@hose/eui-icons'
import { removeEmptyObjects } from '../dynamic/parseAIResult'
import { isEqual } from 'lodash'
import { enableAIChatCharge } from '../../lib/charge-util'

export const isValueChange = ({
  form, 
  flowId, 
  fieldName,
  dataSource
}) => {
  if (!enableAIChatCharge()) return true
  if (!fieldName || !(form || dataSource) || !flowId) return true
  const formValue = form ? form.getFieldValue(fieldName) : dataSource // 有可能直接给数据
  const aiFilledFields = getAiFilledFields(flowId)
  const aiInfo = aiFilledFields[fieldName]
  if (Array.isArray(formValue)) {
    const justIds = formValue.map(item => item.id).filter(Boolean)
    // 人员多选会再变更一次对象
    if (justIds.length > 0 && isEqual(justIds, aiInfo)) {
      return false
    }
  }

  // 人员单选也会变更一次对象
  if (typeof formValue === 'object') {
    const justId = formValue?.id
    // 金额的判断金额币种就可以了
    const isMoney = formValue?.standard && aiInfo?.standard
    if(isMoney && formValue?.standardStrCode == aiInfo?.standardStrCode && formValue?.standard == aiInfo?.standard) {
      return false
    }
    if (justId && isEqual(justId, aiInfo)) {
      return false
    }
  }

  // 提交人处理, 对象和 id 对比一致，也不要再执行清理动作
  if (fieldName === 'submitterId' && aiInfo && formValue?.id === aiInfo) {
    return false
  }

  // 这个情况就是附件了
  const isAttachments = Array.isArray(aiInfo) &&  aiInfo.every(item => item?.fileName && item?.fileId && item?.key)
  if(isAttachments) {
    return !isEqual(formValue?.map(item => item?.key), aiInfo?.map(item => item?.key))
  }

  console.log('look', removeEmptyObjects(formValue), removeEmptyObjects(aiInfo), isEqual(removeEmptyObjects(formValue), removeEmptyObjects(aiInfo)))
  if (aiInfo && isEqual(removeEmptyObjects(formValue), removeEmptyObjects(aiInfo))) {
    return false
  }
  if (aiInfo === formValue) {
    return false
  }
  return true
}

const MAX_LABEL_LENGTH_OF_HORIZONTAL = 10

export const wrapper = (readonly, wrapLayout, labelStyle = {}, hideLabel) => (props, CC) => {
  let {
    hiddenFields,
    field,
    layout,
    isNewStyle,
    external,
    isForbid,
    stackerManager,
    autoCalFields = {},
    multiplePayeesMode = false,
    payPlanMode,
    payeePayPlan,
    keel,
    isPopup,
    labelNoWrap,
    template = [],
    flowId,
    validateError = [],
    isDetail,
    detailId,
    canEditNote,
    offsetWidth,
    billSpecification,
    labelAlign,
    noPayInfo,
    isAlign = false,
    dataSource,
    isModify,
    originalValue,
    maxHorizonLabelLength
  } = props
  let {
    label,
    defaultValue,
    editable,
    isEdit,
    optional,
    configs,
    visible,
    hiddenLabel,
    labelView,
    className,
    hideInput,
    labelSize,
    name,
    hide,
    showLabel = true,
    noColon,
    dataLinkHiddenLabel = false,
    attributeHide = false,
    isReview = false,
    whiteVisibility,
    dataType = {},
    open,
    ability,
    extra = '',
    blockUIConfig = {},
    groupSubtitle,
    groupTitle,
    extraTag,
    currentNodeShowField // 审批流节点里面配置的当前节点下字段是否显示
  } = field
  label = fnGetFieldLabel(field)
  const billType = billSpecification?.type
  const supplierBills = ['reconciliation', 'settlement']
  const blackName = ['supplierSettlement', 'supplierReconciliation']
  let style = {}
  // visible === undefined 当做 true  处理
  if (visible === false) {
    style.display = 'none'
  }
  if (!showLabel) {
    labelStyle = { ...labelStyle, display: 'none' }
  }

  // 处理互联卡片隐藏操作（只读状态下展示的逻辑）
  // PEER_AUDIT_MAIN(同行人稽查) 会在后面的代码里面单独处理
  const isBlockUI =
    dataType?.entity?.startsWith('connect.BlockUI') && dataType?.entity !== 'connect.BlockUI.PEER_AUDIT_MAIN'

  if (!readonly && isBlockUI) {
    const entityStr = dataType.entity
    const count = entityStr.lastIndexOf('.')
    let id = entityStr.substring(count + 1, entityStr.length)
    if (entityStr.indexOf('connect.BlockUI.widgetCard') >= 0) {
      id = 'loanStatisticsWidgetCard'
    }
    //id目前有 APPROVAL_STATISTICS(审批统计) ENTERPRISE_PURCHASE_ORDER(合思企业购) ADJUSTMENTNODE(KA预算调整) LOAN_STATISTICS(借款统计)
    if (blockUIConfig?.[id] === 'hide') {
      style.display = 'none'
    }
  }

  layout = wrapLayout ? wrapLayout : layout
  if (label === '' && layout.wrapperCol && layout.labelCol) {
    layout.wrapperCol.offset = layout.labelCol.span
  }
  let Component = !!readonly ? FormItemReadonly : FormItem
  let clsNames = isNewStyle ? classnames('label-style') : ''

  if (labelSize === 'large') {
    clsNames = classnames('large-style')
  }
  if (labelSize === 'large-normal') {
    clsNames = classnames('large-normal')
  }
  const isLegalEntityMultiCurrency = name === 'legalEntityMultiCurrency'
  const { onFields = [], oldOnFields = [] } = autoCalFields
  const isFormula = defaultValue && defaultValue.type === 'formula'
  const isFormulaAttr = Array.isArray(configs) && !!~configs.findIndex(el => el.ability === 'caculate')
  const isFormulaCheck = Array.isArray(configs) && !!~configs.findIndex(el => el.ability === 'check')
  const isNewCostStandard = defaultValue && defaultValue.type === 'costStandard'
  const isOldCostStandard = oldOnFields?.includes(name) && !isFormula //旧差标
  const isVertical = layout === 'vertical' || layout?.labelCol?.span === 24
  const isNeedMaxWidth = (isFormula && !editable) || isNewCostStandard || isOldCostStandard //有自动计算的图标时需要设置最大宽度
  const isSingleCol = template.length && template.filter(item => item.length === 1).length === template.length //是否是一列布局
  const labelV = () => {
    return labelView ? (
      labelView(label, clsNames, CC.props)
    ) : (
      <>
        <span
          style={{ ...labelStyle }}
          className={classnames(clsNames, {
            'risk-warning-label-vertical': isNeedMaxWidth && isVertical && isSingleCol,
            'risk-warning-label-icon-horizontal': !isEdit && isNeedMaxWidth && !isVertical,
            'risk-warning-label-horizontal': !isEdit && !isVertical && !isNeedMaxWidth,
            'ie-risk-warning-label-horizontal':
              !isEdit && !isVertical && !isNeedMaxWidth && external && external.length && isIE()
          })}
        >
          <Tooltip placement="topLeft" getPopupContainer={() => document.body} title={label}>
            {(() => {
              // 处理标签文本截断逻辑
              if (isAlign && label.length > 5) {
                // 对齐模式下，超过5个字符时截断
                return `${label.substring(0, 5)}...`
              }

              if (!isVertical && label.length > MAX_LABEL_LENGTH_OF_HORIZONTAL) {
                // 水平布局下，超过最大长度时截断
                return `${label.slice(0, MAX_LABEL_LENGTH_OF_HORIZONTAL)}...`
              }
              // 其他情况直接显示完整标签
              return label
            })()}
          </Tooltip>
          {field?.optionalPayeeByZero && (
            <Tooltip title={i18n.get('支付金额不为0时收款信息必填')}>
              <img className="stand-16-icon ml-5" src={IMAGE_TIP} />
            </Tooltip>
          )}
          {isFormula && !editable && (
            <Tooltip title={i18n.get('系统自动计算，不可修改，无需再次审核')}>
              <FilledGeneralIntelligentService className={classnames(styles['img-wrapper'], 'ml-4')} />
            </Tooltip>
          )}
          {(isNewCostStandard || isOldCostStandard) && (
            <Tooltip title={i18n.get('系统费标计算，不可修改，无需再次审核')}>
              <FilledGeneralIntelligentService className={classnames(styles['img-wrapper'], 'ml-4')} />
            </Tooltip>
          )}
          {isFormulaAttr && (
            <Tooltip title={i18n.get('由系统条件配置判断选必填')}>
              <FilledGeneralIntelligentService className={classnames(styles['img-wrapper'], 'ml-4')} />
            </Tooltip>
          )}
          {isFormulaCheck && (
            <Tooltip title={i18n.get('由系统条件配置校验规则')}>
              <FilledGeneralIntelligentService className={classnames(styles['img-wrapper'], 'ml-4')} />
            </Tooltip>
          )}
          {isLegalEntityMultiCurrency && (
            <Tooltip title={i18n.get('法人实体多币种切换后，已有的费用明细会受影响，建议删除并重新添加明细')}>
              <OutlinedTipsMaybe className="ml-4" />
            </Tooltip>
          )}
          {!!field?.toolTip && (
            <Tooltip title={field?.toolTip} placement={field.toolTipPlacement}>
              <OutlinedTipsMaybe style={{ color: 'var(--eui-icon-n2)' }} className="ml-4" fontSize={15} />
            </Tooltip>
          )}
        </span>
      </>
    )
  }

  let mlabel = label && labelV()
  mlabel = !dataLinkHiddenLabel ? mlabel : ''
  const hasInput = !hideInput || !hideInput(CC.props)
  let hasAuto = configs === null ? false : configs?.find(r => r?.property === 'hide') === undefined ? false : true
  let flowFieldShow = true
  if ((dataSource?.plan || originalValue?.plan) && isModify) {
    const plan = dataSource?.plan || originalValue?.plan
    const { currentNodeShowFieldMap } = fnFlowShowFields(plan)
    if (currentNodeShowFieldMap[field.name]) {
      currentNodeShowField = true
    }
  }
  if (currentNodeShowField) {
    flowFieldShow = false
  } else if (isReview) {
    //blockUI 字段类型 同行人稽查全局字段的处理逻辑
    let isShow = fnHideFields(isReview, whiteVisibility) && readonly
    if (!isShow) {
      style = {
        height: '0px',
        overflow: 'hidden',
        margin: '0px',
        padding: '0px'
      }
      flowFieldShow = false
    }
  } else if (
    !hide ||
    (hide && !hasAuto) ||
    (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
    (hide && isHiddenFieldsInclude(hiddenFields, field) && !hasAuto)
  ) {
    let isShow =
      !hide ||
      (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
      validateError.includes(name) ||
      (external && external.length)
    if (!supplierBills.includes(billType) && blackName.includes(name)) {
      isShow = false
    }
    if (!isShow) {
      style = {
        height: '0px',
        overflow: 'hidden',
        margin: '0px',
        padding: '0px'
      }
      flowFieldShow = false
    }
  } else if (hide && hasAuto && isHiddenFieldsInclude(hiddenFields, field)) {
    let isShow = (hide && !attributeHide) || validateError.includes(name) || (external && external.length)
    if (!supplierBills.includes(billType) && blackName.includes(name)) {
      isShow = false
    }
    if (!isShow) {
      style = {
        height: '0px',
        overflow: 'hidden',
        margin: '0px',
        padding: '0px'
      }
      flowFieldShow = false
    }
  }
  //审批流是否设置了字段隐藏
  if (flowFieldShow) {
    const flowHiddenFields = fnFlowHideFields(dataSource?.plan)
    if (flowHiddenFields.includes(name)) {
      style = {
        height: '0px',
        overflow: 'hidden',
        margin: '0px',
        padding: '0px'
      }
    }
  }

  /**
   * 分摊必填
   * 禁止隐藏
   */
  if (open && ability === 'apportion') {
    style = {}
  }

  layout = typeof layout === 'string' ? { layout } : { ...layout }


  const showAiIcon = !isValueChange({
    form:props.form, 
    flowId, 
    fieldName:field?.field
  })

  return (
    <>
      {groupTitle && <span className={styles['form-item-group-title']}>{groupTitle}</span>}
      {groupSubtitle && <span className={styles['form-item-group-sub-title']}>{groupSubtitle}</span>}
      <Component
        {...layout}
        labelAlign={labelAlign}
        offsetWidth={offsetWidth}
        labelNoWrap={labelNoWrap}
        noColon={noColon}
        label={hideLabel ? '' : !hiddenLabel ? mlabel : ''}
        className={classnames(
          `${className}`,
          { [styles['hidden-required']]: name === 'defaultValue' },
          { [styles['form-item-wrapper-editable']]: !readonly },
          { 'form-list-item': !readonly },
          styles['form-item-wrapper'],
          styles[className]
        )}
        flowId={flowId}
        required={!optional}
        style={style}
        extra={extra}
        external={external}
        isForbid={isForbid}
        isFeeType={!!(stackerManager || keel || isPopup)}
        multiplePayeesMode={multiplePayeesMode}
        payPlanMode={payPlanMode}
        payeePayPlan={payeePayPlan}
        help={hasInput ? undefined : ''}
        validateStatus={hasInput ? undefined : 'success'}
        field={field}
        isDetail={isDetail}
        detailId={detailId}
        canEditNote={canEditNote}
        noPayInfo={noPayInfo}
        isAlign={isAlign}
        extraTag={extraTag}
        showAiIcon={showAiIcon}
      >
        {!hasInput
          ? React.cloneElement(CC, {
            ...CC.props,
            hidden: true,
            style: { display: 'none' }
          })
          : Children.only(CC)}
      </Component>
    </>
  )
}

export const wrapperDataLinkEdit = (readonly, wrapLayout, labelStyle, showStatus = true, isTrip, hideLabel) => (
  props,
  CC
) => {
  let {
    hiddenFields,
    field,
    layout,
    isNewStyle,
    external,
    isForbid,
    multiplePayeesMode = false,
    stackerManager,
    keel,
    isDetail,
    detailId,
    validateError = [],
    billSpecification,
    dataSource
  } = props
  let {
    label,
    optional,
    visible,
    hiddenLabel,
    labelView,
    className,
    hideInput,
    labelSize,
    name,
    importMode,
    showLabel = true,
    dataLinkHiddenLabel = false,
    configs,
    hide,
    attributeHide = false
  } = field
  const billType = billSpecification?.type
  const supplierBills = ['reconciliation', 'settlement']
  const blackName = ['supplierSettlement', 'supplierReconciliation']
  const isFormulaAttr = Array.isArray(configs) && !!~configs.findIndex(el => el.ability === 'caculate')
  let style = {}
  if (visible === false) {
    style.display = 'none'
  }
  layout = wrapLayout ? wrapLayout : layout
  if (label === '' && layout.wrapperCol && layout.labelCol) {
    layout.wrapperCol.offset = layout.labelCol.span
  }

  let Component = FormItem

  if (isTrip) {
    Component = FormItem
  }

  let clsNames = isNewStyle ? classnames('label-style') : ''

  if (labelSize === 'large') {
    clsNames = classnames('large-style')
  }
  if (showLabel === false) {
    labelStyle = { ...labelStyle, display: 'none' }
  }
  const labelV = () => {
    return labelView ? (
      labelView(label, clsNames, CC.props)
    ) : (
      <>
        <span style={labelStyle} className={clsNames}>{`${label}`}</span>
        {isFormulaAttr && (
          <Tooltip title={i18n.get('由系统条件配置判断选必填')}>
            <FilledGeneralIntelligentService className={classnames(styles['img-wrapper'], 'ml-4')} />
          </Tooltip>
        )}
      </>
    )
  }
  if (!showStatus && importMode === 'MULTIPLE') {
    showStatus = true
  }
  if (importMode === 'SINGLE') {
    if (!optional) {
      if (
        !showStatus &&
        (CC.props.value === undefined || (Array.isArray(CC.props.value) && CC.props.value.length === 0))
      ) {
        showStatus = true
      } else if (
        showStatus &&
        Array.isArray(CC.props.value) &&
        CC.props.value[0] &&
        CC.props.value[0].dataLinkTemplateId
      ) {
        showStatus = false
      }
    } else {
      showStatus = false
    }
  }

  label = fnGetFieldLabel(field)

  let mlabel = !!readonly ? label : labelV()
  mlabel = !dataLinkHiddenLabel ? mlabel : ''
  const hasInput = (!hideInput || !hideInput(CC.props)) && showStatus
  let hasAuto = configs === null ? false : configs?.find(r => r?.property === 'hide') === undefined ? false : true
  let flowFieldShow = true
  if (
    !hide ||
    (hide && !hasAuto) ||
    (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
    (hide && isHiddenFieldsInclude(hiddenFields, field) && !hasAuto)
  ) {
    let isShow =
      !hide ||
      (hide && !isHiddenFieldsInclude(hiddenFields, field)) ||
      validateError.includes(name) ||
      (external && external.length)
    if (!supplierBills.includes(billType) && blackName.includes(name)) {
      isShow = false
    }
    if (!isShow) {
      style = {
        height: '0px',
        overflow: 'hidden',
        margin: '0px',
        padding: '0px'
      }
      flowFieldShow = false
    }
  } else if (hide && hasAuto && isHiddenFieldsInclude(hiddenFields, field)) {
    let isShow = (hide && !attributeHide) || validateError.includes(name) || (external && external.length)
    if (!supplierBills.includes(billType) && blackName.includes(name)) {
      isShow = false
    }
    if (!isShow) {
      style = {
        height: '0px',
        overflow: 'hidden',
        margin: '0px',
        padding: '0px'
      }
      flowFieldShow = false
    }
  }

  //审批流是否设置了字段隐藏
  if (flowFieldShow) {
    const flowHiddenFields = fnFlowHideFields(dataSource?.plan)
    if (flowHiddenFields.includes(name)) {
      style = {
        height: '0px',
        overflow: 'hidden',
        margin: '0px',
        padding: '0px'
      }
    }
  }
  const newBillInfoViewWidth = document?.getElementById('NewBillInfoView')?.clientWidth
  if (newBillInfoViewWidth) {
    style.maxWidth = newBillInfoViewWidth - 128 + 'px'
  }

  return (
    <Component
      {...layout}
      label={hideLabel ? '' : !hiddenLabel ? mlabel : ''}
      className={classnames(
        {
          [styles['hidden-required']]: name === 'defaultValue',
          [styles['form-item-wrapper-editable']]: !readonly
        },
        styles['form-item-wrapper'],
        styles[className]
      )}
      required={!optional}
      style={style}
      external={external}
      isForbid={isForbid}
      isFeeType={!!(stackerManager || keel)}
      multiplePayeesMode={multiplePayeesMode}
      help={hasInput ? undefined : ''}
      validateStatus={hasInput ? undefined : 'success'}
      field={field}
      isDetail={isDetail}
      detailId={detailId}
    >
      {!hasInput
        ? React.cloneElement(CC, {
          ...CC.props,
          hidden: true,
          style: { display: 'none' }
        })
        : Children.only(CC)}
    </Component>
  )
}
