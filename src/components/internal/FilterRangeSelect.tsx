import { EnhanceField } from '@ekuaibao/template'
import React, { useEffect, useState } from 'react'
import { Select } from '@hose/eui'
import styles from './FilterRangeSelect.module.less'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
const { Option } = Select
import ComplexSelectV2 from '../../elements/select/ComplexSelectV2'
const customConditionResource = new Resource('/api/v1/flow/customCondition')

const customFields = [
  {
    name: '发票销售方名称',
    value: '发票销售方名称',
    label: i18n.get('发票销售方名称'),
    active: true,
    dataType: {
      type: 'text'
    },
  },
  {
    name: '发票购买方名称',
    value: '发票购买方名称',
    label: i18n.get('发票购买方名称'),
    active: true,
    dataType: {
      type: 'text'
    },
  },
  {
    name: '发票日期',
    value: '发票日期',
    label: i18n.get('发票日期'),
    active: true,
    dataType: {
      type: 'date'
    },
  }
]

const FilterRangeSelect = props => {
  const { value, field, form } = props
  const { fetchType, relatedConfig } = field
  const [customConditionList, setCustomConditionList] = useState([])
  const getCustomConditionList = async () => {
    const res = await customConditionResource.GET('/', {
      orgId: '',
      type: fetchType
    })
    console.log(res.items)
    setCustomConditionList(res.items)
  }
  useEffect(() => {
    getCustomConditionList()
  }, [fetchType])

  const openModal = async (conditionData = {}) => {
    const res = await api.open('@custom-flow:ConditionalFlowModal', {
      mainTitle: i18n.get('配置筛选规则'),
      contentLabel: i18n.get('规则名称'),
      type: fetchType,
      conditionData: conditionData,
      customFields
    })
    handleOnChange(res?.id)
  }

  const handleAdd = async (value: string) => {
    await openModal()
    await getCustomConditionList()
  }
  const handleEdit = async conditionData => {
    await openModal(conditionData)
    await getCustomConditionList()
  }
  const handleDelete = async conditionData => {
    await customConditionResource.DELETE('/$id', {
      id: conditionData.id
    })
    await getCustomConditionList()
  }
  const handleOnChange = (data: any) => {
    const { onChange } = props
    onChange && onChange(data)
  }
  if (relatedConfig && form) {
    const relatedValue = form.getFieldValue(relatedConfig.ability)
    if (relatedValue !== relatedConfig.value) {
      return null
    }
  }

  return (
    <ComplexSelectV2
      classNames={styles.selectWrapper}
      value={value}
      list={customConditionList}
      emptyTitle={i18n.get('暂无规则')}
      title={i18n.get('新建筛选规则')}
      editTitle={i18n.get('查看')}
      handleAdd={handleAdd}
      handleEdit={handleEdit}
      handleDelete={handleDelete}
      labelInValue={false}
      disabled={false}
      disabledEdit={false}
      enableDelete={false}
      onChange={handleOnChange}
      single
    />
  )
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'filter-range-select'
  },
  validator: (field, props) => (rule, value, callback) => {
    const relatedConfig = field.relatedConfig
    if (relatedConfig && props?.form) {
      const relatedValue = props?.form?.getFieldValue(relatedConfig.ability)
      if (relatedValue !== relatedConfig.value) {
        return callback()
      }
    }
    if (value) {
      return callback()
    }
    callback(i18n.get('请选择筛选规则'))
  },
  wrapper: wrapper()
})(
  // @ts-ignore
  EnhanceConnect(state => {
    return {
      baseDataProperties: state['@common'].globalFields.data
    }
  })(FilterRangeSelect)
)
