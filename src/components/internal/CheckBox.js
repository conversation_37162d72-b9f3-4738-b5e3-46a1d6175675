/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 30/06/2017 15:34.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { Divider, Icon } from 'antd'
import { Checkbox, Tooltip } from '@hose/eui'
import styles from './CheckBox.module.less'
import { get } from 'lodash'
import { isObject } from '@ekuaibao/helpers'
import { showMessage } from '@ekuaibao/show-util'
import { app } from '@ekuaibao/whispered'
import { validatorCheckBoxValue } from '../validator/validator'
const { UniversalComponent } = app.require('@elements/StandardVersionComponent')
import { getBoolVariation } from '../../lib/featbit'
import { OutlinedTipsMaybe } from '@hose/eui-icons'
import classNames from 'classnames'

@EnhanceField({
  descriptor: {
    type: 'checkbox'
  },
  initialValue(props) {
    let { field = {} } = props
    return field?.defaultValue?.value
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(validatorCheckBoxValue(field, value, props))
  }
})
@EnhanceConnect(state => ({
  powers: state['@common'].powers
}))
export default class CheckBox extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      delayedCheckCompleted: false
    }
  }

  componentWillMount() {
    let { bus, field, onChange, powers } = this.props
    const { name } = field
    this.disabled = field.disabled
    if (name === 'pay:allowSelectionReceivingCurrency') {
      if (!powers['Currency'] || !getBoolVariation('multi_currency_payment')) {
        onChange && onChange(false)
      }
    }
    bus.on('field:update:optional', this.fnHanldeUpdateOptional)
    bus.on('field:update:optional:currency', this.handleCrossCurrencyChange)
  }

  componentDidMount() {
    let { bus, field, value } = this.props
    if (field.name === 'writtenOff:crossCurrencyWrittenOff') {
      bus.emit('field:update:optional:currency', value)
    }

    // 延迟100毫秒后检查disabledByCondition，确保form字段值已经初始化完成
    setTimeout(() => {
      this.setState({ delayedCheckCompleted: true })
    }, 100)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('field:update:optional', this.fnHanldeUpdateOptional)
    bus.un('field:update:optional:currency', this.handleCrossCurrencyChange)
  }

  handleCrossCurrencyChange = checked => {
    const { field } = this.props
    const name = field?.name
    let disabled = this.disabled
    if (name === 'writtenOff:foreignCurrency' || name === 'writtenOff:sameLegalEntityWrittenOff') {
      disabled = checked
    }
    if (this.disable !== disabled) {
      this.disable = disabled
      this.forceUpdate()
    }
  }

  fnHanldeUpdateOptional = (value, field = {}) => {
    const { onChange } = this.props
    let fieldName = this.getFieldName()
    let disabled = this.disabled
    if (fieldName === 'required' && value && (value.type == 'invoiceSum' || value.type === 'costStandard')) {
      disabled = true
      onChange && onChange(true)
    }
    if (
      fieldName === 'required' &&
      field.checkDisabled &&
      field.editable === false &&
      value &&
      value.type === 'formula'
    ) {
      disabled = true
      onChange && onChange(true)
    }
    if (fieldName === 'allowExternalStaff' && field.name === 'dependence') {
      disabled = value
      value && onChange && onChange(!value)
    }

    if (this.disable !== disabled) {
      this.disable = disabled
      this.forceUpdate()
    }
  }

  getAutoShare = async () => {
    const { bus } = this.props
    const formValue = await bus.getValue()
    const autoShareObj = formValue['requisition:autoShare']
    if (!autoShareObj) {
      return true
    }
    return (
      autoShareObj?.visibility?.departments?.length === 0 &&
      autoShareObj?.visibility?.roles?.length === 0 &&
      autoShareObj?.visibility?.staffs?.length === 0 &&
      (!autoShareObj?.selection || (autoShareObj?.selection && Object.keys(autoShareObj?.selection)?.length === 0)) &&
      autoShareObj?.autoShareRule?.fieldIds?.length === 0
    )
  }

  getFieldName() {
    return get(this.props, 'field.name', '')
  }

  forceUpdateDom = checked => {
    const dom1 = document.getElementsByClassName('allow-modify-repayment-date-config')[0]
    const dom2 = document.getElementsByClassName('limit-repayment-date-range')[0]
    const display = checked ? 'block' : 'none'
    dom1.style.display = display
    dom2.style.display = display
  }

  onChange = async e => {
    let { onChange, field, form, bus, value } = this.props
    let { config, name, controlField, mutualExclusion = [] } = field
    let { checked } = e.target

    if (checked && mutualExclusion?.length > 0 && form) {
      const currentFormValue = form.getFieldsValue()
      const formValue = {}
      mutualExclusion.forEach(v => {
        if (currentFormValue[v.name] !== undefined && currentFormValue[v.name] !== null) {
          if (v.name === 'pay:allowMultiplePayees') {
            if (currentFormValue[v.name].isAll !== v.value) {
              formValue[v.name] = {
                ...currentFormValue[v.name],
                isAll: v.value
              }
              const messageStatus = v.messageStatus || 'info'
              v.message && showMessage[messageStatus](v.message)
            }
          } else {
            if (currentFormValue[v.name] !== v.value) {
              formValue[v.name] = v.value
              const messageStatus = v.messageStatus || 'info'
              v.message && showMessage[messageStatus](v.message)
            }
          }
        }
      })
      if (Object.keys(formValue).length > 0) {
        form.setFieldsValue(formValue)
      }
    }
    if (name === 'loan:isLimitRepaymentDate') {
      this.forceUpdateDom(checked)
    }
    if (config && form) {
      //如果该组件关联了其他组件 用config描述关联关系
      let { relateData, relateValue } = config
      if (relateValue === checked) {
        let obj = {}
        relateData.forEach(v => {
          let { ability, defaultValue } = v
          obj[ability] = defaultValue
        })
        bus.setValue(obj)
      }
    } else if (!checked && controlField && controlField === 'filterId' && form) {
      bus.setValue({ filterId: '' })
    }

    if (name === 'canLoan:allShare') {
      const autoShare = await this.getAutoShare()
      if (autoShare && !value) {
        showMessage.error(i18n.get('请先配置共享人及条件后再勾选此配置'))
        return
      }
    }

    if ((name === 'allowInteriorStaff' || name === 'allowExternalStaff') && checked) {
      const allowExternalStaffValue = form.getFieldValue('allowExternalStaff')
      form.setFields({ allowExternalStaff: { value: allowExternalStaffValue, errors: null } })
    }
    if (name === 'allowInteriorStaff' && !checked) {
      const allowExternalStaffValue = form.getFieldValue('allowExternalStaff')
      if (!allowExternalStaffValue) {
        form.setFields({ allowExternalStaff: { errors: [i18n.get('至少选择一项')] } })
      }
    }
    if (name === 'hideConditionSetting' && checked) {
      const formValue = form.getFieldsValue()
      if (
        formValue &&
        formValue.hideBlacklistVisibility?.fullVisible == true &&
        formValue.hideCalculationFormula == false
      ) {
        form.setFieldsValue({
          hideBlacklistVisibility: {
            fullVisible: false,
            departments: [],
            roles: [],
            staffs: [],
            departmentsIncludeChildren: true
          }
        })
      }
    }

    if (name === 'hide') {
      const visibilityDefaultValue = {
        fullVisible: true,
        staffs: [],
        roles: [],
        departments: [],
        departmentsIncludeChildren: false
      }
      form.setFieldsValue({
        hideBlacklistVisibility: visibilityDefaultValue,
        hideVisibility: visibilityDefaultValue,
        hideCalculationFormula: false,
        hideConditionSetting: false
      })
    }

    if (name === 'writtenOff:crossCurrencyWrittenOff') {
      bus.emit('field:update:optional:currency', checked)
      checked && bus.setValue({ 'writtenOff:foreignCurrency': false, 'writtenOff:sameLegalEntityWrittenOff': true })
    }

    if (checked && name === 'corpPayEnhanced:usePrepayment') {
      const corporatePaymentMode = form.getFieldValue('corpPayEnhanced:corporatePaymentMode')
      if (corporatePaymentMode === 'CUSTOM') {
        form.setFieldsValue({ 'corpPayEnhanced:useInvoice': true })
      }
    }

    onChange && onChange(checked)
  }

  disabledByCondition = () => {
    const { field, form } = this.props
    const { disabledCondition = [] } = field

    // 如果延迟检查还未完成，直接返回false，避免在初始化时获取不到form值
    if (!this.state.delayedCheckCompleted) {
      return false
    }

    return disabledCondition.some(condition => {
      const { ability, value } = condition
      const abilityValue = form.getFieldValue(ability)
      return abilityValue === value
    })
  }

  render() {
    const { field, form, value, powers } = this.props
    const {
      text,
      name,
      relatedConfig,
      size,
      style = {},
      hiddenLabel = false,
      label,
      tips,
      isDivider,
      tipsSize,
      disabled = false,
      toolTip,
      customLabel,
      className
    } = field
    let visible = true
    const relateDataValue = value
    if (relatedConfig && form) {
      //该组件被其他组件关联，用related config配置
      const { ability, value: _value, valueInArrScope = [], valueNotInArrScope = [], valueRange = [] } = relatedConfig
      const abilityValue = form.getFieldValue(ability)
      //针对 ‘该单据不使用核销借款’ 作的处理
      if (
        ability === 'writtenOff:writeOffTurnOff' &&
        name === 'writtenOff:mustWrittenOff' &&
        relateDataValue &&
        abilityValue
      ) {
        const tempValue = {}
        tempValue[name] = false
        form.setFieldsValue(tempValue)
      }
      //针对 '该单据只能核销一笔借款' 作的处理
      if (
        ability === 'writtenOff:writeOffTurnOff' &&
        name === 'writtenOff:limitOnlyOneSubmitExpense' &&
        relateDataValue &&
        abilityValue
      ) {
        const tempValue = {}
        tempValue[name] = false
        form.setFieldsValue(tempValue)
      }
      let cValue = isObject(abilityValue) ? abilityValue.checked : abilityValue
      if (ability === 'requisition:applyCloseRule') {
        cValue = abilityValue?.rule
      }
      if (valueInArrScope?.length) {
        visible = !!~valueInArrScope.indexOf(cValue)
      } else if (valueNotInArrScope?.length) {
        visible = !~valueNotInArrScope.indexOf(cValue)
      } else {
        visible = !!(cValue === _value || valueRange.includes(cValue))
      }
      if (name === 'pay:allowSelectionReceivingCurrency') {
        visible = visible && powers['Currency'] && getBoolVariation('multi_currency_payment')
      }
    }
    let errors
    if (name === 'allowExternalStaff') {
      errors = form.getFieldError('allowExternalStaff')
    }
    const fieldList = [
      'writtenOff:foreignCurrency',
      'canLoan:foreignCurrency',
      'loan:foreignCurrency',
      'ignorePlanConfig',
      'writtenOff:crossCurrencyWrittenOff'
    ]
    if (!visible) return null

    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <div
          className={classNames(styles['ekb-check'], styles[className])}
          data-cy={`ekb-check-${field.name}`}
          style={visible && relatedConfig && !relatedConfig.notNeedMargin ? { marginLeft: 20, ...style } : { ...style }}
        >
          {!hiddenLabel && <div className={styles['new_label']}>{label}</div>}
          {customLabel && (
            <div className={styles['custom_label']}>
              {customLabel.label}
              {customLabel.required && <span className={styles.required}>*</span>}
            </div>
          )}
          <Checkbox
            onChange={this.onChange}
            disabled={disabled || this.disabledByCondition() || this.disable}
            checked={value}
          >
            <span className={size === 'large' ? 'text-large' : 'text-normal'}>{text}</span>
          </Checkbox>
          {(field.name === 'writtenOff:foreignCurrency' ||
            field.name === 'ignorePlanConfig' ||
            field.name === 'corpPayEnhanced:allowGeneratePrepayment' ||
            field.name === 'corpPayEnhanced:allowGenerateAccountsPayable' ||
            field.name === 'pay:allowSelectionReceivingCurrency') && (
            <Tooltip placement="bottomLeft" title={toolTip}>
              <OutlinedTipsMaybe fontSize={16} className="tooltip-icon" />
            </Tooltip>
          )}
          {field.name === 'hide' && !field.hiddenTip && (
            <Tooltip
              placement="bottom"
              title={i18n.get(
                '适用于系统计算且无需审批的字段，如匹配审批流中自定义生效节点条件。其中「以下人员始终可查看」优先级高于「仅对以下人员隐藏」。'
              )}
            >
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )}
          {field.name === 'allowUseDeactivatedData' && (
            <Tooltip
              placement="bottom"
              title={i18n.get('勾选之后，已经停用的部门可以被带出，如驳回或草稿状态下已经停用的部门也可以显示。')}
            >
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )}
          {field.helpText && !field.hiddenTip && (
            <Tooltip placement="bottom" title={field.helpText}>
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )}
          {field.name === 'expense:isForbiddenByFeeType' && (
            <Tooltip placement="bottom" title={i18n.get('该条件仅适用于申请单可借款的情况，对借款单核销不生效。')}>
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )}
          {field.name === 'showDetailNo' && (
            <Tooltip placement="bottom" title={i18n.get('将按照单据首次提交的先后顺序对费用明细进行排序并展示')}>
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )}
          {field.name === 'realtimeCalculateBudget' && (
            <Tooltip
              placement="bottom"
              title={i18n.get(
                '选中后，单据为“草稿态、被驳回和审批中”时，计算“占用预算”并形成预览（预览数据并非实际占用数据，提交单据后会更新预算占用）'
              )}
            >
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )}
          {(field.name === 'expense:draftFlowCheckCostControl' ||
            field.name === 'requisition:draftFlowCheckCostControl') && (
            <Tooltip placement="bottom" title={i18n.get('选中后在单据“存为草稿”时，计算“费用标准”并形成预览')}>
              <Icon
                className="enum-tooltip"
                type="question-circle-o"
                style={{ fontSize: 12, paddingTop: 4, color: '#1d2b3d' }}
              />
            </Tooltip>
          )}
          {tips && (
            <div className="ekb-check-tips" style={tipsSize === 'small' ? { marginLeft: 0, fontSize: '12px' } : {}}>
              {tips}
            </div>
          )}
          {isDivider && <Divider dashed />}
          {!!errors && <div className="ekb-check-error">{errors.map(error => error)}</div>}
        </div>
      </UniversalComponent>
    )
  }
}
