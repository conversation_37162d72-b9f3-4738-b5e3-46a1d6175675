import React from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import { app } from '@ekuaibao/whispered'
import { Select } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'

@EnhanceField({
  descriptor: {
    type: 'select:flow'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class SelectFlow extends React.PureComponent {
  constructor(props) {
    super(props)
  }

  gotoFlow = () => {
    const { value, bus } = this.props
    bus.emit('jump:to:customFlow', value)
  }

  emptyValue = () => {
    this.onChange('')
  }

  onChange = value => {
    let { onChange } = this.props
    onChange && onChange(value)
  }

  fnBuildItems() {
    let { tag } = this.props
    let children = []
    tag = tag || []
    tag.forEach(line => {
      let { value, label } = line
      children.push(<Select.Option data-testid={`field-selectFlow-${label}`} key={value} value={value}>{label}</Select.Option>)
    })
    return children
  }

  render() {
    const { value, field } = this.props
    const children = this.fnBuildItems()
    const { placeholder } = field
    const disabled = isDisable(this.props)
    const userInfo = app.getState()['@common'].userinfo
    const haveCustomFLowMenu = userInfo?.data?.permissions?.includes('FLOW_ADMIN')
    return (
      <div className="dis-f jc-sb">
        <Select
          style={{ flex: 1 }}
          disabled={disabled}
          placeholder={placeholder}
          onChange={this.onChange}
          getPopupContainer={triggerNode => triggerNode.parentNode}
          value={value}
          data-testid={`field-Flow-select`}
          size="large"
        >
          {children}
        </Select>
        {haveCustomFLowMenu && (
          <div className="ml-10 color-blue cur-p" onClick={!!value ? this.gotoFlow : this.emptyValue}>
            {i18n.get('查看流程')}
          </div>
        )}
      </div>
    )
  }
}
