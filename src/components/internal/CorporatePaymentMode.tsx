import { EnhanceField } from '@ekuaibao/template'
import React, { useEffect, useRef, useState } from 'react'
import styles from './CorporatePaymentMode.module.less'
import { wrapper } from '../layout/FormWrapper'
import { OutlinedTipsYes, OutlinedTipsMoreClose, OutlinedTipsNo, OutlinedTipsDone } from '@hose/eui-icons'
import classNames from 'classnames'

const PAYMENT_MODES = [
  {
    key: 'ADVANCE_PAYMENT',
    title: i18n.get('预付款'),
    features: [
      { label: i18n.get('无法使用发票'), status: 'fail' },
      { label: i18n.get('无法核销预付款'), status: 'fail' },
      { label: i18n.get('支持生成预付款'), status: 'success' },
      { label: i18n.get('不支持生成应付款'), status: 'fail' }
    ]
  },
  {
    key: 'PAYMENT_UPON_DELIVERY',
    title: i18n.get('到票付款'),
    features: [
      { label: i18n.get('支持使用发票'), status: 'success' },
      { label: i18n.get('无法核销预付款'), status: 'fail' },
      { label: i18n.get('可选是否生成预付款'), status: 'optional' },
      { label: i18n.get('可选是否生成应付款'), status: 'optional' }
    ]
  },
  {
    key: 'VERIFICATION_UPON_ARRIVAL',
    title: i18n.get('票核销预付'),
    features: [
      { label: i18n.get('支持使用发票'), status: 'success' },
      { label: i18n.get('支持核销预付款'), status: 'success' },
      { label: i18n.get('不支持生成预付款'), status: 'fail' },
      { label: i18n.get('可选是否生成应付款'), status: 'optional' }
    ]
  },
  {
    key: 'CUSTOM',
    title: i18n.get('自定义模式'),
    features: [
      { label: i18n.get('选择后可在下方')},
      { label: i18n.get('自定义配置')}
    ]
  }
]

const CorporatePaymentMode = props => {
  const { value, onChange, form, field } = props
  const { abilityDefaultValues } = field
  const [cardHeight, setCardHeight] = useState(140)
  const cardRefs = useRef<(HTMLDivElement | null)[]>([])

  // 计算卡片宽度
  const getCardWidth = () => {
    return i18n.currentLocale === 'zh-CN' ? 180 : 220
  }

  // 计算所有卡片的最大高度
  const calculateMaxHeight = () => {
    const heights = cardRefs.current
      .filter(ref => ref)
      .map(ref => {
        if (!ref) return 0
        
        // 临时移除高度限制和居中效果，让元素自然展开
        const originalHeight = ref.style.height
        const originalJustifyContent = ref.style.justifyContent
        ref.style.height = 'auto'
        ref.style.justifyContent = 'flex-start'

        // 获取实际内容高度
        const height = ref.scrollHeight
        
        // 恢复原始样式
        ref.style.height = originalHeight
        ref.style.justifyContent = originalJustifyContent
        
        return height
      })
    
    const maxHeight = Math.max(...heights)
    setCardHeight(maxHeight)
  }

  useEffect(() => {
    if (!value) {
      onChange(PAYMENT_MODES[0].key)
      form.setFieldsValue(abilityDefaultValues[PAYMENT_MODES[0].key])
    }
  }, [])

  // 在组件挂载后计算高度
  useEffect(() => {
    // 使用多个延迟确保DOM完全渲染后再计算高度
    const timers = [
      setTimeout(() => calculateMaxHeight(), 100),
      setTimeout(() => calculateMaxHeight(), 300),
      setTimeout(() => calculateMaxHeight(), 500)
    ]
    
    return () => timers.forEach(timer => clearTimeout(timer))
  }, [])

  const handleChange = (key: string) => {
    onChange(key)
    form.setFieldsValue(abilityDefaultValues[key])
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <OutlinedTipsYes className={classNames(styles.successIcon, styles.icon)} />
      case 'fail':
        return <OutlinedTipsMoreClose className={classNames(styles.failIcon, styles.icon)} />
      case 'optional':
        return <OutlinedTipsNo className={classNames(styles.optionalIcon, styles.icon)} />
      default:
        return null
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.title}>{i18n.get('对公付款设置')}</div>
        <div className={styles.subtitle}>
          {i18n.get('选择模式')}
          <span className={styles.required}>*</span>
        </div>
      </div>

      <div className={styles.modeGrid}>
        {PAYMENT_MODES.map((mode, index) => (
          <div
            key={mode.key}
            ref={el => (cardRefs.current[index] = el)}
            className={classNames(styles.modeCard, {
              [styles.selected]: value === mode.key
            })}
            onClick={() => handleChange(mode.key)}
            style={{
              width: `${getCardWidth()}px`,
              height: `${cardHeight}px`
            }}
          >
            {mode.key !== 'CUSTOM' && (
              <>
                <div className={styles.cardTitle}>{mode.title}</div>
                <div className={styles.cardBody}>
                  <div className={styles.features}>
                    {mode.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className={styles.featureItem}>
                        {getStatusIcon(feature.status)}
                        <span className={styles.featureLabel}>{feature.label}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}

            {mode.key === 'CUSTOM' && (
              <div className={styles.customCard}>
                <div className={styles.cardTitle}>{mode.title}</div>
                <div className={styles.customDesc}>
                  {mode.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className={styles.featureItem}>
                      {feature.label}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {value === mode.key && (
              <div className={styles.selectedIndicator}>
                <OutlinedTipsDone className={styles.selectedIcon} />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'corporate-payment-mode-select'
  },
  wrapper: wrapper()
})(CorporatePaymentMode)
