import styles from './CheckBoxSelectShares.module.less'
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox } from 'antd'
import TagSelector from '../../elements/tag-selector'
import { FieldTagsRule } from './FieldTagsRule'
import { cloneDeep, isNil, remove } from 'lodash'
import { wrapper } from '../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import SelectCustomCondition from '../../elements/SelectCustomCondition'
const FlowNodeRole = api.require('@custom-flow/elements/plan-detail/FlowNodeRole')
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

@EnhanceField({
  descriptor: {
    type: 'checkbox:select:shares'
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  planRoleConfig: state['@custom-flow'].planRoleConfig || [],
  // departmentTree: state['@common'].department.data,
  departmentList: state['@common'].department.list,
  conditionList: state['@custom-flow'].conditionList
}))
export default class CheckBoxSelectShares extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      checkedCustom: !isNil(props.value?.condition),
      checkedPeople: props.value?.isVisibility,
      checkedRole: props.value?.isSelection,
      checkedField: props.value?.autoShareRule?.isOpen
    }
  }
  componentWillMount() {
    api.invokeService('@common:get:staffs:roleList:department')
  }

  updateValue = (value, { staffs, departments, roles, departmentsIncludeChildren }) => {
    return { ...cloneDeep(value), visibility: { staffs, departments, roles, departmentsIncludeChildren } }
  }

  updateFormValue = async val => {
    const { bus } = this.props
    const formValue = await bus.getValue()
    if (formValue.hasOwnProperty('canLoan:allShare')) {
      if (
        val?.visibility?.departments?.length === 0 &&
        val?.visibility?.roles?.length === 0 &&
        val?.visibility?.staffs?.length === 0
      ) {
        bus.setValue({ 'canLoan:allShare': false })
      }
    }
  }

  getDeptItemsByIds = (list = [], ids = []) => {
    let items = []
    let fn = item => {
      if (ids && ids.indexOf(item.id) > -1) {
        items.push(item)
      }
    }
    list.forEach(item => {
      fn(item)
    })
    return items
  }

  getItemByIds = (data = [], ids = []) => {
    return data.filter(line => {
      return ids.indexOf(line.id) > -1
    })
  }

  valueParse = value => {
    if (!value) {
      return { checked: false, tags: [], condition: null, selection: null, autoShareRule: {} }
    }
    return {
      checked: value.isEnable,
      tags: [
        ...this.getDeptItemsByIds(this.props.departmentList, value.visibility.departments),
        ...this.getItemByIds(this.props.staffs, value.visibility.staffs),
        ...this.getItemByIds(this.props.roles, value.visibility.roles)
      ],
      condition: value.condition,
      isVisibility: value.isVisibility,
      isSelection: value.isSelection,
      selection: value.selection,
      autoShareRule: value.autoShareRule
    }
  }

  handleChecked = e => {
    const { onChange } = this.props
    const checked = e.target.checked
    const val = {
      isEnable: checked,
      visibility: {
        fullVisible: !checked,
        staffs: [],
        roles: [],
        departments: [],
        departmentsIncludeChildren: false
      },
      condition: null,
      isVisibility: false, //是否选择添加固定共享人
      isSelection: false, //是否选择“通过角色自动匹配添加共享人”
      selection: null,
      autoShareRule: {
        isOpen: false, //是否选择“单据模板上的人员（多选）字段”
        fieldIds: [] //单据模板上的人员（多选）字段
      }
    }
    this.setState({
      checkedCustom: false,
      checkedPeople: false,
      checkedRole: false,
      checkedField: false
    })
    this.updateFormValue(val)
    onChange && onChange(val)
  }

  handlePeopleTagsChanged = (tags, deleteTag) => {
    let { onChange, value } = this.props
    let { staffs, departments, roles, departmentsIncludeChildren } = value?.visibility
    remove(staffs, id => id === deleteTag.id)
    remove(departments, id => id === deleteTag.id)
    remove(roles, id => id === deleteTag.id)
    let val = this.updateValue(value, { staffs, departments, roles, departmentsIncludeChildren })
    // this.updateFormValue(val)
    onChange && onChange(val)
  }

  handlePeopleClick = () => {
    const { onChange, bus, field, value } = this.props
    bus.invoke('checkbox:tags:click', field, value.visibility).then(data => {
      const { checkedList, departmentsIncludeChildren } = data
      const staffs = checkedList.find(o => o.type === 'department-member').checkedData?.map(e => e.id)
      const departments = checkedList.find(o => o.type === 'department').checkedData?.map(e => e.id)
      const roles = checkedList.find(o => o.type === 'role').checkedData?.map(e => e.id)
      const val = this.updateValue(value, { staffs, departments, roles, departmentsIncludeChildren })
      // this.updateFormValue(val)
      onChange && onChange(val)
    })
  }
  handleRoleTagsChanged = v => {
    const { onChange, value } = this.props
    value.selection = { ...v, type: 'ROLE' }
    const val = { ...cloneDeep(value) }
    onChange && onChange(val)
  }
  handleFieldTagsChanged = ({ fieldIds }) => {
    const { onChange, value } = this.props
    value.autoShareRule.fieldIds = fieldIds ?? []
    const val = { ...cloneDeep(value) }
    onChange && onChange(val)
  }

  handleChangePeople = (type, e) => {
    const { onChange, value, planRoleConfig } = this.props
    this.setState({
      [type]: e.target.checked
    })
    switch (type) {
      case 'checkedPeople':
        value.isVisibility = e.target.checked
        value.visibility = {
          staffs: [],
          roles: [],
          departments: []
        }
        break
      case 'checkedRole':
        value.isSelection = e.target.checked
        if (value.isSelection) {
          value.selection = planRoleConfig?.[0] ?? {}
        } else {
          value.selection = null
        }
        break
      case 'checkedField':
        !value.autoShareRule && (value.autoShareRule = {})
        value.autoShareRule.isOpen = e.target.checked
        value.autoShareRule.fieldIds = []
        break
    }
    const val = { ...cloneDeep(value) }
    onChange && onChange(val)
  }
  handleChangeCustom = e => {
    const { onChange, value } = this.props
    this.setState({
      checkedCustom: e.target.checked
    })
    const val = { ...cloneDeep(value), condition: null }
    onChange && onChange(val)
  }

  handleCustomSelectCondition = params => {
    const { onChange, value } = this.props
    let newValue = cloneDeep(value)
    if (!params) {
      newValue.condition = null
      return onChange && onChange(newValue)
    }
    const condition = isNil(newValue.condition) ? {} : newValue.condition
    condition.id = params && params.key
    condition.name = params && params.label
    condition.type = 'CUSTOM'
    condition.level = null
    newValue.condition = condition
    onChange && onChange(newValue)
  }

  render() {
    const { field, value, conditionList, orgId, planRoleConfig, specification } = this.props
    const { checked, tags, condition, autoShareRule, selection } = this.valueParse(value)
    const { checkedCustom, checkedPeople, checkedRole, checkedField } = this.state
    const { disabled } = field ?? {}
    return (
      <UniversalComponent uniqueKey={`customSpecification.pc.${field?.name}`}>
        <div className={styles.check_box_tags}>
          <Checkbox onChange={this.handleChecked} checked={checked} disabled={disabled}>
            {field.label}
          </Checkbox>
          {checked && (
            <div className={styles.select_tags_container}>
              <div data-platform-wx2-hidden={window.isInWeComISV}>
                <Checkbox onChange={this.handleChangeCustom} checked={checkedCustom} disabled={disabled}>
                  {i18n.get('依据条件筛选')}
                </Checkbox>
                {checkedCustom && (
                  <SelectCustomCondition
                    onSelect={this.handleCustomSelectCondition}
                    conditionList={conditionList}
                    condition={condition}
                    orgId={orgId}
                  />
                )}
              </div>
              <span data-platform-wx2-hidden={window.isInWeComISV}>{i18n.get('选择共享人')}</span>
              <>
                <div className={styles.systemConditionContainer} data-platform-wx2-hidden={window.isInWeComISV && field?.name === 'loan:autoShare' || undefined}>
                  <Checkbox onChange={this.handleChangePeople.bind(this, 'checkedPeople')} checked={checkedPeople}>
                    {i18n.get('手动选择共享人')}
                  </Checkbox>
                  {checkedPeople && (
                    <TagSelector
                      className={styles.select_tags}
                      editable={disabled}
                      value={tags}
                      onClick={this.handlePeopleClick}
                      onChange={this.handlePeopleTagsChanged}
                    />
                  )}
                </div>
                <div className={styles.systemConditionContainer}>
                  <Checkbox disabled={disabled} onChange={this.handleChangePeople.bind(this, 'checkedRole')} checked={checkedRole}>
                    {i18n.get('通过角色自动匹配添加共享人')}
                  </Checkbox>
                  {checkedRole && (
                    <FlowNodeRole
                      edit={checkedRole}
                      selections={selection ?? planRoleConfig?.[0] ?? {}}
                      planRoleConfig={planRoleConfig}
                      fnHandleSelectSelection={this.handleRoleTagsChanged}
                    />
                  )}
                </div>
                <div className={styles.systemConditionContainer}>
                  <Checkbox disabled={disabled} onChange={this.handleChangePeople.bind(this, 'checkedField')} checked={checkedField}>
                    {i18n.get('通过单据字段自动匹配添加共享人')}
                  </Checkbox>
                  {checkedField && (
                    <FieldTagsRule
                      value={autoShareRule}
                      onChange={this.handleFieldTagsChanged}
                      components={specification?.components ?? []}
                    />
                  )}
                </div>
              </>
            </div>
          )}
        </div>
      </UniversalComponent>
    )
  }
}
