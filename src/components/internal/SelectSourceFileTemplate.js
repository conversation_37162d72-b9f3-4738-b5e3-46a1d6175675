import React, { PureComponent } from 'react'
import { <PERSON>han<PERSON>Field } from '@ekuaibao/template'
import { Select } from '@hose/eui'
import { wrapper } from '../layout/FormWrapper'
@EnhanceField({
  descriptor: {
    type: 'select:sourceFileTemplate'
  },
  wrapper: wrapper()
})
export default class SelectSourceFileBox extends PureComponent {
  fnBuildItems() {
    let { tag } = this.props
    let children = []
    tag = tag || []
    tag.forEach(line => {
      let { value, label } = line
      children.push(<Select.Option value={value} data-testid={`field-SourceFileTemplate-${label}`} key={value}>{label}</Select.Option>)
    })
    return children
  }

  onChange = value => {
    let { onChange } = this.props
    let result = this.props.value
    onChange &&
      onChange({
        ...result,
        sourceFileTemplateId: value
      })
  }
  printPreview = () => {
    let { bus, value } = this.props
    if (!value) return
    bus.emit('previewPrintTemplate', value?.sourceFileTemplateId)
  }
  render() {
    let { value } = this.props
    let children = this.fnBuildItems()
    return (
      <div>
        <div className="dis-f jc-sb">
          <Select
            style={{ width: '100%' }}
            allowClear={true}
            onChange={this.onChange}
            value={value?.sourceFileTemplateId}
            getPopupContainer={triggerNode => triggerNode.parentNode}
            data-testid={`field-SourceFileTemplate-select`}
          >
            {children}
          </Select>
          <div className="ml-10 w-60 color-blue cur-p" onClick={this.printPreview}>
            {i18n.get('预览模板')}
          </div>
        </div>
      </div>
    )
  }
}
