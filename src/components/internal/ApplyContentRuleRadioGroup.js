/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/2.
 */
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { Radio } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import { wrapper } from '../layout/FormWrapper'
import styles from './ApplyContentRuleRadioGroup.module.less'
import CheckBoxFeetypeTags from '../../elements/puppet/CheckBoxSelectTypesTag'
const RadioGroup = Radio.Group
const { UniversalComponent } = api.require('@elements/StandardVersionComponent')

@EnhanceField({
  descriptor: {
    type: 'applyContentRule'
  },
  initialValue(props) {
    let { field = {} } = props
    return field?.defaultValue
  },
  wrapper: wrapper()
})
export default class ApplyContentRuleRadioGroup extends PureComponent {
  componentWillReceiveProps(np) {
    let { value, bus } = np
    if (value) {
      bus && bus.emit('only:trip:selected', value.rule)
    }
  }

  formatValue(value, tags, type = 'feeType') {
    return { rule: value, [type]: tags }
  }

  handleChange = e => {
    let val = e.target.value
    let { onChange, value, bus } = this.props
    let feeTypes = value ? value.feeTypes : []
    bus && bus.emit('only:trip:selected', val)
    onChange && onChange(this.formatValue(val, feeTypes))
  }

  handleCheckBoxChange = (tagsValue, type) => {
    let { value, onChange } = this.props
    onChange && onChange(this.formatValue(value.rule, tagsValue, type))
  }
  handleGoTravelManage = () => {
    api.open('@expansion-center:AppSettingsModal', { powerCode: 'cargo_travel_management' })
  }
  render() {
    let { field, value = {}, specification, form } = this.props
    let { tags, disabled } = field
    let vv = value ? value.rule : value
    let type = vv === 'auto' ? 'feeType' : 'tripType'

    if (specification.type === 'corpPayEnhanced') {
      type = vv === 'DETAIS_COUNT' ? 'feeType' : 'tripType'
    }

    let tagsValue = type === 'feeType' ? value.feeType : value.tripType
    const radioStyle = { display: 'block', lineHeight: '30px' }
    const powers = api.getState()['@common'].powers
    const travelCharge = powers.CARGO_TRAVEL_MANAGEMENT
    const blackList = ['manual', 'travelManage', 'travel', 'amountAndTravel', 'MANUAL', 'AUTO']
    const showTags = tags.filter(v => {
      if (form && v.relatedConfig) {
        const { ability, value } = v.relatedConfig
        const abilityValue = form.getFieldValue(ability)
        return value === abilityValue
      } else {
        return true
      }
    })
    return (
      <RadioGroup onChange={this.handleChange} value={vv} disabled={disabled} style={{ width: '100%' }}>
        {showTags.map(v => {
          return (
            <Radio value={v.value} style={radioStyle}>
              {v.label}
              {v.value === 'travelManage' && travelCharge && (
                <UniversalComponent uniqueKey={`customSpecification.pc.travelManage`}>
                  <span
                    style={{ marginLeft: '16px', color: 'var(--brand-base)', cursor: 'pointer' }}
                    onClick={this.handleGoTravelManage}
                  >
                    {i18n.get(`行程管理配置页面`)}
                  </span>
                </UniversalComponent>
              )}
              {v.value === vv && !blackList.includes(v.value) && (
                <div className={styles['apply-content']}>
                  <CheckBoxFeetypeTags
                    onChange={this.handleCheckBoxChange}
                    type={type}
                    value={tagsValue}
                    disabled={disabled}
                    className={styles['apply-content-select']}
                    parentValue={v.value}
                  />
                </div>
              )}
            </Radio>
          )
        })}
      </RadioGroup>
    )
  }
}
