.container {
  width: 100%;
}

.header {
  .title {
    font: var(--eui-font-body-b1);
    color: var(--eui-text-title);
    margin-bottom: 20px;
  }

  .subtitle {
    font: var(--eui-font-body-r1);
    color: var(--eui-text-title);
    margin-bottom: 8px;
  }

  .required {
    margin-left: 4px;
    color: var(--eui-function-danger-500);
  }
}

.modeGrid {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.customCard {
  height: 100%;
  display: flex;
  flex-flow: column;
  text-align: center;
  justify-content: center;
  align-items: center;
  
  .cardTitle {
    margin-bottom: 8px;
  }
}

.modeCard {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--eui-line-divider-default);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &:hover {
    border-color: rgba(37, 85, 255, 0.3);
  }

  &.selected {
    border-color: var(--eui-primary-pri-500);
  }
}

.cardTitle {
  font: var(--eui-font-body-b1);
  color: var(--eui-text-title);
  margin-bottom: 4px;
}

.cardFeatures {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.featureItem {
  font: var(--eui-font-body-r1);
  color: var(--eui-text-caption);
}

.icon {
  font-size: 12px;
  margin-right: 4px;
}
.successIcon {
  color: var(--eui-function-success-500);
}

.failIcon {
  color: var(--eui-function-danger-500);
}

.optionalIcon {
  color: var(--eui-function-warning-500);
}

.selectedIndicator {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background-color: var(--eui-primary-pri-500);
  clip-path: polygon(100% 100%, 100% 0, 0 100%);
  border-radius: 8px 0;
}

.selectedIcon {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 12px;
  color: var(--eui-static-white);
}

.features {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

// 响应式设计
@media (max-width: 768px) {
  .modeGrid {
    gap: 8px;
  }

  .modeCard {
    min-height: 120px;
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .modeCard {
    min-height: 110px;
    padding: 8px;
  }
}
