import { EnhanceField } from '@ekuaibao/template'
import React from 'react'
import styles from './SelectInvoiceLocation.module.less'
import { wrapper } from '../layout/FormWrapper'
import { Radio } from '@hose/eui'

const SelectInvoiceLocation = props => {
  const { value, onChange, form, field } = props
  const { relatedConfig } = field

  const handleChange = (e) => {
    onChange(e.target.value)
  }

  if (relatedConfig && form) {
    const relatedValue = form.getFieldValue(relatedConfig.ability)
    if (relatedValue !== relatedConfig.value) {
      return null
    }
  }

  const mode = form.getFieldValue('corpPayEnhanced:corporatePaymentMode')
  const isCustomMode = mode === 'CUSTOM'

  return (
    <div className={isCustomMode ? styles.customContainer : styles.container}>
      <div className={styles.title}>
        {i18n.get('选择发票字段位置')}
        {!isCustomMode && <span className={styles.required}>*</span>}
      </div>
      <Radio.Group onChange={handleChange} value={value} className={styles.radioGroup}>
        <Radio value={'FLOW'}>
          <div className={styles.title}>{i18n.get('单据表头')}</div>
          <div className={styles.desc}>{i18n.get('选中后将自动在单据模板中添加「对公发票」字段')}</div>
        </Radio>
        <Radio value={'DETAIL'}>
          <div className={styles.title}>{i18n.get('费用明细')}</div>
          <div className={styles.desc}>{i18n.get('选中后需手动在费用类型中添加「对公发票」字段')}</div>
        </Radio>
      </Radio.Group>
    </div>
  )
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'select-invoice-location'
  },
  initialValue(props) {
    const { field = {} } = props
    return field?.defaultValue
  },
  wrapper: wrapper()
})(SelectInvoiceLocation)
