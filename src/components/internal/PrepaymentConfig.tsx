import { EnhanceField } from '@ekuaibao/template'
import React, { useEffect, useState } from 'react'
import { Button, ErrorBlock, Select, Tooltip } from '@hose/eui'
import { IllustrationSmallNoBilling, IllustrationSmallNoContent, OutlinedTipsAdd, OutlinedTipsMaybe } from '@hose/eui-icons'
import styles from './PrepaymentConfig.module.less'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
import { Fetch } from '@ekuaibao/fetch'
import classNames from 'classnames'
import { app as api } from '@ekuaibao/whispered'
const { Option } = Select

const PrepaymentConfig = props => {
  const { value = {}, onChange, form, field, baseDataProperties, specification } = props
  const { relatedConfig } = field
  const [fieldOptions, setFieldOptions] = useState([])
  const [accountOptions, setAccountOptions] = useState([])
  const [isSearching, setIsSearching] = useState(false)
  const [errors, setErrors] = useState({ prepaymentField: null, prepaymentAmountField: null })

  useEffect(() => {
    const error = form.getFieldError('corpPayEnhanced:prepaymentConfig')
    const newErrors = { prepaymentField: null, prepaymentAmountField: null }
    if (error?.length) {
      if (!value?.prepaymentField) {
        newErrors.prepaymentField = i18n.get('核销预付款字段不可为空')
      }
      if (!value?.prepaymentAmountField) {
        newErrors.prepaymentAmountField = i18n.get('台账统计字段不可为空')
      }
    }
    setErrors(newErrors)
  }, [value, form])

  useEffect(() => {
    // 业务对象多选字段数据
    const multiSelectFields = getMultiSelectFields(baseDataProperties)
    setFieldOptions(multiSelectFields)
    // 初始化核销相关字段和金额字段
    const initializePrepaymentData = async () => {
      if (value.prepaymentField) {
        const selectedField = multiSelectFields.find(option => option.id === value.prepaymentField)
        if (selectedField) {
          const data = await getPrepaymentAmountFieldList(selectedField.dataLinkId, value.prepaymentField)
          setAccountOptions(data)

          if (value.prepaymentAmountField && !data.some(item => item.id === value.prepaymentAmountField)) {
            onChange({ ...value, prepaymentAmountField: null })
          }
        } else {
          onChange({ prepaymentField: null, prepaymentAmountField: null })
        }
      }
    }

    initializePrepaymentData()
  }, [])

  useEffect(() => {
    if (!value.prepaymentField) {
      setAccountOptions([])
    }
  }, [value.prepaymentField])

  const getMultiSelectFields = baseDataProperties => {
    return baseDataProperties
      .filter(
        item =>
          item.active &&
          !item.ability &&
          item.dataType.type === 'list' &&
          item.dataType.elemType.type === 'complex' &&
          item.dataType?.elemType?.fields?.some(
            f => f?.name === 'dataLinkId' && f?.dataType?.entity?.startsWith('datalink.DataLinkEntity')
          ) && !['travelPlanning', 'u_行程规划'].includes(item.name)
      )
      .map(item => ({
        id: item.name,
        name: item.label,
        dataLinkId: item.dataType?.elemType?.fields
          ?.find(f => f?.name === 'dataLinkId')
          ?.dataType?.entity?.split('.')[2]
      }))
  }

  const getPrepaymentAmountFieldList = async (dataLinkId, fieldId) => {
    const res = await Fetch.GET(
      `/api/v2/datalink/ledger/getApportionRule/$${dataLinkId}?specificationId=${specification.id}&field=${fieldId}`
    )
    const data = res.items
      .filter(item => item.ledgerConfig.sumFieldType === 'MONEY')
      .map(item => ({
        id: item.ledgerConfig.propertyName,
        name: item.apportionConfig.label
      }))
    return data
  }

  const handlePrepaymentFieldChange = async (fieldId, option) => {
    const newValue = { prepaymentField: fieldId, prepaymentAmountField: null }
    onChange(newValue)
    const data = await getPrepaymentAmountFieldList(option.dataLinkId, fieldId)
    setAccountOptions(data)
  }

  const handlePrepaymentAmountFieldChange = fieldId => {
    const newValue = { ...value, prepaymentAmountField: fieldId }
    onChange(newValue)
  }

  const addGlobalField = async () => {
    console.log('addGlobalField')
    await api.open('@field-setting:CustomFieldModal', {
      view: 'CreateFieldView',
      fieldType: 'DataLinkWrapEdit'
    })
    const { data } = await api.dataLoader('@common.globalFields').load()
    const multiSelectFields = getMultiSelectFields(data)
    setFieldOptions(multiSelectFields)

    const newField = multiSelectFields[multiSelectFields.length - 1]
    handlePrepaymentFieldChange(newField.id, newField)
  }

  if (relatedConfig && form) {
    const relatedValue = form.getFieldValue(relatedConfig.ability)
    if (relatedValue !== relatedConfig.value) {
      return null
    }
  }

  const mode = form.getFieldValue('corpPayEnhanced:corporatePaymentMode')
  const isCustomMode = mode === 'CUSTOM'

  const renderSearchWithNoResult = () => {
    return <ErrorBlock image={<IllustrationSmallNoContent />} title={i18n.get('暂无搜索结果')} />
  }

  const renderAddFieldWithNoField = () => {
    return (
      <ErrorBlock
        image={<IllustrationSmallNoBilling />}
        title={i18n.get('暂无字段')}
        secondaryButton={
          <Button
            category="secondary"
            theme="highlight"
            size="middle"
            icon={<OutlinedTipsAdd />}
            onClick={addGlobalField}
          >
            {i18n.get('新建字段')}
          </Button>
        }
      />
    )
  }

  const renderAddFieldButton = () => {
    return (
      <Button
        category="text"
        theme="highlight"
        icon={<OutlinedTipsAdd />}
        onClick={addGlobalField}
        className={styles.addFieldButton}
      >
        {i18n.get('新建字段')}
      </Button>
    )
  }

  const renderNoAmountField = () => {
    return (
      <ErrorBlock
        image={<IllustrationSmallNoBilling fontSize={100} />}
        title={i18n.get('暂无字段')}
        description={i18n.get('请前往业务对象中创建')}
      />
    )
  }

  return (
    <>
      {!isCustomMode && (
        <div className={styles.title}>
          {i18n.get('选择核销相关字段')}
          <span className={styles.required}>*</span>
        </div>
      )}
      <div className={classNames(styles.configContainer, isCustomMode && styles.customConfigContainer)}>
        <div className={styles.configItem}>
          <div className={styles.label}>
            {i18n.get('请指定核销预付字段')}
            <span className={styles.desc}>({i18n.get('仅可选业务对象多选字段')})</span>
          </div>
          <Select
            className={styles.select}
            placeholder={i18n.get('请选择字段')}
            value={value.prepaymentField}
            onChange={handlePrepaymentFieldChange}
            status={errors.prepaymentField ? 'error' : ''}
            showSearch
            onSearch={value => setIsSearching(!!value)}
            filterOption={(input, option) => {
              const field = fieldOptions.find(f => f.id === option.value)
              return field ? field.name.toLowerCase().includes(input.toLowerCase()) : false
            }}
            notFoundContent={fieldOptions.length === 0 ? renderAddFieldWithNoField() : renderSearchWithNoResult()}
            dropdownRender={menu => (
              <>
                {menu}
                {fieldOptions.length > 0 && !isSearching && renderAddFieldButton()}
              </>
            )}
          >
            {fieldOptions.map(option => (
              <Option key={option.id} value={option.id} dataLinkId={option.dataLinkId}>
                {option.name}
              </Option>
            ))}
          </Select>
          {errors.prepaymentField && <div className={styles.errorMessage}>{errors.prepaymentField}</div>}
        </div>

        <div className={styles.configItem}>
          <div className={styles.label}>
            {i18n.get('请指定本次核销金额进入的台账统计字段')}
            <Tooltip title={i18n.get('请先保存单据模板，新增到业务对象台账统计处配置一个手动分摊的统计规则')}>
              <OutlinedTipsMaybe className={styles.tipIcon} />
            </Tooltip>
          </div>
          <Select
            className={styles.select}
            placeholder={i18n.get('请选择字段')}
            value={value.prepaymentAmountField}
            onChange={handlePrepaymentAmountFieldChange}
            status={errors.prepaymentAmountField ? 'error' : ''}
            showSearch
            filterOption={(input, option) => {
              const field = accountOptions.find(f => f.id === option.value)
              return field ? field.name.toLowerCase().includes(input.toLowerCase()) : false
            }}
            notFoundContent={accountOptions?.length === 0 ? renderNoAmountField() : renderSearchWithNoResult()}
          >
            {accountOptions.map(option => (
              <Option key={option.id} value={option.id}>
                {option.name}
              </Option>
            ))}
          </Select>
          {errors.prepaymentAmountField && <div className={styles.errorMessage}>{errors.prepaymentAmountField}</div>}
        </div>
      </div>
    </>
  )
}

// @ts-ignore
export default EnhanceField({
  descriptor: {
    type: 'prepayment-config'
  },
  validator: (field, props) => (rule, value, callback) => {
    const usePrepayment = props?.form?.getFieldValue('corpPayEnhanced:usePrepayment')
    if (!usePrepayment) {
      return callback()
    }
    if (value?.prepaymentField && value?.prepaymentAmountField) {
      return callback()
    }
    callback(' ')
  },
  wrapper: wrapper()
})(
  // @ts-ignore
  EnhanceConnect(state => {
    return {
      baseDataProperties: state['@common'].globalFields.data
    }
  })(PrepaymentConfig)
)
