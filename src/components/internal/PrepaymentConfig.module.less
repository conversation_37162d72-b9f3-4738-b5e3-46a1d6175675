.title {
  font: var(--eui-font-body-r1);
  color: var(--eui-text-title);
  margin-bottom: 8px;
}

.required {
  margin-left: 4px;
  color: var(--eui-function-danger-500);
}

.customConfigContainer {
  margin-left: 24px;
}

.configContainer {
  background: var(--eui-bg-float-base);
  border-radius: 6px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 380px;
  margin-top: 8px;
  margin-bottom: 16px;
  
  .configItem {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .label {
      font: var(--eui-font-body-r1);
      color: var(--eui-text-title);
      display: flex;
      align-items: center;
      gap: 4px;
      
      .tipIcon {
        font-size: 16px;
        color: var(--eui-icon-n2);
      }

      .desc {
        font: var(--eui-font-note-r2);
        color: var(--eui-text-caption);
      }
    }
  }
}
.addFieldButton {
  margin: 4px 8px;
}
.errorMessage {
  font: var(--eui-font-body-r1);
  color: var(--eui-function-danger-500);
}