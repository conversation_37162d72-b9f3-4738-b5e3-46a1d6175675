import React, { PureComponent } from 'react'
import { <PERSON>han<PERSON><PERSON>ield } from '@ekuaibao/template'
import styles from './Describe.module.less'
import { Popover } from '@hose/eui'
import { OutlinedTipsMaybe } from '@hose/eui-icons'
@EnhanceField({
  descriptor: {
    type: 'describe'
  }
})
export default class Describe extends PureComponent {
  render() {
    let { field } = this.props
    const { value, config = {} } = field
    const { popover, optional = true, style = {} } = config
    return (
      <div className={styles.describe} style={style}>
        {value}
        {!optional && <span className={styles.required}>*</span>}
        {!!popover && (
          <Popover placement="bottomLeft" title={popover} style={{ width: '480px' }}>
            <OutlinedTipsMaybe fontSize={14} style={{ marginLeft: 4 }} />
          </Popover>
        )}
      </div>
    )
  }
}
