import { DriveStep } from 'driver.js'
import { enableHidingFinishedBills } from '../../lib/featbit'
import useLayoutDriver from './useLayoutDriver'
import { omit } from 'lodash'

const DONE_BILLS_ONBOARDING_STORAGE_KEY = 'hose-bills-finished-bills-onboarding-shown'

const ifNeedShowingOnBoardingOfFinishedBills = () => {
  const storageValue = localStorage.getItem(DONE_BILLS_ONBOARDING_STORAGE_KEY)
  if (storageValue) {
    return false
  }
  return !window.isInWeComISV && enableHidingFinishedBills()
}

export default function driveDoneBillsOnBoarding(el: HTMLElement, stepCogfig: DriveStep = {}) {
  if (ifNeedShowingOnBoardingOfFinishedBills()) {
    const refreshDriver = () => {
      driverObj.refresh()
    }

    const driverObj = useLayoutDriver({
      driverSteps: [
        {
          element: el,
          popover: {
            title: i18n.get('已完成的单据无需确认通过'),
            description: i18n.get('已完成单据与其他单据统一展示，支持筛选和查询'),
            nextBtnText: i18n.get('我知道了'),
            showButtons: ['next'],
            onNextClick: () => {
              localStorage.setItem(DONE_BILLS_ONBOARDING_STORAGE_KEY, 'true')
              window.removeEventListener('resize', refreshDriver)
              driverObj.destroy()
            },
            ...stepCogfig?.popover,
          },
          ...omit(stepCogfig, 'popover'),
        }
      ]
    })

    window.addEventListener('resize', refreshDriver)
  }
}