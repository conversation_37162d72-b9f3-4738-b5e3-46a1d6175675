import loadable, { LoadableComponent } from '@loadable/component'

import { ENUM_TYPES } from './consts'
import { includes } from 'lodash'

function loadableWithDescriptor<T>(fn: () => Promise<any>, descriptor: any): LoadableComponent<T> {
  const oo: any = loadable(fn)
  oo.descriptor = descriptor
  return oo
}

/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:43.
 **************************************************/

const Attachment = loadableWithDescriptor(() => import('./dynamic/Attachment'), {
  type: 'attachments'
})
const AIAttachment = loadableWithDescriptor(() => import('./dynamic/AIAttachment'), {
  type: 'aiAttachments'
})
const DateCell = loadableWithDescriptor(() => import('./dynamic/Date'), {
  type: 'date'
})
const DateMonth = loadableWithDescriptor(() => import('./dynamic/Month'), {
  type: 'date-month'
})
const Details = loadableWithDescriptor(() => import('./dynamic/Details'), {
  test({ type = '' }) {
    return type === 'requisitionDetails' || type === 'details'
  }
})
const Money = loadableWithDescriptor(() => import('./dynamic/Money'), {
  type: 'money'
})
const Number = loadableWithDescriptor(() => import('./dynamic/Number'), {
  type: 'number'
})
const Ref = loadableWithDescriptor(() => import('./dynamic/Ref'), {
  test({ type = '' }) {
    return (
      type.startsWith('ref') &&
      type !== 'ref:organization.Staff' &&
      type !== 'ref:organization.StaffSimple' &&
      type !== 'ref:organization.Department' &&
      type !== 'ref:basedata.Enum.currency' &&
      !includes(ENUM_TYPES, type)
    )
  }
})
const RefDepartment = loadableWithDescriptor(() => import('./dynamic/RefDepartment'), {
  type: 'ref:organization.Department'
})
const RefStaff = loadableWithDescriptor(() => import('./dynamic/RefStaff'), {
  type: 'ref:organization.Staff'
})
const RefStaffSimple = loadableWithDescriptor(() => import('./dynamic/RefStaffSimple'), {
  type: 'ref:organization.StaffSimple'
})
const City = loadableWithDescriptor(() => import('./dynamic/CityPicker'), {
  type: 'city'
})
const Text = loadableWithDescriptor(() => import('./dynamic/Text'), {
  type: 'text'
})
const TextArea = loadableWithDescriptor(() => import('./dynamic/TextArea'), {
  type: 'textarea'
})
const Phone = loadableWithDescriptor(() => import('./dynamic/Phone'), {
  type: 'phone'
})
const PayeeInfo = loadableWithDescriptor(() => import('./dynamic/PayeeInfo'), {
  type: 'payeeInfo'
})
const MutilPayeeInfo = loadableWithDescriptor(() => import('./dynamic/MutilPayeeInfo'), {
  type: 'list:ref:pay.PayeeInfo'
})
const Separator = loadableWithDescriptor(() => import('./dynamic/Separator'), {
  type: 'separator'
})
const Annotation = loadableWithDescriptor(() => import('./dynamic/Annotation'), {
  type: 'annotation'
})
const Select = loadableWithDescriptor(() => import('./dynamic/Select'), {
  type: 'select'
})
const Cascader = loadableWithDescriptor(() => import('./dynamic/Cascader'), {
  type: 'cascader'
})
const SelectSearch = loadableWithDescriptor(() => import('./dynamic/SelectSearch'), {
  type: 'select_search'
})
const DateRange = loadableWithDescriptor(() => import('./dynamic/DateRange'), {
  type: 'dateRange'
})
const SelectPrintTemplate = loadableWithDescriptor(() => import('./internal/SelectPrintTemplate'), {
  type: 'select:printTemplate'
})
const SelectSourceFileTemplate = loadableWithDescriptor(() => import('./internal/SelectSourceFileTemplate'), {
  type: 'select:sourceFileTemplate'
})
const Description = loadableWithDescriptor(() => import('./dynamic/Description'), {
  type: 'description'
})
const LinkRequisitionInfo = loadableWithDescriptor(() => import('./dynamic/LinkRequisitionInfo'), {
  type: 'linkRequisitionInfo'
})
const ExpenseLink = loadableWithDescriptor(() => import('./dynamic/ExpenseLink'), {
  type: 'expenseLink'
})
const ExpenseLinkList = loadableWithDescriptor(() => import('./dynamic/ExpenseLinkList'), {
  type: 'expenseLinks'
})
const RefEnum = loadableWithDescriptor(() => import('./dynamic/RefEnum'), {
  test({ type = '' }) {
    return includes(ENUM_TYPES, type)
  }
})
const Switcher = loadableWithDescriptor(() => import('./dynamic/Switcher'), {
  type: 'switcher'
})
const Apportion = loadableWithDescriptor(() => import('./dynamic/Apportions'), {
  type: 'apportions'
})
const DataLink = loadableWithDescriptor(() => import('./dynamic/DataLink'), {
  type: 'dataLink'
})
const InvoiceSelect = loadableWithDescriptor(() => import('./dynamic/InvoiceSelect'), {
  type: 'invoice'
})

const Trips = loadableWithDescriptor(() => import('./dynamic/Trips'), { type: 'trips' })
const VPhoto = loadableWithDescriptor(() => import('./dynamic/VPhoto'), {
  name: 'vphoto_order'
})

const Time = loadableWithDescriptor(() => import('./dynamic/Time'), {
  type: 'time'
})
const MutilStaff = loadableWithDescriptor(() => import('./dynamic/MutilStaff'), {
  type: 'list:ref:organization.Staff'
})
const EnterWay = loadableWithDescriptor(() => import('./dynamic/EnterWay'), {
  type: 'enter-way'
})
const DataLinkEdit = loadableWithDescriptor(() => import('./dynamic/dataLinkEdit/DataLinkEdit'), {
  test(field) {
    const { type, referenceData, name } = field
    return (type === 'dataLinkEdits' && referenceData.type !== 'TRIP') || name === 'budgetAdjustDetails'
  }
})
const DataLinkList = loadableWithDescriptor(() => import('./dynamic/DataLinkList'), {
  type: 'dataLinks'
})
const RelatedDetails = loadableWithDescriptor(() => import('./dynamic/RelatedDetails'), {
  type: 'linkDetailEntities'
})
const TripDataLink = loadableWithDescriptor(() => import('./dynamic/dataLinkEdit/TripDataLink'), {
  test(field) {
    const { type, referenceData } = field
    return type === 'dataLinkEdits' && referenceData.type === 'TRIP'
  }
})
const sourceType = loadableWithDescriptor(() => import('./dynamic/SourceType'), {
  name: 'sourceType'
})

const payment = loadableWithDescriptor(() => import('./dynamic/Payment'), {
  name: 'payment'
})
const supplierTilte = loadableWithDescriptor(() => import('./dynamic/SupplierTitle'), {
  type: 'supplierTitle'
})

const travelList = loadableWithDescriptor(() => import('./dynamic/TravelList/TravelList'), {
  type: 'travel'
})

const BankSelect = loadableWithDescriptor(() => import('./dynamic/BankSelect'), {
  type: 'bankSelect'
})
const StaffSelectTags = loadableWithDescriptor(() => import('./dynamic/StaffSelectTags'), {
  type: 'staff-select-tags'
})
const LoanRuleTemplate = loadableWithDescriptor(() => import('./dynamic/LoanRuleTemplate'), {
  type: 'loan-rule-template'
})
const LoanRuleType = loadableWithDescriptor(() => import('./dynamic/LoanRuleType'), {
  type: 'loan-rule-type'
})
const LoanRuleAmount = loadableWithDescriptor(() => import('./dynamic/LoanRuleAmount'), {
  type: 'loan-rule-amount'
})
const MutilDimensionList = loadableWithDescriptor(() => import('./dynamic/MutilDimensionList'), {
  test({ type = '' }) {
    return type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select')
  }
})
const MutilDimensionListSearch = loadableWithDescriptor(() => import('./dynamic/MutilDimensionListSearch'), {
  test({ type = '' }) {
    return type.startsWith('list:ref:basedata.Dimension') && type.endsWith(':select_search')
  }
})
const RadioGroupSupplier = loadableWithDescriptor(() => import('./dynamic/RadioGroupSupplier'), {
  type: 'radio-group-supplier'
})
const SplitCalculation = loadableWithDescriptor(() => import('./dynamic/SplitCalculation'), {
  type: 'splitCalculation'
})
const Unknown = loadableWithDescriptor(() => import('./internal/Unknown'), { type: 'unknown' })
const InterConnection = loadableWithDescriptor(() => import('./dynamic/interConnection/index'), {
  test(field) {
    const { type } = field
    return type === 'engineConnect'
  }
})
const SupplierSelect = loadableWithDescriptor(() => import('./unify_invoice/Supplier'), {
  type: 'select:supplier'
})

const CheckboxGroup = loadableWithDescriptor(() => import('./unify_invoice/CheckboxGroup'), {
  type: 'checkbox:group'
})
const AutoSplitCheckbox = loadableWithDescriptor(() => import('./unify_invoice/AutoSplitCheckbox'), {
  type: 'autoSplitCheckbox'
})
const RadioGroup = loadableWithDescriptor(() => import('./unify_invoice/RadioGroup'), {
  type: 'radio:group'
})
const SplitRule = loadableWithDescriptor(() => import('./unify_invoice/SplitRule'), {
  type: 'split:rule'
})
const CorpPayerInfo = loadableWithDescriptor(() => import('./unify_invoice/CorpPayerInfo'), {
  type: 'corp:payer:info'
})
const CascaderGroup = loadableWithDescriptor(() => import('./unify_invoice/CascaderGroup'), {
  type: 'cascader:group'
})
const SplitRuleCheckingImport = loadableWithDescriptor(() => import('./unify_invoice/SplitRuleCheckingImport'), {
  type: 'split:rule:checkingImport'
})
const Subsidy = loadableWithDescriptor(() => import('./dynamic/Subsidy'), {
  type: 'subsidy'
})
// 场景群插件
const Association = loadableWithDescriptor(() => import('./dynamic/Association'), {
  name: 'isOpenAssociation'
})
const BlockUI = loadableWithDescriptor(() => import('./dynamic/BlockUI'), {
  type: 'engineBlockUI'
})
const HABWidget = loadableWithDescriptor(() => import('./dynamic/HABWidget'), {
  type: 'widget'
})
const SelectCodeRule = loadableWithDescriptor(() => import('./internal/SelectCodeRule'), {
  type: 'select:code:rule'
})
const Currency = loadableWithDescriptor(() => import('./dynamic/Currency'), {
  type: 'ref:basedata.Enum.currency'
})
const CheckingBillForm = loadableWithDescriptor(() => import('./dynamic/CheckingBillForm'), {
  name: 'checkingBillForm'
})

const DataLinkStaff = loadableWithDescriptor(() => import('./dynamic/DataLinkStaff'), {
  type: 'list:ref:dataLink.Staff'
})

const GroupTitle = loadableWithDescriptor(() => import('./dynamic/GroupTitle'), {
  type: 'group'
})

const OrderConfigRules = loadableWithDescriptor(() => import('./dynamic/OrderConfigRules'), {
  type: 'order-config-rules'
})

const OrderConfigSelect = loadableWithDescriptor(() => import('./dynamic/OrderConfigSelect'), {
  type: 'order-config-select'
})

const FlowLinks = loadableWithDescriptor(() => import('./dynamic/FlowLinks'), {
  type: 'flowLinks'
})

const CheckCitySelect = loadableWithDescriptor(() => import('./dynamic/CheckCitySelect'), {
  type: 'check-city-select'
})
const Amortization = loadableWithDescriptor(() => import('./dynamic/Amortization'), {
  type: 'amortizes'
})
const ReconcileResultForm = loadableWithDescriptor(() => import('./dynamic/ReconcileResultForm'), {
  name: 'reconcileResultForm'
})
const CorporateExpenseCard = loadableWithDescriptor(() => import('./dynamic/EBusinessCard'), {
  type: 'corporateExpenseCard'
})
const CorporateInvoice = loadableWithDescriptor(() => import('./dynamic/CorporateInvoice/index'), {
  type: 'corporateInvoice'
})
export const editable = [
  Currency,
  Attachment,
  DateCell,
  Details,
  Money,
  Number,
  Ref,
  RefDepartment,
  RefStaff,
  RefStaffSimple,
  City,
  Text,
  TextArea,
  Phone,
  PayeeInfo,
  MutilPayeeInfo,
  Separator,
  Annotation,
  Select,
  Cascader,
  DateRange,
  SelectPrintTemplate,
  SelectSourceFileTemplate,
  Description,
  LinkRequisitionInfo,
  ExpenseLink,
  ExpenseLinkList,
  RefEnum,
  Switcher,
  Apportion,
  Association,
  SelectSearch,
  DataLink,
  InvoiceSelect,
  Trips,
  VPhoto,
  Time,
  MutilStaff,
  EnterWay,
  DataLinkEdit,
  DataLinkList,
  RelatedDetails,
  TripDataLink,
  BankSelect,
  StaffSelectTags,
  LoanRuleTemplate,
  LoanRuleType,
  LoanRuleAmount,
  MutilDimensionList,
  MutilDimensionListSearch,
  RadioGroupSupplier,
  SplitCalculation,
  InterConnection,
  Unknown,
  payment,
  sourceType,
  supplierTilte,
  SupplierSelect,
  CheckboxGroup,
  RadioGroup,
  AutoSplitCheckbox,
  SplitRule,
  CorpPayerInfo,
  DateMonth,
  Subsidy,
  SelectCodeRule,
  CheckingBillForm,
  BlockUI,
  DataLinkStaff,
  travelList,
  GroupTitle,
  OrderConfigSelect,
  OrderConfigRules,
  FlowLinks,
  CheckCitySelect,
  Amortization,
  CascaderGroup,
  SplitRuleCheckingImport,
  ReconcileResultForm,
  CorporateExpenseCard,
  HABWidget,
  AIAttachment,
  CorporateInvoice
]
