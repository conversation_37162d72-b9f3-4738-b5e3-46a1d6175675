import React, { useState, useEffect, useRef } from 'react'
import { Tooltip } from '@hose/eui'
import './index.module.less'

export default function TooltipEllipsis(props) {
  const childrenRef = useRef(null)
  const { children, title, width, placement, ...other } = props

  const [showTooltip, setShowTooltip] = useState(false)

  useEffect(() => {
    // 使用 setTimeout 确保 DOM 已经渲染完成
    const timer = setTimeout(() => {
      validShowTooltip()
    }, 0)
    return () => clearTimeout(timer)
  }, [title, width, children])

  const validShowTooltip = () => {
    if (!childrenRef.current) {
      return
    }
    const { scrollWidth, clientWidth } = childrenRef.current;
    setShowTooltip(scrollWidth > clientWidth)
  }

  const refChildren = (ref: any) => {
    childrenRef.current = ref;
  }

  const renderChildren = () => {
    return (
      React.cloneElement(
        <span className="ellipsis-children">{children}</span>, {
          ref: refChildren
        }
      )
    )
  }

  if (showTooltip) {
    return (
      <Tooltip
        title={title}
        trigger="hover"
        placement={placement || "top"}
        {...other}
      >
        { renderChildren() }
      </Tooltip>
    )
  }
  return renderChildren()
}