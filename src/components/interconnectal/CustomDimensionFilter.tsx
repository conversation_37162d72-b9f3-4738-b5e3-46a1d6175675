import './CustomDimensionFilter.less'
import React, { ReactElement, Component, useEffect, memo, useMemo } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { Input, Cascader, Select, DatePicker, message, TreeSelect, Tooltip } from '@hose/eui'
import { Col, Row } from 'antd'
import { cloneDeep, memoize, get, debounce } from 'lodash'
import { EKBSelect } from '../../ekb-components/index'
import { OutlinedTipsAdd, OutlinedTipsReduce } from '@hose/eui-icons'
import {
  CustomDimensionModel,
  FilterInterface,
  CustomDimensionNodeModel,
  DepartmentTree,
  GlobalFieldModel,
  LeftTags
} from '../types'
import { FieldInterface } from '../layout/types'
import * as utils from '../utils/utilFunctionsParams'

import { T } from '@ekuaibao/i18n'
import moment from 'moment'

const commonActions = api.invokeServiceAsLazyValue('@common:import:action')
const customDimensionActions = api.invokeServiceAsLazyValue('@custom-dimension:import:actions')

const dimensionPrefix = 'basedata.Dimension.'
const enumeratePrefix = 'basedata.Enum.'
const globalFieldPrefix = 'feeTypeForm.'
const typesIsShowInput = ['LEDGER', 'PLAN', 'number', 'text', 'money']
const numberArr = ['LEDGER', 'PLAN', 'number', 'money']
const optionBlackList = ['reviewStatus', 'invoiceBagCode']
const { Option } = Select
const { RangePicker } = DatePicker

const formatRightDateValue = (dates): any => {
  if (Array.isArray(dates) && dates?.length == 2) {
    const sortedDates = dates.sort((a, b) => moment(a).isBefore(b) ? -1 : 1)
    return [moment(sortedDates[0], 'YYYY-MM-DD'), moment(sortedDates[1], 'YYYY-MM-DD')]
  }
  return undefined;
}

export interface CustomDimensionFilterProps {
  field: FieldInterface[]
  value: FilterInterface[]
  onChange?(value: FilterInterface[]): void
  globalFieldsList: GlobalFieldModel[]
  globalFields: { [P: string]: GlobalFieldModel }
  records: { [P: string]: CustomDimensionModel }
  departmentTree: DepartmentTree[]
  dimensions: { [K: string]: CustomDimensionNodeModel[] }
  dimensionMap: { [K: string]: CustomDimensionNodeModel }
  hidden?: boolean
  enumerateMap: { [key: string]: any[] }
  staffsVisibility: any[]
  deleteRowDisabled?: boolean
}
export interface CustomDimensionFilterState {
  value: FilterInterface[]
  leftTags: LeftTags[]
  billDetailField: any[]
}


@EnhanceConnect(
  (state: any): Partial<CustomDimensionFilterProps> => ({
    globalFieldsList: state['@common'].globalFields.data || [],
    globalFields: state['@common'].globalFields.baseDataPropertiesMap || {},
    records: (state['@custom-dimension'].customRecord || []).reduce(
      (map: any, item: any) => ((map[item.name] = item) || 1) && map,
      {}
    ),
    departmentTree: state['@common'].department.data || [],
    dimensions: state['@custom-dimension'].customDimensions || {},
    dimensionMap: state['@custom-dimension'].customDimensionMap || {},
    enumerateMap: state['@custom-dimension'].enumerateMap,
    staffsVisibility: state['@common'].staffsVisibility
  })
)
export default class CustomDimensionFilter extends Component<CustomDimensionFilterProps, CustomDimensionFilterState> {
  static defaultProps: CustomDimensionFilterProps
  labelMapType: { [P: string]: string } = {}
  typesMap: any = {}
  source: string = ''
  enumerateMapCache = {}
  timer: any
  constructor(props: CustomDimensionFilterProps) {
    super(props)
    this.source = props.field?.source
    this.typesMap = this.source === 'apportionRule' ? utils.typesMap2 : utils.typesMap1
    const leftTags = this.setLeftSelectFields()
    const value = cloneDeep(this.initProps(props))
    this.state = {
      leftTags: leftTags || [],
      value: value,
      billDetailField: [] // 预设规则右值
    }
  }

  componentDidMount(): void {
    api.dispatch(customDimensionActions.value.getCustomRecord())
    api.dispatch(commonActions.value.getBaseDataProperties())
    api.dataLoader('@common.department').load()
    api.dataLoader('@common.staffsVisibility').load()
    this.checkLoadDimensions(this.props)
    if (this.source === 'apportionRule') {
      this.filterBillDetailField()
    }
  }

  componentWillReceiveProps(nextProps: Readonly<CustomDimensionFilterProps>, nextContext: any): void {
    this.checkLoadDimensions(nextProps)
  }

  private initProps = props => {
    const { value = [] } = props
    value.forEach(item => {
      const type = item.type
      if (Object.keys(this.typesMap).includes(type)) {
        item.middleTags = this.typesMap[type]
      } else {
        item.middleTags = this.typesMap['department']
      }
      if (typesIsShowInput.includes(type)) {
        item.isShowInput = true
      } else {
        item.isShowInput = false
      }
      // 兼容原先三项条件的老数据
      if (this.source === 'apportionRule' && item?.right && !item?.right?.value) {
        let right: any = {}
        right.type = 'CONSTANT'
        right.value = item.right
        item.right = right
      }
    })
    return value
  }

  private left(left: string) {
    if (left && left.indexOf(globalFieldPrefix) === 0) {
      return left.substr(globalFieldPrefix.length)
    }
    return left
  }

  private record(field: GlobalFieldModel, prefix) {
    return (
      (field &&
        field.dataType &&
        field.dataType.entity &&
        field.dataType.entity.indexOf(prefix) === 0 &&
        field.dataType.entity.substr(prefix.length)) ||
      ''
    )
  }

  private dimensionName = (name, prefix) => {
    return (name.indexOf(prefix) === 0 && name.substr(prefix.length)) || ''
  }

  private newFilter(): FilterInterface {
    const value = {
      left: undefined,
      operator: undefined,
      right: this.source === 'apportionRule' ? { type: '', value: [] } : [],
      includeChildren: false,
      fromWhere: '',
      isShowInput: false,
      middleTags: []
    }
    return value
  }

  private getAllDimension = memoize(async (id: string) => {
    try {
      await api.dispatch(customDimensionActions.value.getAllDimension({ id }))
    } finally {
      this.getAllDimension.cache.delete(id)
    }
  })

  private getAllEnumerate = memoize(async (id: string) => {
    try {
      await api.dispatch(customDimensionActions.value.getEnumerateDimensionValueById(id))
    } finally {
      this.getAllDimension.cache.delete(id)
    }
  })

  private checkLoadDimensions(props: CustomDimensionFilterProps) {
    if (!Array.isArray(props.value)) {
      return
    }
    props.value
      .filter(item => !['number', 'money', 'text', 'department'].includes(item.type))
      .forEach(item => {
        const left = this.left(typeof item.left === 'string' ? item.left : item.left?.[0])
        const { leftTags } = this.state
        const tag = leftTags.find(item => item.value === left)
        const source = get(tag, 'source')
        let recordName = ''
        let isEnumerate = false
        if (source === 'dataLink') {
          const entity = get(tag, 'entity')
          if (entity?.includes(enumeratePrefix)) {
            isEnumerate = true
            recordName = this.dimensionName(entity, enumeratePrefix)
          } else {
            recordName = this.dimensionName(entity, dimensionPrefix)
          }
        } else {
          const field = props.globalFields[this.left(item.left)]
          recordName = this.record(field, dimensionPrefix)
        }
        item.isShowInput = false
        if (isEnumerate) {
          !props.enumerateMap[recordName] && this.getAllEnumerate(recordName)
        } else {
          const record = props.records[recordName]
          if (record && !props.dimensions[record.id]) {
            this.getAllDimension(record.id)
          }
        }
      })
  }

  private handleChange = (index: number, field: keyof FilterInterface) => (input: any) => {
    const value: FilterInterface[] =
      Array.isArray(this.state.value) && this.state.value.length > 0 ? this.state.value.slice(0) : [this.newFilter()]
    if (this.source !== 'apportionRule') {
      // field为right时，分摊预设规则走这里会改变value中right结构
      value[index] = {
        ...value[index],
        [field]: input
      }
    }
    if (field === 'operator') {
      value[index].operator = input
      value[index].includeChildren = value[index].type === 'text' ? false : input === 'include'
    }
    if (field === 'right') {
      if (this.source === 'apportionRule') {
        value[index].right.value = Array.isArray(input) ? input : [input]
        // 后端要求，如果右值是BILL_DETAIL_FIELD类型，要传字符串
        if (value[index].right.type === 'BILL_DETAIL_FIELD') {
          value[index].right.value = value[index].right.value[0]
        }
      } else {
        value[index].right = Array.isArray(input) ? input : [input]
      }
    }
    if (field === 'rightType') {
      // 切换右值类型时清空右值选项
      value[index].right.type = input
      value[index].right.value = []
    }
    if (field === 'left') {
      this.getMiddleTags(input, index, value)
    }
    const newValue = cloneDeep(value)
    this.setState({ value: newValue })
    this.props.onChange && this.props.onChange(value)
  }

  private getMiddleTags = (input: string, index: number, value: FilterInterface[]) => {
    value[index].left = input
    value[index].right = this.source === 'apportionRule' ? { type: '', value: [] } : []
    value[index].operator = undefined
    // @ts-ignore
    const str = this.source === 'apportionRule' ? input : input && input.length > 1 ? input.join('') : input?.[0]
    if (this.labelMapType[str] && this.labelMapType[str].indexOf(dimensionPrefix) > -1) {
      value[index].type = null
    } else {
      value[index].type = this.labelMapType[str]
    }
    const type = value[index].type
    if (Object.keys(this.typesMap).includes(type)) {
      value[index].middleTags = this.typesMap[type]
    } else if (type?.includes(enumeratePrefix)) {
      value[index].middleTags = this.typesMap['staff']
      value[index].entity = this.labelMapType[str]
      value[index].type = null // 后台需要null
    } else {
      value[index].middleTags = this.typesMap['department']
    }
    if (typesIsShowInput.includes(type)) {
      value[index].isShowInput = true
    } else {
      value[index].isShowInput = false
    }
  }

  private handleAdd = (index: number) => () => {
    const value: FilterInterface[] = Array.isArray(this.state.value) ? this.state.value.slice() : []
    if (value.length === 0) {
      value.push(this.newFilter())
      value.push(this.newFilter())
    } else {
      value.splice(index + 1, 0, this.newFilter())
    }
    const newValue = cloneDeep(value)
    this.setState({ value: newValue })
    this.props.onChange && this.props.onChange(value)
  }

  private handleDelete = (index: number) => () => {
    const value = this.state.value
    if (!Array.isArray(value)) {
      return
    }
    const arr = value.slice(0, index).concat(value.slice(index + 1))
    const newValue = cloneDeep(arr)
    if (this.props.deleteRowDisabled && arr.length == 0) {
      message.error(i18n.get('至少要保留一个条件组'))
      return
    }
    this.setState({ value: newValue })
    this.props.onChange && this.props.onChange(arr)
  }

  private filterTreeNode = (input: string, node: ReactElement<{ value: string; title: string }>) => {
    if (!input) {
      return true
    }
    if (node.props.value === input) {
      return true
    }

    return new RegExp(input, 'i').test(node.props.title)
  }

  private renderTree = (items: CustomDimensionNodeModel[]) => {
    return Array.isArray(items)
      ? items.map(item => (
        <TreeSelect.TreeNode value={item.id} title={item.name} key={item.id}>
          {this.renderTree(item.children)}
        </TreeSelect.TreeNode>
      ))
      : []
  }

  filterBillDetailField = () => {
    const { globalFieldsList = [] } = this.props
    let arr: GlobalFieldModel[] = []
    // 明细字段按左值规则过滤
    arr = globalFieldsList.filter(item => {
      if (optionBlackList.includes(item.name)) {
        return false
      }
      let type = get(item, 'dataType.type', '')
      const entity = get(item, 'dataType.entity', '')
      if (type === 'ref') {
        type = entity
      }
      if (type === 'organization.Department') {
        type = 'department'
      }
      if (type === 'organization.Staff') {
        type = 'staff'
      }
      if (type === 'boolean') {
        type = 'switcher'
      }
      // 单独处理自定义档案字段对应自定义档案多选字段
      if (type === 'list') {
        const listType = get(item, 'dataType.elemType.type', '')
        const listEntity = get(item, 'dataType.elemType.entity', '')
        if (listType === 'ref') {
          type = listEntity
        }
      }
      item.type = type
      if (type.indexOf(dimensionPrefix) > -1) {
        return true
      }
      return Object.keys(this.typesMap).includes(type)
    })
    this.setState({ billDetailField: arr })
  }

  private renderDetailTree = (leftValue: string) => {
    const { billDetailField } = this.state
    const leftType = this.labelMapType[leftValue] || ''
    return billDetailField
      .filter(el => el.type === leftType)
      .map(item => (
        <TreeSelect.TreeNode value={item.name} title={i18n.get(item.label)} key={item.name}></TreeSelect.TreeNode>
      ))
  }

  private handleDateChange = (index: number, field: keyof FilterInterface) => (input: any) => {
    const value: FilterInterface[] =
      Array.isArray(this.state.value) && this.state.value.length > 0 ? this.state.value.slice(0) : [this.newFilter()]
    let date

    if (Array.isArray(input) && input?.length === 2) {
      date = [moment(input[0]).format('YYYY-MM-DD'), moment(input[1]).format('YYYY-MM-DD')]
      if (date[0] === date[1]) {
        message.error(i18n.get('日期范围不能选择同一天'))
        return
      }
    } else {
      date = input ? [moment(input).format('YYYY-MM-DD')] : []
    }

    value[index] = {
      ...value[index],
      [field]: date
    }
    if (field === 'right') {
      value[index].right = date
    }
    const newValue = cloneDeep(value)
    this.setState({ value: newValue })
    this.props.onChange && this.props.onChange(value)
  }

  private renderRow = ({ left, operator, right, isShowInput, middleTags, entity }: FilterInterface, index: number) => {
    const {
      value,
      field: { maxLength = 5 }
    } = this.props
    let dimensions = []
    const type = this.labelMapType[this.left(left)]
    if (Object.keys(this.typesMap).includes(type)) {
      if (type === 'switcher') {
        dimensions = utils.switchSelector
      }
      if (type === 'department') {
        dimensions = this.generateDepartment()
      }
      if (type === 'staff') {
        dimensions = this.generateStaff()
      }
    } else if (entity?.includes(enumeratePrefix)) {
      dimensions = this.generateEnumerate(entity, this.dimensionName(entity, enumeratePrefix))
    } else {
      dimensions = this.generateDimension(dimensions, type, left)
    }
    const isApportionRule = this.source === 'apportionRule'
    let inputValue = (Array.isArray(right) && right[0]) || right || undefined
    let treeSelectValue = isApportionRule ? right : (right && right?.[0]) || undefined
    if (isApportionRule) {
      inputValue = (Array.isArray(right.value) && right.value?.[0]) || right?.value || undefined
      treeSelectValue = isApportionRule ? right.value : (right.value && right.value?.[0]) || undefined
    }
    const rightType = right?.type || undefined
    const leftValue = this.handleLeftValue(type, left)
    const suffix = this.inputSuffix(leftValue)
    const hiddenAdd = maxLength && value?.length >= maxLength
    const multiple = isApportionRule && ['in', 'notIn'].includes(operator)
    const options = !isApportionRule
      ? this.state.leftTags
      : this.state.leftTags.map(v => <Option key={v.value}>{v.label}</Option>)
    const isBILLDETAILFIELD = isApportionRule && rightType === 'BILL_DETAIL_FIELD'
    return (
      <div className="interconnectal-custom-dimension-filter-row" key={index}>
        <Row>
          <Col span={7}>
            {isApportionRule ? (
              <Select
                popupClassName="Cascader-styles"
                placeholder={i18n.get('请选择')}
                notFoundContent={i18n.get('没有找到您搜索的信息')}
                showSearch
                value={leftValue}
                onChange={this.handleChange(index, 'left')}
                style={{ width: '100%' }}
              >
                {options}
              </Select>
            ) : (
              <Cascader
                popupClassName="Cascader-styles"
                placeholder={i18n.get('请选择')}
                notFoundContent={i18n.get('没有找到您搜索的信息')}
                showSearch
                options={options}
                value={leftValue}
                onChange={this.handleChange(index, 'left')}
                style={{ width: '100%' }}
              />
            )}
          </Col>
          <Col span={6} style={{ padding: '0 8px' }}>
            <EKBSelect
              value={operator}
              onChange={this.handleChange(index, 'operator')}
              tags={middleTags}
              isEui
              size="middle"
              optionFilterProp="label"
              placeholder={i18n.get('请选择')}
            />
          </Col>
          {isApportionRule && (
            <Col span={5} style={{ paddingRight: '8px' }}>
              <Select
                popupClassName="Cascader-styles"
                placeholder={i18n.get('请选择')}
                notFoundContent={i18n.get('没有找到您搜索的信息')}
                value={rightType}
                onChange={this.handleChange(index, 'rightType')}
                style={{ width: '100%' }}
              >
                <Option key="CONSTANT">{i18n.get('固定值')}</Option>
                <Option key="BILL_DETAIL_FIELD">{i18n.get('费用明细字段')}</Option>
              </Select>
            </Col>
          )}

          <Col span={isApportionRule ? 6 : 11}>
            {isBILLDETAILFIELD && (
              <TreeSelect
                showSearch
                allowClear
                style={{ width: '100%', maxHeight: '100px' }}
                onChange={this.handleChange(index, 'right')}
                value={[treeSelectValue]}
                filterTreeNode={this.filterTreeNode}
                dropdownMatchSelectWidth
                getPopupContainer={triggerNode => triggerNode.parentNode}
                dropdownStyle={{ maxHeight: '300px' }}
                placeholder={i18n.get('请选择')}
              >
                {this.renderDetailTree(leftValue)}
              </TreeSelect>
            )}
            {!isBILLDETAILFIELD && isShowInput && type !== 'date' && (
              <DebounceInput type={numberArr.includes(type) ? 'number' : 'text'}
                placeholder={i18n.get('请输入')}
                value={inputValue}
                className="del-defalut-style"
                addonAfter={suffix}
                onChange={val => {
                  this.handleInputChange(val, index)
                }}
              />

            )}
            {!isBILLDETAILFIELD && !isShowInput && type !== 'date' && (
              <TreeSelect
                showSearch
                allowClear
                multiple={multiple}
                style={{ width: '100%', maxHeight: '100px' }}
                onChange={this.handleChange(index, 'right')}
                value={treeSelectValue}
                filterTreeNode={this.filterTreeNode}
                dropdownMatchSelectWidth
                getPopupContainer={triggerNode => triggerNode.parentNode}
                dropdownStyle={{ maxHeight: '300px' }}
                placeholder={i18n.get('请选择')}
              >
                {this.renderTree(dimensions)}
              </TreeSelect>
            )}

            {/* 字段类型为date,判断展示日期还是范围 */}
            {type === 'date' ? (
              ['not in', 'in'].includes(operator) ? (
                <RangePicker
                  value={formatRightDateValue(right)}
                  onChange={this.handleDateChange(index, 'right')}
                  format="YYYY-MM-DD"
                  allowClear={true}
                  className="picker-box"
                ></RangePicker>
              ) : (
                <DatePicker
                  value={!Array.isArray(inputValue) && inputValue ? moment(inputValue) : undefined}
                  onChange={this.handleDateChange(index, 'right')}
                  format="YYYY-MM-DD"
                  allowClear={true}
                  className="picker-box"
                  placeholder={i18n.get('请选择')}
                ></DatePicker>
              )
            ) : null}
          </Col>
        </Row>
        <div className="action">
          {!hiddenAdd && <OutlinedTipsAdd fontSize={14} color="var(--brand-base)" onClick={this.handleAdd(index)} />}
          {!(this.props.deleteRowDisabled && this.state.value.length === 1) && <OutlinedTipsReduce fontSize={14} color="var(--brand-base)" onClick={this.handleDelete(index)} />}
        </div>
      </div>
    )
  }

  private generateDimension = (dimensions = [], type = '', left) => {
    const { field } = this.props
    const tags = get(field, 'tags')

    let recordName = ''
    if (!!tags) {
      recordName = type.substr(dimensionPrefix.length) || ''
    } else {
      const fields = this.props.globalFields[this.left(left)]
      recordName = this.record(fields, dimensionPrefix)
    }
    const record = this.props.records[recordName]
    dimensions = record ? this.props.dimensions[record.id] || [] : []
    return dimensions
  }

  private generateEnumerate = (type, left) => {
    console.log(this.props.enumerateMap, left)
    return this.props.enumerateMap[left]
  }

  private inputSuffix = leftValue => {
    if (!leftValue) {
      return ''
    }
    if (leftValue.includes('_percentage') || (Array.isArray(leftValue) && leftValue.includes('_percentage'))) {
      return '％' // @i18n-ignore
    }
    return ''
  }

  private handleLeftValue = (type, left) => {
    let leftValue = left
    if (typeof left === 'string' && left.indexOf('_percentage') > -1) {
      leftValue = left.split('_percentage').filter(item => item)
      leftValue.push('_percentage')
    }
    if (typeof left === 'string' && left.indexOf('_balance') > -1) {
      leftValue = left.split('_balance').filter(item => item)
      leftValue.push('_balance')
    }
    if (!Array.isArray(leftValue) && this.source !== 'apportionRule') {
      leftValue = this.left(left) ? [this.left(left)] : undefined
    }
    return leftValue
  }

  generateDepartment = () => {
    const { departmentTree = [] } = this.props
    return departmentTree
  }

  generateStaff = () => {
    return this.props.staffsVisibility || []
  }

  private setLeftSelectFields = () => {
    const { globalFieldsList = [], field } = this.props
    const tags = get(field, 'tags')
    let arr: GlobalFieldModel[] = []

    if (!tags) {
      arr = globalFieldsList.filter(item => {
        if (optionBlackList.includes(item.name)) {
          return false
        }
        let type = get(item, 'dataType.type', '')
        const entity = get(item, 'dataType.entity', '')
        if (type === 'ref') {
          type = entity
        }
        if (type === 'organization.Department') {
          type = 'department'
        }
        if (type === 'boolean') {
          type = 'switcher'
        }

        this.labelMapType[item.name] = type
        if (type.indexOf(dimensionPrefix) > -1) {
          return true
        }
        return Object.keys(this.typesMap).includes(type)
      })
    }

    if (!!tags) {
      tags.forEach(item => {
        switch (item.type) {
          case 'PLAN':
            this.labelMapType[item.id + '_balance'] = 'money'
            this.labelMapType[item.id + '_percentage'] = 'number'
            return
          case 'LEDGER':
            this.labelMapType[item.id] = 'money'
            return
          case 'active':
            this.labelMapType['active'] = 'switcher'
            return
          case 'ref':
            if (item.entity.startsWith('basedata.Dimension') || item.entity.startsWith('basedata.Enum')) {
              this.labelMapType[item.id] = item.entity
            }
            if (item.entity.startsWith('organization.Department')) {
              this.labelMapType[item.id] = 'department'
            }
            if (item.entity.startsWith('organization.Staff')) {
              this.labelMapType[item.id] = 'staff'
            }
            return
          default:
            this.labelMapType[item.id] = item.type
        }
      })
      arr = tags
    }
    return arr.map(item => ({
      ...item,
      dataType: item.dataType,
      value: item.id || item.name,
      children: item.children,
      label: item.label || item.name,
      entity: item.entity,
      source: item.source
    }))
  }

  private handleInputChange = (inputVal, index: number) => {
    const { value } = this.state
    if (Array.isArray(value[index]?.right)) {
      if (this.source === 'apportionRule') {
        value[index].right.value[0] = inputVal
        value[index].right.value = value[index].right.value.filter(item => item)
      } else {
        value[index].right[0] = inputVal
        value[index].right = value[index].right.filter(item => item)
      }
    } else {
      if (this.source === 'apportionRule') {
        value[index].right.value = inputVal
      } else {
        value[index].right = inputVal
      }
    }
    const newValue = cloneDeep(value)
    this.setState({ value: newValue })
    this.props.onChange && this.props.onChange(value)
  }

  renderRowWrapper = (item, index) => {
    return (
      <div className="custom-dimension-filter-row-wrapper">
        <div className="custom-dimension-filter-row-wrapper-operator">
          {index > 0 && <T name="且" />}
        </div>
        {this.renderRow(item, index)}
      </div>
    )
  }

  render() {
    const { hidden } = this.props
    const { value } = this.state
    if (hidden || !Array.isArray(value) || value.length === 0) {
      return null
    }
    return <>{value.map((item, index) => this.renderRowWrapper(item, index))}</>
  }
}


// 创建可控的输入组件
const DebounceInput = memo(({ value, onChange, ...props }: any) => {
  // 创建防抖函数
  const debouncedChange = useMemo(
    () => debounce((val: string) => {
      onChange?.(val);
    }, 300),
    [onChange]
  );

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      debouncedChange.cancel();
    };
  }, [debouncedChange]);

  return (
    <Input
      {...props}
      defaultValue={value}
      onChange={(e) => debouncedChange(e.target.value)}
    />
  );
});