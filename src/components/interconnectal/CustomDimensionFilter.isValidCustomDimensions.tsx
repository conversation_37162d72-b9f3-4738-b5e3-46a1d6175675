import './CustomDimensionFilter.less'
import { app as api } from '@ekuaibao/whispered'
import { FilterInterface } from '../types'

export function isValidCustomDimensions(input: FilterInterface[] | FilterInterface[][], isApportionRule?: boolean) {
  const isValidSingleCondition = (input: FilterInterface[], isApportionRule?: boolean) => {
    const map = api.getState()['@custom-dimension'].customDimensionMap || {}
    for (const item of input) {
      let conditions =
        !item.left ||
        !item.operator ||
        !item.right ||
        !item.right.length ||
        (item.right === null && !map[item.right[0]])
      if (isApportionRule) {
        conditions =
          !item.left ||
          !item.operator ||
          !item.right ||
          !item.right?.type ||
          !item.right?.value ||
          !item.right?.value.length ||
          (item.right?.value === null && !map[item.right?.value[0]])
      }
      if (conditions) {
        return false
      }
    }
    return true
  }
  // 如果是一维数组,转换为二维数组格式
  const is2DArray = Array.isArray(input) && Array.isArray(input[0])
  if (is2DArray) {
    const value2D = input as FilterInterface[][]
    // 至少要有一组条件
    if (!Array.isArray(value2D) || value2D.length === 0) {
      return false
    }
    // 每组条件都要验证通过
    return value2D.every((item) => isValidSingleCondition(item, isApportionRule))
  }
  return isValidSingleCondition(input as FilterInterface[], isApportionRule)
}

export default isValidCustomDimensions
