import { isWindowsOS } from '../../lib/os'

class DINAlternateFontRegistry {

  fontVariants: Record<string, {
    file: string;
    weight: string;
    style: string;
  }> = {};

  constructor() {
    this.fontVariants = {
      regular: {
        file: './D-DIN-PRO-400-Regular.otf',
        weight: 'normal',
        style: 'normal'
      },
    };
  }

  async registerFonts() {
    const fontPromises = Object.entries(this.fontVariants).map(([variant, config]) => {
      return this.loadFontVariant(variant, config);
    });

    try {
      await Promise.all(fontPromises);
      return true;
    } catch (error) {
      console.error('Font registration failed:', error);
      return false;
    }
  }

  async loadFontVariant(variant, config) {
    try {
      const font = new FontFace('DIN Alternate1', `url(${config.file})`, {
        weight: config.weight,
        style: config.style
      });

      const loadedFont = await font.load();
      (document.fonts as any).add(loadedFont);

      console.log(`Loaded ${variant} variant of DIN Alternate`);
      return loadedFont;
    } catch (error) {
      console.error(`Failed to load ${variant} variant:`, error);
      throw error;
    }
  }

}


export const initBinAlternateFont = () => {
  const fontRegistry = new DINAlternateFontRegistry();
  fontRegistry.registerFonts().then(success => {
    if (success) {
      console.log('DIN Alternate fonts registered successfully');
    }
  });
}

export const patchFontForWindows = () => {
  if (isWindowsOS()) {
    initBinAlternateFont()
  }
}
