/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/19 下午2:04.
 */
import { Input, Select } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import React from 'react'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'

const Option = Select.Option
const otherText = i18n.get('其他')

export default class SelectBankField extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = { isOther: false, searchText: '', value: props.value, dataSource: this.handleDataSource(props) }
  }

  componentWillMount() {
    this.initData(this.props)
  }

  componentWillReceiveProps(nextProps) {
    const fn = fnCompareProps(this.props, nextProps)
    fn('dataSource', () => {
      this.updateState(nextProps)
    })
  }

  initData = props => {
    let { value } = props
    value &&
      api.invokeService('@common:get:banks', { filter: `name.containsIgnoreCase("${value}")` }).then(banks => {
        let isOther = value ? !!!banks.length : false
        this.setState({ isOther, value })
      })
  }

  updateState = props => {
    let { value, dataSource } = props
    let templateDataSource = this.search && dataSource.length ? dataSource : this.handleDataSource(props)
    this.setState({ dataSource: templateDataSource, value })
  }

  handleSearch = value => {
    this.search = true
    let { onSearch } = this.props
    onSearch && onSearch(value)
  }

  handleInputChange(e) {
    this.setState({ value: e.target.value })
    this.props.onChange && this.props.onChange(e.target.value)
  }

  renderBankList(dataSource) {
    const { isConcise } = this.props
    return (
      dataSource &&
      dataSource.map(o => {
        const icon = isConcise ? o.unionIcon : o.icon
        return (
          <Option className="select-bank-wrapper" value={o.name} key={o.name} title={o.name}>
            <div className="line-bank dis-f ai-c">
              <img className="mr-5" src={icon} />
              {o.name}
            </div>
          </Option>
        )
      })
    )
  }

  handleChange = value => {
    let isOther = value === otherText
    this.setState({ isOther }, () => {
      !isOther ? this.props.onChange(value) : this.props.onChange('')
    })
  }

  handleSelect = value => {
    this.search = false
    let isOther = value === otherText
    const { onSelect } = this.props
    onSelect && onSelect('')
    this.setState({ isOther })
  }

  handleDataSource(props) {
    let data = []
    const { dataSource, otherField = true } = props
    data = data.concat(dataSource)
    data.push({
      name: otherText,
      icon: 'https://images.ekuaibao.com/bank/bank-other.svg'
    })
    const cData = otherField ? data : dataSource
    return cData
  }

  render() {
    const { value = '', placeholder, disabled = false, isConcise, allowClear } = this.props
    const { isOther, dataSource } = this.state
    let v = !isConcise && isOther ? otherText : value || undefined
    return (
      <div id="unionBank">
        <div className="dis-f ai-c select-bank">
          <Select
            showSearch
            id="SelectBankField"
            placeholder={placeholder}
            style={{ width: '100%' }}
            filterOption={false}
            value={v}
            onChange={this.handleChange}
            onSearch={this.handleSearch}
            onSelect={this.handleSelect}
            disabled={disabled}
            dropdownClassName="dropdown_SelectBankField"
            getPopupContainer={triggerNode => triggerNode.parentNode}
            allowClear={allowClear}
            data-testid="pay-selectBankField-select"
          >
            {this.renderBankList(dataSource)}
          </Select>
        </div>
        {!isConcise && isOther && (
          <Input
            id="Input_SelectBankField"
            placeholder={placeholder}
            value={value}
            className="mt-20"
            onChange={this.handleInputChange.bind(this)}
            data-testid="pay-selectBankField-input"
          />
        )}
      </div>
    )
  }
}
