/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/5/25.
 */
import { Input, Select } from 'antd'

const Option = Select.Option
import React from 'react'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
const otherText = i18n.get('其他')

export default class SelectBranchField extends React.PureComponent {
  constructor(props) {
    super(props)
    let banchData = this.handleDataSource(props)
    this.state = { isOther: false, searchText: '', value: props.value, dataSource: banchData }
    this.branchData = banchData
  }

  componentDidMount() {
    this.updateState(this.props)
  }

  componentWillReceiveProps(nextProps) {
    const fn = fnCompareProps(this.props, nextProps)
    fn('dataSource', () => {
      this.updateState(nextProps)
    })
    fn('city', () => {
      this.updateState(nextProps)
    })

    if (this.props.afterGetBranchList !== nextProps.afterGetBranchList) {
      this.updateState(nextProps)
    }
  }

  updateState = props => {
    let { dataSource, value, afterGetBranchList } = props
    let isOther = value && dataSource.length ? !dataSource.find(v => v.name === value) : false
    if (afterGetBranchList) {
      isOther = !dataSource.find(v => v && v.name === value)
    }
    let templateDataSource = this.search && dataSource.length ? dataSource : this.handleDataSource(props)
    this.branchData = templateDataSource
    this.setState({ isOther, dataSource: templateDataSource, value })
  }

  handleChange = value => {
    let isOther = value === otherText
    this.setState({ isOther }, () => {
      !isOther ? this.props.onChange(value) : this.props.onChange('')
    })
  }

  handleInputChange(e) {
    this.setState({ value: e.target.value })
    this.props.onChange && this.props.onChange(e.target.value)
  }

  renderBranchList(dataSource) {
    return (
      dataSource &&
      dataSource.map(branch => {
        const name = typeof branch === 'object' ? branch.name : branch
        return <Option key={name}>{name}</Option>
      })
    )
  }

  handleSelect = value => {
    const { onBranchSelect } = this.props
    let isOther = value === otherText
    this.search = false
    const branchObject = this.getBranchObj(value)
    onBranchSelect && onBranchSelect(isOther, branchObject)
    this.setState({ isOther })
  }

  getBranchObj(value) {
    const { dataSource } = this.props
    return dataSource && dataSource.find(line => line.name === value)
  }

  handleDataSource(props) {
    let data = []
    const { dataSource } = props
    data = data.concat(dataSource)
    data.push(otherText)
    return data
  }

  handleSearch = value => {
    const { onBranchSearch } = this.props
    onBranchSearch && onBranchSearch(value)
  }

  render() {
    const { value = '', placeholder, city } = this.props
    const { isOther, dataSource } = this.state
    let v = isOther ? otherText : value || undefined
    return (
      <div>
        <div className="dis-f ai-c select-bank">
          <Select
            id="account_branch"
            showSearch
            placeholder={placeholder}
            style={{ width: '100%' }}
            onChange={this.handleChange}
            filterOption={(input, option) => option.key.indexOf(input) > -1}
            value={v}
            onSearch={this.handleSearch}
            onSelect={this.handleSelect}
            disabled={!!!city}
            getPopupContainer={triggerNode => triggerNode.parentNode}
            data-testid="pay-selectBranchField-select"
          >
            {this.renderBranchList(dataSource)}
          </Select>
        </div>
        {isOther && (
          <Input
            value={value}
            placeholder={i18n.get('请输入银行网点')}
            className="mt-20"
            onChange={this.handleInputChange.bind(this)}
            id="other_branch"
            data-testid="pay-selectBranchField-input"
          />
        )}
      </div>
    )
  }
}
