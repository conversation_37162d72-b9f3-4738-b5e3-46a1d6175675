/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/5/25.
 * 现金罗盘支付显示逻辑：判断现金罗盘支付授权功能是否已开通
 现金罗盘支付是否可用逻辑：
 1.判断现金罗盘支付授权功能是否已开通
 2.判断现金罗盘支付渠道是否已开启
 3.所选账户是否支持现金罗盘
 4.所支付单据是否填写了收款账户信息
 */

import './select-payment-method.less'
import { Checkbox, Radio, Icon, Tooltip, Row, Col, Tag } from '@hose/eui'
import classnames from 'classnames'
import { filterChannels } from '../../components/utils/fnFilterPaymentChannel'
import React from 'react'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
import EKBIcon from '../../elements/ekbIcon'
import classNames from 'classnames'
import { EnhanceConnect } from '@ekuaibao/store'

const CheckboxGroup = Checkbox.Group
const RadioGroup = Radio.Group

@EnhanceConnect(state => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap
}))
export default class SelectPaymentThirdMethod extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      value: props.value || ['OFFLINE'],
      radioValue: props.radioValue || 'OFFLINE',
      currId: null
    }
  }

  componentWillReceiveProps(nextProps) {
    let fn = fnCompareProps(this.props, nextProps)
    fn('value', value => {
      this.setState({ value })
    })
  }

  handleChange(checkedValues) {
    const { onSetDefault, onChange, defaultChannel } = this.props
    console.log(checkedValues, 'handleChange', this.state.currId)
    if (checkedValues.includes(this.state.currId)) {
      onSetDefault && onSetDefault(this.state.currId)
    }
    if (!checkedValues.includes(defaultChannel)) {
      onSetDefault && onSetDefault('OFFLINE')
    }
    onChange && onChange(checkedValues)
  }

  handleRadioChange(e) {
    this.setState({
      radioValue: e.target.value
    })
    this.props.onChange && this.props.onChange(e.target.value)
  }

  onSetDefault = (e, newValue) => {
    const { onSetDefault, value } = this.props
    e.stopPropagation()
    e.preventDefault()
    if (!value.includes(newValue)) {
      this.props.onChange && this.props.onChange([...value, newValue])
    }
    onSetDefault && onSetDefault(newValue)
  }

  //选择账户时
  renderSingleSelection(channels = []) {
    //这个方法目前好像不用了，全局搜了没有找到这个引用，先保留
    const { getDisableReason, defaultChannel, dynamicChannelMap = {} } = this.props
    let isDisabled = false
    let tooltip = ''
    const oneChannel = channels.length === 1
    return (
      <RadioGroup
        className="ekb-select-third-payment-method"
        value={this.state.radioValue}
        onChange={this.handleRadioChange.bind(this)}
        data-testid="pay-selectPaymentMethod-radio-group"
      >
        {channels.map((item, idx) => {
          if (getDisableReason) {
            const { disable, reason } = getDisableReason(item)
            isDisabled = disable
            tooltip = reason
          }
          return (
            <div className={classnames('channel-wrapper', { 'one-channel': oneChannel })} key={item.channel}>
              <Radio
                className={
                  isDisabled
                    ? 'checkbox-payment disabled'
                    : this.state.radioValue === item.channel
                    ? 'selected checkbox-payment'
                    : 'checkbox-payment'
                }
                disabled={isDisabled}
                value={item.channel}
                data-testid={`pay-selectPaymentMethod-radio-${item.channel}`}
              >
                <div className={classnames('payment-item', { 'pr-9': isDisabled })}>
                  {
                    <EKBIcon
                      className={classNames('mr-4 stand-icon', { disabled: isDisabled })}
                      name={dynamicChannelMap[item.value].icon}
                    />
                  }
                  {dynamicChannelMap[item.value].name}
                  {tooltip && (
                    <Tooltip title={tooltip}>
                      <Icon type="question-circle-o" className="warn-icon" />
                    </Tooltip>
                  )}
                  {item.value === defaultChannel && <span className="default-title">{i18n.get('默认')}</span>}
                </div>
              </Radio>
            </div>
          )
        })}
      </RadioGroup>
    )
  }

  //创建账户时
  renderMultiple = (channels, walletHide) => {
    const channelsFilter = filterChannels(channels)
    const channelsArr = walletHide ? channelsFilter.filter(v => v.value !== 'WALLET') : channelsFilter
    const { defaultChannel, dynamicChannelMap = {}, disabled } = this.props
    const { currId, value = [] } = this.state
    return (
      <CheckboxGroup className="ekb-select-third-payment-method" value={value} onChange={this.handleChange.bind(this)} data-testid="pay-selectPaymentMethod-checkbox-group">
        <Row style={{ width: '100%' }} type="flex" justify="space-between">
          {!!channelsArr?.length &&
            channelsArr?.map((item, index) => {
              return (
                <Col span={12} key={index}>
                  <div
                    className={classnames('checkbox-payment', {
                      disabled: item.value === 'OFFLINE' || disabled,
                      'mr-8': index % 2 === 0
                    })}
                  >
                    <div className="dis-f ai-c">
                      <EKBIcon
                        style={{ width: 16, height: 16, marginRight: 8 }}
                        name={dynamicChannelMap[item.value].icon}
                      />
                      <span className="payment-item-name">{dynamicChannelMap[item.value].name}</span>
                      {item.value === defaultChannel && (
                        <Tag size="small" color="pri" style={{ marginLeft: '4px' }}>
                          {i18n.get('默认')}
                        </Tag>
                      )}
                    </div>
                    <div>
                      {item.value !== defaultChannel && (
                        <span className="set-default" onClick={e => this.onSetDefault(e, item.value)} data-testid={`pay-selectPaymentMethod-set-default-${item.value}`}>
                        {i18n.get('设为默认')}
                      </span>
                      )}
                      <Checkbox key={item.value} disabled={item.value === 'OFFLINE' || disabled} value={item.value} data-testid={`pay-selectPaymentMethod-checkbox-${item.value}`} />
                    </div>
                  </div>
                </Col>
              )
            })}
        </Row>
      </CheckboxGroup>
    )
  }

  render() {
    let { isSingle, channels, walletHide = false } = this.props
    return isSingle ? this.renderSingleSelection(channels) : this.renderMultiple(channels, walletHide)
  }
}
