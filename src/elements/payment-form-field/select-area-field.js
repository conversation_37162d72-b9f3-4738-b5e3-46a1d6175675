/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/5/25.
 */
import { Select, Col, Row } from 'antd'
import React from 'react'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
const Option = Select.Option

export default class SelectAreaField extends React.PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      cities: [],
      area: {
        province: '',
        city: ''
      }
    }
  }

  componentDidMount() {
    let { value } = this.props
    this.setValue(value)
  }

  componentWillReceiveProps(nextProps) {
    let fn = fnCompareProps(this.props, nextProps)
    fn('value', value => {
      this.setValue(value)
    })
  }

  setValue(value) {
    let area = value ? JSON.parse(value || '') : ''
    this.setState({ area })
  }

  handleProvinceChange(value) {
    let { area } = this.state
    let newArea = { ...area }
    newArea.province = value
    newArea.city = ''
    this.props.onChange && this.props.onChange(JSON.stringify(newArea), 'province')
  }

  onSecondCityChange(value) {
    let { area } = this.state
    let newArea = { ...area }
    newArea.city = value
    this.props.onChange && this.props.onChange(JSON.stringify(newArea), 'city')
  }

  renderProvinceItems() {
    return this.props.provinceList.map(v => (
      <Option key={v.id} value={v.name}>
        {v.name}
      </Option>
    ))
  }

  renderCityItems() {
    return this.props.cityList.map(v => (
      <Option key={v.id} value={v.name}>
        {v.name}
      </Option>
    ))
  }

  render() {
    let { area } = this.state
    const provinceValue = area.province ? area.province : undefined
    const cityValue = area.city ? area.city : undefined
    return (
      <Row span={24}>
        <Col span={16}>
          <Select
            id="SelectAreaField"
            style={{ width: '100%' }}
            showSearch
            value={provinceValue}
            dropdownClassName="dropdown_SelectAreaField"
            placeholder={i18n.get('省')}
            onChange={this.handleProvinceChange.bind(this)}
            getPopupContainer={triggerNode => triggerNode.parentNode}
            data-testid="pay-selectAreaField-province"
          >
            {this.renderProvinceItems()}
          </Select>
        </Col>
        <Col span={7} offset={1}>
          <Select
            showSearch
            style={{ width: '100%' }}
            value={cityValue}
            disabled={!area.province}
            placeholder={i18n.get('市')}
            onChange={this.onSecondCityChange.bind(this)}
            getPopupContainer={triggerNode => triggerNode.parentNode}
            data-testid="pay-selectAreaField-city"
          >
            {this.renderCityItems()}
          </Select>
        </Col>
      </Row>
    )
  }
}
