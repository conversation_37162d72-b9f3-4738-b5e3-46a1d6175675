import React, { useLayoutEffect, useMemo, useRef } from "react"

const GAP = ':'

export default function WWOpenDataCom({ type, openid }) {
	const ref = useRef(null)
	useLayoutEffect(() => {
		ref.current && window.WWOpenData.bind(ref.current)
	})

	// 费控接口的id为 corpId + GAP + userId。在企微场景下，userId即为openId
	// 企业信息也有类似的规则
	const _openDataOpenId = useMemo(() => {
		if (openid && openid.includes(':')) {
			return openid.split(GAP)[1]
		}
		return openid
	}, [openid])

	// @ts-ignore
	return <ww-open-data ref={ref} type={type} openid={_openDataOpenId} />
}
