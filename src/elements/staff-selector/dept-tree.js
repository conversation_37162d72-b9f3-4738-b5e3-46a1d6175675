/**************************************************
 * Created by panwei on 2017/5/22 下午6:03.
 **************************************************/
import './dept-tree.less'
import React from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { Checkbox, Radio } from 'antd'
import EkbHighLighter from '../EkbHighLighter'
import {cloneDeep, map, includes, remove, uniq, find, isString, debounce} from 'lodash'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
import NewDeptTree from './new-dept-tree'
import TreeMenu from '../puppet/tree-menu/tree-menu'
import { getDisplayName } from "../utilFn";
import { NameCell } from '../name-cell'

function depthFirstTraversal(root, arr = []) {
  if (!root) {
    return arr
  }
  let stack = [root]

  let current
  while ((current = stack.pop())) {
    arr.push(current)

    const children = current.children
    if (!children || !children.length) {
      continue
    }
    children.length == 1
      ? stack.push(children[0])
      : stack = stack.concat(children)
  }
  return arr
}
@EnhanceConnect(state => ({
  departmentTree: state['@common'].department.data
}))
export default class DeptTree extends React.PureComponent {
  flatTreeDataList = []
  flatTreeDataMap = {}

  constructor(props) {
    super(props)
    let { checkedKeys = [] } = props
    if (isString(checkedKeys)) {
      checkedKeys = [checkedKeys]
    }
    this.state = {
      checkedKeys,
      expandedKeys: this.fnGetParentId(props.treeData) || [],
      departmentTreeData: props.treeData
    }
    this._CHECKEDDATA_ = []
    const treeData = !props.isNewContactList ? props.treeData : props.departmentTree
    this.filterCheckedData(treeData, checkedKeys)
    if (treeData && treeData.length) {
      const list = depthFirstTraversal(treeData[0])
      let map = {}
      list.forEach(item => (map[item.id] = item))
      this.flatTreeDataMap = map
      this.flatTreeDataList = list
    }
  }


  componentWillReceiveProps(nextProps) {
    let fn = fnCompareProps(this.props, nextProps)
    fn('treeData', _ => {
      const treeData = nextProps.treeData
      if (treeData && treeData.length) {
        const list = depthFirstTraversal(treeData[0])
        let map = {}
        list.forEach(item => (map[item.id] = item))
        this.flatTreeDataMap = map
        this.flatTreeDataList = list
      }

      if (nextProps.isNewContactList) {
        this.setState({
          departmentTreeData: nextProps.treeData
        })
        return
      }

      this.setState({ expandedKeys: [this.fnGetParentId(nextProps.treeData)] })
      this.forceUpdate()
    })

    fn('checkedKeys', _ => {
      this.setState({ checkedKeys: nextProps.checkedKeys })
      if (nextProps?.searchText?.length > 0) {
        return
      }
      this._CHECKEDDATA_ = []
      const treeData = nextProps.isNewContactList ? nextProps.departmentTree : nextProps.treeData
      this.filterCheckedData(treeData, nextProps.checkedKeys)
    })

    fn('searchText', _ => {
      if (!nextProps.isNewContactList) {
        this.handleSearch(nextProps.searchText)
      }
    })
  }

  handleSearch = debounce((value) => {
    let searchList = this.flatTreeDataList
    let map = this.flatTreeDataMap
    if (value.length > 0) {
      let expandedKeys = []
      let matchedChildList = []
      function getAllParentId(item) {
        const idList = []
        let parent = map[item.parentId]
        while(parent) {
          idList.push(parent.id)
          parent = map[parent.parentId]
        }
        return idList
      }
      console.time('search over')
      searchList.forEach((item) => {
        if (item.name.indexOf(value) >= 0) {
          matchedChildList.push(item)
        }
      })

      console.timeEnd('search over')

      console.time('get all expandedKeys')
      matchedChildList.forEach((item) => {
        expandedKeys = expandedKeys.concat(getAllParentId(item))
      })
      console.timeEnd('get all expandedKeys')

      expandedKeys = uniq(expandedKeys)

      this.setState({ expandedKeys })
    } else {
      this.setState({ expandedKeys: [] })
    }
  }, 300)


  fnGetParentId(data) {
    return map(data, item => item.id)
  }

  fnSearchTree(tree = [], text, expandedKeys = []) {
    tree.forEach(line => {
      if (!!~line.name.indexOf(text)) {
        this.getExpandKeys(this.searchList, line.parentId, expandedKeys)
      }

      if (line.children && line.children.length > 0) {
        this.fnSearchTree(line.children, text, expandedKeys)
      }
    })
    return expandedKeys
  }

  getExpandKeys(list, parentId, expandedKeys = []) {
    list.forEach(el => {
      let tempParentId = undefined
      if (el.id === parentId) {
        tempParentId = el.parentId
        expandedKeys.push(el.id)
      }

      if (tempParentId) {
        this.getExpandKeys(this.searchList, tempParentId, expandedKeys)
      } else {
        this.getExpandKeys(el.children || [], parentId, expandedKeys)
      }
    })
  }

  filterCheckedData(treeData, checkedKeys) {
    treeData.forEach(line => {
      line.children = line.children || []
      if (includes(checkedKeys, line.id)) {
        this._CHECKEDDATA_.push(line)
      }

      this.filterCheckedData(line.children, checkedKeys)
    })
  }

  handleCheckedChange(data) {
    let checkedKeys = this.state.checkedKeys || []
    let checked = includes(checkedKeys, data.id)
    if (!checked) {
      checkedKeys.push(data.id)
      this._CHECKEDDATA_.push(data)
    } else {
      remove(checkedKeys, line => line === data.id)
      remove(this._CHECKEDDATA_, line => line.id === data.id)
    }

    checkedKeys = uniq(checkedKeys)
    this.setState({ checkedKeys })
    this.props.onChange && this.props.onChange({ checkedKeys, checkedData: this._CHECKEDDATA_ })
  }

  handleRadioChange(data) {
    const { onlyLeafCanBeSelected, isDependence, virtualSelectable = false } = this.props
    const isVirtualDisabled = data.virtual && !virtualSelectable
    if ((isDependence && !data.selectable) || isVirtualDisabled) {
      return
    }
    if (onlyLeafCanBeSelected && data.children && data.children.length) return
    let checkedKeys = [data.id]
    this._CHECKEDDATA_ = [data]
    this.setState({ checkedKeys })
    this.props.onChange && this.props.onChange({ checkedKeys, checkedData: this._CHECKEDDATA_ })
  }

  renderTitle(item) {
    const {
      onlyLeafCanBeSelected,
      isDependence,
      disabledKeys = [],
      rootDisabled,
      dimension = {},
      virtualSelectable = false
    } = this.props
    let checked = includes(this.state.checkedKeys, item.id)
    if (checked) {
      let data = find(this._CHECKEDDATA_, line => line.id === item.id)
      !data && this._CHECKEDDATA_.push(item)
    }
    const name = getDisplayName(item)
    const highlight = window.isInWeComISV && item.id ? (
      <NameCell type="department" id={item.id} name={name} />
    ) : (
      <>
        <EkbHighLighter
          highlightClassName="highlight"
          searchWords={[this.props.searchText]}
          textToHighlight={name}
        />
        <TitleTip item={item} />
      </>
    )
    const isVirtualDisabled = item.virtual && !virtualSelectable
    const isSuffix = item.isSuffix
    const checkedStyle = checked
      ? {
          backgroundColor: '#f1f2f2',
          color: '#797979'
        }
      : {}
    let style = {}
    if (isDependence) {
      style =
        !item.selectable || isVirtualDisabled ? { cursor: 'not-allowed', color: '#797979' } : { cursor: 'pointer' }
    } else {
      style =
        (onlyLeafCanBeSelected && item.children && item.children.length) || isVirtualDisabled
          ? { cursor: 'not-allowed', color: '#797979' }
          : { cursor: 'pointer' }
    }
    const { dimensionId, type } = dimension
    const currentCostCenterId = item?.form?.costCenter
    const currentLegalEntityId = item?.form?.legalEntity
    let disabled = disabledKeys.includes(item.id) || (rootDisabled && !item.parentId)
    if (type === 'costCenter') {
      disabled = currentCostCenterId && currentCostCenterId !== dimensionId
    } else if (type === 'FRST') {
      disabled = currentLegalEntityId && currentLegalEntityId !== dimensionId
    }
    disabled = disabled || isVirtualDisabled
    return (
      <div key={item.id} className="tree-title-wrapper">
        {isSuffix ? (
          highlight
        ) : this.props.multiple ? (
          <Checkbox
            data={item}
            className="dept-tree-checkbox"
            dataType="dept"
            checked={checked}
            disabled={disabled}
            onClick={this.handleCheckedChange.bind(this, item)}
          >
            {highlight}
          </Checkbox>
        ) : this.props.notShowDepartmentTitle ? (
          <div
            data={item}
            className="dept-tree-radio"
            dataType="dept"
            onClick={this.handleRadioChange.bind(this, item)}
            style={{ ...checkedStyle, ...style }}
          >
            {highlight}
          </div>
        ) : (
          <Radio
            data={item}
            className="dept-tree-radio"
            dataType="dept"
            checked={checked}
            disabled={disabled}
            onClick={this.handleRadioChange.bind(this, item)}
          >
            {highlight}
          </Radio>
        )}
      </div>
    )
  }

  fnUpdateData = data => {
    let temData = cloneDeep(data)
    let fn = (item = []) => {
      item.forEach(line => {
        let children = line.children || []
        if (children.length) {
          line.active = false
          fn(children)
        }
      })
    }
    fn(temData)
    return temData
  }

  // 渲染单条搜索结果
  renderSearchTitle = item => {
    const { disabledKeys = [], rootDisabled, dimension = {}, virtualSelectable = false } = this.props
    let checked = includes(this.state.checkedKeys, item.id)
    if (checked) {
      let data = find(this._CHECKEDDATA_, line => line.id === item.id)
      !data && this._CHECKEDDATA_.push(item)
    }

    const isVirtualDisabled = item.virtual && !virtualSelectable
    const { dimensionId, type } = dimension
    const currentCostCenterId = item?.form?.costCenter
    const currentLegalEntityId = item?.form?.legalEntity
    let disabled = disabledKeys.includes(item.id) || (rootDisabled && !item.parentId)
    if (type === 'costCenter') {
      disabled = currentCostCenterId && currentCostCenterId !== dimensionId
    } else if (type === 'FRST') {
      disabled = currentLegalEntityId && currentLegalEntityId !== dimensionId
    }
    disabled = disabled || isVirtualDisabled
    const name = getDisplayName(item)
    const highlight = (
      <>
        <EkbHighLighter
          highlightClassName="highlight"
          searchWords={[this.props.searchText]}
          textToHighlight={name}
        />
        <TitleTip item={item} />
      </>
    )
    return (
      <Checkbox
        data={item}
        className="dept-search-checkbox"
        dataType="dept"
        checked={checked}
        disabled={disabled}
        onClick={this.handleCheckedChange.bind(this, item)}
      >
        {highlight}
      </Checkbox>
    )
  }

  render() {
    return (
      <div className="ekb-multiple-tree">
        {this.props.isNewContactList ? (
          <NewDeptTree
            isNewModal={this.props.isNewModal}
            treeData={this.state.departmentTreeData}
            searchText={this.props.searchText}
            renderTitle={this.renderTitle.bind(this)}
            renderSearchTitle={this.renderSearchTitle.bind(this)}
          />
        ) : (
          <TreeMenu
            data={this.props.treeData}
            expandedKeys={this.state.expandedKeys}
            isNeedExpandParentProp={false}
            renderTitle={this.renderTitle.bind(this)}
          />
        )}
      </div>
    )
  }
}

function TitleTip(props) {
  return window.__PLANTFORM__ === 'MC' && props.item?.virtual ? (
    <span className="title-tip-text">{i18n.get('（虚拟部门）')}</span>
  ) : null
}
