/**************************************************
 * Created by panwei on 2017/5/26 下午9:15.
 **************************************************/
import React from 'react'

import { Radio, Checkbox } from 'antd'
import <PERSON>kbHighLighter from '../EkbHighLighter'
import './select-staff-wrapper.less'
import { ListView } from '@ekuaibao/eui-isomorphic'
import { filter, map, uniqBy, remove, uniq, without, includes, get, cloneDeep } from 'lodash'
import TreeMenu from '../puppet/tree-menu/tree-menu'
import { getV, sequenceStaffs } from '@ekuaibao/lib/lib/help'
import { getStaffShowByConfig, getStaffShowExternal, getStaffName as getUserDisplayName } from "../utilFn";
import { searchDimension } from './dept-util'
import NewDept<PERSON>ree from './new-dept-tree'
import { NameCell } from '../name-cell'
export default class DeptMemberSelector extends React.Component {
  constructor(props) {
    super(props)
    this._listKey = props.listKey || 'id'
    this._listFilter = props.listFilter || (line => true)
    this._listData = filter(this.props.users, this._listFilter)
    this.state = {
      checkAllStatus: false,
      treeData: props.orgtree || [],
      openKeys: [],
      checkedKeys: props.checkedKeys || [],
      checkedData: [],
      listData: this._listData,
      selectedItem: {},
      randomKey: Math.random(),
      forceReFetch: false,
      searchData: []
    }
  }

  componentDidMount() {
    if (this.props.isMcTransfer && this.state.treeData?.length) {
      this.handleSelectTreeNode1(this.state.treeData[0])
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.checkedKeys !== nextProps.checkedKeys) {
      this.setState({ checkedKeys: nextProps.checkedKeys, checkedData: this.fnGetCheckedData(nextProps.checkedKeys) })
    }

    if (this.props.searchText !== nextProps.searchText) {
      this.handleSearchInputChange(nextProps.searchText)
    }

    if (this.props.users !== nextProps.users) {
      const users = getV(nextProps, 'users', [])
      const treeData = getV(nextProps, 'orgtree', [])
      this._listData = filter(users, this._listFilter)
      let { searchText } = nextProps
      let listData = searchText && searchText.length ? this.searchUsers(this._listData, searchText) : this._listData
      const id = treeData.length ? treeData[0].id : ''
      listData = sequenceStaffs(listData, id)
      if (this.props.isMcTransfer) {
        this.handleSelectTreeNode1(treeData[0])
        this.setState({ forceReFetch: false })
      } else {
        this.setState({ listData, forceReFetch: true })
      }
    } else {
      this.setState({ forceReFetch: false })
    }

    if (this.props.orgtree !== nextProps.orgtree && !this.props.isMcTransfer) {
      this.setState({ treeData: nextProps.orgtree || [] })
    }
  }

  get isAllCheck() {
    let { users, searchText, defaultDisabledKeys, filterActiveStaff, checkedKeys: selectedStaff = [] } = this.props
    // 选中人从有效人员过滤(去除离职人员)
    const checkedKeys = filterActiveStaff ? selectedStaff.filter(it => users.find(us => us.id === it)) : selectedStaff
    const { listData } = this.state
    if (searchText && searchText.length) {
      const searcheCheckedKeys = listData.filter(line => checkedKeys.indexOf(line.id) >= 0)
      return searcheCheckedKeys && listData && searcheCheckedKeys.length === listData.length
    }
    if (defaultDisabledKeys) {
      users = filter(users, user => !includes(defaultDisabledKeys, user.id))
    }
    if (listData.length !== users.length) {
      let listDataIds = listData.map(item => item.id)
      let listDataCheckedKeys = []
      checkedKeys?.forEach(a => {
        listDataIds.forEach(b => {
          if (a === b) {
            listDataCheckedKeys.push(b)
          }
        })
      })
      return listData.length === listDataCheckedKeys.length
    }
    return this.props.checkedKeys && users && checkedKeys.length === users.length
  }

  fnGetCheckedData(checkedKeys) {
    const { users } = this.props
    const map = {}
    users?.forEach(item => {
      map[item[this._listKey]] = item
    })
    const resultList = []
    checkedKeys?.forEach(id => {
      if (map[id]) {
        resultList.push(map[id])
      }
    })
    return resultList
  }

  filterUsers(users = [], id) {
    const ret = []
    users.forEach(line => {
      line.departments?.forEach(dpId => {
        if (dpId === id) {
          ret.push(line)
        }
      })
    })
    const staffs = sequenceStaffs(ret, id)
    return uniqBy(staffs, 'id')
  }

  createSearchREG(text) {
    if (/[\\,\|,\+,\?,\(,\*,\&,\),\$,\^,\~,\"]/.test(text)) {
      text = text.replace(/\\/g, '\\\\')
      //@i18n-ignore
      let strSpecial = ['+', '?', '|', '^', '~', '(', ')', '*', '&', '^', '$', '"', '（', '）']
      strSpecial.forEach(s => {
        let reg = new RegExp('\\' + s, 'gim')
        let l = '\\' + s
        text = text.replace(reg, l)
      })
      return new RegExp(text, 'ig')
    }

    text = text.trim()
    let ss = text.split('').join('\\w*')
    return new RegExp(ss, 'ig')
  }

  searchUsersByOrgTree = (treeData = [], listData = [], staffs = [], searchSubTenant = true) => {
    treeData.forEach(line => {
      if (line) {
        line.children = line.children || []
        const staff = this.filterUsers(listData, line.id)
        if (staff && staff.length) {
          staffs.push(...staff)
        }
        let children = cloneDeep(line.children)
        if (!searchSubTenant) {
          children = children.filter(item => !item?.belong?.auth)
        }
        this.searchUsersByOrgTree(children, listData, staffs)
      }
    })
    return uniqBy(staffs, 'id')
  }

  searchOrgTree(tree = [], text, ret = []) {
    tree.forEach(line => {
      if (line) {
        line.children = line.children || []
        if (!!~line.name.indexOf(text) || this.createSearchREG(text).test(line.name)) {
          ret.push(line)
        }

        this.searchOrgTree(line.children, text, ret)
      }
    })
    return ret
  }

  searchUsers(users = [], text, ret = []) {
    users.forEach(line => {
      const name = getStaffShowByConfig(line)
      const str = text?.toLowerCase()
      const lowerName = name.toLowerCase()
      const lowerSpell = line.nameSpell?.toLowerCase()
      if (
        lowerName.includes(str) ||
        (line.nameSpell.includes(str) && i18n.currentLocale === 'zh-CN')
      ) {
        ret.push(line)
      }
    })
    return ret
  }

  // 新部门搜索接口
  searchOrgTreeNew = async value => {
    let searchData = []
    if (value.length) {
      let params = {
        searchText: value
      }
      searchData = await searchDimension(params)
    }
    return searchData
  }

  handleSearchInputChange = async value => {
    const { isNewContactList = false } = this.props
    let listData = this._listData
    let searchData = []
    let treeData = isNewContactList ? this.state.treeData : this.props.orgtree
    if (value) {
      if (isNewContactList) {
        searchData = await this.searchOrgTreeNew(value)
      } else {
        treeData = this.searchOrgTree(treeData, value)
      }
      listData = this.searchUsers(listData, value)
      if (listData.length > 0) {
        if (isNewContactList) {
          searchData.unshift({
            code: 'SEKEY001',
            name: `${i18n.get('包含')}'${value}'${i18n.get('成员')}`
          })
        } else {
          treeData.unshift({
            code: 'SEKEY001',
            name: `${i18n.get('包含')}'${value}'${i18n.get('成员')}`
          })
        }
      } else {
        const _searchData = isNewContactList ? searchData : treeData
        if (_searchData.length > 0) {
          listData = this.filterUsers(this._listData, _searchData[0].id)
        }
      }
      this.__listDataCache = listData
    }

    let { selectedItem } = this.state
    if (this.props.isMcTransfer) {
      selectedItem = {}
      this.props.onSelectTreeNode(null)
      listData = this.searchUsersByOrgTree(this.props.orgtree, listData, [], !!value)
    }

    this.setState({
      treeData,
      listData,
      checkAllStatus: false,
      searchKey: value,
      randomKey: Math.random(),
      searchData,
      selectedItem
    })
  }

  handleOneRadioChange = e => {
    let data = e.target.data
    let checkedKeys = [data[this._listKey]]
    this.setState({ checkedKeys })
    this.props.onChange &&
      this.props.onChange({
        checkedKeys,
        checkedData: this.fnGetCheckedData(checkedKeys)
      })
  }

  handleOneCheckedChange = e => {
    let checkedKeys = this.state.checkedKeys
    let checked = e.target.checked
    let data = e.target.data
    checked ? checkedKeys.push(data[this._listKey]) : remove(checkedKeys, line => line === data[this._listKey])

    checkedKeys = uniq(checkedKeys)
    this.setState({ checkedKeys, checkAllStatus: false })
    this.props.onChange &&
      this.props.onChange({
        checkedKeys,
        checkedData: this.fnGetCheckedData(checkedKeys)
      })
  }

  handleCheckAll = e => {
    let listData = this.state.listData
    let checkedKeys = this.state.checkedKeys
    let checked = e.target.checked

    listData = filter(listData, line => !line.__disabled)
    let ids = map(listData, line => line[this._listKey])

    if (this.props.defaultDisabledKeys) {
      ids = without(ids, ...this.props.defaultDisabledKeys)
    }

    checked ? checkedKeys.push(...ids) : remove(checkedKeys, line => includes(ids, line))
    checkedKeys = uniq(checkedKeys)

    this.setState({ checkedKeys, checkAllStatus: checked })
    this.props.onChange &&
      this.props.onChange({
        checkedKeys,
        checkedData: this.fnGetCheckedData(checkedKeys)
      })
  }

  handleSelectTreeNode1 = item => {
    if (this.props.isMcTransfer) {
      this.handleSelectTreeNodeMcTransfer(item)
      return
    }
    let key = get(item, 'id', '')
    key = key.trim()
    let listData = null
    if (key === 'SEKEY001') {
      //回复搜索的缓存
      listData = this.__listDataCache
    } else if (key.startsWith('@')) {
      listData = this._listData //其他预置数据
    } else {
      listData = this.filterUsers(this._listData, key)
    }
    this.setState({ checkAllStatus: false, listData, selectedItem: item, randomKey: Math.random() })
  }

  handleSelectTreeNodeMcTransfer = item => {
    let key = get(item, 'id', '')
    key = key.trim()
    let listData = null

    if (key === 'SEKEY001') {
      //回复搜索的缓存
      listData = this.__listDataCache
    } else if (key.startsWith('@')) {
      listData = this._listData //其他预置数据
    } else if (this.props.isMcTransfer && item?.belong?.auth) {
      listData = this.searchUsersByOrgTree([item], this._listData, [], false)
    } else {
      listData = this.filterUsers(this._listData, key)
    }
    this.setState({ checkAllStatus: false, listData, selectedItem: item, randomKey: Math.random() })
    this.handleClearCheckedKeys()
    this.props.onSelectTreeNode(item)
  }

  handleClearCheckedKeys = () => {
    const checkedKeys = []
    this.setState({ checkedKeys })
    this.props.onChange &&
      this.props.onChange({
        checkedKeys,
        checkedData: this.fnGetCheckedData(checkedKeys)
      })
  }

  renderRadioItem = props => {
    let searchWords = [this.state.searchKey]
    const { defaultDisabledKeys = [], showResignationStaff } = this.props
    const { data, key, status, style } = props
    if (status === 1) {
      return (
        <div className="line" key={key} style={style}>
          <Radio
            data={data}
            disabled={includes(defaultDisabledKeys, data[this._listKey])}
            checked={includes(this.state.checkedKeys, data[this._listKey])}
            onChange={this.handleOneRadioChange}
          >
            {
              window.isInWeComISV && data.id ? (
                <>
                  <NameCell type="user" id={data.id} name={data.name} />
                  {showResignationStaff && !data.active && <>{i18n.get('(离职)')}</>}
                  {showResignationStaff && !data.authState && <>{i18n.get('(已停用)')}</>}
                </>
              ) : (
            <EkbHighLighter
              className="highlight-wrapper"
              highlightClassName="highlight"
              searchWords={searchWords}
              textToHighlight={getStaffName(data, showResignationStaff)}
            />)}
          </Radio>
        </div>
      )
    }
    return <div>{i18n.get('加载中')}</div>
  }

  /**
   * 最大选择人数，暂时只作用于 member ，不作用于 department 和 role
   * @returns {boolean|boolean}
   */
  isMax() {
    const { max } = this.props
    const { checkedKeys } = this.state
    if (!max) return false
    return checkedKeys?.length >= max
  }

  renderItem = props => {
    let searchWords = [this.state.searchKey]
    const { defaultDisabledKeys = [], showResignationStaff } = this.props
    const { data, key, status, style } = props
    const isMax = this.isMax()
    if (status === 1) {
      let checked = includes(this.state.checkedKeys, data[this._listKey])
      return (
        <div className="line" key={key} style={style}>
          <Checkbox
            className="ekb-checkbox-right"
            data={data}
            disabled={includes(defaultDisabledKeys, data[this._listKey]) || (!checked && isMax)}
            checked={checked}
            onChange={this.handleOneCheckedChange}
          >
            {
              window.isInWeComISV && data.id ? (
                <>
                  <NameCell type="user" id={data.id} name={data.name} />
                  {showResignationStaff && !data.active && <>{i18n.get('(离职)')}</>}
                  {showResignationStaff && !data.authState && <>{i18n.get('(已停用)')}</>}
                </>
              ) : (
            <EkbHighLighter
              className="highlight-wrapper"
              highlightClassName="highlight"
              searchWords={searchWords}
              textToHighlight={getStaffName(data, showResignationStaff)}
            />)}
          </Checkbox>
        </div>
      )
    }
    return <div>{i18n.get('加载中')}</div>
  }

  renderTree = () => {
    const { searchData, treeData, selectedItem } = this.state
    const { searchText, isNewContactList = false } = this.props
    return isNewContactList ? (
      <NewDeptTree
        treeData={treeData}
        searchData={searchData}
        searchText={searchText}
        isSearchProps={true}
        selectedItem={selectedItem}
        virtualSelectable={true}
        handleSelectTreeNode={this.handleSelectTreeNode1.bind(this)}
      />
    ) : (
      <TreeMenu
        data={this.state.treeData}
        searchText={this.state.searchKey}
        expandedKeys={this.state.openKeys}
        selectedItem={this.state.selectedItem}
        virtualSelectable={true}
        onSelect={this.handleSelectTreeNode1}
      />
    )
  }

  renderCheckList = () => {
    const { max } = this.props
    const { listData, randomKey, forceReFetch } = this.state
    if (!listData || listData.length === 0) return
    return (
      <div className="right-wrapper">
        <div className="title">
          {!max && (
            <>
              <div>{i18n.get('全选')}</div>
              <div className="flex" />
              <div>
                <Checkbox
                  disabled={!listData.length}
                  checked={this.isAllCheck}
                  onChange={this.handleCheckAll.bind(this)}
                />
              </div>
            </>
          )}
          {max && (
            <>
              <div className="flex" />
              <div className="tip">{i18n.get('最多选择{__k0}个', { __k0: max })}</div>
            </>
          )}
        </div>
        <div
          style={{
            height: 220,
            overflow: 'hidden',
            flex: 1
          }}
        >
          <ListView
            key={randomKey}
            noRows={'norows'}
            refetch={() => Promise.resolve({ items: listData, count: listData.length })}
            className="ekb-list-box-wrapper"
            rowHeight={40}
            renderItem={this.renderItem}
            forceReFetch={forceReFetch}
          />
        </div>
      </div>
    )
  }

  renderRadioList() {
    const { listData, randomKey, forceReFetch } = this.state
    if (!listData || listData.length === 0) return
    return (
      <div className="right-wrapper-2">
        <div
          style={{
            height: 260,
            overflow: 'hidden',
            flex: 1
          }}
        >
          <ListView
            key={randomKey}
            noRows={'norows'}
            refetch={() => Promise.resolve({ items: listData, count: listData.length })}
            className="ekb-list-box-wrapper"
            rowHeight={40}
            renderItem={this.renderRadioItem}
            forceReFetch={forceReFetch}
          />
        </div>
      </div>
    )
  }

  render() {
    const { isShowLeftCrossCorpNode = true } = this.props
    return (
      <div className="content-part">
        {isShowLeftCrossCorpNode && <div className="left"> {this.renderTree()} </div>}
        <div className="right">{this.props.multiple ? this.renderCheckList() : this.renderRadioList()}</div>
      </div>
    )
  }
}

function getStaffName(line, showResignationStaff) {
  const showValue = getStaffShowExternal(line)
  const name = getUserDisplayName(line)
  if (showResignationStaff && !line.active) {
    return `${i18n.get(`{__k0}(离职)`, { __k0: name })}${showValue}`
  }
  if (showResignationStaff && !line.authState) {
    return `${i18n.get(`{__k0}(已停用)`, { __k0: name })}${showValue}`
  }
  return `${name}${showValue}`
}
