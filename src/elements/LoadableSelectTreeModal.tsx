import React, { useState, useEffect, useRef } from 'react'
import { SelectTree } from '@hose/pro-eui-pc-components/es/select-tree'
import '@hose/pro-eui-pc-components/es/select-tree/index.css'
import { OutlinedTipsClose } from '@hose/eui-icons'
import styles from './LoadableSelectTreeModal.module.less'
import { Button, Tooltip } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import Highlighter from 'react-highlight-words'

export interface LoadableSelectTreeModalProps {
  dataset?: any
  title?: string
  multiple?: boolean
  data?: any
  searchType?: 'select' | 'select_search'
  blackListCanNotSelect?: string[]
  layer?: any
  queryData?: any
}

interface NodePageInfo {
  hasNextPage: boolean
  loading: boolean
  loaded: boolean
}

interface SearchPageInfo {
  hasNextPage: boolean
  loading: boolean
}

const formatItems = (items = []) => {
  const dfs = (items = []) => {
    return items.map(item => {
      const { children, ...rest } = item
      return {
        ...rest,
        isLeaf: item.leaf,
        children: Array.isArray(children) ? dfs(children) : children
      }
    })
  }

  return dfs(items)
}

const findNodeById = (items = [], id) => {
  const dfs = (items = []) => {
    for(const item of items) {
      if(item.id === id) {
        return item
      }
      if(item.children) {
        const res = dfs(item.children)
        if(res) {
          return res
        }
      }
    }
  }

  return dfs(items)
}

const ROOT_LOADABLE_ID = '__loadable_more_root__'

const LoadableSelectTreeModal = (props: LoadableSelectTreeModalProps) => {
  const {
    title,
    layer,
    multiple,
    data,
    blackListCanNotSelect,
    searchType = 'select',
    queryData
  } = props
  const [dataSource, setDataSource] = useState<any>([])
  const [selectedNode, setSelectedNode] = useState<any>([])
  const [nodePageInfo, setNodePageInfo] = useState<Record<string, NodePageInfo>>({
    root: { hasNextPage: true, loading: false, loaded: false }
  })
  const [searchMode, setSearchMode] = useState(searchType === 'select_search')
  const [searchResults, setSearchResults] = useState([])
  const [searchPageInfo, setSearchPageInfo] = useState<SearchPageInfo>({ hasNextPage: true, loading: false })
  const searchTextRef = useRef('')

  // 暂无多选场景，暂不考虑多选的默认选中数据
  const selectedIds = (multiple ? [] : [data?.selectedNode?.id]).filter(item => item)
  const selectedList = (multiple ? [] : [data?.selectedNode]).filter(item => item && Object.keys(item).length)

  const fetchDimensionV2 = async (params = {}) => {
    const data = {
      parentId: '',
      cancelRelation: false,
      ...(queryData || {}),
      searchText: searchTextRef.current,
      ...params,
    }
    return api.invokeService('@bills:get:record:link:v2', data)
  }

  const onBeforeFetch = (params, parentId, isSearchMode) => {
    if(isSearchMode) {
      setSearchMode(isSearchMode)
      setSearchPageInfo(prev => ({ ...prev, loading: true }))
    } else {
      setNodePageInfo(prev => ({ ...prev, [parentId]: { ...prev[parentId], loading: true } }))
    }
  }

  const onAfterFetch = (data, parentId, isSearchMode) => {
    const { items, hasNextPage } = data
    if(isSearchMode) {
      setSearchPageInfo(prev => ({ ...prev, loading: false, hasNextPage }))
      setSearchResults(items)
    } else {
      setNodePageInfo(prev => ({ ...prev, [parentId]: { ...prev[parentId], loading: false, hasNextPage }}))
      setDataSource(prev => prev.concat(items))
    }
  }

  const initDimensionData = async (params = {}, parentId?, isSearchMode = false) => {
    parentId = parentId || 'root'
    onBeforeFetch(params, parentId, isSearchMode)

    try {
      const data = await fetchDimensionV2(params)
      const { items } = data

      onAfterFetch(data, parentId, isSearchMode)
      return items
    } catch (error) {
      console.error('initDimensionData error', error)
    }
  }

  useEffect(() => {
    if(queryData && searchType !== 'select_search') {
      initDimensionData(queryData)
    }
  }, [queryData])

  const getId = key => {
    const isRoot = key === ROOT_LOADABLE_ID
    const id = isRoot ? '' : key
    return id
  }

  const getLastItem = (key) => {
    const id = getId(key)
    const data = searchMode ? searchResults : dataSource
    let lastItem = data[data.length - 1]
    if(id) {
      lastItem = findNodeById(data, id)
    }
    return lastItem
  }

  const handleLoadMore = (node) => {
    const { key } = node
    const lastItem = getLastItem(key)
    const id = getId(key)
    const { id: lastId, code: lastCode } = lastItem || {}
    return initDimensionData({ parentId: id, lastId, lastCode }, id, searchMode)
  }
  const handleRemoteSearch = async (value) => {
    const searchText = value.trim()
    if(searchText) {
      searchTextRef.current = searchText
      return initDimensionData({ parentId: '', searchText }, '', true)
    } else {
      searchTextRef.current = ''
      setSearchMode(false)
      setSearchResults([])
      setSearchPageInfo({ hasNextPage: false, loading: false })
      return []
    }
  }

  const loadData = async (node) => {
    const { key } = node
    const id = key === ROOT_LOADABLE_ID ? '' : key
    return initDimensionData({ parentId: id }, id)
  }

  const hasMore = (node) => {
    const { key } = node
    const id = key === ROOT_LOADABLE_ID ? 'root' : key
    return searchMode ? searchPageInfo.hasNextPage : nodePageInfo[id]?.hasNextPage
  }

  const handleDataChange = data => {
    console.log('handleDataChange', data)
  }

  const handleModalClose = () => {
    layer.emitCancel()
  }
  const handleModalOK = () => {
    if (multiple) {
      layer.emitOk(selectedNode)
    } else {
      layer.emitOk(selectedNode[0])
    }
  }
  const handleChange = async (_id, list) => {
    if(list?.length < 1) {
      setSelectedNode(list)
      return
    }
    const fieldIds = list.map(item => item.id)
    const result = (await api.invokeService('@common:get:dimensionItems:fullName:byId', fieldIds)) || []
    if (result.length) {
      const map: any = {}
      result.forEach((item: any) => {
        map[item.id] = item.fullName
      })
      setSelectedNode(list.map(item => ({
        ...item,
        fullPath: map[item.id] || item.fullPath
      })))
    } else {
      setSelectedNode(list)
    }
  }

  const renderSearchItem = (node: any, searchText?: string, selected?: boolean, disabled?: boolean) => {
    const { name, id, enName, code, fullPath } = node
    let showName = i18n.currentLocale === 'en-US' && enName ? enName : name

    if (code) {
      showName = `${showName}(${code})`
    }
    return (
      <div
        key={id}
        data-testid={`loadable-select-tree-modal-search-item-${id}`}
        className={`${styles['search-item-container']} ${disabled ? styles['search-item-disabled'] : ''}`}
      >
        <Tooltip
          title={
            <div>
              <div>{showName}</div>
              {fullPath && <div>{fullPath}</div>}
            </div>
          }
          placement="bottom"
          mouseEnterDelay={0.5}
          zIndex={10000}
        >
          <Highlighter
            highlightClassName={styles['highlight']}
            searchWords={[searchText]}
            textToHighlight={showName}
            className={styles['search-item-title']}
            autoEscape={true}
          />
          {fullPath && <div className={`${styles['search-item-path']} ${styles['text-ellipsis']}`}>{fullPath}</div>}
        </Tooltip>
      </div>
    )
  }

  return (
    <div className={styles['select-tree-modal-wrapper']}>
      <div className="select-tree-modal-header">
        <div className="header-title">{title}</div>
        <OutlinedTipsClose fontSize={16} onClick={handleModalClose} color="var(--eui-icon-n2)" />
      </div>
      <div className={`select-tree-modal-main ${searchMode ? 'search-mode-main' : ''}`}>
        <SelectTree
          language={i18n.currentLocale}
          dataSource={searchMode ? searchResults : dataSource}
          multiple={multiple}
          selectedIds={selectedIds}
          selectedList={selectedList}
          onChange={handleChange}
          backList={blackListCanNotSelect}
          searchType={searchType}
          height={400}
          loadableMode
          onLoadMore={handleLoadMore}
          searchMode='remote'
          onRemoteSearch={handleRemoteSearch}
          loadData={loadData}
          hasMore={hasMore}
          onDataChange={handleDataChange}
          titleRender={searchMode ? renderSearchItem : undefined}
        />
      </div>
      <div className="select-tree-modal-footer">
        <Button category="secondary" className="btn-ml" onClick={handleModalClose} data-testid="loadable-select-tree-modal-cancel-button">
          {i18n.get('取消')}
        </Button>
        <Button category="primary" className="btn-ml" onClick={handleModalOK} disabled={selectedNode.length === 0} data-testid="loadable-select-tree-modal-ok-button">
          {i18n.get('确定')}
        </Button>
      </div>
    </div>
  )
}

export default LoadableSelectTreeModal
