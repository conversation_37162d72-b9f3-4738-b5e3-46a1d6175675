import React, { ReactNode } from 'react'
import { Avatar } from '@hose/eui'
import { getDisplayName, getStaffShowByConfig } from "./utilFn";
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'
import { NameCell } from './name-cell'

export const getTagLabel = (
  item,
  showAvatar,
  avatarOptions = {
    style: {
      marginRight: 4,
      width: 16,
      height: 16,
      lineHeight: 0,
      marginBottom: 1,
      border: '1px solid var(--eui-static-white)',
      boxSizing: 'content-box'
    }
  }
) => {
  const staffShowName = getStaffShowByConfig(item)
  const status = item.active === false ? `(${i18n.get('已停用')})` : ``
  let name: any = `${staffShowName}${status}`
  const showA = showAvatar && item.hasOwnProperty('avatar')
  const avatarSrc = item?.avatar || null

  if (window.isInWeComISV) {
    let _type: 'user' | 'department'
    if (item.userId) {
      _type = 'user'
    } else if (item.type === 'department') {
      _type = 'department'
    } else if (item.dataType) {
      _type = item.dataType === 'department-member' ? 'user' : item.dataType === 'department' ? 'department' : _type
    }

    const name = <>{staffShowName}{status}</>
    return <>
      {showA && <Avatar size="small" shape="circle" style={{ marginRight: 4 }} src={avatarSrc} {...avatarOptions} />}
      {
        _type
          ? <NameCell type={_type} id={item.id} name={name} />
          : name
      }
    </>
  }

  return (
    <>
      {showA && <Avatar size="small" shape="circle" style={{ marginRight: 4 }} src={avatarSrc} {...avatarOptions} />}
      {item.label ? item.label : name}
    </>
  )
}

const getStaffShow = staff => {
  const staffShowName = getStaffShowByConfig(staff)
  let status = ''
  if (staff.authState === false && !staff.external) {
    status = `${i18n.get('（')}${i18n.get('已停用')}${i18n.get('）')}`
  }
  if (staff.active === false) {
    status = `${i18n.get('（')}${i18n.get('已离职')}${i18n.get('）')}`
  }
  return `${staffShowName}${status}`
}
const getDimensionShow = dimension => {
  const name = getDisplayName(dimension)
  return !!dimension?.code ? `${name}${i18n.get('（')}${dimension.code}${i18n.get('）')}` : name
}
export const getShowString = (item, type: 'staff' | 'role' | 'department') => {
  const actionMap = {
    staff: getStaffShow,
    role: getDimensionShow,
    department: getDimensionShow
  }
  return actionMap[type](item)
}

export const getItemLabel = item => {
  let action = getDimensionShow
  if (item.hasOwnProperty('avatar')) {
    action = getStaffShow
  }
  return action(item)
}

export const getDepartmentShowName = department => {
  return department?.name ?? ''
}

/**
 * 通过当前语言环境获取名称
 */
export const getNameByCurrentLocale = (obj: { enName?: string; name: string }) => {
  return getContentByLocale(obj, 'name')
}

export const realName = ({ name, enName }: { name: string, enName?: string }) => {
  return (i18n.currentLocale === 'en-US' && enName) ? enName : i18n.get(name)
}

export const realTitle = ({ title, enTitle }: { enTitle: string, title?: string }) => {
  return (i18n.currentLocale === 'en-US' && enTitle) ? enTitle : i18n.get(title)
}

export const realDescribe = ({ describe, enDescribe }: { describe: string, enDescribe?: string }) => {
  return (i18n.currentLocale === 'en-US' && enDescribe) ? enDescribe : i18n.get(describe)
}
