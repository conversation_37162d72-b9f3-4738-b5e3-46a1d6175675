/**************************************************
 * Created by panwei on 2017/6/26 上午10:34.
 **************************************************/
import styles from './countersign-selector.module.less'
import React, { PureComponent } from 'react'
import classnames from 'classnames'
import { Tooltip, Popover, Avatar, Button } from '@hose/eui'
import { OutlinedEditEdit } from '@hose/eui-icons'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import FLOW_CONFIG_DONE from '../../images/flow-config-done.svg'
import TIP_IMAGE from '../../images/budget-tip.svg'
import COMMENT_TIP from '../../images/comment-tip.svg'
import FLOW_CONFIG_REJECT from '../../images/flow-config-reject.svg'
import { agreeType, skippedTypeMap } from '@ekuaibao/lib/lib/enums'
import { getCounterProgress, getCountersigners } from '@ekuaibao/lib/lib/lib-util'
import { findLast, get } from 'lodash'
import { isString } from '@ekuaibao/helpers'
import { fnSubstrByCount } from '../ekbc-business/bills/flow-log-item'
import { checkStaffHasExternalStaff } from '../../plugins/bills/layers/flow/flowHelper'
import { getStaffShowByConfig } from '../utilFn'

const policy = () => ({
  ALL: i18n.get('人会签，所有人同意后审批通过'),
  ANY: i18n.get('人会签，任意人同意后审批通过')
})
const NOAUTOAGREE = [
  'NO_AUTO_AGREE',
  'ADMIN_SKIP_NODE_AUTO',
  'REJECT_FORCE_APPROVALS',
  'ROLLBACK_FORCE_APPROVALS',
  'EDIT_FORCE_APPROVALS'
]
function isSubmitterChoice(node, action) {
  const { isAuto, isSubmitterChoice } = node?.config || {}
  if (action === 'agree') {
    return !isAuto && isSubmitterChoice && !node.counterSigners.length
  }
  return !isAuto && isSubmitterChoice
}

function getCounterSignersIds(counterSigners = []) {
  return counterSigners.map(line => line.signerId.id)
}

async function handleModifyCounterSigners(node, selectCounterSigners, onResult) {
  const crossCorpApprove = get(node, 'config.crossCorpApprove')
  if (crossCorpApprove) {
    fncrossCorpApprove(node, selectCounterSigners, onResult)
  } else {
    let counterSigners = node.counterSignersCandidate
    let ids = getCounterSignersIds(counterSigners)
    let checkedKeys = getCounterSignersIds(selectCounterSigners)
    const data = [
      {
        type: 'department-member',
        checkIds: checkedKeys
      }
    ]
    const hasExternalStaff = await checkStaffHasExternalStaff(ids)
    if (hasExternalStaff) {
      data.push({
        type: 'external',
        checkIds: checkedKeys
      })
    }
    api
      .open('@organizationManagement:SelectStaff', {
        title: i18n.get('选择人员'),
        multiple: true,
        staffLimitData: node.isAllStaffs ? undefined : ids,
        blackListValue: {
          staff: node?.signersBlacklist ?? []
        },
        data,
        // 会签节点选人遵循通讯录组织范围规则
        followContactRules: true,
        notFollowExternalChargeRules: hasExternalStaff
      })
      .then(checkedList => {
        const staffData = checkedList.find(v => v.type === 'department-member')
        const externalStaffData = checkedList.find(v => v.type === 'external')
        let staffs = []
        if (staffData?.checkList?.length) {
          staffs = staffData?.checkList
        }
        if (externalStaffData?.checkList?.length) {
          staffs = staffs.concat(externalStaffData?.checkList)
        }
        const selectData = node.isAllStaffs ? staffs.map(line => ({ signerId: line })) : staffs.map(line => line.id)
        const selectCountersigners = node.isAllStaffs
          ? selectData
          : counterSigners.filter(line => selectData.indexOf(line.signerId.id) >= 0)
        onResult(selectCountersigners)
      })
    // api.emit('@vendor:select-user', {
    //   whiteList: node.isAllStaffs ? undefined : ids,
    //   checkedKeys: checkedKeys,
    //   multiple: true,
    //   staffFilterList: node?.signersBlacklist ?? [],
    //   callback(staffs) {
    //     let selectData = node.isAllStaffs ? staffs.map((line) => ({ signerId: line })) : staffs.map((line) => line.id)
    //     let selectCountersigners = node.isAllStaffs
    //       ? selectData
    //       : counterSigners.filter((line) => selectData.indexOf(line.signerId.id) >= 0)
    //     onResult(selectCountersigners)
    //   }
    // })
  }
}

function fncrossCorpApprove(node, selectCounterSigners, onResult) {
  const counterSigners = get(node, 'config.crossCorpApprovers')
  const ids = selectCounterSigners.map(line => line.id)
  api.open('@bills:CollaborationApproveStaff', { node, ids, isMultiple: true }).then(staffs => {
    let selectData = staffs.map(line => line.id)
    let selectCountersigners = counterSigners.filter(line => selectData.indexOf(line.id) >= 0)
    onResult(selectCountersigners)
  })
}

@EnhanceConnect(state => ({
  staffDisplayConfigField: state['@common'].organizationConfig?.staffDisplayConfig?.[1] || ''
}))
export class CountersignSelector extends PureComponent {
  constructor(props) {
    super(props)
    const { node, action } = props
    this.state = { selectCountersigners: isSubmitterChoice(node, action) ? node.counterSigners : [] }
  }

  renderCounterComponent(desc, type) {
    const cls = classnames('content', { selectColor: type === 'select' })
    return (
      <div className="countersign-selector">
        <div className={cls}>{desc}</div>
      </div>
    )
  }

  getSelectCounterStr(desc) {
    const { node, action } = this.props
    const { selectCountersigners } = this.state
    const str = isSubmitterChoice(node, action) ? i18n.get('请选择会签人') : desc
    return selectCountersigners.length ? selectCountersigners.length + policy()[node.policy] : str
  }

  handleSelectCounters(node, selectCounterSigners) {
    const { getSelectCounterSigners, onChange } = this.props
    handleModifyCounterSigners(node, selectCounterSigners, result => {
      getSelectCounterSigners?.(node, result)
      onChange && onChange(result)
      this.setState({ selectCountersigners: result })
    })
  }

  handleOnChange = selectCountersigners => {
    this.setState({ selectCountersigners })
  }

  renderSelectCountersignView(desc, node) {
    const { selectCountersigners } = this.state
    return (
      <span className="select_counters">
        {selectCountersigners.length ? (
          <CountersignTooltip
            {...this.props}
            node={node}
            onChangeToolTip={this.handleOnChange}
            selectCountersigners={selectCountersigners}
            component={this.renderCounterComponent(desc)}
          />
        ) : (
          <span onClick={this.handleSelectCounters.bind(this, node, [])} data-testid="flow-countersign-select">
            {this.renderCounterComponent(desc, 'select')}
          </span>
        )}
      </span>
    )
  }

  renderCountersignView(desc) {
    const { node, customStaffs } = this.props
    const counterSigners = customStaffs || getCountersigners(node)
    return (
      <span>
        {counterSigners.length ? (
          <CountersignTooltip
            {...this.props}
            node={node}
            isEditConfig={false}
            component={this.renderCounterComponent(desc)}
          />
        ) : (
          this.renderCounterComponent(desc)
        )}
      </span>
    )
  }

  render() {
    const { style, node, action, noticeDom, customDesc } = this.props
    let desc = customDesc
    if (!customDesc) {
      desc = node?.counterSigners?.length
        ? i18n.get(node.policy === 'ALL' ? 'all-person' : 'any-person', { count: node.counterSigners.length })
        : i18n.get('connot-match-approver', { skippedType: skippedTypeMap()[node.skippedType] })
      desc = this.getSelectCounterStr(desc)
    }
    return (
      <div style={{ ...style }} className={styles['countersign-selector-wrapper']}>
        <div className="countersign-wrapper">
          {isSubmitterChoice(node, action)
            ? this.renderSelectCountersignView(desc, node)
            : this.renderCountersignView(desc)}
        </div>
        {node.nonMatchedDefines && node.nonMatchedDefines.length > 0 && (
          <span className="non-match-defines">{i18n.get('存在未匹配到的审批人')}</span>
        )}
        {!!noticeDom && noticeDom}
      </div>
    )
  }
}

export class CountersignTooltip extends PureComponent {
  constructor(props) {
    super(props)
    const { selectCountersigners, node } = props
    this.state = {
      visible: false,
      counterSigners: isSubmitterChoice(node) ? selectCountersigners : getCountersigners(node)
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.selectCountersigners !== nextProps.selectCountersigners && isSubmitterChoice(nextProps.node)) {
      this.setState({ counterSigners: nextProps.selectCountersigners })
    }
  }

  fnGetStateIMG(counterSigner) {
    if (counterSigner?.nonMatched) {
      return undefined
    }

    if (
      ['freeflow.agree', 'freeflow.receive', 'freeflow.send'].includes(counterSigner?.action) ||
      (!counterSigner?.action && counterSigner?.state === 'PROCESSED')
    ) {
      return FLOW_CONFIG_DONE
    }

    if (counterSigner?.action === 'freeflow.reject') {
      return FLOW_CONFIG_REJECT
    }

    return undefined
  }

  getNameDesc(counterSigner) {
    if (!counterSigner?.agreeType) return ''
    if (counterSigner.agreeType === 'ADMIN_SKIP_NODE_AUTO') {
      return i18n.get('（管理员跳过）')
    }

    if (
      counterSigner.agreeType === 'REJECT_FORCE_APPROVALS' ||
      counterSigner.agreeType === 'ROLLBACK_FORCE_APPROVALS' ||
      counterSigner.agreeType === 'EDIT_FORCE_APPROVALS'
    ) {
      return ''
    }

    let autoAgree = counterSigner.agreeType && counterSigner.agreeType !== 'NO_AUTO_AGREE'
    if (autoAgree) {
      return i18n.get('自动同意')
    }

    let isSkip = counterSigner.skippedType === 'APPROVER_NOT_FOUND'

    if (isSkip) {
      return i18n.get('自动跳过')
    }

    return ''
  }

  getCommentLog(flowLogs, line, node) {
    return findLast(flowLogs, o => {
      let signerId = line.signerId && line.signerId.id
      let nodeId = o.attributes && o.attributes.nodeId
      return nodeId && nodeId === node.id && o.operatorId && signerId === o.operatorId
    })
  }

  getSignature(flowLogs, node, signer) {
    const curLogs = flowLogs.filter(o => o.attributes && o.attributes.nodeId && o.attributes.nodeId === node.id)
    let curLog = curLogs.length > 0 ? curLogs.filter(o => o.operatorId && o.operatorId.id === signer.id)[0] : {}
    return curLog && curLog.attributes && curLog.attributes.autographImageId
  }

  fnGetName = (signer, crossCorpApprovers) => {
    const { signerId, id } = signer
    const cId = signerId || id
    const oId = isString(cId) ? cId : cId?.id
    const item = crossCorpApprovers.find(v => v.id === oId)
    return getStaffShowByConfig(item)
  }

  handleSelectCounters = (node, selectCounterSigners) => {
    this.setState({ visible: false })
    const { onChangeToolTip, getSelectCounterSigners, onChange } = this.props
    handleModifyCounterSigners(node, selectCounterSigners, result => {
      getSelectCounterSigners?.(node, result)
      onChange && onChange(result)
      onChangeToolTip && onChangeToolTip(result)
      this.setState({ counterSigners: result })
    })
  }

  renderCountersignStaff(counterSigner, index, node) {
    let signer = counterSigner?.signerId || counterSigner?.signer || {}
    const crossCorpApprove = get(node, 'config.crossCorpApprove')
    const crossCorpApprovers = get(node, 'config.crossCorpApprovers') || []
    const { crossCorpNode } = node
    if (crossCorpApprove) {
      signer = counterSigner
    }
    let stateIMG = this.fnGetStateIMG(counterSigner)
    let autoAgree = counterSigner?.agreeType && !NOAUTOAGREE.includes(counterSigner.agreeType)
    let isSkip = counterSigner?.skippedType === 'APPROVER_NOT_FOUND'
    const curLog = this.getCommentLog(this.props.flowLogs, counterSigner, node)
    let comment = curLog && curLog.attributes.comment
    let signature = this.props.flowLogs && this.getSignature(this.props.flowLogs, node, signer)
    const name = crossCorpNode ? this.fnGetName(signer, crossCorpApprovers) : getStaffShowByConfig(signer)
    return (
      <div key={index} className="item">
        <Avatar size="small" src={signer.avatar} />
        <div className="user">
          <div className="name-content">
            <div className="name">
              <Tooltip placement="bottom" title={name.length > 24 ? name : ''}>
                <span>{fnSubstrByCount(name, 24)}{this.getNameDesc(counterSigner)}</span>
              </Tooltip>
            </div>
            {signature && (
              <Tooltip placement="bottom" title={i18n.get('该签名影像会在打印单据时携带')}>
                <div className="user-signature">
                  {i18n.get('（')}
                  {i18n.get('签名')}
                  {i18n.get('）')}
                </div>
              </Tooltip>
            )}
            {autoAgree && (
              <Tooltip placement="bottom" title={agreeType()[counterSigner.agreeType]}>
                <img className="w-20 h-20" src={TIP_IMAGE} />
              </Tooltip>
            )}
            {isSkip && (
              <Tooltip placement="bottom" title={i18n.get('config-notFound')}>
                <img className="w-20 h-20" src={TIP_IMAGE} />
              </Tooltip>
            )}
            {comment && (
              <Tooltip placement="bottom" title={comment}>
                <img className="w-20 h-20 ml-10" src={COMMENT_TIP} />
              </Tooltip>
            )}
          </div>
          <div className="position">{counterSigner?.description}</div>
        </div>
        {stateIMG && <img className="done" src={stateIMG} />}
      </div>
    )
  }

  renderCountersignTooltip(node) {
    const { counterSigners } = this.state
    const { showLogView, onlyShowProcessing, customStaffs } = this.props
    let subTitle = ''
    if (showLogView && !customStaffs) {
      subTitle = node.policy === 'ALL' ? i18n.get('所有人同意即审批通过') : i18n.get('任意人同意即审批通过')
    }
    let counterSignersTemp = customStaffs || (counterSigners ? counterSigners : node.counterSigners)
    if (onlyShowProcessing && !customStaffs) {
      counterSignersTemp = counterSignersTemp.filter(v => v.state === 'APPROVING')
    }

    return (
      <div className={styles['countersign-tooltip-wrapper']}>
        <div className="content">
          {subTitle.length ? <div className="countersign-tooltip-subTitle">{subTitle}</div> : null}
          {counterSignersTemp.map((counterSigner, index) => {
            return this.renderCountersignStaff(counterSigner, index, node)
          })}
        </div>
      </div>
    )
  }

  renderTitle(node) {
    const { isEditConfig, showLogView, onlyShowProcessing } = this.props
    const { counterSigners = [] } = this.state
    let counterSignersTemp = isEditConfig ? counterSigners : node.counterSigners
    if (onlyShowProcessing) {
      counterSignersTemp = counterSignersTemp.filter(v => v.state === 'APPROVING')
    }
    let title = counterSignersTemp.length + policy()[node.policy]
    if (showLogView) {
      const policyMsg = node.policy === 'ALL' ? i18n.get('会签') : i18n.get('或签')
      title = i18n.get('{__k0}名成员{__k1}审批', { __k0: counterSignersTemp.length, __k1: policyMsg })
    }
    return (
      <div className={styles['countersign-tooltip-header']}>
        <div className="title">{title}</div>
        {isSubmitterChoice(node) && isEditConfig ? (
          <Button
            category="text"
            size="small"
            theme="highlight"
            icon={<OutlinedEditEdit />}
            onClick={this.handleSelectCounters.bind(this, node, counterSignersTemp)}
          >
            {i18n.get('修改')}
          </Button>
        ) : onlyShowProcessing ? null : (
          <div className="progress">{node.policy === 'ALL' && getCounterProgress(counterSignersTemp)}</div>
        )}
      </div>
    )
  }

  handleVisibleChange = visible => {
    this.setState({ visible })
  }

  render() {
    const { placement = 'bottomLeft', trigger = 'hover', customDesc } = this.props
    return (
      <Popover
        overlayClassName={styles['countersign-staff-tooltip-container']}
        trigger={trigger}
        placement={placement}
        open={this.state.visible}
        onOpenChange={this.handleVisibleChange}
        title={customDesc || this.renderTitle(this.props.node)}
        content={this.renderCountersignTooltip(this.props.node)}
      >
        {this.props.component}
      </Popover>
    )
  }
}
