/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/25 下午5:35.
 */
import React, { PureComponent } from 'react'
import { Select, ErrorBlock, Button } from '@hose/eui'
import TooltipEllipsis from '../../components/TooltipEllipsis'
import { EUIHighLighter } from '../EUIHighLighter'
import styles from './ComplexSelectV2.module.less'
const Option = Select.Option
import { findDOMNode } from 'react-dom'
import { IllustrationSmallNoBilling, OutlinedTipsAdd } from '@hose/eui-icons'
export default class ComplexSelect extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      searchValue: ''
    }
  }

  handleEdit = (line, e) => {
    e.preventDefault()
    e.stopPropagation()
    let { handleEdit } = this.props
    handleEdit && handleEdit(line)
  }
  handleDelete = (line, e) => {
    e.preventDefault()
    e.stopPropagation()
    const { handleDelete } = this.props
    handleDelete && handleDelete(line)
  }

  handleAdd = value => {
    let { handleAdd } = this.props
    handleAdd && handleAdd(value)
  }

  handleSearch = value => {
    this.setState({ searchValue: value })
  }

  handleChange = value => {
    // 选中元素后清空搜索内容
    this.setState({ searchValue: '' })
    // 调用原有的 onChange 回调
    const { onChange } = this.props
    onChange && onChange(value)
  }

  render() {
    let {
      list,
      title,
      disabled,
      emptyTitle,
      value,
      classNames,
      labelInValue = true,
      style = {},
      disabledEdit,
      allowClear = false,
      onChange,
      enableDelete = false,
      dropdownStyle,
      canEdit = true,
      mode,
      placeholder,
      showSearch = true,
      editTitle
    } = this.props

    const { searchValue } = this.state
    const isSearching = searchValue && searchValue.trim() !== ''
    if (list && list.length) {
      return (
        <div ref="complex-select" style={style}>
          <Select
            mode={mode}
            dropdownStyle={dropdownStyle}
            disabled={disabled}
            placeholder={placeholder || i18n.get('请选择')}
            value={value}
            showSearch={showSearch}
            optionFilterProp="label"
            className={classNames}
            dropdownClassName={styles['area-wrapper']}
            allowClear={allowClear}
            onChange={this.handleChange}
            onSearch={this.handleSearch}
            labelInValue={labelInValue}
            optionLabelProp="label"
            getPopupContainer={() => findDOMNode(this.refs['complex-select'])}
            dropdownRender={menu => (
              <>
                {menu}
                {list.length > 0 && !isSearching && (
                  <Button
                    category="text"
                    theme="highlight"
                    icon={<OutlinedTipsAdd />}
                    onClick={this.handleAdd}
                    className="add-condition"
                  >
                    {title}
                  </Button>
                )}
              </>
            )}
          >
            {list.map((line, idx) => (
              <Option key={idx} disabled={line.disabled} value={line.id} label={line.name}>
                <div className="line">
                  <div className="title">
                    <TooltipEllipsis title={line.name} placement="bottom">
                      {isSearching ? EUIHighLighter(line.name, searchValue) : line.name}
                    </TooltipEllipsis>
                  </div>
                  {!isSearching && (
                    <div className="button-wrapper">
                      {!disabledEdit && !line.disabled && (
                        <Button
                          category="text"
                          theme="highlight"
                          className="editbtn"
                          onClick={this.handleEdit.bind(this, line)}
                        >
                          {editTitle || i18n.get('编辑')}
                        </Button>
                      )}
                      {enableDelete && !line.disabled && (
                        <Button
                          category="text"
                          theme="highlight"
                          className="editbtn"
                          onClick={this.handleDelete.bind(this, line)}
                        >
                          {i18n.get('删除')}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </Option>
            ))}
          </Select>
          {this.props.children}
        </div>
      )
    }
    return (
      <div ref="complex-select" style={style}>
        <Select
          disabled={disabled}
          placeholder={placeholder || i18n.get('请选择')}
          value={value}
          className={classNames}
          labelInValue={labelInValue}
          optionLabelProp="label"
          getPopupContainer={() => findDOMNode(this.refs['complex-select'])}
          options={[]}
          notFoundContent={
            <ErrorBlock
              image={<IllustrationSmallNoBilling />}
              title={emptyTitle}
              secondaryButton={
                <Button
                  category="secondary"
                  theme="highlight"
                  size="middle"
                  icon={<OutlinedTipsAdd />}
                  onClick={this.handleAdd}
                >
                  {title}
                </Button>
              }
            />
          }
        />
      </div>
    )
  }
}
