@import '~@ekuaibao/web-theme-variables/styles/default';

.area-wrapper {
  z-index: 1000;
  :global {
    .eui-select-item {
      margin: 2px 4px;
    }
    .line {
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      font: var(--eui-font-body-r1);

      .title {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .button-wrapper {
        .editbtn {
          margin: 4px;
        }
      }
    }
    .add-condition {
      margin: 4px 8px 8px;
    }
  }
}
