/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/15.
 */

import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import styles from './DeptEditView.module.less'
import { NameCell } from '../name-cell'
const findDeptPath = api.invokeServiceAsLazyValue('@contacts:import:findDeptPath')

export default class DeptEditView extends PureComponent {
  state = {
    depts: []
  }

  componentWillMount() {
    const { departments = [] } = this.props
    this.fnSetDep(departments)
  }

  componentWillReceiveProps(nextProps) {
    let lastDept = this.props.departments
    let lastValue = this.props.value
    let nextDept = nextProps.departments
    let nextValue = nextProps.value
    if (JSON.stringify(lastDept) !== JSON.stringify(nextDept) || lastValue !== nextValue) {
      this.fnSetDep(nextDept)
    }
  }

  fnSetDep = departments => {
    const { deptMap } = this.props
    let list = departments
      .map(item => {
        let id = typeof item === 'object' ? item.id : item
        const label = findDeptPath.value(deptMap[id], deptMap)
        if (!!label) {
          return { id, label: findDeptPath.value(deptMap[id], deptMap) }
        }
      })
      .filter(e => !!e)
    this.setState({ depts: list })
  }

  handleChangeDef = () => {
    let { value, onChange } = this.props
    let { depts } = this.state
    value = typeof value === 'object' ? value.id : value
    api.open('@layout:ChangeDefModal', { depts, defaultDepartment: value }).then(defaultId => {
      onChange && onChange(defaultId)
    })
  }
  render() {
    let { value, isRead } = this.props
    let { depts } = this.state
    value = typeof value === 'object' ? value.id : value
    const isWeixin = window.__PLANTFORM__ === 'WEIXIN' || window.isInWeComISV
    return (
      <div className={styles['dept-edit-view']}>
        {depts.map((v, index) => {
          return (
            <div key={index}>
              <span className="mr-5">
                <NameCell type="department" id={v.id} name={v.label} />
              </span>
              {v.id === value && <span className={styles['tag']}>{i18n.get('默认')}</span>}
            </div>
          )
        })}
        {!isRead && !isWeixin && !!this.props.defaultDepartmentEditable && <a onClick={this.handleChangeDef}>{i18n.get('修改默认')}</a>}
      </div>
    )
  }
}
