/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/6/20.
 */
import React from 'react'
import style from './member-edit-style.module.less'
import { Input, Form, Col, Tooltip, Row, Select, Checkbox, Icon, Radio } from 'antd'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import { permissionName, permissionDescribe } from '../configure/config'
import { EnhanceConnect } from '@ekuaibao/store'
import DeptEditView from './DeptEditView'
import { app as api } from '@ekuaibao/whispered'
import { remove, get } from 'lodash'
import { convertStaffFieldsToTemplate, setInputDisabled } from './util'
import SrcMapComponents from './SrcMapComponents'
import EnhanceFormCreate from '../enhance/enhance-form-create'
import TagSelector from '../../elements/tag-selector'
import { getV, isFullPhoneNumber, isEmail } from '@ekuaibao/lib/lib/help'
import { isObject } from '@ekuaibao/helpers'
import { showModal } from '@ekuaibao/show-util'
import { Dynamic } from '@ekuaibao/template'
import Loading from '@ekuaibao/loading'
import { editable as elements } from '../../components'
import MessageCenter from '@ekuaibao/messagecenter'
import moment from 'moment'
import classnames from 'classnames'
import { Fetch } from '@ekuaibao/fetch'
import { getDisplayName } from '../utilFn'
import { NameCell } from '../name-cell'
const StaffCredential = api.require('@elements/StaffCredential')
const { isExternalCharge } = api.require('@lib/charge-util');
const IMAGE_TIP = api.require('@images/custom-plan-tip.svg')
const RadioGroup = Radio.Group
const credentialOptions = {
  type: {
    "身份证": 'ID_CARD',
    "护照": 'PASSPORT',
    "军人证": 'SERVICEMAN_CARD',
    "港澳居民来往内地通行证": 'HONG_KONG_AND_MACAO_GO',
    "台湾居民来往内地通行证": 'TAIWAN_GO',
    "外国人永久居留身份证": 'FOREIGNERS_LIVE',
    "港澳台居民居住证": 'HONG_KONG_MACAO_TAIWAN',
    "大陆居民来往台湾通行证": 'MAINLAND_TO_TAIWAN',
    "港澳通行证": 'HONG_KONG_AND_MACAO_PASS',
    "其他": 'OTHER',
  },
  gender: {
    "男": "MALE",
    "女": "FEMALE"
  }
}

function create(T) {
  return Form.create({
    onValuesChange(props, changedValues) {
      setTimeout(() => {
        //先走的  onValuesChange
        //再运行  setFieldValue
        props.bus.emit('dynamic:value:changed', changedValues)
      }, 0)
    }
  })(T)
}

const findDeptPath = api.invokeServiceAsLazyValue('@contacts:import:findDeptPath')

const FormItem = Form.Item
const { TextArea } = Input
const validators = {
  rule(type, message) {
    return { type: type, message: message }
  },

  required(message) {
    return { required: true, message: message }
  },

  checkPhone(globalRoaming) {
    return (rule, value, callback) => {
      if (value && globalRoaming === "+86" && !/^1[3456789]\d{9}$/.test(value)) {
        return callback(new Error(i18n.get('手机格式不正确')))
      }
      if (value && globalRoaming !== "+86" && !isFullPhoneNumber(value)) {
        return callback(new Error(i18n.get('手机格式不正确')))
      }
      return callback()
    }
  },

  checkEmail() {
    return (rule, value, callback) => {
      if (!value || isEmail(value)) {
        callback()
      } else {
        callback(new Error(i18n.get('请输入正确的邮箱地址')))
      }
    }
  },

  checkMemberCode() {
    if (window.__PLANTFORM__ === 'DING_TALK' || window.__PLANTFORM__ === 'FEISHU') {
      return true
    }
    return (rule, value, callback) => {
      if (!value || /^[\u4e00-\u9fa5\w\./-@_-]+$/.test(value)) {
        callback()
      } else {
        callback(new Error(i18n.get('员工编号只能包含汉字、数字、字母、@、-、_、.')))
      }
    }
  },

  checkBankCardNum() {
    return (rule, value, callback) => {
      if (!value || /^\d+$/.test(value)) {
        callback()
      } else {
        callback(new Error(i18n.get('银行卡号不正确')))
      }
    }
  },

  max(label, max) {
    return { max, message: label + i18n.get('不能超过{max}个字', { max }) }
  }
}

function initializeFormProps(form, data) {
  return (field, validators, args) =>
    form.getFieldDecorator(field, {
      initialValue: data[field],
      rules: validators,
      ...args
    })
}

@EnhanceConnect(state => ({
  user: state['@common'].userinfo.data,
  editableFields: state['@common'].editableFields,
  CSCPower: state['@common'].powers.CivilServiceCard,
  baseDataProperties: state['@common'].globalFields.data,
  roleDefsAndDept: state['@contacts'].roleDefsAndDept,
  validateError: state['@bills'].validateError
}))
@EnhanceFormCreate()
export default class BasicInfoForm extends React.Component {
  constructor(props) {
    super(props)
    this.bus = new MessageCenter()
    props.getResult(this.getResult.bind(this))
    this.state = {
      isSyncWorkId: props.isSyncWorkId,
      editAbleFields: props.editableFields || [],
      userInfo: props.userInfo,
      'defaultDepartment.form.legalEntity': '',
      'defaultDepartment.form.costCenter': '',
      deptList: this.getDeptList((props.userInfo && props.userInfo.departments) || []) || [],
      defaultDepartment: (props.userInfo && props.userInfo.defaultDepartment) || '',
      template: [],
      globalFields: [],
      globalRoaming: props.userInfo?.globalRoaming || '+86',
      countryList: [],
      maxModifyCount: {}, // 各证件限制最大修改次数
      appNames: '',
      staffRoles: [],
    }
  }

  get sourceChannel() {
    const { user } = this.props
    return user?.staff?.corporation?.sourceChannel
  }

  initStaffRolesInfo = async () => {
    const data = await api.invokeService('@role:get:role:by:staffId', this.props.userInfo.id)
    this.setState({
      staffRoles: data.items || []
    })
  }

  async componentDidMount() {
    api.dataLoader('@common.editableFields').load()
    api.on('refresh:form:value', this.refreshFormValue)
    const resp = await Fetch.GET("/api/v1/basedata/country")
    this.setState({
      countryList: resp?.items
    })
    const { isExternalMember, isSyncWorkId, disableKeys, isFieldDisabled} = this.props
    if (window.__PLANTFORM__ === 'WEIXIN' && isSyncWorkId === undefined) {
      api.invokeService('@contacts:get:Auto').then(res => {
        this.setState({ isSyncWorkId: res?.value?.syncWorkId })
      })
    }

    this.initStaffRolesInfo()

    const res = await api.invokeService('@contacts:get:staff:dynamic:property')
    let isAdmin = false
    let { currentUser } = this.props
    if (currentUser.permissions.findIndex(e => e === 'SYS_ADMIN') !== -1 || currentUser.permissions.findIndex(e => e === 'CONTACTS_MANAGE') !== -1) {
      isAdmin = true
    }
    const template = convertStaffFieldsToTemplate({ fields: res.items, _isAdmin: isAdmin, disableKeys, isFieldDisabled })
    // 先过滤掉证件类型，（证件类型在最下面展示了）
    const filterCdtTpl = template.filter(e => e.name !== "certificate")
    if (!isExternalMember) {
      this.setState({ template: filterCdtTpl, globalFields: res.items, loading: false }, () => {
        this.findCostCenter(true)
      })
    } else {
      this.setState({ globalFields: res.items, loading: false })
    }
    this.getMaxModifyCount()
    this.getAppNamesStr()
  }

  componentWillUnmount() {
    api.un('refresh:form:value', this.refreshFormValue)
  }

  componentWillReceiveProps(nextProp) {
    if (this.props.editableFields !== nextProp.editableFields) {
      this.setState({
        editAbleFields: nextProp.editableFields || []
      })
    }
    if (this.props.userInfo !== nextProp.userInfo) {
      if (this.props.form) this.props.form.resetFields()
      if (this.props.userInfo.departments !== nextProp.userInfo.departments) {
        this.setState({ deptList: this.getDeptList(nextProp.userInfo.departments || []) || [] })
      }
      this.setState(
        {
          globalRoaming: nextProp.userInfo?.globalRoaming || '+86',
          defaultDepartment: (nextProp.userInfo && nextProp.userInfo.defaultDepartment) || ''
        },
        () => {
          this.findCostCenter()
          this.getAppNamesStr()
        }
      )
    }
  }

  /**
   * 获取用户的应用授权信息
   * @returns {Promise<void>}
   */
  async getAppNamesStr() {
    const appList = await Fetch.GET(`/api/charge/v2/apps`);
    const _userApps = await Fetch.GET(`/api/organization/staffs/auth/apps/[${[this.props.userInfo.id]}]`)
    const names = [];
    appList?.items?.forEach((app) => {
      // 过滤过期的应用
      if (app.expireTime < Date.now()) {
        return
      }
      _userApps?.items?.forEach((item) => {
        Object.values(item)?.[0].forEach((code) => {
          if (app.powerCode === code) {
            names.push(app.appName)
          }
        })
      })
    })
    this.setState({
      appNames: names.join(',')
    })
  }

  async getMaxModifyCount() {
    // 获取各证件限制最大修改次数
    const res = await Fetch.GET('/api/v1/organization/staffs/certificate/maxModifyCount')
    this.setState({
      maxModifyCount: res?.value ?? {}
    })
  }

  refreshFormValue = data => {
    const { form } = this.props
    api.emit('refresh:account:user:info')
    form.setFieldsValue(data)
    this.findCostCenter()
  }

  getDeparts = values => {
    //后期优化
    let defaultDepartment = values.defaultDepartment
    let departments = this.state.deptList
    if (window.__PLANTFORM__ === 'APP' || window.__PLANTFORM__ === 'MC' || this.props.isExternalMember) {
      defaultDepartment = this.state.defaultDepartment
    }
    let defaultDepartmentId = typeof defaultDepartment === 'object' ? defaultDepartment.id : defaultDepartment
    let departmentList = departments.map(item => item.id)
    return {
      defaultDepartment: defaultDepartmentId,
      departments: departmentList
    }
  }
  findCostCenter = async (init = false) => {
    if (init && this.props.type === 'add') {
      const currentDept = this.props?.currentDept
      const legalEntity = currentDept?.form?.legalEntity
      const costCenter = currentDept?.form?.costCenter
      if (legalEntity || costCenter) {
        this.setState({
          'defaultDepartment.form.legalEntity': legalEntity,
          'defaultDepartment.form.costCenter': costCenter
        })
      }
    } else {
      const deptMap = getV(this, 'props.deptMap', {})
      let defaultDepartment = this.state.defaultDepartment
      defaultDepartment = typeof defaultDepartment === 'object' ? defaultDepartment : deptMap[defaultDepartment]
      const legalEntity = get(defaultDepartment, 'form.legalEntity', '')
      const costCenter = get(defaultDepartment, 'form.costCenter', '')
      if (legalEntity || costCenter) {
        this.setState({
          'defaultDepartment.form.legalEntity': legalEntity,
          'defaultDepartment.form.costCenter': costCenter
        })
      }
    }
  }

  getCredentialResult = (composeData) => {
    let certificate = []
    const result = Object.keys(composeData)
    for (const item of result) {
      const credentialItem = composeData[item]
      if (credentialItem?.type && credentialItem?.number) {
        certificate.push(credentialItem)
      }
    }
    return certificate
  }

  formatCredentialType = (credentialField, value) => {
    const exitPropperty = credentialOptions?.[credentialField]

    if (credentialField === 'validDate' || credentialField === 'birthDate') {
      return moment(value)
        .startOf('day')
        .valueOf()
    }
    // if (exitPropperty) {
    //   return exitPropperty?.[value]
    // }
    return value
  }

  formatCredentialField = (values) => {
    let composeData = {}
    if (values && isObject(values) && Object.keys(values)?.length > 0) {
      const keys = Object.keys(values)
      for (const item of keys) {
        if (item.includes('@')) {
          const prefix = item.split('@')[0]
          const credentialField = item.split('@')[1]
          const formatValue = this.formatCredentialType(credentialField, values[item])
          composeData[prefix] = {
            [credentialField]: formatValue,
            ...composeData[prefix]
          }
          delete values[item]
        }
      }
    }
    const certificateLisst = this.getCredentialResult(composeData)
    const certificate = certificateLisst.map(e => {
      if (e.extended) {
        delete e.validDate
      }
      return e
    })

    return { certificate }
  }

  getResult() {
    let { validateFieldsAndScroll } = this.props.form
    let { editAbleFields } = this.state
    let fields = {}
    const { isExternalMember } = this.props
    return new Promise((resolve, reject) => {
      validateFieldsAndScroll({ editAbleFields, ...fields }, (errors, values) => {
        if (!!errors) {
          reject()
          return
        }
        values.globalRoaming = this.state.globalRoaming
        values.maxModifyCount = this.state.maxModifyCount
        if (isExternalMember) {
          const credentialStaffCustomForm = this.formatCredentialField(values)
          values = {
            ...values,
            ...this.getDeparts(values),
            staffCustomForm: {
              ...credentialStaffCustomForm
            }
          }
          resolve(values)
          return false
        }
        // 处理动态表单数据
        this.bus.getValueWithValidate().then(staffCustomForm => {
          let userinfo = this.props.userInfo
          const baseStaffCustomForm = this.fnHandleStaffCustomForm(staffCustomForm)
          const credentialStaffCustomForm = this.formatCredentialField(values)
          values.id = userinfo.id
          values.email = values.email || ''
          values.bankCardNum = values.bankCardNum ? [values.bankCardNum] : []
          values = {
            ...values,
            ...this.getDeparts(values),
            staffCustomForm: { ...baseStaffCustomForm, ...credentialStaffCustomForm }
          }
          resolve(values)
        })
      })
    })
  }

  fnHandleStaffCustomForm = (staffCustomForm = {}) => {
    return Object.keys(staffCustomForm).reduce((result, key) => {
      const value = staffCustomForm[key]
      result[key] = isObject(value) ? (value.id ? value.id : value) : key.indexOf('.') > -1 ? '' : value
      return result
    }, {})
  }

  editable = item => {
    const { isExternalMember, cannotEditStaff, canEditDept = true, type, disableKeys, isFieldDisabled } = this.props
    // 以后判断人员字段是否可编辑使用isFieldDisabled传入的方法判断！！！！
    if (isFieldDisabled) {
      return !isFieldDisabled(item)
    }
    if (cannotEditStaff) return false
    // if (isExternalMember) return true
    // 来自需求，个人信息页面不允许编辑字段
    if (disableKeys?.includes(item)) {
      return false
    }

    const editAbleFields = this.state.editAbleFields
    if (type !== 'add' && item === 'departments' && !canEditDept) {
      return false
    }
    if (window.__PLANTFORM__ === 'APP' && item === 'departments' && this.props.type !== 'add') {
      let { currentUser } = this.props
      let isAdmin = false
      if (currentUser.permissions.findIndex(e => e === 'SYS_ADMIN') !== -1 || currentUser.permissions.findIndex(e => e === 'CONTACTS_MANAGE') !== -1) {
        isAdmin = true
      }
      return isAdmin
    }
    if (window.__PLANTFORM__ === 'APP' && item === 'cellphone' && this.props.type !== 'add') {
      return false
    }
    if (window.__PLANTFORM__ === 'APP' && item === 'email' && this.props.type !== 'add') {
      return false
    }
    if (window.__PLANTFORM__ !== 'APP' && item === 'name' && isExternalMember) {
      return true
    }
    if (window.__PLANTFORM__ !== 'APP' && item === 'code' && isExternalMember) {
      return true
    }
    if (window.__PLANTFORM__ !== 'APP' && item === 'departments' && isExternalMember) {
      return true
    }
    return !!~editAbleFields.indexOf(item)
  }

  placeHolder = item => {
    if (this.editable(item)) {
      return
    }
    if (window.__PLANTFORM__ === 'DING_TALK') {
      return i18n.get('（需管理员在钉钉设置）')
    } else if (window.__PLANTFORM__ === 'FEISHU') {
      return i18n.get('（需管理员在飞书设置）')
    } else {
      return
    }
  }

  renderPermissionView(permissions) {
    return (
      <div>
        {permissions.map((item, index) => {
          return (
            <Tooltip
              arrowPointAtCenter
              key={index}
              overlayClassName="userinfo-select-item-tooltip"
              title={permissionDescribe(item)}
            >
              {index === permissions.length - 1
                ? permissionName(item)
                : i18n.get(`{__k0}、`, { __k0: permissionName(item) })}
            </Tooltip>
          )
        })}
      </div>
    )
  }

  handleSelectDept = async () => {
    let { deptList } = this.state
    const { isExternalMember, isNewContactList } = this.props
    let departmentIds = (deptList && deptList.map(item => item.id)) || []
    const checkedList = await api.open('@organizationManagement:SelectStaff', {
      title: i18n.get('选择部门'),
      multiple: true,
      data: [
        {
          type: 'department',
          checkIds: departmentIds
        }
      ],
      fetchDataSourceAction: {
        department: isExternalMember ? api.invokeService('@organizationManagement:get:external:departments') : api.invokeService('@organizationManagement:get:visibility:departments')
      }
    })
    const departmentData = checkedList.find(item => item.type === 'department')
    const departments = departmentData?.checkList
    this.setState({ deptList: departments })
  }

  handleTagChange(_, deleteItem) {
    let { deptList } = this.state
    remove(deptList, id => id.id === deleteItem.id)
  }

  getDeptList(departs) {
    const deptMap = getV(this, 'props.deptMap', {})
    let list = departs
      .map(item => {
        let nitem = typeof item === 'object' ? item : deptMap[item]
        return nitem
      })
      .filter(item => !!item)
    return list
  }
  handleConfirmChange = (e) => {
    session.set('_donotshowmodal', e.target.checked)
  }

  handleChangeDef = defaultDepartment => {
    const { coverDefaultDepartment, userInfo } = this.props
    let { deptList } = this.state
    const _donotshowmodal = session.get('_donotshowmodal')
    const callback = () => {
      api
        .open('@layout:ChangeDefModal', {
          depts: deptList,
          defaultDepartment: (defaultDepartment && defaultDepartment.id) || ''
        })
        .then(defaultId => {
          this.setState({ defaultDepartment: defaultId }, _ => {
            this.findCostCenter()
          })
        })
    }
    if (!_donotshowmodal && !userInfo.external && !coverDefaultDepartment && this.sourceChannel === 'DT') {
      return showModal.info({
        content: (
          <div className={style['modal-content']}>
            <p className={style['tip-info']}>{i18n.get('该员工信息的默认部门字段在易快报里修改后，第三方系统同步时，将不会再同步该员工的默认部门')}</p>
            <Checkbox onChange={this.handleConfirmChange}>
              {i18n.get('不再提示')}
            </Checkbox>
          </div>
        ),
        okText: i18n.get('知道了'),
        onOk: () => {
          callback()
        },
      })

    } else {
      return callback()
    }

  }

  renderComponent = (type, userInfo) => {
    const { source, userAccountInfo, checkConsistent } = this.props
    return (
      <SrcMapComponents
        type={type}
        source={source}
        userAccountInfo={userAccountInfo}
        userInfo={userInfo}
        checkConsistent={checkConsistent}
      />
    )
  }
  initCommunication = type => {
    const { userAccountInfo, source, userInfo } = this.props
    let str = ''
    if (window.__PLANTFORM__ === 'APP') {
      str = source === 'userInfo' ? get(userAccountInfo, type, '') : get(userInfo, type, '')
    } else {
      str = get(userInfo, type, '')
    }
    return str
  }


  credentialedit = () => {
    const { globalFields } = this.state
    const credential = globalFields?.find(e => e.name === 'certificate')
    return { canEditCredential: credential?.active || false, optional: false }
  }

  handleGlobRoamingChange = (e) => {
    this.setState({
      globalRoaming: e.target.value
    }, () => {
      const value = this.props.form.getFieldValue('cellphone')
      this.props.form.setFieldsValue?.({ cellphone: value })
    })
  }

  get email() {
    const showEmail = this.initCommunication('showEmail')
    const email = this.initCommunication('email')
    return !!showEmail ? showEmail : email
  }


  render() {
    let {
      cannotEditStaff,
      userInfo = {},
      deptMap = {},
      permissions,
      className,
      CSCPower,
      isEditSelf,
      type,
      isExternalMember,
      modifyCardBySelf, // 是否允许员工修改公务卡号
      credentialList
    } = this.props
    let staffCustomForm = {
      ...userInfo.staffCustomForm,
      'defaultDepartment.form.legalEntity': this.state['defaultDepartment.form.legalEntity'],
      'defaultDepartment.form.costCenter': this.state['defaultDepartment.form.costCenter']
    }
    const { source } = this.props
    const { canEditCredential, optional } = this.credentialedit()
    if (!isExternalMember && !userInfo) {
      return null
    }
    let { deptList, defaultDepartment, isSyncWorkId, countryList, maxModifyCount, appNames, staffRoles } = this.state
    let { name, enName, cellphone, email, showEmail, note, roles, bankCardNums, nickName, authState, external } = userInfo
    if (authState === undefined) {
      authState = true
    }
    // 如果是编辑状态，并且是外部人员，那么就是默认停用
    if(type === 'add' && isExternalMember) {
      authState = false
    }

    if (external === undefined) {
      external = false
    }
    let code = userInfo.code ? userInfo.code : this.placeHolder('code') || userInfo.code
    let initProps = ''
    let CSCNumberField = ''
    let permCodes = ''
    let sname = ''
    let isAdmin = false
    deptList = deptList.map(item => {
      let nitem = typeof item === 'object' ? item : deptMap[item]
      nitem.fullname = findDeptPath.value(item, deptMap)
      return nitem
    })
    defaultDepartment = typeof defaultDepartment === 'object' ? defaultDepartment : deptMap[defaultDepartment]
    if (defaultDepartment) {
      let fullname = findDeptPath.value(defaultDepartment, deptMap)
      defaultDepartment.fullname = fullname ? fullname : ''
    }

    // 这里补充应用授权信息
    if (isExternalMember) {
      initProps = initializeFormProps(this.props.form, {
        name,
        enName,
        code,
        cellphone,
        email: showEmail ?? email,
        note,
        defaultDepartment,
        deptList,
        nickName,
        authState,
        external
      })
    } else {
      let bankCardNum = (bankCardNums && bankCardNums.values.length && bankCardNums.values[0].bankCardNum) || ''
      permCodes = permissions.map(e => {
        return typeof e === 'string' ? e : e.permission
      })

      initProps = initializeFormProps(this.props.form, {
        name,
        enName,
        code,
        cellphone: this.initCommunication('cellphone'),
        email: this.email,
        note,
        roleDeptRelations: roles,
        defaultDepartment,
        deptList,
        bankCardNum,
        nickName,
        authState,
        external
      })
      let disabled = !this.editable('bankCardNum') || isEditSelf
      CSCNumberField = CSCPower ? (
        <FormItem label={i18n.get('公务卡号') }>
          {initProps('bankCardNum', [validators.checkBankCardNum(), validators.max(i18n.get('公务卡号'), 30)])(
            <Input disabled={modifyCardBySelf ? !modifyCardBySelf : disabled} />
          )}
        </FormItem>
      ) : null
    }
    let { currentUser } = this.props
    if (currentUser.permissions.findIndex(e => e === 'SYS_ADMIN') !== -1 || currentUser.permissions.findIndex(e => e === 'CONTACTS_MANAGE') !== -1) {
      isAdmin = true
    }
    if (defaultDepartment) {
      sname = defaultDepartment.fullname ? defaultDepartment.fullname : defaultDepartment.name
    }

    const disableCellPhone = window.__PLANTFORM__ === 'APP' || window.__PLANTFORM__ === 'MC'
      ? cannotEditStaff || setInputDisabled(source, 'cellphone', userInfo)
      : !this.editable('cellphone')

    const selectBefore = (
      <div className={classnames(`${style['custom-select-wrapper']}`)}>
        <span className={classnames({ "disabled_cellphone": disableCellPhone })}>
          {this.state.globalRoaming}
          <Icon type="down" style={{ fontSize: 12, marginLeft: 4, color: "#9c9c9c" }} />
        </span>
        <select size="large" value={this.state.globalRoaming} disabled={disableCellPhone} className={`select-before ${style['custom-select']}`} onChange={this.handleGlobRoamingChange}>
          {countryList.map(el => <option key={el.code} value={`+${el.code}`}>{`${el.name}(+${el.code})`}</option>)}
        </select>
      </div>
    );

    let showAuthStateItem = false


    if(isExternalMember) {
      if(authState || isExternalCharge()) {
        showAuthStateItem = true
      }
    } else {
      showAuthStateItem = true
    }

    return (
      <div className={`${style['userinfo-select']} ${className}`}>
        <Form layout="vertical" className="form-wrap" >
          <FormItem label={i18n.get('员工类型') }>
            {initProps('external')(
              <RadioGroup disabled={true}>
                <Radio value={false}>{i18n.get('内部员工')}</Radio>
                <Radio value={true}>{i18n.get('外部员工')}</Radio>
              </RadioGroup>
            )}
          </FormItem>
          <FormItem label={i18n.get('真实姓名') }>
            {window.isInWeComISV ? <NameCell type="user" id={userInfo.id} name={userInfo.name} /> : initProps('name', [
              { required: true, whitespace: true, message: i18n.get('请填写真实姓名') },
              validators.max(i18n.get('真实姓名'), 100)
            ])(<Input size="large" disabled={!this.editable('name')} placeholder={i18n.get('请填写真实姓名')} />)}
          </FormItem>
          <FormItem label={i18n.get('英文姓名')}>
            {initProps('enName', [
              { whitespace: true, message: i18n.get('请填写英文姓名') },
              validators.max(i18n.get('英文姓名'), 500)
            ])(<Input size="large" disabled={!this.editable('enName')} placeholder={i18n.get('请填写英文姓名')} />)}
          </FormItem>
          <FormItem label={i18n.get('别名') }>
            {initProps('nickName', [
              { required: false, whitespace: true, message: i18n.get('请填写别名') },
              validators.max(i18n.get('别名'), 40)
            ])(<Input disabled={!this.editable('nickName')} size="large" placeholder={i18n.get('请填写别名')} />)}
          </FormItem>
          <FormItem
            label={
              <div className="horizontal">
                {i18n.get('工号') }
                {isSyncWorkId && (
                  <Tooltip placement="top" title={i18n.get('数据来自企业微信，易快报内不支持修改')}>
                    <img className="ml-4" src={IMAGE_TIP} />
                  </Tooltip>
                )}
              </div>
            }
          >
            {initProps('code', [validators.checkMemberCode(), validators.max(i18n.get('工号'), 150)])(
              <TextArea
                className={this.editable('code') ? '' : 'color-gray-7'}
                disabled={!this.editable('code') || isSyncWorkId}
                autosize={true}
                size="large"
                placeholder={i18n.get('请输入工号')}
              />
            )}
          </FormItem>
          <FormItem label={i18n.get('手机') }>
            <div className="show-other item-with-addon">
              {initProps('cellphone', [validators.checkPhone(this.state.globalRoaming), validators.max(i18n.get('手机号'), 20)])(
                <Input
                  addonBefore={selectBefore}
                  placeholder={i18n.get('请输入手机号')}
                  disabled={disableCellPhone}
                  size="large"
                />
              )}
              {this.renderComponent('cellphone', userInfo)}
            </div>
          </FormItem>
          {!isExternalMember && CSCNumberField}
          <FormItem label={i18n.get('邮箱') }>
            <div className="show-other">
              {initProps('email', [validators.checkEmail(), validators.max(i18n.get('邮箱'), 50)])(
                <Input
                  placeholder={i18n.get('请输入邮箱')}
                  size="large"
                  disabled={
                    window.__PLANTFORM__ === 'APP' || window.__PLANTFORM__ === 'MC'
                      ? cannotEditStaff || setInputDisabled(source, 'email', userInfo)
                      : !this.editable('email')
                  }
                />
              )}
              {this.renderComponent('email', userInfo)}
            </div>
          </FormItem>
          <FormItem label={i18n.get('备注') }>
            {initProps('note', [validators.max(i18n.get('备注'), 100)])(
              <TextArea size="large" placeholder={i18n.get('请填写备注')} disabled={!this.editable('note')} autosize={true} />
            )}
          </FormItem>
          {!isExternalMember ? (
            window.__PLANTFORM__ === 'APP' || window.__PLANTFORM__ === 'MC' ? (
              <div className="dept-item">
                <Row className="userinfo-select-item" style={{ paddingBottom: 5 }} span={24}>
                  <Col className="userinfo-select-item-label">{i18n.get('部门')}</Col>
                  <Col>
                    <TagSelector
                      editable={this.editable('departments')}
                      value={deptList}
                      className="selectDept"
                      onClick={this.handleSelectDept.bind(this)}
                      onChange={this.handleTagChange.bind(this)}
                      placeholder={i18n.get('请选择部门')}
                    />
                  </Col>
                </Row>
                <Row className="userinfo-select-item change-dept-btn" span={24}>
                  <Col />
                  <Col>
                    <div>
                      {`${i18n.get('默认部门')}:${(defaultDepartment && sname) || ''} `}
                      {isAdmin && !cannotEditStaff && this.editable('defaultDepartment') && (
                        <a onClick={this.handleChangeDef.bind(this, defaultDepartment)}>{i18n.get('修改')}</a>
                      )}
                    </div>
                  </Col>
                </Row>
              </div>
            ) : (
              <FormItem label={i18n.get('部门') }>
                {initProps('defaultDepartment')(<DeptEditView defaultDepartmentEditable={this.editable('defaultDepartment')} deptMap={deptMap} departments={deptList} />)}
              </FormItem>
            )
          ) : (
            <div className="dept-item">
              <Row className="userinfo-select-item" style={{ paddingBottom: 5 }} span={24}>
                <Col className="userinfo-select-item-label">{i18n.get('部门')}</Col>
                <Col>
                  <TagSelector
                    editable={this.editable('departments')}
                    value={deptList}
                    className="selectDept"
                    onClick={this.handleSelectDept.bind(this)}
                    onChange={this.handleTagChange.bind(this)}
                    placeholder={i18n.get('请选择部门')}
                  />
                </Col>
              </Row>
              <Row className="userinfo-select-item" span={24}>
                <Col />
                <Col>
                  <div>
                    {`${i18n.get('默认部门')}:${(defaultDepartment && sname) || ''} `}
                    {isAdmin && this.editable('defaultDepartment') && <a onClick={this.handleChangeDef.bind(this, defaultDepartment)}>{i18n.get('修改')}</a>}
                  </div>
                </Col>
              </Row>
            </div>
          )}
          {!isExternalMember && !this.state.loading ? (
            <Dynamic
              bus={this.bus}
              create={create}
              layout="vertical"
              loading={Loading}
              template={this.state.template}
              value={staffCustomForm}
              elements={elements}
              baseDataProperties={this.props.baseDataProperties}
              large={true}
            />
          ) : null}
          {'add' === type || isExternalMember ? null : (
            <Row className="userinfo-select-item">
              <Col className="userinfo-select-item-label">{i18n.get('权限')}</Col>
              <Col className="userinfo-select-item-content">{permCodes.length > 0 ? this.renderPermissionView(permCodes) : i18n.get('无')}</Col>
            </Row>
          )}
          {'add' === type || isExternalMember ? null : (
            <Row className="userinfo-select-item">
              <Col className="userinfo-select-item-label">{i18n.get('角色')}</Col>
              <Col className="userinfo-select-item-content">
                {staffRoles.length > 0
                  ? staffRoles.map((item, index) => {
                    const name = getDisplayName({ name: item.roleDefName, enName: item.roleDefEnName})
                    return item.properties.length ? item.properties.map((property) => {
                      return (
                        <div className="dis-f mb-5" key={`${item.roleDefId}${property.value}`}>
                          <div className="flex-s-0 mr-5">{name} </div>
                          <div className="color-gray-7">{getDisplayName({ name: property.fullPath, enName: property.fullEnNamePath })}</div>
                        </div>
                      )
                    }) : <div className="dis-f mb-5" key={item.roleDefId}>
                        <div className="flex-s-0 mr-5">{name} </div>
                      </div>
                  })
                  : i18n.get('无')}
              </Col>
            </Row>
          )}
          {(!isExternalMember || isExternalCharge()) && (
            <>
              <Row className="userinfo-select-item">
                <Col className="userinfo-select-item-label">{i18n.get('应用授权')}</Col>
                <Col className="userinfo-select-item-content">{appNames || i18n.get('无')}</Col>
              </Row>
            </>
          )}
          {showAuthStateItem && (
            <>
              <FormItem label={i18n.get('激活状态')}>
                {initProps('authState')(
                  <RadioGroup disabled={!this.editable('authState')}>
                    <Radio value={true}>{i18n.get('激活')}</Radio>
                    <Radio value={false}>{i18n.get('停用_Staff')}</Radio>
                  </RadioGroup>
                )}
              </FormItem>
            </>
          )}
          {
            canEditCredential && (
              <FormItem label={<>
              <span>{i18n.get('证件信息及证件号码')}</span>
                <Tooltip title={i18n.get('仅限修改一次，保存后不可撤销')}>
                  <OutlinedTipsInfo style={{ fontSize: 16, marginLeft: 5, marginBottom: -1 }} />
                </Tooltip>
              </>} className='credential-info'>
                <StaffCredential onRef={ref => this.props?.getInitCredential(ref)} form={this.props.form} countryList={countryList} credentialList={credentialList} optional={optional} maxModifyCount={maxModifyCount} />
              </FormItem>
            )
          }
        </Form>
      </div>
    )
  }
}


