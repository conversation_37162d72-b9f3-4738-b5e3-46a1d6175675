import styles from './InvoiceItem.module.less'
import React, { PureComponent, useState, useEffect } from 'react'
import moment from 'moment'
import { Button, Tooltip, message } from 'antd'
import InvoiceCard from '../../elements/invoice-card'
import { app, app as api } from '@ekuaibao/whispered'
import { formatInvoiceData, invoiceTypeFields } from './FormatInvoiceData'
import EKBIcon from '../../elements/ekbIcon'
import Money from '../puppet/Money'
import SVG_INVOICE_TEXT from '../../images/icon-file-text.svg'
import SVG_INVOICE_TOKEN from '../../images/qkl.svg'
import SVG_INVOICE_PDF from '../../images/icon-file-pdf.svg'
import SVG_XML from '../../images/SVG_Xml.svg'
import SVG_WARNING from '../../images/warning.svg'
import { groupBy, get, cloneDeep } from 'lodash'
import { INVOICE_TYPE, IMG_REG } from '@ekuaibao/lib/lib/enums'
import CopyToClipboard from 'react-copy-to-clipboard'
import { showModal, showMessage } from '@ekuaibao/show-util'
import InvoiceTaxInfo from '../CarouselInvoiceReviewer/utils/InvoiceTaxInfo'
import { isIE } from '../../lib/misc'
import { TupleInvoiceType } from '../ConditionalEditComponent/Type'
import { T } from '@ekuaibao/i18n'
import { invoiceMetaile } from '@ekuaibao/lib/lib/invoice/invoiceHelper'
import InvoiceUploadView from '../../plugins/bills/views/InvoiceUploadView'
import classNames from 'classnames'
import { submitDisableInvoice, submitMarkInvoice, getAiSummary } from '../../plugins/bills/bills.action'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { Fetch } from '@ekuaibao/fetch'
import { EnhanceConnect } from '@ekuaibao/store'
import { showWarningModal } from './WarningModal'
import InvoiceExtraFields from './InvoiceExtraFields'
import { TwoToneLogoAi, OutlinedDirectionRight, TwoToneDataPdf, TwoToneDataCode } from '@hose/eui-icons'
import { Popover, Button as EButton } from '@hose/eui'
import {
  isElectronicAirAndTrain,
  isElectronicTitle,
  ELECTRONIC_AIRCRAFT_INVOICE,
  ELECTRONIC_TRAIN_INVOICE,
  getFormattedInvoiceTitle
} from '../../plugins/bills/layers/import-bill/invoice/ImportInvoiceUtils'
import RichMessage from '../../plugins/bills/layers/import-bill/RichMessage'
// const { getBoolVariation } = app.require('@lib/featbit')
import { getBoolVariation } from '../../lib/featbit'
import { onlyOneCurrencyHideSeaLocalCurrrency, enableReCheck } from '../../lib/featbit/feat-switch'

const IMAGE_MAP = new Map()

const InvoiceMarkDisableType = {
  MARK: 'mark',
  DISABLE: 'disable'
}

class AddInvoice extends PureComponent {
  constructor(props) {
    super(props)
    const isShowInvoice = !!(props.invoiceNum && props.invoiceNum === 1)
    this.state = {
      isShowInvoice
    }
  }

  componentDidMount() {
    const payerInfoArr = app.getState('@common.payerInfo')
    if (!payerInfoArr?.length) {
      app.dataLoader('@common.payerInfo').load()
    }
  }

  handleLookClick = e => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    this.setState({
      isShowInvoice: !this.state.isShowInvoice
    })
  }

  handleUploadMetaileResponse = invoice => {
    const { index, onUploadInvoiceMetaile } = this.props
    onUploadInvoiceMetaile(index, invoice)
  }

  render() {
    const { isShowInvoice } = this.state
    const {
      bus,
      item,
      submitterId,
      isEdit,
      isInvoiceManagePermissions,
      isReadOnlyPage,
      onChange,
      checkout,
      permissions,
      onOpenEditModal,
      canSeeLink = false,
      isHover,
      billState,
      isInHistory,
      index,
      onRetryCheckerInvoiceClick,
      isEditAuthenticity = true,
      modifyApproveMoney,
      showPriceAndRate = true,
      checkInvoiceAgain,
      showCheckAginButton,
      imgInfo,
      showInfoBtn = true,
      DIGITAL_ORIGINAL_FILE,
      disableDelete
    } = this.props
    const { invoiceInfo } = formatInvoiceData({ data: item, dateFormat: i18n.get('YYYY年MM月DD日') })
    // @ts-ignore
    const { E_税额, E_是否抵扣, E_system_发票主体_XML } = item.master.form
    const { invamt, masterItems, type } = invoiceInfo
    const isFullDigital =
      ['FULL_DIGITAl_SPECIAL', 'FULL_DIGITAl_NORMAL'].includes(type) || isElectronicAirAndTrain(item.master?.form) //全电票
    const hasPdf = fnCheckInvoicePdf(invoiceInfo, item, isFullDigital)
    const title = getTitleByInvoiceMark(item.master.form, invoiceInfo)
    const invoiceName = INVOICE_TYPE()[type]
    const time = masterItems.filter(i => i.type === 'date')[0].value
    let clsC = 'invoice-item-yellowType'
    const isInvoice = get(item, 'master.entityId', '') === 'system_发票主体'
    // @i18n-ignore
    const isCheckerInvoice = isEditAuthenticity ? getIsCheckerInvoice(item?.master?.form) : true
    const { shoMetaileAction } = invoiceMetaile(item?.master)
    // 复用一下isEditAuthenticity，在指定的页面不可以上传原件
    const isShowMetaileAction = isEditAuthenticity ? shoMetaileAction : false
    const cls = getClassNameOfInvoiceCardWrap(item)
    const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(this.props)
    const { showMark, showDisable } = invoiceDisableMarkStatus
    const bindValidateMessage = get(item, 'master.bindValidateMessage', '')
    const isOverseas = get(item, 'master.entityId', '') === 'system_海外发票'
    return (
      <div className={styles[cls]}>
        <div className="invoice-item-yellow">
          {isEdit && <EditDelete data={this.props} isEditCard={false} />}
          <InvoiceMid
            title={title}
            allMoney={invamt}
            info={time}
            {...this.props}
            isEditCard={false}
            showPriceAndRate={showPriceAndRate}
            typeText={invoiceName || i18n.get('增值税发票')}
            typeTextCls={clsC}
          />
          {(isReadOnlyPage || !isEdit) && (
            <InvoiceOtherInfo
              item={item}
              modifyApproveMoney={modifyApproveMoney}
              billState={billState}
              isInHistory={isInHistory}
              isHover={isHover}
              isInvoiceManagePermissions={isInvoiceManagePermissions}
              allMoney={invamt}
              taxAmount={E_税额}
              isDeduction={E_是否抵扣}
              onChange={onChange}
              onOpenEditModal={() => onOpenEditModal(item)}
              source={invoiceInfo.type}
              showPriceAndRate={showPriceAndRate}
              isReadOnlyPage={isReadOnlyPage}
              canEditTaxAmount={true}
              disableDelete={disableDelete}
            />
          )}
          {isShowInvoice && <div className="invoice_line" />}
          {isShowInvoice && !isOverseas && (
            <InvoiceCard
              className={'invoice_distance'}
              invoice={item}
              bus={bus}
              submitterId={submitterId}
              hiddenHeader={true}
              checkout={checkout}
              permissions={permissions}
              canSeeLink={canSeeLink}
            />
          )}
          {showInfoBtn && (
            <div className="invoice-item-wrap">
              <div className="look-and-open">
                <div className="invoice-item-open" onClick={this.handleLookClick}>
                  {isShowInvoice ? i18n.get('收起明细') : i18n.get('展开明细')}
                </div>
                {hasPdf && (
                  <span className="invoice-item-look" onClick={() => fnHandleDownloadPDF(item)}>
                    {isFullDigital && DIGITAL_ORIGINAL_FILE ? i18n.get('查看PDF') : i18n.get('查看文件')}
                  </span>
                )}
                {(isFullDigital || DIGITAL_ORIGINAL_FILE) && E_system_发票主体_XML && (
                  <span className="invoice-item-look" onClick={() => fnHandleDownloadXML(item)}>
                    {i18n.get('查看XML')}
                  </span>
                )}
              </div>
              <div className="right-action">
                {isShowMetaileAction ? (
                  <InvoiceUploadView
                    invoiceId={item?.invoiceId || item?.master?.id}
                    children={<div className="checker-invoice">{i18n.get('上传原文件')}</div>}
                    onResult={this.handleUploadMetaileResponse}
                  />
                ) : null}
                <InvoiceToDisableOrMark {...this.props} showDisable={showDisable} showMark={showMark} />
                {isInvoice && !isCheckerInvoice && (
                  <Button
                    type="primary"
                    className={classNames('checker-invoice', { 'ml-8': shoMetaileAction })}
                    onClick={() => onRetryCheckerInvoiceClick(index, item)}
                  >
                    {i18n.get('点击验真')}
                  </Button>
                )}
                {showCheckAginButton && !isFullDigital && !isElectronicAirAndTrain(item?.master?.form) && !enableReCheck() && (
                  <Button onClick={() => checkInvoiceAgain(index, item)} className="checkAgain" type="primary">
                    {i18n.get('重新查验')}
                  </Button>
                )}
              </div>
            </div>
          )}
          {!!imgInfo?.orgFileName && handleOrgFileName(imgInfo?.orgFileName)}
          {!!bindValidateMessage && (
            <div className={`type-error mb-8 mt-5`}>
              <RichMessage message={bindValidateMessage} theme="light" />
            </div>
          )}
        </div>
      </div>
    )
  }
}
// 销项票展示购买方信息，非销项票展示销售方信息
export const getTitleByInvoiceMark = (invoiceForm, invoiceInfo) => {
  // 电子航电火车票
  if (isElectronicAirAndTrain(invoiceForm)) {
    return isElectronicTitle(invoiceForm)
  } else if (invoiceForm?.['E_system_发票主体_进销项标识'] !== 'OUTPUT_INVOICE_RECEIPT') {
    const { masterItems } = invoiceInfo
    const seller = masterItems.find(line => line.type === 'seller')
    const title = seller ? seller.value?.payer : masterItems[0].value
    return getFormattedInvoiceTitle(title)
  }
  const title = invoiceInfo.buyer?.value?.payer
  return getFormattedInvoiceTitle(title)
}

class NontaxInvoice extends PureComponent {
  constructor(props) {
    super(props)
  }

  render() {
    const {
      item,
      isEdit,
      index,
      onRetryCheckerInvoiceClick,
      onOpenEditModal,
      isEditAuthenticity = true,
      showPriceAndRate = true,
      isReadOnlyPage,
      showInfoBtn = true,
      DIGITAL_ORIGINAL_FILE
    } = this.props
    const { invoiceInfo } = formatInvoiceData({ data: item, dateFormat: i18n.get('YYYY年MM月DD日') })

    // @ts-ignore
    const {
      E_system_非税收入类票据_开票日期,
      E_system_非税收入类票据_金额合计,
      E_system_非税收入类票据_发票种类,
      E_system_非税收入类票据_收款单位,
      E_system_非税收入类票据_发票类别,
      E_system_发票主体_XML
    } = item.master.form

    const title = E_system_非税收入类票据_发票种类 || E_system_非税收入类票据_收款单位
    const info = moment(E_system_非税收入类票据_开票日期).format(i18n.get('YYYY年MM月DD日'))
    let clsC = 'invoice-item-nontax'
    // const isInvoice =
    //   get(item, 'master.entityId', '') === 'system_非税收入类票据' &&
    //   get(item, 'master.form.E_system_非税收入类票据_发票类别', '') === '非税收入通用票据'
    // @i18n-ignore
    const isCheckerInvoice = isEditAuthenticity ? getIsCheckerInvoice(item?.master?.form) : true
    const { shoMetaileAction } = invoiceMetaile(item?.master)
    const cls = getClassNameOfInvoiceCardWrap(item)
    const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(this.props)
    const { showMark, showDisable } = invoiceDisableMarkStatus
    const bindValidateMessage = get(item, 'master.bindValidateMessage', '')
    const { type } = invoiceInfo || {}
    const isFullDigital = ['FULL_DIGITAl_SPECIAL', 'FULL_DIGITAl_NORMAL', 'ELECTRONIC_PAPER_CAR'].includes(type) //全电票
    const hasPdf = fnCheckInvoicePdf(invoiceInfo, item, isFullDigital)
    return (
      <div className={styles[cls]}>
        <div className="invoice-item-yellow">
          {isEdit && <EditDelete data={this.props} isEditCard={true} />}
          <InvoiceMid
            title={title}
            allMoney={E_system_非税收入类票据_金额合计}
            info={info}
            {...this.props}
            isEditCard={false}
            showPriceAndRate={showPriceAndRate}
            typeText={E_system_非税收入类票据_发票类别}
            typeTextCls={clsC}
          />
          {(isReadOnlyPage || !isEdit) && (
            <InvoiceOtherInfo
              item={item}
              {...this.props}
              allMoney={E_system_非税收入类票据_金额合计}
              onOpenEditModal={() => onOpenEditModal(item)}
            />
          )}
          {!!bindValidateMessage && (
            <div className={`type-error mb-8 mt-5`}>
              <RichMessage message={bindValidateMessage} theme="light" />
            </div>
          )}
          {showInfoBtn && <div className="invoice_line" />}
          <div className="invoice-item-wrap">
            <div className="look-and-open">
              {hasPdf && (
                <span className="invoice-item-look" style={{ marginLeft: 0 }} onClick={() => fnHandleDownloadPDF(item)}>
                  {DIGITAL_ORIGINAL_FILE ? i18n.get('查看PDF') : i18n.get('查看文件')}
                </span>
              )}
              {DIGITAL_ORIGINAL_FILE && E_system_发票主体_XML && (
                <span className="invoice-item-look" onClick={() => fnHandleDownloadXML(item)}>
                  {i18n.get('查看XML')}
                </span>
              )}
            </div>
            <div className="right-action">
              <InvoiceToDisableOrMark {...this.props} showDisable={showDisable} showMark={showMark} />
            </div>
          </div>
        </div>
      </div>
    )
  }
}

function MedicalInvoice(props) {
  const { item, isEdit, isReadOnlyPage, imgInfo, showInfoBtn = true } = props
  const { master } = item
  const { E_system_医疗发票_开票日期, E_system_医疗发票_金额合计, E_system_医疗发票_发票种类 } = master.form
  const title = getFormattedInvoiceTitle(E_system_医疗发票_发票种类)
  const info = moment(E_system_医疗发票_开票日期).format(i18n.get('YYYY年MM月DD日'))
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className={'invoice-item-yellow'}>
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          item={item}
          allMoney={E_system_医疗发票_金额合计}
          title={title}
          info={info}
          {...props}
          typeText={i18n.get('医疗发票')}
          typeTextCls={'invoice-item-machineType'}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo item={item} {...props} allMoney={E_system_医疗发票_金额合计} />
        )}
        {showInfoBtn && (
          <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
            <div className="right-action">
              <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
            </div>
          </div>
        )}

        {!!imgInfo?.orgFileName && handleOrgFileName(imgInfo?.orgFileName)}
        {master && master.bindValidateMessage && (
          <div className={`type-error mb-8 mt-5`}>
            <RichMessage message={master.bindValidateMessage} theme="light" />
          </div>
        )}
      </div>
    </div>
  )
}

const mapItem = {
  system_发票主体: AddInvoice,
  system_非税收入类票据: NontaxInvoice,
  system_医疗发票: MedicalInvoice,
  system_出租车票: TaxiInvoice,
  system_过路费发票: TollInvoice,
  system_定额发票: QuotaInvoice,
  system_火车票: TrainInvoice,
  system_航空运输电子客票行程单: PlaneInvoice,
  system_客运汽车发票: BusInvoice,
  system_海外发票: OverseasInvoice,
  system_其他: OtherInvoice,
  system_消费小票: ShoppingInvoice,
  system_机打发票: MachineInvoice,
  invoicePhoto: InvoicePhotoView
}

const invoiceTaxInfo = new InvoiceTaxInfo()
export default EnhanceConnect(state => ({
  KA_INVOICE: state['@common'].powers.KA_INVOICE,
  DIGITAL_ORIGINAL_FILE: state['@common'].powers.DIGITAL_ORIGINAL_FILE, // todo lhhh
  dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo
}))(InvoiceItem)
const InvoiceList = props => {
  const { dataSource } = props
  const [currentPage, setCurrentPage] = useState(1)
  const [displayData, setDisplayData] = useState([])
  const pageSize = 100
  const imgList = groupBy(props.invoiceImgList, 'id', [])

  const loadOptimization = getBoolVariation('cyxq-73109-fee-detail-load-optimization')

  useEffect(() => {
    if (dataSource?.length && loadOptimization) {
      const startIndex = 0
      const endIndex = Math.min(pageSize, dataSource.length)
      setDisplayData(dataSource.slice(startIndex, endIndex))
    } else {
      setDisplayData(dataSource)
    }
  }, [dataSource])

  const handleLoadMore = () => {
    const endIndex = Math.min(displayData.length + pageSize, dataSource?.length || 0)
    setDisplayData(prev => [...prev, ...dataSource.slice(displayData.length, endIndex)])
  }

  return (
    <div className={styles.invoiceListContainer}>
      <div className={styles.invoiceList}>
        {displayData?.map((item, index) => {
          const { attachment, master, type } = item
          const { id, entityId } = master || {}
          const Component = mapItem[entityId || type]
          const imgInfo = attachment ? attachment : imgList[id] ? imgList[id][0] : undefined

          if (!!props.KA_INVOICE && imgInfo?.fileName) {
            imgInfo.orgFileName = imgInfo?.fileName
            if (imgInfo.fileName.includes('invoice/')) {
              const nameArr = imgInfo.fileName.split('-')
              imgInfo.orgFileName = decodeURIComponent(nameArr[nameArr.length - 1])
            }
          }

          return (
            <Component
              {...props}
              key={`${id}_${index}`}
              item={item}
              index={index}
              imgInfo={imgInfo}
              isEdit={props.isEdit}
              isInHistory={props.isInHistory}
              onDeleteItem={props.onDeleteItem}
              handleImgPreview={props.handleImgPreview}
              onOpenEditInvoice={props.onOpenEditInvoice}
              isReadOnlyPage={props.isReadOnlyPage}
              isHover={props.isHover}
              showInfoBtn={props.showInfoBtn}
              isInvoiceManagePermissions={props.isInvoiceManagePermissions}
              needRerequestInvoiceAttachment={props.needRerequestInvoiceAttachment}
              onOpenEditModal={() => props.onOpenEditModal(item)}
              onOpenEditReviewAmount={() => props.onOpenEditReviewAmount(item)}
            />
          )
        })}
      </div>

      {dataSource?.length > displayData?.length && (
        <div style={{ padding: '16px 0' }}>
          <EButton style={{ width: '100%' }} category="secondary" onClick={handleLoadMore}>
            {i18n.get('加载更多')}
          </EButton>
        </div>
      )}
    </div>
  )
}

function InvoiceItem(props) {
  return <InvoiceList {...props} />
}

// 删除发票前，弹出二次确认弹窗
function fnDeleteItem(data) {
  showWarningModal(() => {
    handleDeleteItem(data)
  })
}

function handleDeleteItem(data) {
  IS_ZJZY && message.info(i18n.get('请先保存费用明细再上传发票'))
  const { item, onDeleteItem, isRecordExpends, isQuickExpends } = data
  if (isHongShanTestingEnterprise(Fetch.ekbCorpId) && !isRecordExpends && !isQuickExpends) {
    showMessage.info(i18n.get('删除后，若需要重新添加该发票，请先将此单据存为草稿或直接提交后进行'))
  }
  onDeleteItem && onDeleteItem(item)
}

function EditDelete(props) {
  const { data, isEditCard = true, showInfoBtn = true } = props
  const { onOpenEditInvoice, item, index, imgInfo, dataSource, disableDelete, isEdit } = data
  const fileName = get(item, 'master.form.E_system_发票主体_图片', '')
  const isOfd = fileName?.toLowerCase()?.endsWith('.ofd')
  //说明来源于统一开票
  const showEdit = disableDelete ? isEdit : isEditCard && onOpenEditInvoice && !isOfd
  return (
    <div className="edit-delete">
      {showEdit && (
        <EKBIcon
          name="#EDico-edit"
          onClick={handleOpenEditInvoice.bind(this, { dataSource, item, index, imgInfo, onOpenEditInvoice })}
          className="icon-edit"
        />
      )}
      {showInfoBtn && !disableDelete && (
        <EKBIcon
          name="#EDico-delete"
          onClick={e => {
            e.stopPropagation()
            fnDeleteItem(data)
          }}
          className="icon-delete"
        />
      )}
    </div>
  )
}

function handleOpenEditInvoice(args) {
  const { dataSource, item, index, imgInfo, onOpenEditInvoice } = args
  onOpenEditInvoice && onOpenEditInvoice(dataSource, item, index, imgInfo)
}

// 获取当前发票的提示内容
function getContent(warningMsg) {
  return (
    <ul>
      {warningMsg.map((v, i) => {
        return <li key={i}>{v}</li>
      })}
    </ul>
  )
}

function BaseInfoView(props) {
  const {
    title,
    info,
    dataSource,
    imgInfo,
    onOpenEditInvoice,
    item,
    index,
    isEditCard = true,
    isEdit,
    isModify,
    riskData,
    isReadOnlyPage
  } = props
  const [className, url] =
    isEdit && !isModify && !isReadOnlyPage ? ['error', '#EDico-plaint-circle'] : ['warning', '#EDico-plaint-circle']
  const warningMsg = riskData?.singleInvoiceRiskWarning?.find(v => v.invoiceId === item?.invoiceId)?.riskWarning
  const fileName = get(item, 'master.form.E_system_发票主体_图片', '')
  const isOfd = fileName?.toLowerCase()?.endsWith('.ofd')
  return (
    <div className="invoice-item-intr">
      <div className="invoice-item-title-container">
        <div
          className="invoice-item-title"
          onClick={
            isEdit && !isOfd
              ? isEditCard
                ? handleOpenEditInvoice.bind(this, { dataSource, item, index, imgInfo, onOpenEditInvoice })
                : null
              : null
          }
        >
          {title}
          {/* 每张发票存在问题的话，单独标示出来 */}
          {warningMsg?.length > 0 && (
            <Popover content={getContent(warningMsg)} title={i18n.get('风险详情')} trigger="hover">
              <svg className={`icon ${className}`} aria-hidden="true">
                <use xlinkHref={url} />
              </svg>
            </Popover>
          )}
        </div>
      </div>

      <div className="invoice-item-date">{info}</div>
    </div>
  )
}

function handleImgClick(props, img) {
  const { handleImgPreview, imgInfo } = props
  const temImgInfo = imgInfo || img
  let imageList = {
    fileId: temImgInfo.id,
    key: temImgInfo.fileName || '',
    ...temImgInfo
  }
  handleImgPreview && handleImgPreview(imageList)
}

//type表示发票操作类别，disable表示禁用发票 mark标记发票
function handleDisableClick(props, type) {
  const { isModify, item, onDeleteItem, bus, index, onInvoiceStatusChange } = props
  const invoiceId = get(props, 'item.invoiceId')
  api
    .open('@bills:InvoiceDisableModal', { invoiceId, disableInfo: item?.disableInfo, markInfo: item?.markInfo, type })
    .then(reason => {
      if (isModify) {
        const newItem = cloneDeep(item)
        newItem.disableInfo.disable = true
        const needDelete = get(item, 'disableInfo.control') === 'FORBID_SUBMIT_DELETE'
        if (needDelete && onDeleteItem) {
          onDeleteItem(item)
        } else {
          onInvoiceStatusChange && onInvoiceStatusChange(index, newItem)
        }
        bus.emit('invoice:disable:click', { invoiceId, reason })
      } else {
        const param = { [invoiceId]: reason.disableReason }
        let invoiceOperation = type === 'mark' ? submitMarkInvoice(param) : submitDisableInvoice(param)
        api
          .dispatch(invoiceOperation)
          .then(data => {
            if (data.id === 'success') {
              showMessage.success(i18n.get('操作成功'))
              onInvoiceStatusChange && onInvoiceStatusChange()
            }
          })
          .catch(e => {
            showModal.info({
              title: i18n.get('警告'),
              content: e.errorMessage,
              okText: i18n.get('确定')
            })
            return
          })
      }
    })
}

function fnRenderIcon(source, props) {
  const { item, imgInfo } = props
  const imageName = imgInfo?.fileName || ''
  const isXml = imageName.endsWith('.xml') || imageName.endsWith('.XML')
  const type = get(item, 'master.form.E_system_发票主体_发票类别')
  switch (source) {
    case 'QUERY':
      return RenderMedicalImg(imgInfo, props, SVG_INVOICE_TEXT) // 手录发票
    case 'UPLOAD':
    case 'ALIPAY_CARD':
    case 'SCAN':
    case 'AIFAPIAO':
    case 'QXY':
      return RenderMedicalImg(imgInfo, props)
    case 'OCR':
    case 'OVERSEAS_INVOICE':
      return RenderOcrImg(imgInfo, props)
    case 'WECHAT_CARD':
      return renderWxImg(imgInfo, type)
  }
}

function RenderMedicalImg(imgInfo, props, iconSrc) {
  const imageName = imgInfo?.fileName || ''
  const isXml = imageName.endsWith('.xml') || imageName.endsWith('.XML')

  if (iconSrc) {
    return (
      <div className={'invoice-pic-con'}>
        <img className="invoice-pic" src={iconSrc} />
      </div>
    )
  }
  if (isXml) {
    return (
      <div className={'invoice-pic-con'}>
        <img
          className="invoice-pic"
          src={SVG_XML}
          onClick={e => {
            e.stopPropagation()
            handleImgClick(props)
          }}
        />
      </div>
    )
  } else {
    return (
      <div
        className={'invoice-pic-con pdf'}
        onClick={e => {
          e.stopPropagation()
          handleImgClick(props)
        }}
      >
        <TwoToneDataPdf fontSize={20} />
      </div>
    )
  }
}

function RenderOcrImg(imgInfo, props) {
  const id = get(props, 'item.master.id')
  const needRerequestInvoiceAttachment = get(props, 'needRerequestInvoiceAttachment', true)
  const defaultImg = IMAGE_MAP.get(id)
  const [img, setImg] = useState(defaultImg)
  if (imgInfo && imgInfo.thumbUrl) {
    if (IMG_REG.test(imgInfo?.fileName)) {
      return (
        <img
          className="invoice-item-pic"
          src={imgInfo.thumbUrl}
          onClick={e => {
            e.stopPropagation()
            handleImgClick(props)
          }}
        />
      )
    }
    return RenderMedicalImg(imgInfo, props)
  }
  if (img) {
    if (IMG_REG.test(img?.fileName)) {
      return img.thumbUrl ? (
        <img
          className="invoice-item-pic"
          src={img.thumbUrl}
          onClick={e => {
            e.stopPropagation()
            handleImgClick(props, img)
          }}
        />
      ) : (
        RenderMedicalImg(imgInfo, props)
      )
    }
    return RenderMedicalImg(imgInfo, props)
  }
  if (!defaultImg && needRerequestInvoiceAttachment) {
    api.invokeService('@bills:get:invoice:image:by:ids', [id]).then(rep => {
      const [resImg] = rep.items
      IMAGE_MAP.set(id, resImg)
      setImg(resImg)
    })
    IMAGE_MAP.set(id, {})
  }
  return img && img.thumbUrl && IMG_REG.test(img?.fileName) ? (
    <img
      className="invoice-item-pic"
      src={img.thumbUrl}
      onClick={e => {
        e.stopPropagation()
        handleImgClick(props, img)
      }}
    />
  ) : (
    RenderMedicalImg(imgInfo, props)
  )
}

function renderWxImg(imgInfo, type) {
  const imageName = imgInfo?.fileName || ''
  const isXml = imageName.endsWith('.xml') || imageName.endsWith('.XML')
  const cls = type === 'BLOCK_CHAIN' ? `invoice-pic-con invoice-pic-token` : 'invoice-pic-con'
  const imageSrc = type === 'BLOCK_CHAIN' ? SVG_INVOICE_TOKEN : isXml ? SVG_XML : SVG_INVOICE_PDF
  if (type === 'BLOCK_CHAIN' || isXml) {
    return (
      <div className={cls}>
        <img className="invoice-pic" src={imageSrc} />
      </div>
    )
  } else {
    return (
      <div className={cls + ' pdf'}>
        <TwoToneDataPdf fontSize={20} />
      </div>
    )
  }
}

export function getTaxAmount(detail) {
  const { entityId, form } = detail
  const tags = form[`E_${entityId}_价税合计`]
  // @i18n-ignore
  return new Big(getMoney(tags)).toFixed(2)
}
export function getMoney(money) {
  return money ? (typeof money === 'object' ? (money.standard ? money.standard : '0.00') : money) : '0.00'
}

export function getInvoiceSignature(master, cls) {
  const isCheckSignature = get(master, 'form.E_system_发票主体_验签')
  if (isCheckSignature === true) {
    return (
      <div
        style={{ color: '#197cd9', backgroundColor: 'rgba(24, 144, 255, 0.1)' }}
        className={`signature-tag ${cls ? cls : ''}`}
      >
        <T name={'电子签名校验通过'} />
      </div>
    )
  } else if (isCheckSignature === false) {
    return (
      <div style={{ color: '#8d929a', backgroundColor: '#edeeef' }} className={`signature-tag ${cls ? cls : ''}`}>
        <T name={'电子签名校验不通过'} />
      </div>
    )
  }
  return null
}

// 已验真状态下才判断红冲、作废
export const invoiceStatusStr = invoiceForm => {
  const status = invoiceForm.E_system_发票主体_发票状态
  const statusMap = {
    NORMAL_BLUE: i18n.get('已验真'),
    RED_REVERSAL: i18n.get('已红冲'),
    INVOICE_NULLIFY: i18n.get('已作废')
  }
  return statusMap[status] ?? i18n.get('已验真')
}

export const getIsCheckerInvoice = invoiceForm => get(invoiceForm, 'E_system_发票主体_验真')
export const getInvoiceStatus = invoiceForm => get(invoiceForm, 'E_system_发票主体_发票状态')
// @i18n-ignore
function InvoiceMid(props) {
  const {
    item,
    title,
    info,
    allMoney,
    isEdit,
    typeText,
    typeTextCls = 'invoice-item-yellowType',
    isReadOnlyPage,
    showInfoBtn = true,
    taxMoney,
    noTaxMoney
  } = props
  const OverseasInvoicePower = api.getState()['@common'].powers.OverseasInvoice
  const isInvoice = get(item, 'master.entityId', '') === 'system_发票主体' //|| isNontax
  const isCheckerInvoice = getIsCheckerInvoice(item?.master?.form)
  const status = getInvoiceStatus(item?.master?.form)
  const allMoneyDetail =
    item?.details?.length && !isElectronicAirAndTrain(item.master?.form)
      ? invoiceTaxInfo.getAllMoney(item.details)
      : getTaxAmount(item.master)
  const realTagClass = isCheckerInvoice && status !== 'INVOICE_NULLIFY' ? 'real-card' : 'no-real-card'
  const { metaileText, metaileColor, metaileBgColor } = invoiceMetaile(item?.master)
  const { disable } = item.disableInfo || {}
  const source = get(item, 'master.form.E_system_发票主体_来源')
  const sourceLabel = TupleInvoiceType().find(el => el.value === source)?.label
  const isShowForeign = item?.master?.entityId === 'system_海外发票'
  const invoiceType = get(item, 'master.form.E_system_发票主体_发票类别', '')
  const expenseType = get(item, 'master.form.E_system_海外发票_消费类型', '其他')
  const onlyShowForeign = isShowForeign && OverseasInvoicePower && onlyOneCurrencyHideSeaLocalCurrrency()
  // 标记发票
  const { mark } = item.markInfo || {}

  return (
    <div className="invoice-item-mid">
      <div className="invoice-item-mid-content">
        <div className="option-line">
          {fnRenderIcon(source, props)}
          <BaseInfoView title={title} info={info} {...props} />
        </div>
        <div style={{ display: 'flex', flexShrink: 0, flexDirection: 'column' }}>
          <div className="dis-f">
            {disable && <div className="disable-item">{i18n.get('已禁用')}</div>}
            {mark && <div className="disable-item">{i18n.get('已标记')}</div>}
            {isInvoice && (
              <div className={realTagClass}>
                <T name={isCheckerInvoice ? invoiceStatusStr(item.master?.form) : '未验真'} />
              </div>
            )}
            {sourceLabel && <div className="source-item">{sourceLabel}</div>}
            {showInfoBtn && <div className={typeTextCls}>{typeText}</div>}
            {isShowForeign && <div className="expense-type">{i18n.get(expenseType)}</div>}
            {!showInfoBtn && (
              <Money
                style={{ marginLeft: '24px', fontSize: '16px', fontWeight: 'bold' }}
                className="invoice-item-money"
                value={item?.sourceAmount}
                withoutStyle={isIE()}
              />
            )}
          </div>
          <div className="tag-wrapper-row">
            {getInvoiceSignature(item?.master, metaileText?.length ? 'mr-4' : '')}
            {metaileText?.length ? (
              <div style={{ color: metaileColor, backgroundColor: metaileBgColor }} className="metaile-tag">
                <T name={metaileText} />
              </div>
            ) : null}
          </div>
        </div>
      </div>
      {invoiceType === ELECTRONIC_AIRCRAFT_INVOICE && renderAircraft(item.master?.form, 'invoice-item-other')}
      {invoiceType === ELECTRONIC_TRAIN_INVOICE && renderTrain(item.master?.form, 'invoice-item-other')}
      {isEdit && !isReadOnlyPage && showInfoBtn && (
        <>
          <div className="invoice-item-total">
            {item.master.entityId === 'system_发票主体' ? ( // @i18n-ignore
              <>
                <div className="invoice-item-merge">{i18n.get('价税合计')}</div>
                <Money
                  className="invoice-item-money"
                  onlyForeign={onlyShowForeign && allMoneyDetail?.foreignNumCode}
                  value={allMoneyDetail}
                  withoutStyle={isIE()}
                />
              </>
            ) : (
              <>
                <div className="invoice-item-merge">{i18n.get('总金额')}</div>
                <Money
                  className="invoice-item-money"
                  value={allMoney}
                  isShowForeign={isShowForeign ? isShowForeign && allMoney?.foreignNumCode : allMoney?.foreignNumCode }
                  onlyForeign={isShowForeign ? onlyShowForeign && allMoney?.foreignNumCode : allMoney?.foreignNumCode }
                  withoutStyle={isIE()}
                />
              </>
            )}
          </div>
          {taxMoney && (
            <>
              <div className="invoice_line"></div>
              <div className="invoice-item-total">
                <div className="invoice-item-merge">{i18n.get('税额')}</div>
                <Money
                  className="invoice-item-money"
                  isShowForeign={isShowForeign ? isShowForeign && taxMoney.foreignNumCode : taxMoney?.foreignNumCode}
                  onlyForeign={isShowForeign ? onlyShowForeign && taxMoney?.foreignNumCode : taxMoney?.foreignNumCode}
                  value={taxMoney}
                  withoutStyle={isIE()}
                />
              </div>
            </>
          )}
          {noTaxMoney && (
            <>
              <div className="invoice_line"></div>
              <div className="invoice-item-total">
                <div className="invoice-item-merge">{i18n.get('不计税金额')}</div>
                <Money
                  className="invoice-item-money"
                  isShowForeign={isShowForeign ? isShowForeign && noTaxMoney.foreignNumCode : noTaxMoney?.foreignNumCode}
                  onlyForeign={isShowForeign ? onlyShowForeign && noTaxMoney?.foreignNumCode : noTaxMoney?.foreignNumCode}
                  value={noTaxMoney}
                  withoutStyle={isIE()}
                />
              </div>
            </>
          )}
        </>
      )}
      {showInfoBtn && (
        <>
          {!(isReadOnlyPage || !isEdit) && renderInvoiceEdit(props, true)}
          {!(isReadOnlyPage || !isEdit) && renderInvoiceReview(props, true)}
        </>
      )}
    </div>
  )
}

function renderTrain(form, className) {
  const {
    E_system_发票主体_车次,
    E_system_发票主体_乘车人姓名,
    E_system_发票主体_乘车时间,
    E_system_发票主体_座位类型
  } = form || {}
  const time = moment(E_system_发票主体_乘车时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))

  return (
    <div className={className}>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('乘车人姓名')}</div>
        <div className="txt">{E_system_发票主体_乘车人姓名}</div>
      </div>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('乘车时间')}</div>
        <div className="txt">{time}</div>
      </div>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('车次')}</div>
        <div className="txt">{E_system_发票主体_车次}</div>
      </div>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('座位类型')}</div>
        <div className="txt">{E_system_发票主体_座位类型}</div>
      </div>
      <div class="invoice_line"></div>
    </div>
  )
}

function renderAircraft(form, className) {
  const {
    E_system_发票主体_航班号,
    E_system_发票主体_乘机人姓名,
    E_system_发票主体_乘机时间,
    E_system_发票主体_电子客票号码,
    E_system_发票主体_航班舱型
  } = form || {}
  const time = moment(E_system_发票主体_乘机时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))

  return (
    <div className={className}>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('乘机人姓名')}</div>
        <div className="txt">{E_system_发票主体_乘机人姓名}</div>
      </div>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('乘机时间')}</div>
        <div className="txt">{time}</div>
      </div>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('电子客票号码')}</div>
        <div className="txt">{E_system_发票主体_电子客票号码}</div>
      </div>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('航班号')}</div>
        <div className="txt">{E_system_发票主体_航班号}</div>
      </div>
      <div className="invoice-item-total">
        <div className="invoice-item-merge">{i18n.get('航班舱型')}</div>
        <div className="txt">{E_system_发票主体_航班舱型}</div>
      </div>
      <div class="invoice_line"></div>
    </div>
  )
}

function TaxiInvoice(props) {
  const { item, isEdit, isReadOnlyPage, onOpenEditModal, isInHistory, imgInfo, showInfoBtn = true } = props
  const { master } = item
  const {
    E_system_出租车票_发票所在地,
    E_system_出租车票_上车时间,
    E_system_出租车票_下车时间,
    E_system_出租车票_里程,
    E_system_出租车票_金额
  } = master.form
  const taxi_date = `${moment(E_system_出租车票_上车时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))}-${moment(
    E_system_出租车票_下车时间
  ).format('HH:mm')}`
  const title = E_system_出租车票_发票所在地
    ? i18n.get(`出租车发票 ({__k0})`, { __k0: E_system_出租车票_发票所在地 })
    : ''
  let km = E_system_出租车票_里程 ? `${E_system_出租车票_里程}km` : ''
  const money = E_system_出租车票_金额 ? E_system_出租车票_金额 : ''
  const info = `${taxi_date} ${km}`
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus
  return (
    <div className={styles[cls]}>
      <div className={'invoice-item-yellow'}>
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid title={title} allMoney={money} info={info} {...props} typeText={i18n.get('出租车票')} />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            {...props}
            onOpenEditModal={() => onOpenEditModal(item)}
            isInHistory={isInHistory}
            item={item}
            allMoney={money}
          />
        )}
        {showInfoBtn && (
          <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
            <div className="right-action">
              <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
            </div>
          </div>
        )}
        {!!imgInfo?.orgFileName && handleOrgFileName(imgInfo?.orgFileName)}
        {master && master.bindValidateMessage && (
          <div className={`type-error mb-8 mt-5`}>
            <RichMessage message={master.bindValidateMessage} theme="light" />
          </div>
        )}
      </div>
    </div>
  )
}

function TollInvoice(props) {
  const { item, isEdit, isReadOnlyPage, onOpenEditModal, showInfoBtn = true } = props
  const { master } = item
  const {
    E_system_过路费发票_时间,
    E_system_过路费发票_入口,
    E_system_过路费发票_出口,
    E_system_过路费发票_金额
  } = master.form
  const _start = E_system_过路费发票_入口 ? E_system_过路费发票_入口 : ''
  const _end = E_system_过路费发票_出口 ? E_system_过路费发票_出口 : ''
  
  // 修改 title 逻辑：判断拼接字段都为空的情况
  let title
  if (!_start && !_end) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = `${_start} -- ${_end}`
  }
  
  const info = moment(E_system_过路费发票_时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className={'invoice-item-yellow'}>
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          title={title}
          allMoney={E_system_过路费发票_金额}
          info={info}
          {...props}
          typeText={i18n.get('过路费票')}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            {...props}
            item={item}
            onOpenEditModal={() => onOpenEditModal(item)}
            allMoney={E_system_过路费发票_金额}
          />
        )}

        {showInfoBtn && (
          <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
            <div className="right-action">
              <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
            </div>
          </div>
        )}
        {master && master.bindValidateMessage && (
          <div className={`type-error mb-8 mt-5`}>
            <RichMessage message={master.bindValidateMessage} theme="light" />
          </div>
        )}
      </div>
    </div>
  )
}

function QuotaInvoice(props) {
  const { item, isEdit, isReadOnlyPage, onOpenEditModal, imgInfo, showInfoBtn = true } = props
  const { master } = item
  const { E_system_定额发票_金额 } = master.form
  const title = i18n.get('定额发票')
  const info = ''
  const money = E_system_定额发票_金额 ? E_system_定额发票_金额 : ''
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className={'invoice-item-yellow'}>
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid title={title} allMoney={money} info={info} {...props} typeText={i18n.get('定额发票')} />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo {...props} onOpenEditModal={() => onOpenEditModal(item)} item={item} allMoney={money} />
        )}
        {showInfoBtn && (
          <>
            <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
              <div className="right-action">
                <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
              </div>
            </div>
          </>
        )}

        {!!imgInfo?.orgFileName && handleOrgFileName(imgInfo?.orgFileName)}
        {master && master.bindValidateMessage && (
          <div className={`type-error mb-8 mt-5`}>
            <RichMessage message={master.bindValidateMessage} theme="light" />
          </div>
        )}
      </div>
    </div>
  )
}

function TrainInvoice(props) {
  const {
    item,
    isEdit,
    isInvoiceManagePermissions,
    isReadOnlyPage,
    onChange,
    onOpenEditModal,
    showInfoBtn = true
  } = props
  const { master } = item
  const {
    E_system_火车票_乘车时间,
    E_system_火车票_车次,
    E_system_火车票_乘车人姓名,
    E_system_火车票_上车车站,
    E_system_火车票_下车车站,
    E_system_火车票_金额,
    E_system_火车票_座位类型,
    E_税额,
    E_是否抵扣
  } = master.form
  const train_date = moment(E_system_火车票_乘车时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))
  const _start = E_system_火车票_上车车站 ? E_system_火车票_上车车站 : ''
  const _end = E_system_火车票_下车车站 ? E_system_火车票_下车车站 : ''
  
  // 修改 title 逻辑：判断拼接字段都为空的情况
  let title
  if (!_start && !_end) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = `${_start} -- ${_end}`
  }
  
  const info = isEdit
    ? `${train_date} ${E_system_火车票_车次 !== undefined ? E_system_火车票_车次 : ''} ${E_system_火车票_乘车人姓名}`
    : train_date
  const otherData = [
    { label: i18n.get('车次'), value: E_system_火车票_车次 !== undefined ? E_system_火车票_车次 : '' },
    { label: i18n.get('乘车人'), value: E_system_火车票_乘车人姓名 },
    { label: i18n.get('火车席别'), value: E_system_火车票_座位类型 !== undefined ? E_system_火车票_座位类型 : '' }
  ]
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className="invoice-item-blue">
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          item={item}
          allMoney={E_system_火车票_金额}
          title={title}
          info={info}
          {...props}
          typeText={i18n.get('铁路客票')}
          typeTextCls={'invoice-item-blueType'}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            item={item}
            {...props}
            allMoney={E_system_火车票_金额}
            otherData={otherData}
            taxAmount={E_税额}
            onOpenEditModal={() => onOpenEditModal(item)}
            onChange={onChange}
            isDeduction={E_是否抵扣}
            isInvoiceManagePermissions={isInvoiceManagePermissions}
          />
        )}
        {showInfoBtn && (
          <>
            <div className="invoice-item-wrap">
              <div className="look-and-open">
                {isFile(item) && (
                  <span
                    className="invoice-item-look"
                    style={{ marginLeft: 0 }}
                    onClick={e => {
                      e.stopPropagation()
                      fnHandleDownloadPDF(item)
                    }}
                  >
                    {i18n.get('查看文件')}
                  </span>
                )}
              </div>
              <div className="right-action">
                <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
              </div>
            </div>
            {master && master.bindValidateMessage && (
              <div className={`type-error mb-8 mt-5`}>
                <RichMessage message={master.bindValidateMessage} theme="light" />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

function MachineInvoice(props) {
  const {
    item,
    isEdit,
    isInvoiceManagePermissions,
    isReadOnlyPage,
    onChange,
    onOpenEditModal,
    isEditAuthenticity = true,
    showInfoBtn = true
  } = props
  const { master } = item
  const {
    E_system_机打发票_时间,
    E_system_机打发票_金额,
    E_税额,
    E_是否抵扣,
    E_system_机打发票_销售方名称,
    E_system_机打发票_发票种类
  } = master.form
  const title = getFormattedInvoiceTitle(E_system_机打发票_销售方名称)
  const { invoiceInfo } = formatInvoiceData({ data: item, dateFormat: i18n.get('YYYY年MM月DD日') })

  const info = moment(E_system_机打发票_时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))
  const { shoMetaileAction } = invoiceMetaile(item?.master)
  // 复用一下isEditAuthenticity，在指定的页面不可以上传原件
  const isShowMetaileAction = isEditAuthenticity ? shoMetaileAction : false
  const handleUploadMetaileResponse = invoice => {
    const { index, onUploadInvoiceMetaile } = props
    onUploadInvoiceMetaile(index, invoice)
  }
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus
  const hasPdf = fnCheckInvoicePdf(invoiceInfo, item, false)
  return (
    <div className={styles[cls]}>
      <div className={'invoice-item-machine'}>
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          item={item}
          allMoney={E_system_机打发票_金额}
          title={title}
          info={info}
          {...props}
          typeText={E_system_机打发票_发票种类 || i18n.get('机打发票')}
          typeTextCls={'invoice-item-machineType'}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            item={item}
            {...props}
            allMoney={E_system_机打发票_金额}
            taxAmount={E_税额}
            onOpenEditModal={() => onOpenEditModal(item)}
            onChange={onChange}
            isDeduction={E_是否抵扣}
            isInvoiceManagePermissions={isInvoiceManagePermissions}
          />
        )}
        <div class="invoice_line"></div>
        {hasPdf && (
          <div className="invoice-item-wrap" style={{ justifyContent: 'space-between' }}>
            <div className="look-and-open">
              <span
                className="invoice-item-look"
                style={{ marginLeft: 0 }}
                onClick={e => {
                  e.stopPropagation()
                  fnHandleDownloadPDF(item)
                }}
              >
                {i18n.get('查看文件')}
              </span>
            </div>
          </div>
        )}

        <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
          <div className="right-action">
            <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
            {isShowMetaileAction ? (
              <InvoiceUploadView
                invoiceId={item?.invoiceId || item?.master?.id}
                children={<div className="checker-invoice">{i18n.get('上传原文件')}</div>}
                onResult={handleUploadMetaileResponse}
              />
            ) : null}
          </div>
        </div>
        {master && master.bindValidateMessage && (
          <div className={`type-error mb-8 mt-5`}>
            <RichMessage message={master.bindValidateMessage} theme="light" />
          </div>
        )}
      </div>
    </div>
  )
}

function PlaneInvoice(props) {
  const {
    item,
    isEdit,
    isInvoiceManagePermissions,
    isReadOnlyPage,
    onChange,
    onOpenEditModal,
    showInfoBtn = true
  } = props
  const { master } = item
  const {
    E_system_航空运输电子客票行程单_乘机时间,
    E_system_航空运输电子客票行程单_航班号,
    E_system_航空运输电子客票行程单_乘机人姓名,
    E_system_航空运输电子客票行程单_航班舱型,
    E_system_航空运输电子客票行程单_出发站,
    E_system_航空运输电子客票行程单_到达站,
    E_system_航空运输电子客票行程单_金额,
    E_system_航空运输电子客票行程单_电子客票号码,
    E_system_航空运输电子客票行程单_填开日期,
    E_税额,
    E_是否抵扣
  } = master.form
  const plane_date = moment(E_system_航空运输电子客票行程单_乘机时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))
  const _start = E_system_航空运输电子客票行程单_出发站 ? E_system_航空运输电子客票行程单_出发站 : ''
  const _end = E_system_航空运输电子客票行程单_到达站 ? E_system_航空运输电子客票行程单_到达站 : ''
  
  // 修改 title 逻辑：判断拼接字段都为空的情况
  let title
  if (!_start && !_end) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = `${_start} -- ${_end}`
  }
  
  const _code = E_system_航空运输电子客票行程单_航班号 ? E_system_航空运输电子客票行程单_航班号 : ''
  const _name = E_system_航空运输电子客票行程单_乘机人姓名 ? E_system_航空运输电子客票行程单_乘机人姓名 : ''
  const _grade = E_system_航空运输电子客票行程单_航班舱型 ? E_system_航空运输电子客票行程单_航班舱型 : ''
  const _id = E_system_航空运输电子客票行程单_电子客票号码 ? E_system_航空运输电子客票行程单_电子客票号码 : ''
  const _fillDate = moment(E_system_航空运输电子客票行程单_填开日期).format(i18n.get('YYYY年MM月DD日'))
  const info = isEdit ? `${plane_date} ${_code} ${_name} ${_grade}` : `${plane_date}`
  const otherData = [
    { label: i18n.get('航班号'), value: _code },
    { label: i18n.get('航班舱型'), value: _grade },
    { label: i18n.get('乘机人'), value: _name, isCopy: true },
    { label: i18n.get('电子客票号'), value: _id, isCopy: true },
    { label: i18n.get('填开日期'), value: _fillDate }
  ]
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className="invoice-item-blue">
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          item={item}
          allMoney={E_system_航空运输电子客票行程单_金额}
          title={title}
          info={info}
          {...props}
          typeText={i18n.get('机票行程单')}
          typeTextCls={'invoice-item-blueType'}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            item={item}
            {...props}
            onOpenEditModal={() => onOpenEditModal(item)}
            allMoney={E_system_航空运输电子客票行程单_金额}
            otherData={otherData}
            taxAmount={E_税额}
            isDeduction={E_是否抵扣}
            onChange={onChange}
            isInvoiceManagePermissions={isInvoiceManagePermissions}
          />
        )}
        {showInfoBtn && (
          <>
            <div className="invoice-item-wrap" style={{ justifyContent: 'space-between' }}>
              <div className="look-and-open">
                {isFile(item) && (
                  <span
                    className="invoice-item-look"
                    style={{ marginLeft: 0 }}
                    onClick={e => {
                      e.stopPropagation()
                      fnHandleDownloadPDF(item)
                    }}
                  >
                    {i18n.get('查看文件')}
                  </span>
                )}
              </div>
              <div className="right-action">
                <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
              </div>
            </div>
            {master && master.bindValidateMessage && (
              <div className={`type-error mb-8 mt-5`}>
                <RichMessage message={master.bindValidateMessage} theme="light" />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

function BusInvoice(props) {
  const {
    item,
    isEdit,
    isInvoiceManagePermissions,
    isReadOnlyPage,
    onChange,
    onOpenEditModal,
    showInfoBtn = true
  } = props
  const { master } = item
  const {
    E_system_客运汽车发票_时间,
    E_system_客运汽车发票_出发车站,
    E_system_客运汽车发票_达到车站,
    E_system_客运汽车发票_金额,
    E_system_客运汽车发票_姓名,
    E_税额,
    E_是否抵扣
  } = master.form
  const _start = E_system_客运汽车发票_出发车站 ? E_system_客运汽车发票_出发车站 : ''
  const _end = E_system_客运汽车发票_达到车站 ? E_system_客运汽车发票_达到车站 : ''
  
  // 修改 title 逻辑：判断拼接字段都为空的情况
  let title
  if (!_start && !_end) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = `${_start} -- ${_end}`
  }
  
  const bus_name = E_system_客运汽车发票_姓名 ? E_system_客运汽车发票_姓名 : ''
  const info = isEdit
    ? `${moment(E_system_客运汽车发票_时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))} ${bus_name}`
    : `${moment(E_system_客运汽车发票_时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))}`
  const otherData = [{ label: i18n.get('乘车人'), value: bus_name }]
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className="invoice-item-blue">
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          item={item}
          allMoney={E_system_客运汽车发票_金额}
          title={title}
          info={info}
          {...props}
          typeText={i18n.get('客运汽车票')}
          typeTextCls={'invoice-item-blueType'}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            item={item}
            {...props}
            onOpenEditModal={() => onOpenEditModal(item)}
            allMoney={E_system_客运汽车发票_金额}
            otherData={otherData}
            taxAmount={E_税额}
            isDeduction={E_是否抵扣}
            onChange={onChange}
            isInvoiceManagePermissions={isInvoiceManagePermissions}
          />
        )}
        {showInfoBtn && (
          <>
            <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
              <div className="right-action">
                <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
              </div>
            </div>
            {master && master.bindValidateMessage && (
              <div className={`type-error mb-8 mt-5`}>
                <RichMessage message={master.bindValidateMessage} theme="light" />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

function ShoppingInvoice(props) {
  const {
    item,
    isEdit,
    isInvoiceManagePermissions,
    isReadOnlyPage,
    onChange,
    onOpenEditModal,
    imgInfo,
    showInfoBtn = true
  } = props
  const { master } = item
  const { E_system_消费小票_店名, E_system_消费小票_时间, E_system_消费小票_金额, E_是否抵扣 } = master.form
  const title = getFormattedInvoiceTitle(E_system_消费小票_店名)
  const info = `${moment(E_system_消费小票_时间).format(i18n.get('YYYY年MM月DD日 HH:mm'))}`
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className="invoice-item-blue">
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          item={item}
          allMoney={E_system_消费小票_金额}
          title={title}
          info={info}
          {...props}
          typeText={i18n.get('消费小票')}
          typeTextCls={'invoice-item-blueType'}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            item={item}
            {...props}
            onOpenEditModal={() => onOpenEditModal(item)}
            allMoney={E_system_消费小票_金额}
            isDeduction={E_是否抵扣}
            onChange={onChange}
            isInvoiceManagePermissions={isInvoiceManagePermissions}
          />
        )}
        {showInfoBtn && (
          <>
            <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
              <div className="right-action">
                <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
              </div>
            </div>
            {!!imgInfo?.orgFileName && handleOrgFileName(imgInfo?.orgFileName)}
            {master && master.bindValidateMessage && (
              <div className={`type-error mb-8 mt-5`}>
                <RichMessage message={master.bindValidateMessage} theme="light" />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

function OtherInvoice(props) {
  const { item, isEdit, isReadOnlyPage, onOpenEditModal, showInfoBtn = true } = props
  const { master } = item
  const { E_system_其他_日期, E_system_其他_金额 } = master.form
  const title = i18n.get('其他票据')
  const info = moment(E_system_其他_日期).format(i18n.get('YYYY年MM月DD日'))
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus

  return (
    <div className={styles[cls]}>
      <div className={showInfoBtn ? 'invoice-item-gray' : 'invoice-item-gray'}>
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          title={title}
          allMoney={E_system_其他_金额}
          info={info}
          {...props}
          typeText={i18n.get('其他票据')}
          typeTextCls={'invoice-item-grayType'}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            {...props}
            onOpenEditModal={() => onOpenEditModal(item)}
            item={item}
            allMoney={E_system_其他_金额}
          />
        )}
        {showInfoBtn && (
          <>
            <div class="invoice_line"></div>
            <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
              <div className="right-action">
                <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
              </div>
            </div>
          </>
        )}

        {master && master.bindValidateMessage && (
          <div className={`type-error mb-8 mt-5`}>
            <RichMessage message={master.bindValidateMessage} theme="light" />
          </div>
        )}
      </div>
    </div>
  )
}

function OverseasInvoice(props) {
  const { item, isEdit, isReadOnlyPage, onOpenEditModal, showInfoBtn = true } = props
  const { master } = item
  const {
    E_system_海外发票_日期,
    E_system_海外发票_金额,
    E_system_海外发票_税额,
    E_system_海外发票_title,
    E_system_发票主体_图片,
    E_system_海外发票_票据类型,
    E_system_海外发票_不计税金额,
    E_system_海外发票_AI智能总结
  } = master.form
  const title = getFormattedInvoiceTitle(E_system_海外发票_title)
  const info = moment(E_system_海外发票_日期).format(i18n.get('YYYY年MM月DD日'))
  const cls = getClassNameOfInvoiceCardWrap(item)
  const invoiceDisableMarkStatus = getInvoiceDisableMarkStatus(props)
  const { showMark, showDisable } = invoiceDisableMarkStatus
  const hasPdf = E_system_发票主体_图片 && E_system_发票主体_图片.toLowerCase().endsWith('.pdf')

  const [aiSummaryState, setAiSummaryState] = React.useState(E_system_海外发票_AI智能总结 || '')

  React.useEffect(() => {
    if (!aiSummaryState && master?.id) {
      console.log('invoiceId', master?.id)
      const id = get(item, 'master.id')
      getAiSummary(id).then(res => {
        setAiSummaryState(res?.value?.form['E_system_海外发票_AI智能总结'] || '')
      })
    }
  }, [master?.id])

  return (
    <div className={styles[cls]}>
      <div className={'invoice-item-yellow'}>
        {isEdit && <EditDelete data={props} />}
        <InvoiceMid
          title={title}
          allMoney={E_system_海外发票_金额}
          taxMoney={E_system_海外发票_税额}
          info={info}
          {...props}
          typeText={E_system_海外发票_票据类型 || i18n.get('海外票据')}
          typeTextCls={'invoice-item-nontax'}
          noTaxMoney={E_system_海外发票_不计税金额}
        />
        {(isReadOnlyPage || !isEdit) && (
          <InvoiceOtherInfo
            {...props}
            onOpenEditModal={() => onOpenEditModal(item)}
            item={item}
            allMoney={E_system_海外发票_金额}
            taxMoney={E_system_海外发票_税额}
            noTaxMoney={E_system_海外发票_不计税金额}
          />
        )}
        <RenderInvoiceFooter
          showInfoBtn={showInfoBtn}
          fnHandleDownloadPDF={fnHandleDownloadPDF}
          hasPdf={hasPdf}
          item={item}
          summary={aiSummaryState}
        />
        {showInfoBtn && (
          <div className="invoice-item-wrap" style={{ justifyContent: 'flex-end' }}>
            <div className="right-action">
              <InvoiceToDisableOrMark {...props} showDisable={showDisable} showMark={showMark} />
            </div>
          </div>
        )}
        {master && master.bindValidateMessage && (
          <div className={`type-error mb-8 mt-5`}>
            <RichMessage message={master.bindValidateMessage} theme="light" />
          </div>
        )}
      </div>
    </div>
  )
}

function RenderInvoiceFooter(props) {
  const { showInfoBtn, fnHandleDownloadPDF, hasPdf, item, summary } = props
  const [isShowInvoice, setIsShowInvoice] = useState(false)
  const detailsFields = invoiceTypeFields(item.master.form, item.master.entityId)[item.master.entityId] || []
  if (!showInfoBtn) {
    return null
  }

  const handleOpen = () => {
    setIsShowInvoice(!isShowInvoice)
  }

  return (
    <>
      {isShowInvoice && <div className="invoice_line" />}
      {isShowInvoice && <InvoiceExtraFields form={item.master.form} fields={detailsFields} />}
      <div className="invoice-item-wrap flex-column">
        <div className="look-and-open">
          <div className="invoice-item-open" onClick={handleOpen}>
            {isShowInvoice ? i18n.get('收起明细') : i18n.get('展开明细')}
          </div>
          {hasPdf && (
            <span className="invoice-item-look" onClick={() => fnHandleDownloadPDF(item)}>
              {i18n.get('查看文件')}
            </span>
          )}
        </div>
        {summary && (
          <Popover content={summary} title={i18n.get('AI总结说明')} overlayStyle={{ width: 400 }}>
            <div className="invoice-item-ai-summary">
              <div className="ai-summary">
                <EButton category="text" theme="highlight" icon={<TwoToneLogoAi fontSize={16} />}>
                  <div className="ai-summery-btn">
                    {i18n.get('AI智能总结')}
                    <OutlinedDirectionRight style={{ marginLeft: 4 }} fontSize={14} />
                  </div>
                </EButton>
                <span style={{ verticalAlign: 'text-bottom' }}>{summary}</span>
              </div>
            </div>
          </Popover>
        )}
      </div>
    </>
  )
}

function handlePhotoOperate(item, fn) {
  fn && fn(item)
}

// 删除发票照片前，弹出二次确认弹窗
function fnDeletePhoto(item, fn) {
  showWarningModal(() => {
    fn && fn(item)
  })
}

function InvoicePhotoView(props) {
  const { isEdit, item, onFileDownload, onFilePreview, onRemoveAttachment, isDownload = true } = props
  return (
    <div className={styles['invoice-item-wrapper']}>
      <div className={'invoice-item-gray'}>
        {isEdit && (
          <div
            className="edit-delete"
            onClick={e => {
              e.stopPropagation()
              fnDeletePhoto(item, onRemoveAttachment)
            }}
          >
            <EKBIcon name="#EDico-delete" className="icon-delete" />
          </div>
        )}
        <div className="invoice-item-mid">
          <div className="invoice-item-mid-content">
            <div className="invoice-item-intr option-line" onClick={() => handlePhotoOperate(item, onFilePreview)}>
              <div className="invoice-photo-content" onClick={() => handlePhotoOperate(item, onFilePreview)}>
                <img className="invoice-photo-img" src={item.url} />
              </div>
              <div className="invoice-item-title">{item.fileName}</div>
              <div className="invoice-photo-operate">
                <div
                  className="item"
                  onClick={e => {
                    e.stopPropagation()
                    e.preventDefault()
                    handlePhotoOperate(item, onFilePreview)
                  }}
                >
                  {i18n.get('预览')}
                </div>
                {isDownload && !IMG_REG.test(item.fileName) && (
                  <div className="item" onClick={() => handlePhotoOperate(item, onFileDownload)}>
                    {i18n.get('下载')}
                  </div>
                )}
              </div>
            </div>
            <div className={'invoice-item-grayType'}>{i18n.get('发票照片')}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

function handleCopy() {
  showMessage.success(i18n.get('复制成功！'))
}

function InvoiceOtherInfo(props) {
  const { item, allMoney, otherData = [], isReadOnlyPage, canEditTaxAmount, taxMoney, noTaxMoney } = props
  const OverseasInvoicePower = api.getState()['@common'].powers.OverseasInvoice
  const allMoneyDetail =
    item.details && item.details.length && !isElectronicAirAndTrain(item.master?.form)
      ? invoiceTaxInfo.getAllMoney(item.details)
      : getTaxAmount(item.master)
  const isShowForeign = item?.master?.entityId === 'system_海外发票'
  const className = isReadOnlyPage ? 'invoice-edit-item-system' : ''
  const isEdit = !!canEditTaxAmount
  const onlyShowForeign = isShowForeign && OverseasInvoicePower && onlyOneCurrencyHideSeaLocalCurrrency()

  return (
    <div className="invoice-item-other-info">
      {otherData.map((item, index) => {
        const isCopy = item.isCopy
        const cls = isCopy ? 'invoice-otherInfo-item-bg' : 'invoice-otherInfo-item'
        return (
          <Tooltip title={i18n.get('点击复制')} overlayStyle={isCopy ? {} : { display: 'none' }} key={index}>
            <div className={cls} key={index}>
              <CopyToClipboard text={item.value} onCopy={isCopy ? handleCopy : null}>
                <div className="invoice-info-value">
                  {item.label}
                  <span>{item.value}</span>
                </div>
              </CopyToClipboard>
            </div>
          </Tooltip>
        )
      })}
      <div className="invoice-otherInfo-money">
        {item.master.entityId === 'system_发票主体' ? ( // @i18n-ignore
          <div>
            <div>{i18n.get('价税合计')}</div>
            <Money className="invoice-item-money" value={allMoneyDetail} withoutStyle={isIE()} />
          </div>
        ) : (
          <div>
            <div>{i18n.get('总金额')}</div>
            <Money
              className="invoice-item-money"
              isShowForeign={isShowForeign ? isShowForeign && allMoney?.foreignNumCode : allMoney?.foreignNumCode }
              onlyForeign={isShowForeign ? onlyShowForeign && allMoney?.foreignNumCode : allMoney?.foreignNumCode }
              value={allMoney}
              withoutStyle={isIE()}
            />
          </div>
        )}
        {taxMoney && (
          <>
            <div className="invoice_line2"></div>
            <div>
              <div>{i18n.get('税额')}</div>
              <Money
                className="invoice-item-money"
                isShowForeign={isShowForeign ? isShowForeign && taxMoney.foreignNumCode : taxMoney?.foreignNumCode}
                onlyForeign={isShowForeign ? onlyShowForeign && taxMoney?.foreignNumCode : taxMoney?.foreignNumCode}
                value={taxMoney}
                withoutStyle={isIE()}
              />
            </div>
          </>
        )}
        {noTaxMoney && (
          <>
            <div className="invoice_line2"></div>
            <div>
              <div>{i18n.get('不计税金额')}</div>
              <Money
                className="invoice-item-money"
                isShowForeign={isShowForeign ? isShowForeign && noTaxMoney.foreignNumCode : noTaxMoney?.foreignNumCode}
                onlyForeign={isShowForeign ? onlyShowForeign && noTaxMoney?.foreignNumCode : noTaxMoney?.foreignNumCode}
                value={noTaxMoney}
                withoutStyle={isIE()}
              />
            </div>
          </>
        )}
      </div>
      {renderInvoiceEdit(props, isEdit, className)}
      {renderInvoiceReview(props, false)}
    </div>
  )
}

function renderInvoiceReview(props, isEdit = true) {
  const cls = props?.isEdit ? 'invoice-edit-item' : 'invoice-approve-item'
  const linecls = props?.isEdit ? 'invoice_line' : 'invoice_line_approve'

  const {
    item,
    allMoney,
    onOpenEditReviewAmount,
    isHover = false,
    billState,
    isModify,
    modifyApproveMoney = false
  } = props
  if (!modifyApproveMoney || getBoolVariation('close-approveAmount-validate')) {
    return <></>
  }
  let { approveAmount, comment = '' } = item
  approveAmount =
    approveAmount == void 0
      ? item.master.entityId === 'system_发票主体'
        ? item.details && item.details.length
          ? invoiceTaxInfo.getAllMoney(item.details)
          : getTaxAmount(item.master)
        : allMoney
      : approveAmount
  const hasComment = comment ? !!comment.length : false
  const isSubmitEditable =
    (billState === 'draft' || billState === 'new' || billState === 'rejected') && modifyApproveMoney.isSubmitEditable
  const showEdit = isEdit && !isHover && (isModify || isSubmitEditable)
  const content = <p>{comment}</p>
  return (
    <div className="invoice-edit-wrapper">
      <div className={linecls} style={{ marginBottom: '12px' }} />
      <div className={'invoice-edit-item-tickets'}>
        <div className={'invoice-edit-item-data'} style={{ width: showEdit ? '92%' : '100%' }}>
          <div className={cls}>
            <div className={'invoice-edit-txt'}>
              {i18n.get('核发金额')}
              {hasComment ? <img className="ml-4" src={SVG_WARNING} /> : null}
            </div>
            <Money className="text" value={approveAmount} withoutStyle={isIE()} />
          </div>
          <div className={cls}>
            <div>{i18n.get('核发批注')}</div>
            {/* <div className="text">{comment}</div> */}
            <Popover content={content} title="" placement="topRight">
              <div className="invoice-show-txt text">{comment}</div>
            </Popover>
          </div>
        </div>
        {showEdit && (
          <div className={`${cls} invoice-edit`} onClick={onOpenEditReviewAmount}>
            {i18n.get('编辑')}
          </div>
        )}
      </div>
    </div>
  )
}

function renderInvoiceEdit(props, isEdit = true, className = '') {
  const { item, showPriceAndRate = true } = props
  const {
    master: { entityId }
  } = item
  if (!showPriceAndRate) {
    return null
  }
  return (
    <div className="invoice-edit-wrapper">
      {entityId !== i18n.get('system_发票主体') &&
        invoiceTaxInfo.fnCanEditType(entityId) &&
        renderTickets(props, isEdit)}
      {entityId === i18n.get('system_发票主体') && renderInvoice(props, isEdit, className)}
    </div>
  )
}

function renderTickets(props, isEdit) {
  const {
    item,
    onOpenEditModal,
    isHover = false,
    isInvoiceManagePermissions = false,
    billState,
    isInHistory = false,
    imgInfo,
    isModify
  } = props
  const cls = props?.isEdit ? 'invoice-edit-item' : 'invoice-approve-item'
  const linecls = props?.isEdit ? 'invoice_line' : 'invoice_line_approve'
  const taxAmount = invoiceTaxInfo.getTaxAmount(item)
  const taxRate = invoiceTaxInfo.getTaxRate(item)
  const invoicePriceAndTaxEditPermission = api.getState()['@common']?.invoicePriceAndTaxEditPermission
  const hasEditPermission = isInvoiceManagePermissions || invoicePriceAndTaxEditPermission || (isModify && isEdit)
  return (
    <>
      <div className={linecls} style={{ marginBottom: '12px' }} />
      <div className={'invoice-edit-item-tickets'}>
        <div className={'invoice-edit-item-data'}>
          <div className={cls}>
            <div>{i18n.get('税率')}</div>
            <div className="text">
              {taxRate}
              {'%'}
            </div>
          </div>
          <div className={cls}>
            <div>{i18n.get('可抵扣税额')}</div>
            <Money className="text" value={taxAmount} withoutStyle={isIE()} />
          </div>
        </div>
        {!isHover && hasEditPermission && invoiceTaxInfo.fnCanState(billState) && !isInHistory && (
          <div className={`${cls} invoice-edit`} onClick={onOpenEditModal}>
            {i18n.get('编辑')}
          </div>
        )}
      </div>
      {!!imgInfo?.orgFileName && handleOrgFileName(imgInfo?.orgFileName, true, isEdit)}
    </>
  )
}

function renderInvoice(props, isEdit, invoiceItemClassName = '') {
  const {
    item,
    onOpenEditModal,
    isHover = false,
    isInvoiceManagePermissions = false,
    billState,
    isModify,
    isInHistory = false,
    disableDelete
  } = props
  const cls = props?.isEdit ? 'invoice-edit-item' : 'invoice-approve-item'
  const total = invoiceTaxInfo.getTaxAmount(item, disableDelete)
  const taxRate = invoiceTaxInfo.getTaxRate(item)
  const invoicePriceAndTaxEditPermission = api.getState()['@common']?.invoicePriceAndTaxEditPermission
  const hasEditPermission = isInvoiceManagePermissions || invoicePriceAndTaxEditPermission || (isModify && isEdit)
  return (
    <>
      <div className={'invoice-edit-item-data'}>
        {!!taxRate && (
          <div className={`${cls} ${invoiceItemClassName}`}>
            <div>{i18n.get('税率')}</div>
            <div className="text">
              {taxRate}
              {'%'}
            </div>
          </div>
        )}
        <div className={`${cls} ${invoiceItemClassName}`}>
          <div>{i18n.get('可抵扣税额')}</div>
          <div className="invoice-edit-item-right">
            <Money className="text" value={total} withoutStyle={isIE()} />
            {isEdit && !isHover && hasEditPermission && invoiceTaxInfo.fnCanState(billState) && !isInHistory && (
              <div onClick={onOpenEditModal} className="ml-8 invoice-edit">
                {i18n.get('编辑')}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

function InvoiceToDisableOrMark(props) {
  const { showDisable = false, showMark = false } = props

  return (
    <>
      {showDisable && (
        <span className="invoice-disable-btn" onClick={() => handleDisableClick(props, InvoiceMarkDisableType.DISABLE)}>
          {i18n.get('点击禁用')}
        </span>
      )}
      {showMark && (
        <span className="invoice-disable-btn" onClick={() => handleDisableClick(props, InvoiceMarkDisableType.MARK)}>
          {i18n.get('点击标记')}
        </span>
      )}
    </>
  )
}

function getInvoiceDisableMarkStatus(props) {
  const { item, isHover, isReadOnlyPage } = props //isEdit和isModify在补充发票查看单据详情时是true,故用isReadOnlyPage判断是编辑态
  const { disable, canDisable, currentFlowState } = item?.disableInfo || {}

  //编辑态可以禁用发票
  const showDisable = ['APPROVING'].includes(currentFlowState) && !isReadOnlyPage && canDisable && !disable && !isHover
  //非编辑态可以标记发票
  const { mark, canMark } = item.markInfo || {}
  const showMark = ['APPROVING'].includes(item?.markInfo?.currentFlowState) && isReadOnlyPage && !mark && canMark
  return {
    showMark,
    showDisable
  }
}

function getClassNameOfInvoiceCardWrap(item) {
  const { disable } = item?.disableInfo || {}
  const { mark } = item?.markInfo || {}
  const grey = disable || mark
  return grey ? 'invoice-item-wrapper-disable' : 'invoice-item-wrapper'
}

//处理发票原文件名
function handleOrgFileName(fileName, style, isEdit) {
  const filecls = !style || !!isEdit ? 'invoice-origin-file-name' : 'invoice-origin-file-name origin-file-padding'
  const name = fileName.substring(0, 50) + (fileName.length > 50 ? '...' : '')
  return (
    <div className={filecls}>
      <span>{i18n.get(`文件名：${name}`)}</span>
    </div>
  )
}

// 检查是否有pdf文件
const fnCheckInvoicePdf = (invoiceInfo, invoiceValue, isFullDigital) => {
  const source = invoiceInfo && invoiceInfo.source
  //@i18n-ignore
  const aliPayPDF = get(invoiceValue, 'master.form.E_system_发票主体_attachmentType', '')
  //@i18n-ignore
  const ocrPDF = get(invoiceValue, 'master.form.E_system_发票主体_图片', '') || ''
  const pdf = ocrPDF.toLowerCase().endsWith('.pdf') || ocrPDF.toLowerCase().endsWith('.ofd')

  let hasPdf =
    source === 'UPLOAD' ||
    source === 'WECHAT_CARD' ||
    (source === 'ALIPAY_CARD' && aliPayPDF === 'PDF') ||
    (source === 'OCR' && pdf) ||
    (['AIFAPIAO', 'QXY'].includes(source) && pdf) ||
    (isFullDigital && pdf)
  if (
    source === 'OUTPUT_OPEN_API' &&
    invoiceValue?.master?.entityId === 'system_发票主体' &&
    invoiceValue?.master?.form?.['E_system_发票主体_原件地址']?.length > 0
  ) {
    hasPdf = true
  }
  return hasPdf
}

const fnHandleDownloadPDF = (props, e) => {
  e && e.stopPropagation && e.stopPropagation()
  e && e.preventDefault && e.preventDefault()
  const { invoiceInfo } = formatInvoiceData({ data: props })
  const file = IMAGE_MAP.get(get(invoiceInfo, 'id'))
  api.invokeService('@common:download:invoce:pdf', invoiceInfo, file)
}

const fnHandleDownloadXML = (props, e) => {
  e && e.stopPropagation && e.stopPropagation()
  e && e.preventDefault && e.preventDefault()
  const { E_system_发票主体_XML } = props.master.form
  api.emit('@vendor:preview:byFetch', undefined, E_system_发票主体_XML, E_system_发票主体_XML)
}

const isFile = data => {
  return !IMG_REG.test(data?.master?.form?.['E_system_发票主体_图片'])
}

export { mapItem }
