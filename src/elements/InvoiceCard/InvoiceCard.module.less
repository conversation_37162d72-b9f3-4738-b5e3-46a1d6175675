@import '~@ekuaibao/web-theme-variables/styles/colors';

.invoice_card_wrap {
  width: 100%;
}

.invoice_card_header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  :global {
    .invoice-type {
      position: absolute;
      left: 0;
      width: 100%;
      text-align: center;
      font-size: 12px;
      color: #d37b45;
      line-height: 31px;
    }

    .invoice-header-bg {
      width: 60px;
    }

    .invoice-delete {
      position: absolute;
      right: 0;
      top: 5px;
      height: 20px;
      border-left: 1px solid #d8d8d8;
      padding-left: 10px;
      text-align: center;
      cursor: pointer;

      img {
        vertical-align: inherit;
      }
    }

    .pdf {
      position: absolute;
      right: 0;
      font-size: 12px;
      color: @primary-7;
      cursor: pointer;
    }

    .down-pdf {
      position: absolute;
      right: 40px;
      font-size: 12px;
      color: @primary-7;
      cursor: pointer;
    }
  }
}

.invoice_card_body {
  :global {
    .invoice-amount {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);

      span {
        font-size: 16px;
      }
    }

    .subtitle {
      margin-top: 4px;
      margin-bottom: 8px;
      font-size: 12px;
      text-align: center;
      color: @gray-8;
    }

    .row {
      margin-bottom: 4px;
      display: flex;

      .title {
        flex-shrink: 0;
        width: 126px;
        line-height: 22px;
        font-size: 12px;
        color: #d37b45;
      }

      .label {
        margin-left: 20px;
        flex: 1;
        line-height: 22px;
        text-align: left;
        font-size: 12px;
        color: @gray-9;
      }

      img.ml-5 {
        width: 15px;
      }
    }

    .tips {
      position: relative;
      width: 100%;
      border-bottom: 1px dashed #e6e6e6;

      .payee-tips {
        position: absolute;
        top: -12px;
        width: 234px;
        height: 24px;
        color: #8c8c8c;
        text-align: center;
        line-height: 24px;
        border: 1px solid #f5f5f5;
        border-radius: 12px;
        background: #f5f5f5;
        left: 50%;
        margin-left: -117px;
      }
    }

    .invoice-details {
      margin-top: 12px;

      .invoice-details-thead {
        margin: 17px 0 8px;
        display: flex;
        color: #d37b45;

        .detail-amount,
        .detail-tax {
          flex: 2;
          color: @gray-8;
        }
      }

      .invoice-details-tbody {
        color: @gray-9;

        .detail-tbody-line {
          display: flex;
          overflow: hidden;
          width: 100%;
          margin-bottom: 4px;
          align-items: center;
        }

        .isActive {
          color: #dcdcdc;
        }

        .detail-tbody-hover {
          &:hover {
            background: #f5f5f5;
          }
        }
      }
    }

    .total-info {
      display: flex;
      color: @gray-9;

      .detail-name {
        color: #d37b45;
      }
    }

    .remark-wrapper {
      margin-bottom: 28px;

      .row {
        .label {
          white-space: normal;
          word-wrap: break-word;
          word-break: break-all;
        }
      }
    }

    .invoice-detail-wrapper {
      width: 100%;
      height: 26px;
      line-height: 26px;
      text-align: center;
      color: #595959;
      margin-top: 12px;
      cursor: pointer;
      background-color: #ffffff;
      border: solid 1px #e8e8e8;

      .invoice-detail {
        width: 100%;
        height: 100%;
      }
    }

    .detail-mark {
      width: 6px;
      height: 6px;
      margin-right: 5px;
      background-color: #d37b45;
    }

    .detail-mark-grey {
      width: 6px;
      height: 6px;
      margin-right: 5px;
      background-color: #bfbfbf;
    }

    .detail-name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      flex: 1;
      height: 22px;
      line-height: 22px;
      width: 0;
    }

    .detail-number {
      width: 12%;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      height: 22px;
      line-height: 22px;
    }

    .invoice-watch {
      position: absolute;
      right: 3px;
      width: 12px;
      height: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .detail-amount,
    .detail-tax {
      flex: 2;
      color: @gray-10;
    }
  }
}

.invoice-payer-info-tooltip {
  &>span {
    display: inline-block;
    padding-left: 25px;
    font-size: 12px;
    line-height: 22px;
    color: rgba(20, 34, 52, 0.6);
    background: url(./images/icon-invoice-payer-info-tooltip.svg) 0 0 no-repeat;
    background-size: 18px 18px;

    &>em {
      font-size: 14px;
    }

    &>em,
    &>strong {
      font-weight: normal;
      font-style: normal;
      line-height: 22px;
      color: #FF4D4F;
    }
  }
}
