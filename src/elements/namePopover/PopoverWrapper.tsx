import React from 'react'
import styles from './PopoverWrapper.module.less'
import { PopupProfileCard } from '../profile-card/popup-profile-card'
import { NameCell } from '../name-cell'

interface Props {
  info: any
  id?: any
  name: React.ReactNode
  trigger: any
  placement: any
}

export default ({ name, info, trigger, placement }: Props) => {
  const isUserItem = (item: any) => item.userId

  if (window.isInWeComISV && isUserItem(info)) {
    return (
      <span data-type="wrap_popover_name">
        {isUserItem(info) ? <NameCell type="user" id={info.id} name={info.name} /> : name}
      </span>
    )
  }

  return (
    <PopupProfileCard staffId={info.id} trigger={trigger} placement={placement}>
      <span data-type="wrap_popover_name" className={styles.wrap_popover_name}>
        {isUserItem(info) ? <NameCell type="user" id={info.id} name={info.name} /> : name}
      </span>
    </PopupProfileCard>
  )
}
