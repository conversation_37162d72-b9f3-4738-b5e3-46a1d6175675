.e-card-wrapper {
  display: flex;
  position: relative;
  width: 50%;

  .e-card-content {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 80px;
    padding-left: 16px;
    border-radius: 8px;
    background: linear-gradient(90deg, #E9F2FF 2.65%, #C0CFEA 99.38%);

    .content {
      margin-left: 12px;
      display: flex;
      flex-direction: column;

      .title {
        color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
        font: var(--eui-font-head-b1);
      }

      .cardNo {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.70));
        font: var(--eui-font-body-r1);
      }
    }

    .hose-bg {
      height: 100%;
      position: absolute;
      right: 20px;
      top: 0;
      bottom: 0;
    }
  }

  .del-e-card {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
    width: 22px;
    height: 22px;
    font-size: 14px;
    padding: 4px;
    border-radius: 0px 6px;
    color: var(--eui-icon-n2);
    cursor: pointer;
    background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
  }
}

.e-card-wrapper:hover {
  .del-e-card {
    display: block;
  }

  .e-card-content {
    box-shadow: var(--eui-shadow-down-3);
  }
}