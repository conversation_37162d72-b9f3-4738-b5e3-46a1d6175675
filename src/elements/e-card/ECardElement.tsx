import React from 'react'
import { OutlinedEditDeleteTrash, TwoToneLogoHoseCircular } from '@hose/eui-icons'
import HOSE_BG from '../../images/ecard-hose-bg.png'
import './EBusinessElement.less'
import { IECardItem } from '../../components/dynamic/EBusinessCard'

interface IEBusinessCard {
  selectedItem: IECardItem
  onDel?: () => void
}

const EBusinessCardComponent: React.FC<IEBusinessCard> = props => {
  const { selectedItem, onDel } = props

  return (
    <div className="e-card-wrapper">
      <div className="e-card-content">
        <TwoToneLogoHoseCircular fontSize={32} />
        <div className="content">
          <div className="title">{selectedItem?.cardTypeName}</div>
          <div className="cardNo">{selectedItem?.cardNo}</div>
        </div>
        <img className="hose-bg" src={HOSE_BG} />
      </div>
      <OutlinedEditDeleteTrash className="del-e-card" fontSize={14} onClick={onDel} />
    </div>
  )
}

export default EBusinessCardComponent
