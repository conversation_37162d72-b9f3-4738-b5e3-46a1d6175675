import React from 'react'
import * as DataGrid from '@ekuaibao/datagrid'
import { Map } from '@ekuaibao/datagrid/esm/types/utils'
import { PaginationConfig, PageMode } from '@ekuaibao/datagrid/esm/types/pagination'
import { Data } from '@ekuaibao/datagrid/esm/types/dataSource'
import classnames from 'classnames'
import MessageCenter from '@ekuaibao/messagecenter'
import { Column } from '@ekuaibao/datagrid/esm/types/column'
import { LightingMode } from './LightingMode'
import { Scene, TableFilter } from './types/scene'
import { app as api } from '@ekuaibao/whispered'
import styles from './DataGrid.module.less'
import { Buttons, Button } from './Buttons'
import fetchFixer from '../data-grid/fetchFixer'
import { getColumnPropertyMapping } from '../data-grid/columnsUtil'
import moment from 'moment'
import { cloneDeep } from 'lodash'
import { Resource } from '@ekuaibao/fetch'
import { array2object, isFunction } from '@ekuaibao/helpers'
import { getV } from '@ekuaibao/lib/lib/help'
import { showModal, showMessage } from '@ekuaibao/show-util'
import { EnhanceConnect } from '@ekuaibao/store'
import { debounce } from 'lodash'
import { FieldIF } from '@ekuaibao/ekuaibao_types'
import CustomSearch from './element/CustomSearch'
import { SearchOption } from './element/type'
import driveDoneBillsOnBoarding from '../../components/guide/driveDoneBillsOnBoarding'
import { getBoolVariation } from '../../lib/featbit'
import driveDoneBillsOnBoarding from '../../components/guide/driveDoneBillsOnBoarding'
import { AIAgentTag } from '../ai-agent-utils'
import { AI_CHAT_CREATE_BILLS_KEY, AichatCreateBillCache } from '../../lib/constants'

const TooltipEllipsis: any = api.require('@components/tooltip/ellipsis')
const { logTableSorterChange, logTableFilterChange, logTableGroupChange } = api.require<any>('@lib/logs')

const { injectErrorDom } = api.require<any>('@elements/LoadingError')

const action = api.invokeServiceAsLazyValue('@common:import:action')

const statusMessage: Function = (): Map<string> => ({
  paying: i18n.get('该单据当前状态为「待支付」，请切换待办列表。'),
  approving: i18n.get('该单据当前状态为「待审批」，请切换待办列表。'),
  rejected: i18n.get('该单据已驳回')
})

interface FetchParams {
  scanText?: string
  searchOptionText?: string
  page?: { currentPage: number; pageSize: number }
  searchText?: string
  filters?: {
    [key: string]: any
  }
  sorters?: {
    [key: string]: 'ascend' | 'descend'
  }
  scene?: string
  creditCondition?: string
  total?: number
  columns?: string[]
}
export interface DataGridWrapperProps {
  newSearch?: boolean
  searchOptions?: SearchOption[]
  enableGlobalSearch?: boolean
  lightingMode?: boolean
  dynamicChannelMap: any
  bus: MessageCenter
  scenes: Scene[]
  disabledScenes?: boolean
  fetch: (
    params: FetchParams,
    dimensionItems: any
  ) => Promise<{
    total: number
    dataSource: Data[]
  }>
  buttons: Button[]
  columns: Column[]
  scenesType: string
  status?: string
  columnMinWidth?: number
  onButtonClick: (e: { name: string; data: Map<Data>; keys: string[]; total: number }, fetchParams: FetchParams) => void
  onSelectedAll?: (fetchParams: FetchParams) => void
  onEditScenes: (activeSceneIndex: string) => void
  onSelectedChange?: (selectedKeys: string[], selectedRowsData: Map<Data>) => void
  menuBar?: () => React.FC | JSX.Element
  resource: Resource
  params?: { id: string }
  searchPlaceholder?: string
  isDiDi?: boolean
  showOutsideButtonCount?: number
  rowKey: string
  isNeedHeight?: boolean
  isHiddencolumnChooser: boolean
  isHiddenPagination?: boolean
  isHiddenSearch?: boolean
  filters?: Map<any>
  selectedRowKeys?: string[]
  selectedRowsData?: Map<Data>
  relativeToContaier?: any
  isMultiSelect?: boolean
  styleHeight?: [string, string | null, string | null]
  isVisibleSaveDiff: boolean
  isOnlyShowSaveDiff?: boolean
  bodyStyle?: any
  isGroup?: boolean
  columnsList?: any
  containerStyle?: any
  customColumnPropertyMapping: Record<string, any>
  headerTopRender?: () => React.FC
  onSenceChange?: (sence: Scene) => void
  activeSceneIndex?: string
  expansionScenes?: boolean
  saveSceneWithGroupIndex?: boolean // 保存快速筛选时是否保存维度分组
  useNewNumberFilter?: boolean // 新的数字金额筛选组件（包括操作符）
  DetailTemplate?: any
  enabledDetailTemplate?: boolean
  RenderDetailTemplate?: any
  getInstance?: any
  preRenderCellHandler?: any
  isLimitColumns?: boolean
  searchInputClassName?: string // 搜索框类名
  inFeeTypeMode?: boolean // 是否在明细维度中
  useNewFieldSet?: boolean // 是否使用新的列字段设置
  createAction?: () => void
  headerStyle?: any
  completeBillType?: string
  carbonTabType?: string
  showAIIcon?: boolean
}

export interface DataGridWrapperState {
  lightingValue: string
  currentPage: number
  pageSize: number
  activeSceneIndex: string
  pageMode: PageMode
  searchText: string
  filters: Map<any>
  sorters: Map<'ascend' | 'descend'>
  selectedRowKeys: string[]
  selectedRowsData: Map<Data>
  dataSource: Data[]
  total: number
  loadedAllData: boolean
  visibleColumns: string[]
  isVisibleSaveDiff: boolean
  rowKey?: string
  isNeedHeight?: boolean
  lightingErrorFeedback?: boolean
  isSearching: boolean
  visibleColumnsWidth: { [key: string]: number }
  columns: any[]
  sceneOnLoading: boolean // 判断快速筛选是否正在切换中
  outsideButtonCount?: number | null
  isPageChangeIng?: boolean
  searchOptionText?: string
  fetchError: boolean
  fixedRightColumns: Column[]
}

const DEFAULT_PAGESIZE = 20
const LIGHTING_MODE_PAGESIZE = 500
let _LAST_CLICK_ROW_INDEX: number | null = null
// 记录当前表格的选中行的aria-rowindex
let _CURRENT_SELECTED_ROW_AriaRowIndex: string | null = null
let __CURRENT_SELECTED_DATA_ID = null
const groupPanel = {
  visible: true
}
const scrolling = {
  mode: 'virtual' as 'virtual'
}

@EnhanceConnect((state: any) => ({
  dynamicChannelMap: state['@audit'].dynamicChannelMap,
  isLimitColumns: state['@common'].toggleManage?.['tg_flowlist_filed_filter']
}))
export class DataGridWrapper extends React.PureComponent<DataGridWrapperProps, DataGridWrapperState> {
  state: DataGridWrapperState = {
    lightingValue: '',
    currentPage: 1,
    pageSize: DEFAULT_PAGESIZE,
    activeSceneIndex: this.props.activeSceneIndex ? this.props.activeSceneIndex : 'all',
    pageMode: 'pagination',
    searchText: '',
    filters: this.props.filters ? this.props.filters : {},
    sorters: {},
    selectedRowKeys: this.props.selectedRowKeys ? this.props.selectedRowKeys : [],
    selectedRowsData: this.props.selectedRowsData ? this.props.selectedRowsData : {},
    dataSource: [],
    total: 0,
    loadedAllData: false,
    visibleColumns: this.getDefaultVisibleColumns(this.props.activeSceneIndex ? this.props.activeSceneIndex : 'all'),
    isVisibleSaveDiff: this.props.isVisibleSaveDiff || false,
    lightingErrorFeedback: false,
    visibleColumnsWidth: {},
    fixedRightColumns: []
  }

  private isSearching = false
  private unmounted = false
  private reachBottomRequest = false
  private instance: any
  private prevLightingSearchValue = ''
  private scenesOnSaving = false // 判断【保存变更】动作是否结束
  private hideErrorUI = null
  private resizeObserver: ResizeObserver | null = null

  // global search 的时候，checkbox 修改需要触发 search
  // 但是此时的 state 中 searchText 并不是最新值，需要 ref 去取
  private searchInputRef = React.createRef<any>()

  static defaultProps: Partial<DataGridWrapperProps> = {
    scenes: []
  }

  private bodyRef = React.createRef<HTMLDivElement>()

  constructor(props: DataGridWrapperProps, ...args: any[]) {
    super(props, ...args)
    const { scenes } = props
    const defaultScene: Scene = scenes?.find(d => d.sceneIndex === this.state.activeSceneIndex)
    this.state = {
      ...this.state,
      outsideButtonCount: null,
      sorters: defaultScene?.sorter ? defaultScene?.sorter : {},
      columns: this.formatColumns(props.columns, defaultScene),
      fetchError: false,
      fixedRightColumns: []
    }
  }

  async componentDidMount() {
    if (this.props.lightingMode) {
      this.setState({ dataSource: [], total: 0 })
    } else {
      this.setState(
        {
          pageSize: (this.props.scenes && this.props.scenes[0]?.pageSize) || DEFAULT_PAGESIZE
        },
        () => {
          this.initFetch()
        }
      )
    }

    window.addEventListener('resize', this.handleChangeOutsideButtonCount)
    this.bindBus()
    setTimeout(() => {
      this.instance && this.instance.resize()
    }, 1000)


    if (this.props.scenesType === "MyBill") {
      driveDoneBillsOnBoarding(this.bodyRef.current, { popover: { align: 'center' } })
    }
    this.handleResizeObserver()

    if (this.props.scenesType === "MyBill") {
      driveDoneBillsOnBoarding(this.bodyRef.current, { popover: { align: 'center' } })
    }
  }
  initFetch = async () => {
    const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
    if (!this.unmounted) {
      const { scenes } = this.props
      this.setState({ dataSource, total, pageSize: scenes?.[0]?.pageSize || 20 }, () => {
        this.handleUpdateSelectCurrentRowColor(dataSource)
      })
    }
  }
  handleChangeOutsideButtonCount = () => {
    this.setState({ outsideButtonCount: window?.innerWidth <= 1280 ? 1 : null })
  }

  componentWillUnmount() {
    this.unmounted = true
    this.unbindBus()
    window.removeEventListener('resize', this.handleChangeOutsideButtonCount)
    // 清理ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  }

  componentWillReceiveProps(nextProps) {
    // 组件重新渲染的话，重新获取传入的默认勾选项
    const { activeSceneIndex } = this.state
    let values = this.getUpdatedColumns(nextProps.scenes, activeSceneIndex)
    if (!!nextProps.activeSceneIndex && this.props.activeSceneIndex !== nextProps.activeSceneIndex) {
      values = this.getUpdatedColumns(nextProps.scenes, nextProps.activeSceneIndex)
      this.setState({ activeSceneIndex: nextProps.activeSceneIndex })
    }
    if (this.props.columns !== nextProps.columns && !this.scenesOnSaving) {
      const targetScene = nextProps.scenes.find(d => d.sceneIndex === activeSceneIndex)
      this.setState({ columns: this.formatColumns(nextProps.columns, targetScene) })
    }
    this.setState(values)
  }

  fnGetDataGridViewId = () => {
    const { scenesType = '', carbonTabType = '' } = this.props
    return scenesType === 'CARBONCOPY'
      ? `data-grid-container-wrapper-${carbonTabType}`
      : `data-grid-container-wrapper-${scenesType}`
  }

  handleResizeObserver = () => {
    if (!getBoolVariation('mfrd-3884-fixed-action-column') || typeof ResizeObserver === 'undefined') {
      return
    }
    const { columns = [] } = this.state
    const actionColumns = columns?.filter(col => col?.dataIndex === 'action')
    if (!actionColumns?.length) {
      return
    }

    // 创建ResizeObserver监听容器尺寸变化
    this.resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width } = entry.contentRect
        if (width > 0) {
          // 当容器变为可见且有宽度时，触发宽度变化处理
          this.handleGridTableWidthChange()
        }
      }
    })

    // 开始观察目标元素
    const element = document.getElementById(this.fnGetDataGridViewId())
    if (element) {
      this.resizeObserver.observe(element)
    }
  }

  handleGridTableWidthChange = () => {
    if (!getBoolVariation('mfrd-3884-fixed-action-column')) {
      return
    }
    const element = document.getElementById(this.fnGetDataGridViewId())
    const { columns, fixedRightColumns: stateFixedRightColumns = [] } = this.state
    const fixedRightColumns = columns.filter(col => col.fixed === 'right')
    if (element) {
      const { bus } = this.props
      const { width } = element.getBoundingClientRect()
      if (width <= 0) {
        return
      }
      if (fixedRightColumns.length > 0 || stateFixedRightColumns.length > 0) {
        if (width < 900 && (fixedRightColumns.length > 0 || stateFixedRightColumns.length > 0)) {
          bus.emit('table:coloumn:fixed:disabled', true)
          const fixedColumns = fixedRightColumns.length > 0 ? fixedRightColumns : stateFixedRightColumns
          this.handleUpdateFixedColumn(fixedColumns, { fixed: false, fixedDisabled: true })
          this.setState({ fixedRightColumns: fixedColumns })
        } else if (stateFixedRightColumns.length > 0) {
          bus.emit('table:coloumn:fixed:disabled', false)
          this.handleUpdateFixedColumn(stateFixedRightColumns, { fixed: 'right' })
          this.setState({ fixedRightColumns: [] })
        }
      } else if (!fixedRightColumns.length) {
        bus.emit('table:coloumn:fixed:disabled', true)
        const fixedDisabledColumns = columns.filter(col => col.dataIndex === 'action')
        this.handleUpdateFixedColumn(fixedDisabledColumns, { fixed: false, fixedDisabled: width < 900 })
      }
    }
  }

  getUpdatedColumns(scenes, activeSceneIndex) {
    let activeScene = scenes.find(scene => scene.sceneIndex === activeSceneIndex)
    let visibleColumns = activeScene ? activeScene.defaultColumns : void 0
    const visibleColumnsWidth = {}
    const visibleColumnsFixed = {}
    if (Array.isArray(visibleColumns) && visibleColumns.some(item => typeof item !== 'string')) {
      visibleColumns.forEach(col => {
        visibleColumnsWidth[col.dataField] = col.width
        visibleColumnsFixed[col.dataField] = col.fixed
      })
      return {
        visibleColumns: visibleColumns.map(d => d.dataField),
        visibleColumnsWidth,
        visibleColumnsFixed
      }
    }
    return {
      visibleColumns,
      visibleColumnsWidth,
      visibleColumnsFixed
    }
  }

  refreshState = (obj: any) => {
    if (obj?.silentSave && !this.state.isVisibleSaveDiff) {
      this.onSaveDiffScenes({
        silentSave: obj?.silentSave
      })
    } else {
      this.setState(obj, this.forceUpdate)
    }
    // 当调整列宽时，重绘表格，使列头的 renderCaption 重新触发
    setTimeout(() => {
      this.instance.repaint && this.instance.repaint()
    }, 0)
  }

  onSaveDiffScenes = debounce(async ({ silentSave }) => {
    const { saveSceneWithGroupIndex } = this.props
    if (saveSceneWithGroupIndex) {
      this.setState({ sceneOnLoading: true }, async () => {
        await this.handleOnSaveDiffScenes(silentSave)
        this.asyncSetSceneLoadingStatus()
      })
    } else {
      this.handleOnSaveDiffScenes(silentSave)
    }
  }, 500)

  handleOnSaveScenes = async scenes => {
    const { resource, params = { id: '' }, scenesType } = this.props
    if (!resource) {
      return ''
    }
    const filter = scenes.map((scene: any) => JSON.stringify(scene))
    const { pageSize } = this.state

    const res = await resource.POST(
      '/$type',
      {
        type: `${scenesType}${params.id || ''}`,
        filter,
        pageSize: pageSize
      },
      null,
      null,
      {
        hiddenLoading: true
      }
    )
    return res?.value
  }

  handleOnSaveDiffScenes = async (silentSave, fixedColumnMap = {}) => {
    if (this.instance) {
      const { scenes, bus, saveSceneWithGroupIndex } = this.props
      if (!silentSave) {
        this.scenesOnSaving = true
      }
      this.fnSetCurrentFixedAction()
      const { activeSceneIndex, sorters } = this.state
      const groupIndexMap = {}
      let visibleColumnsWithOther =
        this.instance && this.instance.getVisibleColumns ? this.instance.getVisibleColumns() : []
      visibleColumnsWithOther =
        visibleColumnsWithOther.length > 0 &&
        visibleColumnsWithOther
          .filter(d => d.dataField)
          .map(d => {
            const { dataField } = d
            if (d.groupIndex !== undefined && saveSceneWithGroupIndex) {
              groupIndexMap[dataField] = d.groupIndex
            }
            const fixed =
              fixedColumnMap?.[dataField] !== undefined
                ? fixedColumnMap?.[dataField]
                : this.fixVisibleColumns[dataField]
            return {
              dataField,
              width: d.width,
              // @ts-ignore
              fixed
            }
          })

      const visibleColumns = visibleColumnsWithOther.map(d => d.dataField)
      const targetScene = scenes.find(d => d.sceneIndex === activeSceneIndex)
      targetScene.defaultColumns = visibleColumnsWithOther
      if (!silentSave) {
        targetScene.groupIndexMap = groupIndexMap
      }
      const res = await this.handleOnSaveScenes(scenes)
      if (!silentSave) {
        this.setState(
          {
            isVisibleSaveDiff: false,
            visibleColumns,
            sorters
          },
          () => {
            this.scenesOnSaving = false
            // @ts-ignore
            bus.emit('initScenes:action', res, activeSceneIndex)
          }
        )
      }
    }
  }

  formatColumns(columns, targetScene) {
    // @ts-ignore
    this.targetSceneNote = targetScene
    const { saveSceneWithGroupIndex, useNewFieldSet, showAIIcon } = this.props
    const { activeSceneIndex = 'all', visibleColumns } = this.state
    const { widths, fixeds } = this.getDefaultVisibleColumnsWidth(activeSceneIndex ? activeSceneIndex : 'all')
    // @ts-ignore
    this.fixVisibleColumns = fixeds
    return columns.map((column) => {

      const columnValue = targetScene?.groupIndexMap?.[column.dataIndex]
      const groupIndexValue: any = {}
      if (columnValue !== undefined && saveSceneWithGroupIndex) {
        groupIndexValue.groupIndex = columnValue
      }
      const finalColumn = {
        ...column,
        ...groupIndexValue,
        width: widths[column.dataIndex] || column.width,
        // fixed: useNewFieldSet ? (column.dataIndex === 'action' ? 'right' : fixeds[column.dataIndex]) : undefined
        fixed: useNewFieldSet ? fixeds[column.dataIndex] : undefined
      }
      if (showAIIcon) {
        finalColumn.render = (value, record, index) => {
          // 获取最新的
          let aichatCreateBills: AichatCreateBillCache[] = localStorage.getItem(AI_CHAT_CREATE_BILLS_KEY) ? JSON.parse(localStorage.getItem(AI_CHAT_CREATE_BILLS_KEY)) : []
          const customValue = column.render ? column.render(value, record, index) : value
          if (aichatCreateBills?.some(item => item.id === record.id) && column.dataIndex === record.firstVisibleColumn) {
            return <>
              <AIAgentTag style={{ marginRight: '4px', font: 'var(--eui-font-note-b1) !important' }} text='AI' />
              {customValue}
            </>
          } else {
            return customValue
          }
        }
      }

      return finalColumn
    })
  }

  renderCaption = (column: any) => {
    const caption = column.caption
    return (
      <TooltipEllipsis width={column.width} title={caption}>
        {caption}
      </TooltipEllipsis>
    )
  }

  getTableDefaultVisibleColumns = () => {
    const { createAction, useNewFieldSet } = this.props
    let { visibleColumns } = this.state
    if (!useNewFieldSet) return visibleColumns
    if (createAction) {
      visibleColumns = visibleColumns ? visibleColumns : []
      visibleColumns = visibleColumns.filter(item => item !== 'action')
      visibleColumns.push('action')
    }
    return visibleColumns
  }

  fnShowTitle = (line: any) => {
    if (i18n.currentLocale === 'en-US') {
      if (line?.enText) {
        return line?.enText
      } else if (line?.scene === 'all') {
        return 'All'
      }
    }
    return line?.text
  }

  addFirstVisibleColumnToDataSource = (dataSource, visibleColumns) => {
    if (this.props.showAIIcon) {
      const firstVisibleColumn = visibleColumns?.[0]
      return dataSource.map(item => ({
        ...item,
        firstVisibleColumn
      }))
    }
    return dataSource
  }

  public render() {
    const {
      newSearch,
      searchOptions,
      searchClassname,
      lightingMode,
      columns,
      scenes,
      buttons,
      disabledScenes,
      onSelectedAll,
      columnMinWidth,
      menuBar,
      searchPlaceholder,
      isDiDi,
      isOnlyShowSaveDiff,
      showOutsideButtonCount,
      rowKey,
      isHiddencolumnChooser,
      isHiddenPagination,
      isHiddenSearch,
      isNeedHeight = true,
      isMultiSelect = true,
      relativeToContaier,
      styleHeight,
      bodyStyle = {},
      containerStyle = {},
      headerStyle = {},
      isGroup = false,
      columnsList = {},
      enableGlobalSearch = false,
      expansionScenes,
      saveSceneWithGroupIndex,
      headerTopRender,
      useNewNumberFilter,
      DetailTemplate,
      enabledDetailTemplate,
      RenderDetailTemplate,
      preRenderCellHandler,
      searchInputClassName = '',
      useNewFieldSet,
    } = this.props
    const {
      lightingValue,
      dataSource,
      activeSceneIndex,
      total,
      selectedRowKeys,
      currentPage,
      pageSize,
      pageMode,
      sorters,
      visibleColumns,
      filters,
      isVisibleSaveDiff,
      selectedRowsData,
      sceneOnLoading,
      outsideButtonCount,
      searchText,
      fetchError,
    } = this.state
    let placeholder = searchPlaceholder || i18n.get('搜索标题、单号或提交人')
    if (enableGlobalSearch) {
      placeholder = i18n.get('搜索文本、数字类信息或提交人')
    }
    // 在编辑表头字段的弹窗里过滤掉人员多选字段
    let availableColumns: any[] = columns.map(item => item)
    availableColumns = availableColumns.filter(e => !(e?.dataType === 'list' && e?.entity.includes('.Staff')))

    let selectionProps = undefined
    if (getBoolVariation('mfrd-3884-fixed-action-column')) {
      selectionProps = {
        showCheckBoxesMode: 'always',
        allowSelectAll: true,
        mode: isMultiSelect ? 'multiple' : 'none'
      }
    }
    console.log('scrolling', scrolling)
    return (
      <div
        id={this.fnGetDataGridViewId()}
        className={classnames('lighting', styles.container, isNeedHeight && styles.containerHeight, {
          isLightingMode: lightingMode
        })}
        ref={
          styleHeight &&
          (element => {
            if (element) element.style.setProperty(...styleHeight)
          })
        }
        style={{ ...containerStyle }}
      >
        <div className={styles.header} style={headerStyle}>
          {headerTopRender ? headerTopRender() : null}
          {lightingMode && (
            <LightingMode
              flowCode={lightingValue}
              hasLightingCheck={true}
              onChange={this.handleLightingModeValueChange}
              onPressEnter={this.handleLightingModeInputPressEnter}
              onCheckChange={this.handleLightingModeConfig}
            />
          )}
          {!disabledScenes && !lightingMode && (
            <div className={styles.sceneRow}>
              <div className={styles.sceneWrapper}>
                <DataGrid.Scenes
                  scenes={scenes}
                  saveDiffText={isDiDi ? null : i18n.get('保存变更')}
                  isVisibleSaveDiff={isVisibleSaveDiff}
                  onSaveDiffScenes={isDiDi ? null : this.onSaveDiffScenes}
                  activeScene={activeSceneIndex}
                  onChangeScene={this.handleSceneChange}
                  onEditScenes={isDiDi ? undefined : this.handleEditScenes}
                  expansionScenes={expansionScenes}
                  sceneOnLoading={sceneOnLoading}
                  saveSceneWithGroupIndex={saveSceneWithGroupIndex}
                  fnShowTitle={this.fnShowTitle}
                />
              </div>
              {menuBar && <div className={styles.menuBar}>{isFunction(menuBar) ? menuBar() : menuBar}</div>}
            </div>
          )}
        </div>
        <div className={styles.body} ref={this.bodyRef} style={bodyStyle}>
          <DataGrid.TableWrapper
            DetailTemplate={DetailTemplate}
            RenderDetailTemplate={RenderDetailTemplate}
            preRenderCellHandler={preRenderCellHandler}
            enabledDetailTemplate={enabledDetailTemplate}
            renderCaption={this.renderCaption}
            rowKey={rowKey || 'id'}
            filters={filters}
            className={`${styles.tableWrapper} ${fetchError ? styles.tableWrapperError : ''}`}
            defaultVisibleColumns={this.getTableDefaultVisibleColumns()}
            dataSource={this.addFirstVisibleColumnToDataSource(dataSource, visibleColumns)}
            columns={this.state.columns}
            columnMinWidth={columnMinWidth || 120}
            isMultiSelect={isMultiSelect}
            relativeToContaier={relativeToContaier}
            sorters={sorters}
            allowColumnReordering
            allowColumnResizing
            groupPanel={groupPanel}
            pageSize={pageSize}
            scrolling={scrolling}
            selectedRowKeys={selectedRowKeys}
            sceneOnLoading={sceneOnLoading}
            saveSceneWithGroupIndex={saveSceneWithGroupIndex}
            onRowClick={this.handleRowClick}
            onSorterChange={this.handleSorterChange}
            onFilterChange={this.handleFilterChange}
            onGroupChange={this.handleGroupChange}
            onSelectedChange={this.handleSelectedChange}
            onReachBottom={this.handleReachBottom}
            getInstance={this.getInstance}
            refreshState={this.refreshState.bind(this)}
            useNewNumberFilter={useNewNumberFilter}
            // 固定列参数
            fixedSelect={useNewFieldSet}
            onRowPrepared={this.handleRowPrepared}
            selection={selectionProps}
            loadPanel={{
              enabled: 'auto',
              showPane: false,
              text: i18n.get('加载中…')
            }}
          />
          {isOnlyShowSaveDiff && isVisibleSaveDiff && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                right: '330px',
                height: '52px',
                fontSize: '14px',
                color: 'var(--brand-base)',
                cursor: 'pointer',
                lineHeight: '52px'
              }}
              onClick={this.onSaveDiffScenes}
            >
              {i18n.get('保存变更')}
            </div>
          )}
          <div className={styles.table_actions}>
            {!isHiddenSearch &&
              (newSearch ? (
                <CustomSearch className={searchClassname} options={searchOptions} onSearch={this.handleCustomSearch} />
              ) : (
                <DataGrid.Search
                  // @ts-ignore
                  ref={this.searchInputRef}
                  placeholder={placeholder}
                  // @ts-ignore
                  value={searchText}
                  onClear={this.handleClearSearch}
                  onSearch={this.handleSearch}
                  className={classnames({ 'r-28': isHiddencolumnChooser }, styles.global_search, searchInputClassName)}
                />
              ))}

            {!isHiddencolumnChooser && (
              <React.Fragment>
                {!isHiddenSearch && <div className={styles.divider} data-platform-wx2-hidden={window.isInWeComISV} />}
                <div className={styles.columnChooser} data-platform-wx2-hidden={window.isInWeComISV}>
                  <DataGrid.ColumnChooser
                    KindMap={{
                      invoiceForm: i18n.get('发票字段'),
                      flowForm: i18n.get('单据字段'),
                      detailForm: i18n.get('费用明细字段'),
                      payPlanForm: i18n.get('支付计划字段'),
                      others: i18n.get('其他字段')
                    }}
                    isGroup={isGroup}
                    columns={isGroup ? columnsList : availableColumns}
                    defaultVisibleColumns={visibleColumns}
                    onChange={this.handleVisibleColumnChange}
                    onReset={this.handleVisibleColumnReset}
                    refreshState={this.refreshState.bind(this)}
                    // @ts-ignore
                    useNewFieldSet={useNewFieldSet}
                    // @ts-ignore
                    fixVisibleColumns={this.fixVisibleColumns}
                  />
                </div>
              </React.Fragment>
            )}
          </div>
        </div>
        <div className={styles.footer}>
          {buttons && (
            <Buttons
              rowKey="id"
              buttons={buttons}
              totalLength={total}
              dataSource={dataSource}
              selectedRowKeys={selectedRowKeys}
              selectedRowsData={selectedRowsData}
              showOutsideButtonCount={outsideButtonCount || showOutsideButtonCount}
              onClick={this.handleButtonClick}
            />
          )}
          {isMultiSelect && <DataGrid.SelectionLabel totalLength={total} selectedLength={selectedRowKeys.length} />}
          {onSelectedAll && <DataGrid.SelectAllButton totalLength={total} handleSelectAll={this.handleSelectAll} />}
          {!isHiddenPagination && (
            <DataGrid.Pagination
              scrollPagination={{
                current: 1,
                size: 20
              }}
              totalLength={total}
              pagination={{
                current: currentPage,
                size: lightingMode ? LIGHTING_MODE_PAGESIZE : pageSize
              }}
              pageMode={pageMode}
              onChange={this.handlePageChange}
            />
          )}
        </div>
      </div>
    )
  }

  private handleLightingModeValueChange: React.ChangeEventHandler<HTMLInputElement> = e => {
    this.setState({
      lightingValue: e.target.value
    })
  }

  private handleLightingModeInputPressEnter: React.KeyboardEventHandler<HTMLInputElement> = async e => {
    const value = (e.target as HTMLInputElement).value
    if (!value) {
      this.lightingSearchErrorFeedback(i18n.get('单号不能为空'))
      return
    }
    return await this.lightingSearch(value)
  }

  private handleLightingModeConfig = e => {
    this.setState({ lightingErrorFeedback: e.target.checked })
  }

  private lightingSearchErrorFeedback = (msg: string, errType: string = 'warning') => {
    const { lightingErrorFeedback } = this.state
    if (lightingErrorFeedback) {
      errType === 'warning' ? showModal.warning({ title: msg }) : showModal.error({ title: msg })
    } else {
      errType === 'warning' ? showMessage.warning(msg) : showMessage.error(msg)
    }
  }

  private lightingSearch = async (value: string, allowExisted = false) => {
    const codeArr = value.split(',')
    const batch = codeArr.length > 2
    const { status } = this.props
    if (batch) {
      const { dataSource, total } = await this.fetch(
        {
          page: {
            currentPage: 1,
            pageSize: LIGHTING_MODE_PAGESIZE
          },
          filters: {
            'flowId.form.code': codeArr
          }
        },
        true
      )
      if (!this.unmounted) {
        this.setState(
          {
            dataSource,
            total: total,
            lightingValue: ''
          },
          () => {
            this.handleUpdateSelectCurrentRowColor(dataSource)
          }
        )
      }
    } else {
      if (!allowExisted && this.state.dataSource.length >= LIGHTING_MODE_PAGESIZE) {
        this.lightingSearchErrorFeedback(i18n.get('已添加到上限'))
        return this.setState({ lightingValue: '' })
      }
      const trimmedValue = value.trim()
      if (!allowExisted && this.state.dataSource.findIndex(item => item.flowId.form.code === trimmedValue) > -1) {
        this.lightingSearchErrorFeedback(i18n.get('该单据已存在'))
        return this.setState({ lightingValue: '' })
      }
      const code = await this.lightFetch(trimmedValue, status, allowExisted)
      if (!code) {
        this.lightingSearchErrorFeedback(i18n.get('该单据不存在'))
        return this.setState({ lightingValue: '' })
      }
      this.prevLightingSearchValue = value
      const { dataSource, total } = await this.fetch(
        {
          scanText: value,
          page: {
            currentPage: 1,
            pageSize: LIGHTING_MODE_PAGESIZE
          }
        },
        true
      )
      if (!this.unmounted) {
        const data = getV(this.state, 'dataSource', []).slice()
        const ids = data.map(d => d.id)
        const dataMap = array2object(dataSource)
        Object.keys(dataMap).forEach(key => {
          if (!ids.includes(key)) {
            data.push(dataMap[key])
          }
        })
        this.setState(
          {
            dataSource: data,
            total: total,
            lightingValue: ''
          },
          () => {
            this.handleUpdateSelectCurrentRowColor(data)
          }
        )
      }
    }
  }

  private handleEditScenes = async () => {
    const { activeSceneIndex } = this.state
    await this.props.onEditScenes(activeSceneIndex)
    this.setState(
      {
        currentPage: 1,
        filters: this.props.filters ? this.props.filters : {},
        isVisibleSaveDiff: false,
        activeSceneIndex: activeSceneIndex ?? 'all',
        visibleColumns: this.getDefaultVisibleColumns(activeSceneIndex ?? 'all'),
        selectedRowKeys: [],
        selectedRowsData: {}
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        this.setState({ dataSource, total }, () => {
          this.handleUpdateSelectCurrentRowColor(dataSource)
        })
      }
    )
  }

  private handleSceneChange = (scene: string) => {
    const { scenes, onSenceChange, saveSceneWithGroupIndex } = this.props
    const targetScene: Scene = scenes && scenes.find(item => item.sceneIndex === scene)
    onSenceChange && onSenceChange(targetScene)
    this.setState(
      {
        currentPage: 1,
        filters: this.props.filters ? this.props.filters : {},
        isVisibleSaveDiff: false,
        activeSceneIndex: scene,
        visibleColumns: this.getDefaultVisibleColumns(scene),
        loadedAllData: false,
        selectedRowKeys: [],
        selectedRowsData: {},
        sceneOnLoading: true,
        sorters: targetScene.sorter ? targetScene.sorter : {}
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        this.setState(
          {
            dataSource,
            total,
            columns: this.formatColumns(this.props.columns, targetScene)
          },
          () => {
            if (saveSceneWithGroupIndex) {
              this.asyncSetSceneLoadingStatus()
            }
            this.handleUpdateSelectCurrentRowColor(dataSource)
          }
        )
      }
    )
  }

  // 修改加速筛选的加载状态
  private asyncSetSceneLoadingStatus = () => {
    setTimeout(() => {
      this.setState({ sceneOnLoading: false })
    }, 500)
  }

  private handlePageChange = (pagination: PaginationConfig, pageMode: PageMode = 'pagination') => {
    const { isPageChangeIng, activeSceneIndex } = this.state
    if (isPageChangeIng) {
      return
    }
    this.setState(
      {
        currentPage: pagination.current,
        pageSize: pagination.size,
        pageMode: pageMode,
        loadedAllData: false,
        isPageChangeIng: true
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        if (pageMode === 'pagination') {
          const res = await this.handleOnSaveScenes(this.props.scenes)
          res && this.props.bus.emit('initScenes:action', res, activeSceneIndex)
        }
        this.setState({ dataSource, total, isPageChangeIng: false }, () => {
          this.handleUpdateSelectCurrentRowColor(dataSource)
        })
      }
    )
  }

  private handleSelectAll = () => {
    const {
      currentPage,
      pageSize,
      searchText,
      searchOptionText,
      filters: fetchFilter = {},
      sorters,
      activeSceneIndex,
      total
    } = this.state
    const { lightingMode } = this.props
    let fetchFilters = cloneDeep(fetchFilter)
    fetchFilters = this.formateFilter(fetchFilters)

    this.props.onSelectedAll({
      page: {
        currentPage: currentPage,
        pageSize: lightingMode ? LIGHTING_MODE_PAGESIZE : pageSize
      },
      searchText: searchText,
      searchOptionText,
      filters: fetchFilters,
      sorters: sorters,
      scene: activeSceneIndex,
      total
    })
  }

  private handleSelectedChange = (selectedKeys: string[], selectedRowsData: Map<Data>) => {
    const { onSelectedChange } = this.props
    const { selectedRowsData: selectedRowsDataState } = this.state
    const selectedRowsDataTemp = {}
    selectedKeys.forEach(key => {
      selectedRowsDataTemp[key] = selectedRowsData[key] || selectedRowsDataState[key]
    })
    this.setState({
      selectedRowKeys: selectedKeys,
      selectedRowsData: selectedRowsDataTemp
    })
    onSelectedChange && onSelectedChange(selectedKeys, selectedRowsDataTemp)
  }

  handleSelectCurrentRowColor = () => {
    this.rowColorRender(_LAST_CLICK_ROW_INDEX, 'unset')
    this.rowColorRender(_LAST_CLICK_ROW_INDEX, 'var(--eui-fill-hover)')
  }

  handleUpdateSelectCurrentRowColor = (dataSource: Data[] = []) => {
    if (dataSource && dataSource.length > 0 && !!__CURRENT_SELECTED_DATA_ID) {
      setTimeout(() => {
        const currentIndex = dataSource.findIndex(item => item.id === __CURRENT_SELECTED_DATA_ID)
        this.rowColorRender(_LAST_CLICK_ROW_INDEX, 'unset')
        if (currentIndex >= 0) {
          _LAST_CLICK_ROW_INDEX = currentIndex
        }
        this.rowColorRender(currentIndex, 'var(--eui-fill-hover)')
      }, 100)
    }
  }

  private clearCurrentSelectedRowColor = () => {
    const currentIndex = this.state.dataSource.findIndex(item => item.id === __CURRENT_SELECTED_DATA_ID)
    this.rowColorRender(currentIndex, '')
    __CURRENT_SELECTED_DATA_ID = undefined
  }

  private rowColorRender = (index: number, color: string) => {
    const rows = document.querySelectorAll(`tr[aria-rowindex="${index + 1}"]`)
    if (!rows && rows.length <= 0) {
      return
    }
    rows.forEach(row => {
      if (!row) {
        return
      }
      row.style.backgroundColor = color
    })
  }

  fnSetCurrentFixedAction = () => {
    if (!getBoolVariation('mfrd-3884-fixed-action-column')) {
      return
    }
    const { columns = [] } = this.state
    const actionColumns = columns?.filter(col => col?.dataIndex === 'action')
    if (actionColumns?.length) {
      actionColumns.forEach(col => {
        if (this.fixVisibleColumns && col.fixed === 'right') {
          this.fixVisibleColumns[col.dataIndex] = col.fixed
        }
      })
    }
  }

  handleColoumnFixed = async (column: Column, fixed: 'left' | 'right' | boolean) => {
    await this.handleOnSaveDiffScenes(true, { [column.dataIndex]: fixed })
    this.handleUpdateFixedColumn([column], { fixed })
  }

  handleUpdateFixedColumn = async (
    fixedColumns: Column[],
    params: { fixed: 'left' | 'right' | boolean; fixedDisabled?: boolean }
  ) => {
    const { fixed, fixedDisabled } = params
    const { columns, visibleColumns = [] } = this.state
    const columnMap = columns.reduce((map, col) => {
      map[col.dataIndex] = { ...col }
      return map
    }, {})
    const fixedColumnMap = fixedColumns.reduce((map, col) => {
      const colCopy = { ...col, fixed, fixedDisabled }
      map[col.dataIndex] = colCopy
      return map
    }, {})
    if (!visibleColumns.includes('action')) {
      visibleColumns.push('action')
    }
    const newColumns = visibleColumns
      .map(dataIndex => {
        const col = columnMap[dataIndex]
        const column = fixedColumnMap[dataIndex]
        if (col && column && col.dataIndex === column.dataIndex) {
          col.fixed = fixed
          col.fixedDisabled = fixedDisabled
        }
        if (col && column && this.fixVisibleColumns && dataIndex === 'action') {
          this.fixVisibleColumns[dataIndex] = fixed
        }
        return col
      })
      .filter(Boolean)
    this.setState({ columns: newColumns, visibleColumns: visibleColumns })
  }

  private handleRowClick = (data: Data, index: number, ariaRowIndex: string) => {
    __CURRENT_SELECTED_DATA_ID = data?.id
    _CURRENT_SELECTED_ROW_AriaRowIndex = ariaRowIndex
    this.handleUpdateSelectCurrentRowColor(this.state.dataSource)
    this.props.bus.emit('table:row:click', data, index)
  }

  handleRowPrepared = e => {
    // 发生重绘时重新设置选中行的颜色
    if (e.rowElement && e.rowElement.ariaRowIndex === _CURRENT_SELECTED_ROW_AriaRowIndex) {
      e.rowElement.style.backgroundColor = 'var(--eui-fill-hover)'
    }
  }

  private handleGroupChange = (group: Map<number | undefined>) => {
    logTableGroupChange(group, this.props.columns)
  }

  private handleSorterChange = (sorters: Map<'ascend' | 'descend'>) => {
    const { columns } = this.props
    const { currentPage, pageMode } = this.state
    logTableSorterChange(sorters, columns)
    this.setState(
      {
        sorters,
        currentPage: pageMode === 'scroll' ? 1 : currentPage,
        loadedAllData: false
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        if (!this.unmounted) {
          const ds = dataSource ? dataSource : []
          this.setState({ dataSource: ds, total }, () => {
            this.handleUpdateSelectCurrentRowColor(ds)
          })
        }
      }
    )
  }

  private handleFilterChange = (filters: Map<any>) => {
    logTableFilterChange(filters, this.props.columns)
    this.setState({ filters, currentPage: 1, loadedAllData: false }, async () => {
      const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
      if (!this.unmounted) {
        this.setState({ dataSource, total }, () => {
          this.handleUpdateSelectCurrentRowColor(dataSource)
        })
      }
    })
  }

  private handleClearSearch = () => {
    const { filters } = this.props
    this.setState(
      {
        sorters: {},
        filters: filters ? filters : {},
        currentPage: 1,
        searchText: '',
        loadedAllData: false
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        if (!this.unmounted) {
          this.setState({ dataSource, total }, () => {
            this.handleUpdateSelectCurrentRowColor(dataSource)
          })
        }
      }
    )
  }

  private handleSearch = (value: string) => {
    this.setState(
      {
        sorters: {},
        filters: this.props.filters ? this.props.filters : {},
        currentPage: 1,
        searchText: value,
        loadedAllData: false
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        if (!this.unmounted) {
          this.setState({ dataSource, total }, () => {
            this.handleUpdateSelectCurrentRowColor(dataSource)
          })
        }
      }
    )
  }

  private handleCustomSearch = (value?: string) => {
    this.setState(
      {
        searchOptionText: value,
        currentPage: 1,
        loadedAllData: false
      },
      async () => {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        if (!this.unmounted) {
          this.setState({ dataSource, total }, () => {
            this.handleUpdateSelectCurrentRowColor(dataSource)
          })
        }
      }
    )
  }

  private formatDate(fetchFilter: any) {
    const keys = Object.keys(fetchFilter)
    keys.forEach(key => {
      const value = fetchFilter[key]
      if (value && value.length === 2 && moment.isMoment(value[0]) && moment.isMoment(value[1])) {
        fetchFilter[key] = { start: value[0].valueOf(), end: value[1].valueOf() }
      } else if (moment.isMoment(value)) {
        fetchFilter[key] = value.valueOf()
      }
    })
  }

  private handleButtonClick = (data: { name: string; key?: string; data: Map<Data>; keys: string[] }) => {
    const { onButtonClick, lightingMode } = this.props
    const { currentPage, pageSize, searchText, filters: fetchFilter, sorters, total } = this.state
    const { activeSceneIndex } = this.state
    const cloneDetchFilter = cloneDeep(fetchFilter)
    this.formatDate(cloneDetchFilter)
    onButtonClick &&
      onButtonClick(
        {
          ...data,
          data: this.state.selectedRowsData,
          total
        },
        {
          page: {
            currentPage: currentPage,
            pageSize: lightingMode ? LIGHTING_MODE_PAGESIZE : pageSize
          },
          searchText: searchText,
          filters: cloneDetchFilter,
          sorters: sorters,
          scene: activeSceneIndex
        }
      )
  }

  private handleReachBottom = async () => {
    if (this.state.pageMode === 'pagination' || this.state.loadedAllData || this.reachBottomRequest) {
      return
    }

    const { currentPage, total, dataSource } = this.state
    const fetchParams = this.buildFetchParamsFromState()
    this.reachBottomRequest = true
    try {
      const result = await this.fetch({
        ...fetchParams,
        page: {
          ...fetchParams.page,
          currentPage: currentPage + 1
        }
      })
      if (!this.unmounted) {
        if (result.dataSource && result.dataSource.length > 0) {
          const _data = [...dataSource, ...result.dataSource]
          this.setState(
            {
              currentPage: currentPage + 1,
              dataSource: _data,
              total: result.total
            },
            () => {
              this.handleUpdateSelectCurrentRowColor(_data)
            }
          )
        } else {
          this.setState(
            {
              loadedAllData: true
            },
            () => {
              this.handleSelectCurrentRowColor()
            }
          )
        }
      }
    } catch (err) {
      this.reachBottomRequest = false
    }
    this.reachBottomRequest = false
  }

  private handleVisibleColumnChange = (columns: FieldIF[] | any[]) => {
    const { useNewFieldSet, saveSceneWithGroupIndex } = this.props
    const fixVisibleColumns = {}
    const visibleColumns = useNewFieldSet
      ? columns.map(item => {
        const { dataIndex, fixed } = item
        if (fixed) {
          fixVisibleColumns[dataIndex] = fixed
        }
        return dataIndex
      })
      : columns
    // @ts-ignore
    this.fixVisibleColumns = fixVisibleColumns
    if (useNewFieldSet) {
      const actionField = this.state.columns.filter(it => it.dataIndex === 'action')
      // @ts-ignore
      const columnsWithGroupIndex = [...columns, ...actionField].map(it => {
        // @ts-ignore
        const columnValue = this.targetSceneNote?.groupIndexMap?.[it.dataIndex]
        const groupIndexValue: any = {}
        if (columnValue !== undefined && saveSceneWithGroupIndex) {
          groupIndexValue.groupIndex = columnValue
        }
        return {
          ...it,
          ...groupIndexValue
        }
      })
      this.setState({ columns: columnsWithGroupIndex }, this.forceUpdate)
    }
    this.setState({ visibleColumns }, async () => {
      this.researchByColumnChange()
      // 银行回单管理场景下，不支持通过列设置来查询数据
      const SCENE_TYPE_OF_BANK_RECEIPT = 'RECEIPT'
      if (this.props.scenesType !== SCENE_TYPE_OF_BANK_RECEIPT) {
        const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
        this.setState({ dataSource, total }, () => {
          this.handleUpdateSelectCurrentRowColor(dataSource)
        })
      }
    })
  }
  //过滤select参数时变更可见列需重新查询
  private researchByColumnChange = async () => {
    const { isLimitColumns } = this.props
    if (isLimitColumns) {
      const { dataSource, total } = await this.fetch(this.buildFetchParamsFromState())
      if (!this.unmounted) {
        this.setState({ dataSource, total }, () => {
          this.handleUpdateSelectCurrentRowColor(dataSource)
        })
      }
    }
  }
  private handleVisibleColumnReset = () => {
    this.setState({ visibleColumns: this.getDefaultVisibleColumns(this.state.activeSceneIndex) })
  }

  private getDefaultVisibleColumns(activeSceneIndex: string) {
    const { scenes } = this.props
    const values = this.getUpdatedColumns(scenes, activeSceneIndex)
    return values.visibleColumns
  }

  private getDefaultVisibleColumnsWidth(activeSceneIndex: string) {
    const { scenes } = this.props
    const values = this.getUpdatedColumns(scenes, activeSceneIndex)
    return {
      widths: values.visibleColumnsWidth,
      fixeds: values.visibleColumnsFixed
    }
  }

  private buildFetchParamsFromState = (reload = false): FetchParams => {
    const { lightingMode, scenesType, dynamicChannelMap = {} } = this.props
    const {
      activeSceneIndex,
      currentPage,
      pageSize,
      searchText,
      filters: fetchFilter,
      sorters,
      searchOptionText
    } = this.state
    let f = { ...fetchFilter }
    let channelKey = ''
    switch (scenesType) {
      case 'PAYING':
        channelKey = 'flowId.form.paymentChannel'
        break
      case 'PAID':
        channelKey = 'form.paymentChannel'
        break
      case 'PAYMENTPLAN_PAID':
        channelKey = 'dataLink.E_system_paymentPlan_支付方式'
        break
    }
    const active = dynamicChannelMap['CHANPAYV2'] && dynamicChannelMap['CHANPAYV2'].active
    if (f[channelKey] && active) {
      //兼容新旧银企联数据查询
      if (f[channelKey].includes('CHANPAYV2') && !f[channelKey].includes('CHANPAY')) {
        f[channelKey].push('CHANPAY')
      } else if (f[channelKey].includes('CHANPAY')) {
        const index = f[channelKey].indexOf('CHANPAY')
        f[channelKey].splice(index, 1)
      }
    }
    if (lightingMode) {
      const codes = this.state.dataSource.map(item => item.flowId.form.code)
      f = { ...f, 'flowId.form.code': codes }
    }
    let creditCondition = ''
    if (f?.creditorCode) {
      creditCondition = f?.creditorCode?.reduce((res, item) => {
        return `${res ? res + ' || ' : res}(${item})`
      }, '')
    }
    if (f?.creditorPoint) {
      creditCondition = creditCondition
        ? `(${creditCondition}) && (point >= ${f?.creditorPoint?.start} && point <= ${f?.creditorPoint?.end})`
        : `point >= ${f?.creditorPoint?.start} && point <= ${f?.creditorPoint?.end}`
    }
    const filter = Object?.keys(f)?.reduce((res, it) => {
      if (it !== 'creditorCode' && it !== 'creditorPoint') {
        res[it] = f[it]
      }
      return res
    }, {})
    let params = {
      page: {
        currentPage: reload ? 1 : currentPage,
        pageSize: lightingMode ? LIGHTING_MODE_PAGESIZE : pageSize
      },
      searchText: searchText,
      searchOptionText,
      filters: filter,
      sorters: sorters,
      scene: activeSceneIndex
    }
    if (creditCondition) {
      params = { ...params, creditCondition: creditCondition }
    }
    return params
  }

  private fetch = async (_params: FetchParams, fromLightMode = false) => {
    const { dataSource: _data, total: _total } = this.state

    if (this.isSearching) {
      return { dataSource: _data, total: _total }
    }

    const _fetch = async (_params: FetchParams, fromLightMode = false) => {
      const { scenes = [], lightingMode, customColumnPropertyMapping, enableGlobalSearch, isLimitColumns } = this.props
      const { activeSceneIndex, dataSource: _data, total: _total, searchText, visibleColumns } = this.state
      let globalSearchFields = enableGlobalSearch ? [] : enableGlobalSearch // 兼容有些plugin引用的lib版本 globalSearch是bool类型

      if (enableGlobalSearch) {
        // 环境变量判断：全局搜索是否只搜两个金额字段
        const isSearchMoney = /^-?\d+(\.\d{0,2})?$/.test(searchText)
        const useGlobalSearchV2 = window?.PLATFORMINFO?.useGlobalSearchV2 && isSearchMoney
        globalSearchFields = useGlobalSearchV2 ? ['payMoney.standard', 'expenseMoney.standard'] : ['form']
      }

      if (lightingMode && !fromLightMode) {
        return this.checkBillStatus(_params?.sorters)
      }

      const activeScene = scenes && scenes.find(s => s.sceneIndex === activeSceneIndex)

      // 遍历获取有自定义档案数据
      const filters = (activeScene && activeScene.filters) || []
      const dimensionFilters = filters.filter(f => f.entity && f.entity.includes('basedata.Dimension.'))
      const filtersAll = dimensionFilters.map(f => api.invokeService('@common:get:staff:dimension', { name: f.entity }))

      const data = await Promise.all(filtersAll)
      const items = data.reduce<any>((pre, cur, index) => {
        pre[dimensionFilters[index].key] = cur.items
        return pre
      }, {})
      api.dispatch(action.value.dimensionItems(items))

      const params = { ..._params, filters: { ..._params.filters }, globalSearch: globalSearchFields }
      if (params.filters) {
        params.filters = this.formateFilter(params.filters)
      }
      let columnPropertyMapping = getColumnPropertyMapping()
      if (customColumnPropertyMapping) {
        columnPropertyMapping = Object.assign(columnPropertyMapping, customColumnPropertyMapping)
      }
      const fetchParams: FetchParams = fetchFixer(params, columnPropertyMapping)
      if (isLimitColumns && visibleColumns?.length) {
        Object.assign(fetchParams, { options: { queryLimitingFields: cloneDeep(visibleColumns) } })
      }
      fetchParams.columns = (visibleColumns || []).filter(it => it !== 'action')
      let dataSource = _data
      let total = _total
      if (this.props.fetch) {
        const data: any = await this.props.fetch(fetchParams, items)
        total = data.total
        dataSource = data.dataSource
      }

      return {
        dataSource: dataSource || [],
        total: total
      }
    }

    try {
      this.instance && this.instance.beginCustomLoading()
      this.isSearching = true
      this.clearErrorUI()
      return await _fetch(_params, fromLightMode).then(res => {
        this.clearErrorUI()
        this.setState({
          fetchError: false
        })
        return res
      })
    } catch (error) {
      console.log('DataGridWrapper fetch error', error)
      this.setState(
        {
          fetchError: true
        },
        () => {
          this.showErrorUI()
        }
      )
      return { dataSource: [], total: 0 }
    } finally {
      this.instance && this.instance.endCustomLoading()
      this.isSearching = false
    }
  }

  showErrorUI() {
    setTimeout(() => {
      if (this.instance) {
        const container = this.instance.element?.()?.querySelector('.dx-datagrid-rowsview')
        this.hideErrorUI = injectErrorDom(container, {
          refreshFn: () => {
            this.clearErrorUI()
            return this.props.bus?.reload()
          }
        })
      }
    }, 300)
  }

  clearErrorUI() {
    if (this.hideErrorUI) {
      this.hideErrorUI?.()
      this.hideErrorUI = null
    }
  }

  formateFilter(filters) {
    const keys = Object.keys(filters)
    for (const key of keys) {
      if (filters[key] && filters[key] instanceof Array && Number(filters[key][0]) > 0 && Number(filters[key][1]) > 0) {
        filters[key] = {
          start: +filters[key][0],
          end: +filters[key][1]
        }
      }
    }
    return filters
  }

  private lightFetch(code: string, status?: string, allowExisted = false) {
    return api
      .invokeService('@bills:get:flow-action', { code: code.trim(), hiddenMsg: true })
      .catch((e: any) => {
        this.setState({ lightingValue: '' })
        const msg = e.msg || e.message || e.errorMessage
        this.lightingSearchErrorFeedback(msg, 'error')
        return Promise.reject(e)
      })
      .then(async (data: any) => {
        if (data) {
          if (status === 'PAYING') {
            await api.invokeService('@bills:set:scan:time:by:ids', { id: data?.value?.flow?.id })
          }
          const state = data.value.flow.state
          if (state.toUpperCase() === status) {
            return code
          } else {
            if (statusMessage()[state]) {
              if (!allowExisted) {
                this.lightingSearchErrorFeedback(i18n.get(statusMessage()[state]))
              }
              this.setState({ lightingValue: '' })
              return Promise.reject()
            }
          }
        }
        return ''
      })
  }

  private checkBillStatus = async sorters => {
    const { lightingMode, scenes, status } = this.props
    const { dataSource, pageSize, activeSceneIndex } = this.state
    const codes = dataSource.map(obj => obj.flowId.form.code)
    const activeScene = scenes && scenes.find(s => s.sceneIndex === activeSceneIndex)

    // 遍历获取有自定义档案数据
    const filters = (activeScene && activeScene.filters) || []
    const dimensionFilters = filters.filter(f => f.entity && f.entity.includes('basedata.Dimension.'))
    const filtersAll = dimensionFilters.map(f => api.invokeService('@common:get:staff:dimension', { name: f.entity }))

    const data = await Promise.all(filtersAll)
    const items = data.reduce<any>((pre, cur, index) => {
      pre[dimensionFilters[index].key] = cur.items
      return pre
    }, {})
    api.dispatch(action.value.dimensionItems(items))
    const result = await this.props.fetch(
      {
        page: {
          currentPage: 1,
          pageSize: lightingMode ? LIGHTING_MODE_PAGESIZE : pageSize
        },
        filters: {
          'flowId.form.code': codes
        },
        sorters: status === 'PAYING' ? sorters : {}
      },
      items
    )
    const s: any[] = []
    const source: any[] = result.dataSource
    s.push(...source.filter(item => dataSource.some(obj => obj.flowId.id === item.flowId.id)))
    this.setState(
      {
        dataSource: s,
        total: s.length,
        lightingValue: ''
      },
      () => {
        this.handleUpdateSelectCurrentRowColor(s)
      }
    )
    return { dataSource: s, total: s.length }
  }

  private onActiveRowChanged = (data: Data) => {
    __CURRENT_SELECTED_DATA_ID = data?.id
    this.handleUpdateSelectCurrentRowColor(this.state.dataSource)
  }

  private onActiveRowIndexChanged = (index: number) => {
    const { dataSource } = this.state
    if (index >= 0 && index < dataSource.length) {
      this.onActiveRowChanged(dataSource[index])
    } else {
      this.clearCurrentSelectedRowColor()
    }
  }

  private notifyVisibleColumns = () => {
    return this.state.visibleColumns
  }
  private getLightingValue = () => {
    return this.state.lightingValue
  }
  private bindBus() {
    // TODO: 不使用 bus 的方式实现 reload 或 clearSelectedRowKeys
    const bus = this.props.bus as any
    bus.reload = (forceReloadAll = false) => {
      if (this.props.lightingMode) {
        this.lightingSearch(this.prevLightingSearchValue, true)
      }
      const setFirstPage = forceReloadAll || this.state.pageMode === 'scroll'
      return this.fetch(this.buildFetchParamsFromState(setFirstPage), this.props.lightingMode).then(
        ({ dataSource, total }) => {
          if (!this.unmounted) {
            this.setState(
              {
                currentPage: setFirstPage ? 1 : this.state.currentPage,
                dataSource,
                total,
                selectedRowKeys: [],
                selectedRowsData: {},
                loadedAllData: false
              },
              () => {
                this.handleUpdateSelectCurrentRowColor(dataSource)
              }
            )
          }
        }
      )
    }
    bus.search = (searchValue: string) => {
      this.handleSearch(searchValue)
    }
    bus.searchCustom = (searchValue: string) => {
      this.handleCustomSearch(searchValue)
    }
    bus.clearSelectedRowKeys = () => {
      this.setState({
        selectedRowKeys: [],
        selectedRowsData: {}
      })
    }
    // 导出服务需要被通知当前已选择的列
    bus.watch('get:column:checked:value', this.notifyVisibleColumns)
    bus.watch('get:lighting:value', this.getLightingValue)
    bus.on('table:select:current:row', this.handleSelectCurrentRowColor)
    bus.on('table:coloumn:fixed', this.handleColoumnFixed)
    bus.on('table:change:active:row:index', this.onActiveRowIndexChanged)
    // bus.lightingValue = this.state.lightingValue
    bus.handleLightingModeValueChange = e => {
      return this.handleLightingModeValueChange(e)
    }
    bus.handleLightingModeInputPressEnter = e => {
      return this.handleLightingModeInputPressEnter(e)
    }
    bus.handleLightingModeConfig = e => {
      return this.handleLightingModeConfig(e)
    }
    bus.getSelectedRowKeys = () => {
      return this.state.selectedRowKeys
    }
    bus.setSelectedRowKeys = (rowKeys = []) => {
      this.setState({
        selectedRowKeys: rowKeys
      })
    }
    bus.getSelectedRowData = () => {
      return this.state.selectedRowsData
    }
    bus.setSelectedRowData = (rowsData: Map<Data> = {}) => {
      this.setState({
        selectedRowsData: rowsData
      })
    }
    bus.getFilterParam = () => {
      return this.buildFetchParamsFromState()
    }

    bus.getDataSource = () => {
      return this.state.dataSource
    }
  }

  private unbindBus() {
    const bus = this.props.bus as any
    bus.reload = null
    bus.search = null
    bus.searchCustom = null
    bus.clearSelectedRowKeys = null
    bus.getFilterParam = null
    bus.un('get:column:checked:value', this.notifyVisibleColumns)
    bus.un('get:lighting:value', this.getLightingValue)
    bus.un('table:select:current:row', this.handleSelectCurrentRowColor)
    bus.un('table:coloumn:fixed', this.handleColoumnFixed)
    bus.un('table:change:active:row:index', this.onActiveRowIndexChanged)
    bus.getDataSource = null
    bus.handleLightingModeValueChange = null
    bus.handleLightingModeInputPressEnter = null
    bus.handleLightingModeConfig = null
  }

  private getInstance = (instance: any) => {
    const { getInstance: pGetInstance } = this.props
    pGetInstance && pGetInstance(instance)
    this.instance = instance
  }
}

export default DataGridWrapper
