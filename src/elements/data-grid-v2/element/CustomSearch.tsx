import React  from 'react'
import { Input, Select } from '@hose/eui'
import { FilledTipsClose, OutlinedEditSearch } from '@hose/eui-icons'
import styles from './CustomSearch.module.less'
import { SearchOption, SearchType } from './type'
import StaffSelect from './StaffSelect'
import DepartmentSelect from './DepartmentSelect'
import { debounce } from 'lodash'

interface Props {
  className?: string
  options: SearchOption[]
  onSearch: (value?: any) => void
}

export const CustomSearch: React.FC<Props> = ({ options, onSearch, className }) => {
  const [kind, setKind] = React.useState<SearchOption>(options[0])
  const [searchValue, setSearchValue] = React.useState<any>()
  const handleKindChange = value => {
    const k = options.find(item => item.key === value)
    setKind(k)
    if (!!searchValue?.length) {
      handleSearch('')
    }
    setSearchValue('')
  }
  const handleValueChange = (value) => {
    setSearchValue(value)
  }
  const handleChange = value => {
    setSearchValue(value)
    handleSearch(value)
  }
  const handleSearch = debounce(value => {
    let searchText = value
    if (value && typeof value === 'string') {
      searchText = value.trim()
    }
    const searchOptionText = kind?.format?.(searchText)
    onSearch?.(searchOptionText)
  }, 500)
  const contentMap = {
    [SearchType.text]: <CustomInput key={kind.key} placeholder={kind.placeholder} onChange={handleValueChange} onSearch={handleChange} />,
    [SearchType.staff]: <StaffSelect key={kind.key} placeholder={kind.placeholder} onChange={handleChange} />,
    [SearchType.department]: <DepartmentSelect key={kind.key} placeholder={kind.placeholder} onChange={handleChange} />
  }
  const mergeStyle = () => {
    let style = {}
    options.forEach(item => {
      if (item.style) {
        style = { ...style, ...item.style}
      }
    })
    return style
  }
  return (
    <div className={`${styles['kind-search-wrapper']} ${className}`}>
      <div className="kind-select-wrapper" style={mergeStyle()}>
        <Select
          data-testid="element-customsearch-kindselect"
          value={kind.key}
          onChange={handleKindChange}
          options={options.map(item => ({ label: item.label, value: item.key }))}
          bordered={false}
          style={{width: '100%'}}
        />
      </div>
      <div className="kind-search-content-wrapper">{contentMap[kind.type]}</div>
      <div className="kind-search-icon-wrapper" data-testid="custom-searcher-search"  onClick={() => handleSearch(searchValue)}>
        <OutlinedEditSearch />
      </div>
    </div>
  )
}

const CustomInput: React.FC<{ placeholder: string; onSearch: (value: string) => void; onChange: (value: string) => void }> = ({
  placeholder,
  onSearch,
  onChange
}) => {
  const [value, setValue] = React.useState()
  const handleChange = e => {
    setValue(e.target.value)
    onChange(e.target.value)
  }
  const handlePress = () => {
    onSearch(value)
  }
  const handleClear = () => {
    onSearch('')
  }
  return (
    <Input
      value={value}
      placeholder={placeholder}
      style={{ width: '100%' }}
      onChange={handleChange}
      data-testid="element-customsearch-input"
      onPressEnter={handlePress}
      bordered={false}
      data-testid="custom-searcher-input"
      allowClear={{clearIcon: <FilledTipsClose fontSize={12} onClick={handleClear}/>}}
    />
  )
}

export default CustomSearch
