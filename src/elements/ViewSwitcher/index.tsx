/**************************************
 * Created By LinK On 2021/12/8 14:36.
 **************************************/
import React from 'react'
import { Tooltip, Segmented } from '@hose/eui'
import { OutlinedGeneralApp, OutlinedEditDisorderList } from '@hose/eui-icons'
import styles from './viewSwitcher.module.less'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  isList: boolean
  handleSwitcher?: (type: string) => void
  handleClickList?: () => void
  handleClickTable?: () => void
}

const ViewSwitcher = ({
  isList,
  handleClickList = () => {},
  handleClickTable = () => {},
  handleSwitcher = () => {},
  ...restProps
}: Props) => {
  const handleClick = (type: 'list' | 'table') => {
    type === 'list' ? handleClickList && handleClickList() : handleClickTable && handleClickTable()
    handleSwitcher(type)
  }

  return (
    <div className={styles['viewSwitcher-wrapper']} {...restProps}>
      <Segmented
        value={isList ? 'list' : 'table'}
        onChange={handleClick}
        options={[
          {
            value: 'list',
            icon: (
              <Tooltip title={i18n.get('列表视图')}>
                <OutlinedEditDisorderList />
              </Tooltip>
            )
          },
          {
            value: 'table',
            icon: (
              <Tooltip title={i18n.get('表格视图')}>
                <OutlinedGeneralApp />
              </Tooltip>
            )
          }
        ]}
      />
    </div>
  )
}

export default ViewSwitcher
