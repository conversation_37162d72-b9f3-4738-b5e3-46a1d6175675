import React from 'react'

/**
 * 高亮块
 * @param text
 * @param keyword
 */
export const EUIHighLighter = (text: string, keyword = '') => {
  // eslint-disable-next-line react/no-array-index-key
  const result: any = []
  text.split(keyword).forEach((str, index) => {
    if (index > 0) {
      result.push(
        <span key={`${str}${index}`} style={{ color: 'var(--eui-primary-pri-500) !important' }}>
          {keyword}
        </span>
      )
    }
    result.push(str)
  })
  return result
}
