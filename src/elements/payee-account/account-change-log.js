/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/14 下午6:56.
 */
import React, { PureComponent } from 'react'
import style from './change-log.module.less'
import { Button, Icon } from 'antd'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { app as api } from '@ekuaibao/whispered'
import { mergeWith } from 'lodash'
import moment from 'moment'
import SVG_AVATAR from '../../images/avatar.svg'

const ACTIONS = {
  CREATE: i18n.get('创建'),
  EDIT: i18n.get('编辑'),
  ENABLE: i18n.get('启用'),
  DISABLE: i18n.get('停用'),
  SHARE: i18n.get('共享'),
  CANCEL_SHARE: i18n.get('取消共享')
}
const ACTIONS_COLOR = {
  CREATE: 'rgba(0, 0, 0, 0.65)',
  EDIT: 'rgba(0, 0, 0, 0.65)',
  ENABLE: '#2b9aab',
  DISABLE: '#9C9C9C',
  SHARE: 'var(--brand-base)',
  CANCEL_SHARE: 'var(--brand-base)'
}
const CHANGE_TYPE = {
  name: i18n.get('账户'),
  accountName: i18n.get('账户'),
  cardNo: i18n.get('银行账号'),
  accountNo: i18n.get('银行账号'),
  bank: i18n.get('开户行'),
  unionBank: i18n.get('开户行'),
  branch: i18n.get('网点'),
  type: i18n.get('类型'),
  city: i18n.get('城市'),
  province: i18n.get('省份'),
  visibility: i18n.get('可见性'),
  certificateType: i18n.get('证件类型'),
  certificateNo: i18n.get('证件号码'),
  bankLinkNo: i18n.get('银行联行号'),
  sort: i18n.get('账户类别'),
  bankName: i18n.get('银行名称'),
  swiftCode: i18n.get('银行国际代码'),
  bankCode: i18n.get('联行号'),
  branchCode: i18n.get('支行号'),
  remark: i18n.get('备注'),
  'extensions.country': i18n.get('收款人地址') + '-' + i18n.get('国家（Country/Area）'),
  'extensions.city': i18n.get('收款人地址') + '-' + i18n.get('城市（City）'),
  'extensions.town': i18n.get('收款人地址') + '-' + i18n.get('镇/区（Town/District）'),
  'extensions.street': i18n.get('收款人地址') + '-' + i18n.get('街道（Street）'),
  'extensions.countryEn': i18n.get('Payee address') + '-' + i18n.get('Country/Area'),
  'extensions.cityEn': i18n.get('Payee address') + '-' + i18n.get('City'),
  'extensions.townEn': i18n.get('Payee address') + '-' + i18n.get('Town/District'),
  'extensions.streetEn': i18n.get('Payee address') + '-' + i18n.get('Street'),
  'extensions.accountName': i18n.get('Account Name'),
  'extensions.zipCode': i18n.get('邮编（Zip code）'),
  'extensions.bicCode': i18n.get('BIC代码（BIC code）'),
  'extensions.currency': i18n.get('币种（Currency）'),
  'extensions.bankName': i18n.get('Bank Name'),
  'extensions.bankAddress': i18n.get('Bank Address'),
  'extensions.bankCountry': i18n.get('收款银行所在国家'),
  routingNumber: i18n.get('汇款路线号码(Routing No.)'),
  'extensions.email': i18n.get('邮箱'),
  customFields: i18n.get('自定义档案'),
  receiverAddress: i18n.get('收款人地址(Receiver Address)'),
  nationCode: i18n.get('银行所在地区代码(Nation Code)')
}
const TYPE_NAME = { PUBLIC: i18n.get('对公账户'), PERSONAL: i18n.get('个人账户') }

@EnhanceModal({
  title: '',
  footer: [],
  className: 'custom-modal-layer',
  maskClosable: false
})
export default class AccountChangeLog extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      logs: []
    }
  }

  componentWillMount() {
    let id = this.props.data.id
    this.getChangeLog(id)
  }

  getChangeLog(id) {
    api.invokeService('@common:get:payerlogs', id).then(result => {
      let items = (result && result.items) || []
      this.setState({
        logs: items
      })
    })
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.data !== nextProps.data) {
      let id = nextProps.data.id
      this.getChangeLog(id)
    }
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  renderItem(line) {
    let format = 'YYYY/MM/DD HH:mm:ss'
    let time = line.time ? moment(line.time).format(format) : moment().format(format)
    let action = line.action
    let isEdite = action === 'EDIT'
    let { avatar, name } = line.operator ? line.operator : {}
    if (['EDIT', 'CREATE', 'ENABLE', 'DISABLE'].includes(action) && line.operatorId === 'API') {
      name = 'API'
    }
    let changes =
      isEdite && line.attributes && line.attributes.changes && this.handleDealExtensions(line.attributes.changes)
    return (
      <div className="payee-item">
        <div className="time flex-s-0">{time}</div>
        <div className="infos">
          <div>
            <img src={[avatar || SVG_AVATAR]} />
            <div className="item-name flex-s-0">{name}</div>
            <span
              className="action"
              style={{
                color: ACTIONS_COLOR[action],
                fontWeight: (action === 'CREATE' || action === 'EDIT') && 600
              }}
            >
              {i18n.get(ACTIONS[action])}
            </span>
            <span>{i18n.get('了此账户')}</span>
          </div>
          {isEdite &&
            changes &&
            !!changes.length &&
            changes.map((line, key) => {
              return this.renderEditItem(line, key)
            })}
        </div>
      </div>
    )
  }

  handleDealExtensions = changes => {
    let newChanges = changes.filter(item => item.field !== 'extensions')
    let extensions = changes.filter(item => item.field === 'extensions')
    let newExtensions = []
    if (extensions[0]) {
      const { field, newValue, oldValue } = extensions[0]
      const itemMap = {
        country: 'countryStr',
        countryEn: 'countryEnStr',
        city: 'cityStr',
        cityEn: 'cityEnStr',
        bankCountry: 'bankCountryStr'
      }
      Object.keys(newValue).forEach(item => {
        if (!['shortCode', 'numCode', 'bankCountry_shortCode'].includes(item)) {
          if (!item.includes('Str') && newValue[item] !== oldValue[item]) {
            let obj = {
              field: field + '.' + item,
              newValue: newValue[item],
              oldValue: oldValue[item]
            }
            if (Object.keys(itemMap).includes(item)) {
              obj.newValue = newValue[itemMap[item]]
              obj.oldValue = oldValue[itemMap[item]]
            }
            newExtensions.push(obj)
          }
        }
      })
    }
    return [...newChanges, ...newExtensions]
  }

  formatCustomFieldsValue = line => {
    const { oldValue, newValue } = line
    const res = mergeWith(oldValue, newValue, (o, n) => {
      return o !== n ? { oldValue: o, newValue: n } : false
    })
    return Object.values(res)
  }

  renderEditItem(line, key) {
    let { oldValue, newValue, field } = line
    if (field === 'type') {
      oldValue = TYPE_NAME[oldValue]
      newValue = TYPE_NAME[newValue]
    }
    if (field === 'customFields') {
      const res = this.formatCustomFieldsValue(line)
      line.customFieldsValue = res?.filter(value => value)
    }
    return (
      <div className="mt-10 edit-card" key={key}>
        <div className="change-key">{i18n.get(`{__k0}：`, { __k0: i18n.get(CHANGE_TYPE[field]) })}</div>
        <div>
          {field === 'visibility' ? (
            <span>
              {i18n.get('【')}
              <span className="cardNo">{i18n.get(newValue)}</span>
              {i18n.get('】')}
            </span>
          ) : line?.customFieldsValue ? (
            line?.customFieldsValue?.map(item => (
              <span>{i18n.getHTML('oldValue-newValue', { oldValue: item?.oldValue, newValue: item?.newValue })}</span>
            ))
          ) : (
            <span>{i18n.getHTML('oldValue-newValue', { oldValue, newValue })}</span>
          )}
        </div>
      </div>
    )
  }

  render() {
    let { logs } = this.state
    return (
      <div id={'payeeAccount-payeeChangeLogs'} className={style['account-logs-wrapper']}>
        <div className="modal-header">
          <div className="title flex">{i18n.get('变更记录')}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleCancel} data-testid="pay-accountChangeLog-close-icon" />
        </div>
        <div className="account-logs-content">
          {logs &&
            logs.map((line, index) => {
              return (
                <div key={index} className="logs-item">
                  {this.renderItem(line)}
                </div>
              )
            })}
        </div>
        <div className="modal-footer">
          <Button key="ok" type="primary" size="large" onClick={this.handleCancel} data-testid="pay-accountChangeLog-ok-btn">
            {i18n.get('确定')}
          </Button>
        </div>
      </div>
    )
  }
}
