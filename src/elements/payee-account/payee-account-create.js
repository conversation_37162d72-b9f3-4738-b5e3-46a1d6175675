/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/14 下午3:33.
 */
import style from './account-create.module.less'
import React, { Component } from 'react'
import { Form, Alert, Input, Button, Select, Radio, Divider } from '@hose/eui'
import { EnhanceConnect } from '@ekuaibao/store'
import SelectBankField from '../payment-form-field/select-bank-field'
import SelectCertificateType, { SelectBranchTypeField } from '../payment-form-field/select-certificateType-field'
import { app as api } from '@ekuaibao/whispered'
import { debounce, get, set, compact } from 'lodash'
import className from 'classnames'
import { connect } from '@ekuaibao/mobx-store'
import { T } from '@ekuaibao/i18n'
import { showMessage } from '@ekuaibao/show-util'
import AccountNoInput from './AccountNoInput'
import { getV } from '@ekuaibao/lib/lib/help'
import { getFormItemsArrHSBC, getFormItemsCityCode, accountChannel } from './utils'
import StaffSelect from '../puppet/staff-select-heavy'
import {
  OutlinedTipsInfo,
  FilledGeneralGroup,
  FilledGeneralMember,
  OutlinedTipsClose,
  OutlinedGeneralGroup,
  OutlinedGeneralMember
} from '@hose/eui-icons'
import { Tooltip, Space } from '@hose/eui'
import IntelligentFillingModal from './IntelligentFillingModal'
const { getBoolVariation } = api.require('@lib/featbit')

const RadioGroup = Radio.Group
const FormItem = Form.Item
const Option = Select.Option
const layout = {}
let timeout

const visibilityMap = {
  true: 'ALL',
  false: 'SECTION'
}

const accountChannelNameMap = () => ({
  BANK: i18n.get('银行卡号'),
  ALIPAY: i18n.get('支付宝账号'),
  OVERSEABANK: i18n.get('银行账号(Account No.)'),
  CHECK: i18n.get('支票号'),
  ACCEPTANCEBILL: i18n.get('承兑汇票号'),
  OTHER: i18n.get('账号'),
  WEIXIN: i18n.get('微信账号')
})

const payFromChannelMap = {
  manage: 'manage',
  personal: 'personal'
}

const PERSONAL = 'PERSONAL' // 个人枚举

const titleMap = isNewTips => {
  return {
    PERSONAL: '您可以在对应银行APP的账户管理中查找银行网点，或咨询银行客户经理',
    PUBLIC: '您可以在对应的合同中查询此信息',
    accountName: isNewTips ? 'Please enter in english' : '您在银行开立账户时输入的姓名、公司名或组织机构名称',
    remark: '如您的账户做特殊用途，区别其他账户，可添加账户备注',
    branch: '您可以在对应银行APP的账户管理中查找银行网点，或咨询银行客户经理',
    bankName: 'Please enter in english'
  }
}

@connect(store => ({ size: store.states['@layout']?.size }))
@EnhanceConnect(state => ({
  provinceList: state['@common'].provinceList,
  cityList: state['@common'].cityList,
  userInfo: state['@common'].userinfo.data,
  branchList: state['@common'].branchByCityIdList,
  certificateTypeList: state['@common'].CertificateTypeList,
  CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
  KA_FOREIGN_ACCOUNT: state['@common'].powers.KA_FOREIGN_ACCOUNT,
  staffs: state['@common'].staffs,
  roles: state['@common'].roleList,
  payeeConfig: state['@common'].payeeConfig,
  departmentTree: state['@common'].department.data,
  defaultPayee: state['@common'].defaultPayee,
  payeeConfigCheck: state['@user-info'].payeeConfigCheck,
  searchCountries: state['@custom-payment'].searchCountries.items,
  searchCities: state['@custom-payment'].searchCities.items
}))
export default class PayeeAccountCreate extends Component {
  constructor(props) {
    super(props)
    const { userInfo = {}, isCreate, payeeConfig, payFromChannel, payeeConfigCheck = {}, payee } = props
    let isAdmin =
      userInfo.permissions &&
      (userInfo.permissions.includes('SYS_ADMIN') ||
        userInfo.permissions.includes('BANK_ACCOUNT_MANAGE') ||
        userInfo.permissions.includes('RECEIPT_ACCOUNT') ||
        userInfo.permissions.includes('PAYMENT_ACCOUNT'))
    isAdmin = isAdmin && payFromChannel === payFromChannelMap.manage
    const defaultVisibility = {
      departments: [],
      staffs: [`${userInfo?.staff?.id}`],
      roles: [],
      departmentsIncludeChildren: false,
      isDefault: true
    }
    this.form = React.createRef()
    //是否有创建权限
    const isPersonalHaveCreate = payeeConfigCheck.personalAccount
    const radioValue = isPersonalHaveCreate || isAdmin ? 'PERSONAL' : 'PUBLIC'
    if (isAdmin) {
      defaultVisibility.fullVisible = false
    } else {
      const accountConfig =
        radioValue === 'PERSONAL'
          ? getV(payeeConfig, 'personalAccountConfig')
          : getV(payeeConfig, 'publicAccountConfig')
      defaultVisibility.fullVisible = get(accountConfig, 'createAccount.visible')
    }

    const visibility = get(props, 'payee.visibility', defaultVisibility)
    const visibleValue = isCreate ? defaultVisibility.fullVisible : visibility.fullVisible
    this.state = {
      searchBankList: [],
      radioValue: payee?.type || radioValue,
      payee: payee || {},
      CertificateTypeList: [],
      visibility,
      visibleValue: visibilityMap[visibleValue],
      unionBankList: [],
      defaultChannel: 'OFFLINE',
      bankList: [],
      isAdmin: isAdmin && payFromChannel === payFromChannelMap.manage,
      extraTextFields: [] // 来自收款账户配置的额外 text 字段
    }
  }

  componentDidMount() {
    const { payee, CHANGJIEPay, KA_FOREIGN_ACCOUNT } = this.props
    if (KA_FOREIGN_ACCOUNT) {
      api.dataLoader('@custom-payment.searchCountries').load()
    }
    if (CHANGJIEPay) {
      api.invokeService('@common:get:unionBanks:list', { searchKey: '' }).then(res => {
        this.setState({
          unionBankList: res.items
        })
      })
    }
    api.invokeService(`@common:get:CertificateTypeList`)
    if (payee) {
      const countryId = get(payee, 'extensions.country')
      const countryStr = get(payee, 'extensions.countryStr')
      const cityStr = get(payee, 'extensions.cityStr')
      const countryEnStr = get(payee, 'extensions.countryEnStr')
      const cityEnStr = get(payee, 'extensions.cityEnStr')
      const bankCountryStr = get(payee, 'extensions.bankCountryStr')
      const shortCode = get(payee, 'extensions.shortCode')
      const numCode = get(payee, 'extensions.numCode')
      const bankCountry_shortCode = get(payee, 'extensions.bankCountry_shortCode')
      this.setState({
        countryStr,
        cityStr,
        countryEnStr,
        cityEnStr,
        bankCountryStr,
        //在变更记录中不显示字段 在变更记录逻辑中过滤
        shortCode,
        numCode,
        bankCountry_shortCode
      })
      if (countryId) {
        api.invokeService('@custom-payment:get:CityList', countryId)
      }
    }
    api.invokeService('@common:get:staffs:roleList:department')
    api
      .dataLoader('@common.payeeConfig')
      .reload()
      .then(() => {
        setTimeout(() => this.initPayeeExtraTextFields(this.state.radioValue))
      })
  }

  initPayeeExtraTextFields(type = PERSONAL) {
    const { payeeConfig = {} } = this.props
    const accountConfig = type == PERSONAL ? payeeConfig.personalAccountConfig : payeeConfig.publicAccountConfig
    const globalFields = api.getState('@common.globalFields.data') || []
    const customFields = accountConfig.customFields ?? []

    if (customFields) {
      // 额外字符串赋值
      this.setState({
        extraTextFields: compact(
          customFields.map(item => {
            return globalFields.find(v => v.name == item)
          })
        )
      })
    }
  }

  checkEn = (rule, value, callback) => {
    if (value) {
      if (value.match(/[\u4e00-\u9fa5]/)) return callback('Please enter in English')
    }
    callback()
  }

  checkBankNo(rule, value, callback) {
    if (value) {
      if (!/^[0-9a-zA-Z-]*$/.test(value.replace(/\s/g, ''))) return callback(i18n.get('输入格式不正确'))
      if (!value.length) return callback(i18n.get('输入内容不能为空'))
      if (value.length > 100) return callback(i18n.get('输入内容不能超过100位'))
    }
    callback()
  }

  checkAliPayNo(rule, value, callback) {
    if (value) {
      if (!value.length) return callback(i18n.get('支付宝账号不能为空'))
      if (value.length > 100) return callback(i18n.get('支付宝账号不能超过100位'))
    }
    callback()
  }

  checkEmail = (rule, value, callback) => {
    let reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/
    if (value && !reg.test(value)) {
      return callback(i18n.get('请输入正确格式的邮箱地址'))
    }
    callback()
  }

  inputValidator = (el, rule, value, callback) => {
    if (el?.onlyEn) {
      this.checkEn(rule, value, callback)
    }
    if (el?.checkEmail) {
      this.checkEmail(rule, value, callback)
    }
    callback()
  }

  checkOtherAccount(rule, value, callback) {
    if (value) {
      if (value.length > 100) return callback(i18n.get('账号不能超过100位'))
    }
    callback()
  }
  validCertificateNo = (rule, value, callback) => {
    const isCertificateTypeRequired = getBoolVariation('customized_filling_of_collection_account', false)
    const certificatetype = this.form.current.getFieldValue('certificateType')
    if (isCertificateTypeRequired && !value) {
      return callback(i18n.get('证件号不能为空'))
    }
    if (certificatetype === '01' && value) {
      if (!value.length) return callback(i18n.get('身份证号不能为空'))
      if (value.length > 18) return callback(i18n.get('身份证号不能超过18位'))
      if (!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        return callback(i18n.get('身份证号格式不正确'))
      }
    }
    if (
      window.IS_SMG &&
      window.PLATFORMINFO?.platform != 'OPG' &&
      this.state.radioValue === 'PUBLIC' &&
      certificatetype === '54' &&
      !value
    ) {
      return callback(i18n.get('纳税人识别号不能为空'))
    }
    callback()
  }

  checkRoutingNumber = (rule, value, callback) => {
    if (!!value && !/^[0-9]+$/.test(value)) {
      return callback(i18n.get('汇款路线号码必须是数字'))
    }
    callback()
  }

  resetFields() {
    this.setState({ area: undefined, radioValue: 'PERSONAL', payee: {} }, () => {
      this.form.current.resetFields()
    })
  }

  onAccountChange = e => {
    const { payeeConfig, userInfo = {} } = this.props
    const { isAdmin, visibility } = this.state
    let obj = {}
    if (!isAdmin) {
      //配置数据中个人和对公都选中 和 只选了个人， 默认取个人的配置数据，否则取对公的配置数据
      const accountConfig =
        e.target.value === 'PERSONAL'
          ? getV(payeeConfig, 'personalAccountConfig')
          : getV(payeeConfig, 'publicAccountConfig')
      const visibleValue = visibilityMap[get(accountConfig, 'createAccount.visible')]
      visibility.fullVisible = visibleValue === 'ALL'
      visibility.roles = visibility.departments = []
      visibility.staffs = [`${userInfo?.staff?.id}`]
      const accountChannelList =
        accountConfig?.accountSort && !isAdmin
          ? accountChannel().filter(item => accountConfig.accountSort.includes(item.value))
          : accountChannel()
      obj = { sort: accountChannelList[0].value }
      this.setState({
        radioValue: e.target.value,
        visibility,
        visibleValue
      })
    } else {
      this.setState({
        radioValue: e.target.value
      })
    }
    this.initPayeeExtraTextFields(e.target.value)
    if (this.form.current) {
      const oldValues = this.form.current.getFieldsValue()
      this.form.current.resetFields()
      this.form.current.setFieldsValue({
        ...oldValues,
        certificateType: undefined,
        certificateNo: '',
        accountName: '',
        ...obj
      })
    }
  }

  onCertificateTypeChange = () => {
    this.form.current && this.form.current.setFieldsValue({ certificateNo: '' })
  }

  onVisibleChange = e => {
    const { visibility } = this.state
    let obj = {
      visibleValue: e.target.value,
      visibility
    }
    obj.visibility.fullVisible = e.target.value === 'ALL'
    this.setState({ ...obj })
  }

  handleCancel = () => {
    this.props.layer.emitCancel()
  }

  handleSaveData = () => {
    const form = this.form.current
    return new Promise((resolve, error) => {
      form
        .validateFields()
        .then(values => {
          const {
            visibility,
            bankList,
            countryStr,
            cityStr,
            countryEnStr,
            cityEnStr,
            bankCountryStr,
            shortCode,
            numCode,
            bankCountry_shortCode,
            payee
          } = this.state
          // if (!!errors) return
          values.asPayer = false
          values.asPayee = true
          values.accountNo = values.accountNo.replace(/\s/g, '')
          values.bankLinkNo = payee?.bankLinkNo
          values.branchId = payee?.branchId?.id
          const targetBank = bankList.find(b => b.id === values.branch)
          if (targetBank) {
            values.branch = targetBank?.name
            values.bankLinkNo = targetBank?.code
            values.branchId = targetBank?.id
          }
          values.visibility = visibility
          const data = this.props
          let { owner, id } = data && data.payee ? data.payee : {}
          if (id) {
            values.id = id
          }
          const obj = {
            countryStr,
            cityStr,
            countryEnStr,
            cityEnStr,
            bankCountryStr,
            shortCode,
            numCode,
            bankCountry_shortCode
          }
          const extensions = get(values, 'extensions')
          if (extensions) {
            set(values, 'extensions', { ...extensions, ...obj })
          }
          values.owner = owner || 'INDIVIDUAL'
          resolve(values)
        })
        .catch(err => {
          if (err.errorFields?.[0]) {
            const fieldName = err.errorFields[0].name.join('_')

            const errorElement = document.getElementById(fieldName)
            if (errorElement) {
              errorElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              })
            }
          }
          throw err
        })
    })
  }

  handAccountleSave = () => {
    const { extraTextFields } = this.state
    this.handleSaveData().then(result => {
      const { bus, isCreate } = this.props
      const { isAdmin } = this.state
      const customFields = {}
      result.isCreate = isCreate
      if (isAdmin) {
        result.srcIsAdmin = true
      }
      extraTextFields.forEach(item => {
        customFields[item.name] = result[item.name]
        delete result[item.name]
      })
      result['customFields'] = customFields
      bus && bus.invoke('payee:save:click', result)
      this.handleCancel()
    })
  }

  handleUnionBankSearch = value => {
    if (timeout) {
      clearTimeout(timeout)
      timeout = null
    }
    timeout = setTimeout(() => {
      api.invokeService('@common:get:unionBanks:list', { searchKey: value }).then(res => {
        this.setState({
          unionBankList: res.items
        })
      })
    }, 500)
  }

  unionBankChange = unionBank => {
    if (unionBank) return
    const { payee } = this.state
    payee.unionBank = ''
    this.setState({ payee })
  }

  handleValueChange = value => {
    const data = {
      ...value,
      fullVisible: false
    }
    this.setState({ visibility: data })
  }

  changeLog = () => {
    const { payee } = this.state
    api.open('@payeeAccount:PayeeChangeLogs', { data: payee })
  }

  handleActiveCheck = () => {
    const { handleActiveCheck } = this.props
    const { payee } = this.state
    this.props.layer.emitCancel()
    handleActiveCheck && handleActiveCheck(payee.active, payee)
  }

  handleSetDefault = () => {
    const { handleSetDefault } = this.props
    const { payee } = this.state
    this.props.layer.emitCancel()
    handleSetDefault && handleSetDefault(payee, false)
  }

  onAccountTypeChange = e => {
    const { setFieldsValue } = this.form.current
    const { data } = this.state
    setFieldsValue({ accountNo: '' })
    this.setState({ data: { ...data, sort: e } })
  }

  handleAdvancedSearch = () => {
    api.open('@custom-payment:AdvancedSearch').then(data => {
      const { setFieldsValue } = this.form.current
      setFieldsValue({ branch: data?.id })
      if (data?.id) {
        this.setState({ bankList: [data] })
      }
    })
  }

  handleValidator = (rule, value, callback) => {
    const { userInfo } = this.props
    if (!value) {
      callback()
    }
    if (value != userInfo.staff.name) {
      callback('开户名与当前用户姓名不一致！')
    }
    callback()
  }

  onSearchBankList = debounce(searchKey => {
    const key = searchKey.trim()
    const flag = /^[\u4e00-\u9fff]+$/.test(key)
    if (flag && key?.length > 5) {
      api.invokeService('@payeeAccount:get:banks', { searchKey: key }).then(data => {
        this.setState({ bankList: data.items })
      })
    }
  }, 1000)

  onSelectChange = (value, name, e) => {
    const { searchCountries, searchCities } = this.props
    const { setFieldValue } = this.form.current
    //countryStr countryEnStr cityStr cityEnStr bankCountryStr shortCode numCode bankCountry_shortCode 保存下拉框value值对应全程，在收款变更记录中使用
    if (name === 'extensions.country' || name === 'extensions.countryEn') {
      let selectCountry = searchCountries.filter(item => item.id === e.key)
      let countryStr = selectCountry[0]?.fullCname
      let countryEnStr = selectCountry[0]?.fullEname
      let shortCode = selectCountry[0]?.shortCode
      let numCode = selectCountry[0]?.numCode
      this.setState({
        countryStr,
        countryEnStr,
        shortCode,
        numCode
      })
    }
    if (name === 'extensions.city' || name === 'extensions.cityEn') {
      let selectCity = searchCities.filter(item => item.id === e.key)
      let cityStr = null
      let cityEnStr = null
      if (selectCity[0]) {
        cityStr = selectCity[0].cnState ? `${selectCity[0].cnState}-${selectCity[0].cnName}` : selectCity[0].cnName
        cityEnStr = selectCity[0].state ? `${selectCity[0].state}-${selectCity[0].name}` : selectCity[0].name
      }
      this.setState({
        cityStr,
        cityEnStr
      })
    }
    if (name === 'extensions.bankCountry') {
      let selectCountry = searchCountries.filter(item => item.id === e.key)
      let bankCountryStr = selectCountry[0]?.fullCname
      let bankCountry_shortCode = selectCountry[0]?.shortCode
      this.setState({
        bankCountryStr,
        bankCountry_shortCode
      })
      return
    }
    const formItemMap = {
      'extensions.country': 'extensions.countryEn',
      'extensions.city': 'extensions.cityEn',
      'extensions.countryEn': 'extensions.country',
      'extensions.cityEn': 'extensions.city'
    }
    // 同标签中英文联动
    const labelname = formItemMap[name]
    if (labelname.includes('country')) {
      setFieldValue(labelname.split('.'), searchCountries.filter(item => item.id === e.key)[0]?.id)
    } else {
      setFieldValue(labelname.split('.'), searchCities.filter(item => item.id === e.key)[0]?.id)
    }
    // 切换国家 清空城市
    if (name.includes('country')) {
      setFieldValue(['extensions', 'city'], undefined)
      setFieldValue(['extensions', 'cityEn'], undefined)
      this.setState({
        cityStr: null,
        cityEnStr: null
      })
    }
    if (value && name.includes('country')) {
      api.invokeService('@custom-payment:get:CityList', e.key)
    }
  }

  handleAccountNoBlur = async () => {
    const { setFieldsValue, getFieldsValue } = this.form.current
    const values = await getFieldsValue()
    const { accountNo, sort, type } = values || {}
    if (sort === 'BANK' && type === 'PERSONAL' && accountNo?.length > 14) {
      const res = await api.invokeService('@custom-payment:get:BranchByCardNo', accountNo)
      setFieldsValue({ branch: res?.value?.id })
      if (res?.value?.id) {
        this.setState({ bankList: [res?.value] })
      }
    }
  }

  handleIntelligentFillingValue = async value => {
    const { setFieldsValue } = this.form.current
    const _value = value.trim()
    const data = JSON.stringify(_value)
    const text = data.replace(/^"|"$/g, '')
    api
      .invokeService('@custom-payment:post:Parsing', { text })
      .then(res => {
        const { accountName, accountNo, branches } = res?.value || {}
        showMessage.success(i18n.get('已填入可识别信息，请确认'))
        if (branches?.[0]?.id) {
          this.setState({ bankList: branches })
        }
        setTimeout(() => {
          setFieldsValue({ accountName, accountNo, branch: branches?.[0]?.name })
        }, 500)
      })
      .catch(err => {
        showMessage.error(err?.errorMessage)
      })
  }

  renderOverseaFieldForHSBC = (sortV, isForbiddenModify) => {
    const { KA_FOREIGN_ACCOUNT, searchCountries = [], searchCities = [] } = this.props
    const { payee } = this.state

    const formItemsArrHSBC =
      sortV === 'OVERSEABANK'
        ? getFormItemsArrHSBC()
        : getFormItemsArrHSBC().filter(item => item.name !== 'extensions.bankCountry')
    const selectMap = {
      'extensions.country': searchCountries,
      'extensions.city': searchCities,
      'extensions.countryEn': searchCountries,
      'extensions.cityEn': searchCities,
      'extensions.bankCountry': searchCountries
    }

    if ((sortV === 'OVERSEABANK' || sortV === 'BANK' || sortV === 'ACCEPTANCEBILL') && KA_FOREIGN_ACCOUNT) {
      return (
        <>
          {formItemsArrHSBC.map(el => {
            const label = el.payee ? `${el.payee}-${el.label}` : el.label
            const __k0 = el.payee ? `${i18n.get(el.payee)}-${i18n.get(el.label)}` : i18n.get(el.label)
            const placeholder = el.notChineseOREnglish
              ? i18n.get('请输入{__k0}', { __k0: __k0 })
              : el.onlyEn
              ? i18n.get(`请输入{__k0}_English`, { __k0: __k0 })
              : i18n.get(`请输入{__k0}_Chinese`, { __k0: __k0 })
            if (el.type === 'select') {
              return (
                <FormItem
                  initialValue={getV(payee, el.name)}
                  key={el.name}
                  name={el.name.split('.')}
                  rules={[{ validator: el.onlyEn ? this.checkEn : null }]}
                  label={label}
                  {...layout}
                >
                  <Select
                    className="select"
                    placeholder={placeholder}
                    getPopupContainer={triggerNode => triggerNode.parentNode}
                    showSearch={true}
                    disabled={isForbiddenModify}
                    onChange={(value, e) => this.onSelectChange(value, el.name, e)}
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }}
                  >
                    {get(selectMap, el.name).map(item => {
                      const optionTitle = el.name.includes('city')
                        ? el.onlyEn
                          ? item.state
                            ? `${item.state}-${item.name}`
                            : item.name
                          : item.cnState
                          ? `${item.cnState}-${item.cnName}`
                          : item.cnName
                        : el.onlyEn
                        ? item.fullEname
                        : item.fullCname
                      return (
                        <Option key={item.id} value={item.id}>
                          {optionTitle}
                        </Option>
                      )
                    })}
                  </Select>
                </FormItem>
              )
            } else {
              return (
                <FormItem
                  key={el.name}
                  name={el.name.split('.')}
                  initialValue={getV(payee, el.name)}
                  rules={[
                    { max: el.max, message: i18n.get(el.label) + i18n.get('不能超过{__k0}个文字', { __k0: el.max }) },
                    { validator: this.inputValidator.bind(this, el) }
                  ]}
                  label={label}
                  {...layout}
                >
                  <Input disabled={isForbiddenModify} placeholder={placeholder} />
                </FormItem>
              )
            }
          })}
        </>
      )
    }
    return null
  }

  renderBranchLabel = (name, label) => {
    const { radioValue } = this.state
    const isOVERSEABANK = this.state.payee?.sort === 'OVERSEABANK'
    const isNewTips = getBoolVariation('account_name_tips_new')
    return (
      <>
        {name || i18n.get('银行网点')}
        <Tooltip
          placement="top"
          title={i18n.get(titleMap(isNewTips && isOVERSEABANK)?.[label === 'branch' ? radioValue : label])}
        >
          <OutlinedTipsInfo style={{ fontSize: 14, color: 'var(--eui-icon-n2)', marginTop: 3, marginLeft: 4 }} />
        </Tooltip>
      </>
    )
  }

  handleFormChange = updateValue => {
    const { payee } = this.state
    this.setState({ payee: { ...payee, ...updateValue } })
  }

  render() {
    const {
      isCreate = true,
      CHANGJIEPay,
      payeeConfig,
      payFromChannel,
      defaultPayee,
      disableType,
      mcDisable,
      showTips = true,
      showHistory = true,
      certificateTypeList = {},
      payeeConfigCheck = {},
      showClose,
      isPopup,
      disabled
    } = this.props
    const {
      radioValue,
      visibleValue,
      unionBankList,
      payee,
      bankList,
      visibility,
      isAdmin,
      extraTextFields
    } = this.state
    const accountSort =
      radioValue === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig.accountSort')
        : getV(payeeConfig, 'publicAccountConfig.accountSort')
    let accountChannelList = accountChannel()
    if (!isAdmin && isCreate) {
      accountChannelList = accountChannel().filter(item => accountSort && accountSort.includes(item.value))
    } else if (!isAdmin && !isCreate) {
      accountChannelList = accountChannel().filter(item => {
        item.isVisible = accountSort && (!isAdmin || isAdmin) && accountSort.includes(item.value)
        return item
      })
    }
    const isCertificateTypeRequired = getBoolVariation('customized_filling_of_collection_account', false)
    const sortV = payee.sort || (accountChannelList.length && accountChannelList[0].value)
    const certificateType = payee.certificateType || ''
    const accountConfig =
      radioValue === 'PERSONAL'
        ? getV(payeeConfig, 'personalAccountConfig', {})
        : getV(payeeConfig, 'publicAccountConfig', {})
    const personalChecked = isAdmin ? isAdmin : payeeConfigCheck.personalAccount //配置中允许创建个人
    const publicChecked = isAdmin ? isAdmin : payeeConfigCheck.publicAccount //配置中允许创建对公
    const isPersonalHaveCreate = payeeConfigCheck.personalAccount
    const isPublicHaveCreate = payeeConfigCheck.publicAccount
    const personalForbiddenModify = getV(payeeConfig, 'personalAccountConfig.forbiddenModify', false)
    const publicForbiddenModify = getV(payeeConfig, 'publicAccountConfig.forbiddenModify', false)
    const isEditVisibility = payFromChannel === payFromChannelMap.personal && accountConfig.allowShared
    const isForbiddenModify =
      (!isCreate && payFromChannel === payFromChannelMap.personal && accountConfig.forbiddenModify) || disabled
    const isShow = CHANGJIEPay ? !accountConfig.conciseInput : true
    const accountChannelNameMapEnum = accountChannelNameMap()
    const isAlipay = sortV === 'ALIPAY'
    const isOthers = sortV === 'OTHER' || sortV === 'WEIXIN'
    const accountNoPlaceHolder = isAlipay
      ? i18n.get('请输入与开户名相匹配的支付宝账号')
      : i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[sortV] })
    const accountNamePlaceholder = i18n.get('请输入真实姓名、公司名或组织机构名称')
    const certificateTypeListItems = certificateTypeList.items || []
    const certificateTypeListWithNone = isCertificateTypeRequired
      ? certificateTypeListItems
      : [{ code: '', name: '无' }, ...certificateTypeListItems]
    const MCDisable = disableType ? mcDisable : true
    const isForbiddenAdd =
      payFromChannel === payFromChannelMap.personal && radioValue === 'PERSONAL' && accountConfig.forbiddenAdd
    const isRequired = accountConfig?.networkNotRequired || false // 网点是否必填
    let publicCertificateList =
      certificateTypeListWithNone &&
      certificateTypeListWithNone.filter(item => item.code === '11' || item.code === '54' || item.code == '')

    if (window.IS_SMG) {
      accountChannelList = accountChannelList && accountChannelList.filter(line => line.value !== 'ALIPAY')
    }
    if (window.IS_SMG && window.PLATFORMINFO?.platform != 'OPG') {
      publicCertificateList = publicCertificateList && publicCertificateList.filter(item => item.code === '54')
    }
    const isDisabledPersonal =
      (isCreate ? !personalChecked || !isPersonalHaveCreate : personalForbiddenModify || !isPersonalHaveCreate) &&
      !isAdmin
    const isDisabledPublic =
      (isCreate ? !publicChecked || !isPublicHaveCreate : publicForbiddenModify || !isPublicHaveCreate) && !isAdmin
    const cityCodeList = getFormItemsCityCode()
    const isNewTips = getBoolVariation('account_name_tips_new')
    return (
      <div
        id={'payee-account-create'}
        className={isPopup ? style['account-create-wrapper-popup'] : style['account-create-wrapper-modal']}
      >
        <div className="modal-header">
          <div className="title flex">
            {isCreate ? i18n.get('新建收款信息') : disabled ? i18n.get('查看收款信息') : i18n.get('编辑收款信息')}
          </div>
          {!isCreate && showHistory && (
            <>
              <Button size="small" category="text" onClick={this.changeLog} data-testid="pay-payeeAccountCreate-changeLog-btn">
              <T name="变更记录" />
            </Button>
              <Divider type="vertical" style={{ height: 16, margin: '0 12px' }} />
            </>
          )}
          {showClose && (
            <div style={{ cursor: 'pointer' }} onClick={this.handleCancel} data-testid="pay-payeeAccountCreate-close-btn">
              <OutlinedTipsClose fontSize={16} />
            </div>
          )}
        </div>
        <div className="custom-payment-modal payee-content" style={{ bottom: MCDisable ? 64 : 0 }}>
          {!isCreate && showTips && (
            <Alert
              className="tip-info-wrapper"
              message={i18n.get('修改信息后会同步更新到已提交单据的收款信息')}
              type="info"
              showIcon
            />
          )}
          <Form ref={this.form} layout="vertical" onValuesChange={this.handleFormChange} scrollToFirstError compact>
            <FormItem name="type" initialValue={(payee && payee.type) || radioValue}>
              <RadioGroup size="large" className="account-type" disabled={isForbiddenModify} onChange={this.onAccountChange} data-testid="pay-payeeAccountCreate-account-type">
                <Radio.Button disabled={isDisabledPersonal || isDisabledPublic} value="PERSONAL" className="radio">
                  {radioValue === 'PERSONAL' ? (
                    <FilledGeneralMember fontSize={20} style={{ marginRight: 4 }} />
                  ) : (
                    <OutlinedGeneralMember fontSize={20} style={{ marginRight: 4 }} />
                  )}
                  {i18n.get('个人账户')}
                </Radio.Button>
                <Radio.Button disabled={isDisabledPublic || isDisabledPersonal} value="PUBLIC" className="radio">
                  {radioValue === 'PUBLIC' ? (
                    <FilledGeneralGroup fontSize={20} style={{ marginRight: 4 }} />
                  ) : (
                    <OutlinedGeneralGroup fontSize={20} style={{ marginRight: 4 }} />
                  )}
                  {i18n.get('对公账户')}
                  <Tooltip placement="top" title={i18n.get('个体工商户、企业账户')}>
                    <OutlinedTipsInfo
                      className="public-icon"
                      style={{ fontSize: 14, color: 'var(--eui-icon-n2)', marginLeft: 4, marginBottom: 1 }}
                    />
                  </Tooltip>
                </Radio.Button>
              </RadioGroup>
            </FormItem>
            <Space className="sortSpace">
              <FormItem
                label={i18n.get('账户类别')}
                name="sort"
                initialValue={(payee && payee.sort) || sortV}
                rules={[{ required: true, whitespace: true, message: i18n.get('请选择账户类别') }]}
              >
                <Select
                  className="w-100b"
                  onChange={this.onAccountTypeChange}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  disabled={isForbiddenModify}
                  data-testid="pay-payeeAccountCreate-select-accountType"
                >
                  {accountChannelList.map((item, idx) => (
                    <Option
                      className={className({ 'account-sort-option': isAdmin || isCreate ? false : !item.isVisible })}
                      key={item.value + idx}
                      value={item.value}
                    >
                      {item.label}
                    </Option>
                  ))}
                </Select>
              </FormItem>
              {sortV === 'BANK' && (
                <IntelligentFillingModal
                  disabled={isForbiddenModify}
                  handleIntelligentFillingValue={this.handleIntelligentFillingValue}
                />
              )}
            </Space>

            <FormItem
              name="accountName"
              label={this.renderBranchLabel(i18n.get('开户名'), 'accountName')}
              initialValue={payee && payee.accountName}
              rules={[
                { required: true, whitespace: true, message: accountNamePlaceholder },
                { max: 140, message: i18n.get('开户名不能超过140个字') },
                { validator: isForbiddenAdd ? this.handleValidator : null }
              ]}
            >
              <Input
                data-testid="pay-payeeAccountAccountName-input"
                disabled={isForbiddenModify}
                placeholder={accountNamePlaceholder}
              />
            </FormItem>
            <FormItem
              name="accountNo"
              initialValue={payee && payee.accountNo}
              label={accountChannelNameMapEnum[sortV]}
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: i18n.get(`请输入{__k0}`, { __k0: accountChannelNameMapEnum[sortV] })
                },
                { validator: isAlipay ? this.checkAliPayNo : isOthers ? this.checkOtherAccount : this.checkBankNo }
              ]}
              {...layout}
            >
              <AccountNoInput
                data-testid="pay-payeeAccountAccountNo-input"
                isAlipay={isAlipay}
                disabled={isForbiddenModify}
                placeholder={accountNoPlaceHolder}
                onBlur={this.handleAccountNoBlur}
              />
            </FormItem>
            {isShow && (sortV === 'BANK' || sortV === 'ACCEPTANCEBILL') && (
              <FormItem
                name="branch"
                className="branch"
                initialValue={payee && (payee?.branchId?.name || payee.branch)}
                rules={[{ required: !isRequired, whitespace: true, message: i18n.get('请选择银行网点') }]}
                label={this.renderBranchLabel(i18n.get('银行网点'), 'branch')}
                {...layout}
              >
                <SelectBranchTypeField
                  dataSource={bankList}
                  disabled={isForbiddenModify}
                  handleAdvancedSearch={this.handleAdvancedSearch}
                  onSearchBankList={this.onSearchBankList}
                />
              </FormItem>
            )}
            {accountConfig.conciseInput && CHANGJIEPay && (sortV === 'BANK' || sortV === 'ACCEPTANCEBILL') && (
              <FormItem
                name="unionBank"
                initialValue={(payee && payee.unionBank) || ''}
                rules={[{ required: true, whitespace: true, message: i18n.get('请选择开户行名称') }]}
                label={i18n.get('开户行名称')}
              >
                <SelectBankField
                  disabled={isForbiddenModify}
                  isConcise={true}
                  otherField={false}
                  dataSource={unionBankList}
                  placeholder={i18n.get('请输入开户行关键字进行筛选')}
                  onSearch={this.handleUnionBankSearch}
                  onSelect={this.handleUnionBankSearch}
                  allowClear={true}
                  onChange={this.unionBankChange}
                />
              </FormItem>
            )}
            {sortV === 'OVERSEABANK' && (
              <FormItem
                name="bankName"
                initialValue={payee && payee.bankName}
                rules={[
                  {
                    required: this.props.KA_FOREIGN_ACCOUNT,
                    whitespace: true,
                    message: i18n.get('银行名称(Bank Name)')
                  },
                  { validator: this.checkCode }
                ]}
                label={
                  !isNewTips
                    ? i18n.get('银行名称(Bank Name)')
                    : this.renderBranchLabel(i18n.get('银行名称(Bank Name)'), 'bankName')
                }
                {...layout}
              >
                <Input disabled={isForbiddenModify} placeholder={i18n.get('银行名称(Bank Name)')} />
              </FormItem>
            )}

            {sortV === 'OVERSEABANK' && (
              <FormItem
                name="swiftCode"
                initialValue={payee && payee.swiftCode}
                rules={[
                  { required: true, whitespace: true, message: i18n.get('银行国际代码(Swift Code)') },
                  {
                    validator: (_, value, callback) => {
                      if (!value || value.length === 8 || value.length === 11) {
                        return callback()
                      }
                      return callback('只能输入8位或11位')
                    }
                  },
                  { validator: this.checkCode }
                ]}
                label={i18n.get('银行国际代码(Swift Code)')}
                {...layout}
              >
                <Input disabled={isForbiddenModify} placeholder={i18n.get('银行国际代码(Swift Code)')} />
              </FormItem>
            )}
            {sortV === 'OVERSEABANK' && (
              <FormItem
                name="nationCode"
                initialValue={payee && payee.nationCode}
                rules={[
                  { required: true, whitespace: true, message: i18n.get('请选择银行所在地区代码(Nation Code)') },
                  { validator: this.checkCode }
                ]}
                label={i18n.get('银行所在地区代码(Nation Code)')}
                {...layout}
              >
                <Select
                  showSearch
                  disabled={isForbiddenModify}
                  optionFilterProp="children"
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  placeholder={i18n.get('请选择银行所在地区代码(Nation Code)')}
                >
                  {cityCodeList.map((item, idx) => (
                    <Option key={item.key} value={item.key}>
                      {item.value}
                    </Option>
                  ))}
                </Select>
              </FormItem>
            )}
            {sortV === 'OVERSEABANK' && (
              <FormItem
                name="receiverAddress"
                initialValue={payee && payee.receiverAddress}
                label={i18n.get('收款人地址(Receiver Address)')}
                {...layout}
              >
                <Input disabled={isForbiddenModify} placeholder={i18n.get('请输入收款人地址，不超过35个汉字')} />
              </FormItem>
            )}
            {sortV === 'OVERSEABANK' && (
              <FormItem
                name="routingNumber"
                initialValue={payee && payee.routingNumber}
                rules={[
                  { max: 9, message: i18n.get('汇款路线号码不能超过9个字符') },
                  { validator: this.checkRoutingNumber }
                ]}
                label={i18n.get('汇款路线号码(Routing No.)')}
                {...layout}
              >
                <Input disabled={isForbiddenModify} placeholder={i18n.get('汇款路线号码(Routing No.)')} />
              </FormItem>
            )}
            {sortV === 'OVERSEABANK' && (
              <FormItem
                name="bankCode"
                initialValue={payee && payee.bankCode}
                rules={[{ whitespace: true, message: i18n.get('联行号(Bank Code)') }, { validator: this.checkCode }]}
                label={i18n.get('联行号(Bank Code)')}
                {...layout}
              >
                <Input disabled={isForbiddenModify} placeholder={i18n.get('联行号(Bank Code)')} />
              </FormItem>
            )}
            {sortV === 'OVERSEABANK' && (
              <FormItem
                name="branchCode"
                initialValue={payee && payee.branchCode}
                rules={[{ whitespace: true, message: i18n.get('支行号(Branch Code)') }, { validator: this.checkCode }]}
                label={i18n.get('支行号(Branch Code)')}
                {...layout}
              >
                <Input disabled={isForbiddenModify} placeholder={i18n.get('支行号(Branch Code)')} />
              </FormItem>
            )}
            <FormItem
              rules={[
                {
                  required: isCertificateTypeRequired,
                  message: i18n.get('请选择证件类型')
                }
              ]}
              name="certificateType"
              initialValue={payee.certificateType}
              label={i18n.get('证件类型')}
            >
              <SelectCertificateType
                disabled={isForbiddenModify}
                placeholder={i18n.get('请选择证件类型')}
                onChange={this.onCertificateTypeChange}
                dataSource={
                  radioValue === 'PERSONAL' ? { items: certificateTypeListWithNone } : { items: publicCertificateList }
                }
              />
            </FormItem>
            <FormItem
              name="certificateNo"
              initialValue={payee && payee.certificateNo}
              rules={[
                {
                  validator: this.validCertificateNo,
                  required:
                    (window.IS_SMG && window.PLATFORMINFO?.platform != 'OPG' && radioValue === 'PUBLIC') ||
                    isCertificateTypeRequired
                }
              ]}
              label={i18n.get('证件号码')}
            >
              <Input
                disabled={isForbiddenModify || (!certificateType && !isCertificateTypeRequired)}
                placeholder={i18n.get('请输入证件号码')}
              />
            </FormItem>
            {this.renderOverseaFieldForHSBC(sortV, isForbiddenModify)}
            <FormItem
              name="remark"
              className="remark"
              initialValue={payee && payee.remark}
              rules={[
                { required: false, whitespace: true, message: i18n.get('请输入备注信息') },
                { max: 140, message: i18n.get('备注信息不能超过140个字') }
              ]}
              {...layout}
              colon={false}
              label={this.renderBranchLabel(i18n.get('备注信息'), 'remark')}
            >
              <Input disabled={isForbiddenModify} placeholder={i18n.get('请输入备注信息')} />
            </FormItem>
            {extraTextFields.map(field => (
              <FormItem
                className="custom-text-fields"
                {...layout}
                name={field.name}
                colon={false}
                initialValue={(payee?.customFields && payee?.customFields[field.name]) ?? ''}
                rules={[{ max: 200, message: i18n.get('{name}不能超过200个字', { name: field.label }) }]}
                label={i18n.get(field.label)}
                key={field.name}
              >
                <Input disabled={isForbiddenModify} placeholder={i18n.get('请输入{name}', { name: field.label })} />
              </FormItem>
            ))}
            {(isEditVisibility || payFromChannel === payFromChannelMap.manage) && (
              <FormItem name={'visibility'} initialValue={visibleValue} label={i18n.get('可见性')}>
                <RadioGroup className="account-type" onChange={this.onVisibleChange}>
                  <div>
                    <Radio value="ALL">
                      <T name="全员可见" />
                    </Radio>
                    <Radio value="SECTION">
                      <T name="部分可见" />
                    </Radio>
                  </div>
                  {visibleValue === 'SECTION' && (
                    <div className="currency-select mt-8">
                      <StaffSelect
                        multiple
                        includeSubWrapperStyle={{ marginTop: '8px' }}
                        showIncludeChildren={true}
                        className="subject-select"
                        placeholder={i18n.get('请选择人员、角色(职级)或部门')}
                        value={visibility}
                        tagShowStyle={{ width: '100%' }}
                        onValueChange={this.handleValueChange}
                      />
                    </div>
                  )}
                </RadioGroup>
              </FormItem>
            )}
          </Form>
        </div>
        {MCDisable && (
          <div className="payee-account-modal-footer">
            <Space>
              {isPopup ? (
                <Button
                  data-testid="pay-payeeAccountCreate-save-btn"
                  key="ok"
                  size="middle"
                  onClick={this.handAccountleSave}
                >
                  <T name="保存" />
                </Button>
              ) : (
                <Button key="cancel" category="secondary" size="middle" onClick={this.handleCancel} data-testid="pay-payeeAccountCreate-cancel-btn">
                  <T name="取消" />
                </Button>
              )}
              {payFromChannel === payFromChannelMap.personal &&
                !isCreate &&
                (!defaultPayee || payee.id !== defaultPayee.id) &&
                payee.active && (
                  <Button onClick={this.handleSetDefault} category="secondary" data-testid="pay-payeeAccountCreate-setDefault-btn">
                  <T name="设为默认" />
                </Button>
                )}
              {!isCreate && payee.active && (
                <Button category="secondary" theme="danger" onClick={this.handleActiveCheck} data-testid="pay-payeeAccountCreate-deactivate-btn">
                  <T name="停用" />
                </Button>
              )}
              {!isCreate && !payee.active && (
                <Button category="secondary" theme="highlight" onClick={this.handleActiveCheck} data-testid="pay-payeeAccountCreate-activate-btn">
                  <T name="启用" />
                </Button>
              )}
              {isPopup ? (
                <Button key="cancel" category="secondary" size="middle" onClick={this.handleCancel} data-testid="pay-payeeAccountCreate-cancel-btn">
                  <T name="取消" />
                </Button>
              ) : (
                <Button
                  data-testid="pay-payeeAccountCreate-save-btn"
                  key="ok"
                  size="middle"
                  onClick={this.handAccountleSave}
                >
                  <T name="保存" />
                </Button>
              )}
            </Space>
          </div>
        )}
      </div>
    )
  }
}
