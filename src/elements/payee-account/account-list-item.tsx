/*
 * @Author: Onein
 * @Date: 2019-12-09 16:50:55
 * @Last Modified by: Onein
 * @Last Modified time: 2020-11-25 21:24:10
 */

import './account-list-item.less'
import React, { PureComponent } from 'react'
import { Select, Tooltip } from 'antd'
import EKBIcon from '../../elements/ekbIcon'
import classNames from 'classnames'
import { T } from '@ekuaibao/i18n'
import { ChannelProps, Props } from './intreface'
import { EnhanceConnect } from '@ekuaibao/store'
import _compact from 'lodash/compact'

const { Option } = Select
const formChannelEnum = {
  payee: 'payee',
  paymentc: 'paymentConfirmation',
  pay: 'pay'
}

const colorMap = {
  BANK: 'red-bg',
  ALIPAY: 'blue-bg',
  WEIXIN: 'green-bg',
  CHECK: 'red-bg',
  OVERSEABANK: 'blue-bg',
  ACCEPTANCEBILL: 'blue-bg',
  OTHER: 'blue-bg',
  WALLET: 'red-bg',
  VIRTUALCARD: 'red-bg',
  CORPWALLET: 'red-bg'
}

import { payFromChannelMap, accountIconMap, payeeIconMap } from './account-list-consts'
import { isInWeComISV } from '../../lib/platform'
export { payFromChannelMap, accountIconMap, payeeIconMap }

interface State {
  channel: string
}

@EnhanceConnect(state => ({
  allCurrencyRates: state['@common'].allCurrencyRates,
  standardCurrency: state['@common'].standardCurrency,
  payeeConfig: state['@common'].payeeConfig,
  FOREIGN_CURRENCY_PAY: state['@common'].powers.FOREIGN_CURRENCY_PAY,
  globalFields: state['@common'].globalFields.data
}))
export default class AccountListItem extends PureComponent<Props, State> {
  constructor(props) {
    super(props)
    this.state = { channel: props.data.defaultChannel || 'OFFLINE' }
  }

  componentWillReceiveProps(np) {
    if (np.data !== this.props.data) {
      this.setState({ channel: np.data.defaultChannel || 'OFFLINE' })
    }
  }

  channelClick = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
  }

  handleClickSetDefault = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleSetDefault } = this.props
    handleSetDefault && handleSetDefault()
  }

  handleClickCancelDefault = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleClickCancelDefault } = this.props
    handleClickCancelDefault && handleClickCancelDefault()
  }

  handleCollect = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleCollect } = this.props
    handleCollect && handleCollect()
  }

  handleCancelCollect = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleCancelCollect } = this.props
    handleCancelCollect && handleCancelCollect()
  }

  payeeConfig = () => {
    const { payeeConfig, data } = this.props

    return data.type === 'PERSONAL' ? payeeConfig?.personalAccountConfig : payeeConfig?.publicAccountConfig
  }

  customFilesData = () => {
    const { data, globalFields } = this.props

    const showCardCustomFields = this.payeeConfig()?.showCardCustomFields || []
    return _compact(
      showCardCustomFields.map(item => {
        if (data.customFields && data.customFields[item]) {
          return {
            label: globalFields.filter((v: any) => v.name === item)[0]?.label || '',
            value: (data.customFields && data.customFields[item]) ?? ''
          }
        }
      })
    )
  }

  renderPayee = () => {
    const {
      isManangePage,
      data,
      onClick,
      payFromChannel,
      isDefault,
      isSettingDepency,
      showCancleDefault = false,
      showCollectionBtns = false
    } = this.props
    const fullVisible = data.visibility && data.visibility.fullVisible
    const accountType = data.type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户')
    const bankName = data?.branchId?.name || data?.branch || data.bank || data.unionBank
    const bankIcon = data.icon || data.unionIcon
    const accountNo = data.accountNo || data.cardNo
    const isVIRTUALCARD = data.sort === 'VIRTUALCARD' // 数科虚拟卡
    const isPayeeManager = isVIRTUALCARD ? false : payFromChannelMap.manage === payFromChannel
    const isDefaultAndPersnal = !isDefault && payFromChannelMap.manage !== payFromChannel && !isSettingDepency
    const isShowCancelDefault =
      isDefault &&
      isManangePage &&
      (payFromChannelMap.select === payFromChannel || payFromChannelMap.personal === payFromChannel)
    const isCorporation = data.owner === 'CORPORATION' ? i18n.get('企业') : data.staffId && data.staffId.name

    let defaultSpanClass = 'account-default-red'
    if (data.sort === 'ALIPAY' || data.sort === 'OVERSEABANK') {
      defaultSpanClass = ' account-default-blue'
    }
    const showCancle = showCancleDefault ? data.filterActive && isShowCancelDefault : isShowCancelDefault
    const remarkDisplay = this.payeeConfig()?.remarkDisplay
    const remark = data?.remark || ''
    const _remark = remark?.length < 6 ? remark : remark?.substr(0, 5) + '...'
    const showRemark = remark && remarkDisplay

    // 展示取消收藏按钮的条件判断
    const showCancelCollectBtn = data?.favoriteStatus === true ? true : false

    return (
      <>
        <div className="account-content-wrap payee-account-content-wrap">
          <div className="up-wrap">
            <div className={classNames('payee-title-wrap', { 'payee-large-title-wrap': isManangePage })}>
              <Tooltip title={data.accountName}>
                <span className={classNames('account-title', { 'account-large-title': isManangePage })}>
                  {data.accountName}
                </span>
              </Tooltip>
              {isDefault && (
                <span className={classNames(`account-default ml-8 ${defaultSpanClass}`)}>
                  <T name="默认" />
                </span>
              )}
              <span className="account-type ml-8">{accountType}</span>
              {fullVisible && (
                <span className="account-shared ml-8">
                  <T name="共享" />
                </span>
              )}
              {showRemark && (
                <span className="remark-item ml-8">
                  {remark?.length < 6 ? remark : <Tooltip title={remark}>{_remark}</Tooltip>}
                </span>
              )}
            </div>
            {isPayeeManager && (
              <EKBIcon className="item-setting stand-28-icon cur-p" onClick={onClick} name="#EDico-setting-fill1" data-testid="pay-accountListItem-setting-icon" />
            )}
            {!isVIRTUALCARD && data.filterActive && isDefaultAndPersnal && (
              <span className="set-default" onClick={this.handleClickSetDefault} data-testid="pay-accountListItem-setDefault">
                <T name="设为默认" />
              </span>
            )}
            {!isVIRTUALCARD && showCancle && (
              <span className="set-default" onClick={this.handleClickCancelDefault} data-testid="pay-accountListItem-cancelDefault">
                <T name="取消默认账户" />
              </span>
            )}
            {!isSettingDepency && showCollectionBtns && !showCancelCollectBtn && (
              <span className="set-default set-collect" onClick={this.handleCollect} data-testid="pay-accountListItem-collect">
                <T name="收藏" />
              </span>
            )}
            {!isSettingDepency && showCollectionBtns && showCancelCollectBtn && (
              <span className="set-default set-collect" onClick={this.handleCancelCollect} data-testid="pay-accountListItem-cancelCollect">
                <T name="取消收藏" />
              </span>
            )}
          </div>
          <div className="bottom-wrap">
            <div className="bank-content">
              <div className="bank-wrap payee-bank-wrap text-nowrap-ellipsis">
                <div className="bank-no bank-large-no text-nowrap-ellipsis">{formatCardNo(accountNo)}</div>
                <div className="dis-f ai-c">
                  {bankName && (
                    <div className="icon-bg">
                      <img className="stand-16-icon" src={bankIcon} alt={bankName} />
                    </div>
                  )}
                  {data.sort === 'OVERSEABANK' ? (
                    <>
                      <span className="bank-name text-nowrap-ellipsis">{data.swiftCode}</span>|
                      <Tooltip title={data.bankName}>
                        <span className="bank-name text-nowrap-ellipsis">{data.bankName}</span>
                      </Tooltip>
                    </>
                  ) : (
                    <span className={classNames('bank-name', { 'mt-20': !bankName })}>{bankName}</span>
                  )}
                </div>
                <div className="dis-f ai-c">
                  {this.customFilesData()?.map((item: any) => (
                    <Tooltip title={`${item.label}：${item.value}`}>
                      <div className="other-files text-nowrap-ellipsis">
                        {item.label}：{item.value}
                      </div>
                    </Tooltip>
                  ))}
                </div>
              </div>
              {data.owner && (
                <div className="channel-wrap" data-platform-wx2-hidden={window.isInWeComISV}>
                  <T name="所有者:" /> {isCorporation}
                </div>
              )}
            </div>
          </div>
        </div>
        {this.renderBackground()}
      </>
    )
  }

  handleWalletClick = (e: any) => {
    const { handleWalletClick, isWalletManager = false } = this.props
    if (!isWalletManager) {
      e.preventDefault()
      e.stopPropagation()
      return
    }
    handleWalletClick && handleWalletClick(e)
  }

  handleSelectOnChange = value => {
    const { onChannelChange, data } = this.props
    onChannelChange && onChannelChange(value)
    // this.setState({ channel: data.channels[value] })
  }

  renderOther = () => {
    const {
      isManangePage,
      formChannel,
      data,
      onClick,
      isWalletManager,
      parentID,
      selectDisabled = false,
      configDisabled = false,
      dynamicChannels = [],
      channelList = [],
      dynamicChannelMap = {},
      allCurrencyRates,
      standardCurrency,
      FOREIGN_CURRENCY_PAY,
      isCNY
    } = this.props
    const channel = this.props.channel || 'OFFLINE'
    const newChanneList = channelList.map((channel: ChannelProps) => {
      const target: ChannelProps = dynamicChannels.find((item: ChannelProps) => item.code === channel.channel)
      if (target) channel.active = target.active
      return channel
    })
    const dataChannels = data.channels || []
    const newDataChannels = dataChannels.filter(channel => {
      return newChanneList.find((item: ChannelProps) => item.channel === channel && item.active)
    })
    const accountName = data.name || data.accountName
    let accountNo = data.accountNo || data.cardNo
    if (data.sort === 'OVERSEABANK') {
      accountNo = accountNo && accountNo.length > 4 ? accountNo.substring(accountNo.length - 4) : accountNo
    } else {
      accountNo = formatCardNo(accountNo)
    }
    const defaultChannel = data.defaultChannel || 'OFFLINE'
    const isActive = !data.active && data.sort !== 'WALLET'
    const isWalletAndOpened = data.sort === 'WALLET' && data.isOpened
    const notWallet = data.sort !== 'WALLET'
    const bankName = data.bank || data.unionBank
    const click = notWallet ? onClick : this.handleWalletClick
    const payChannel = formChannel === formChannelEnum.pay
    const paymentcChannel = formChannel === formChannelEnum.paymentc
    const isCORPWALLET = data.sort === 'CORPWALLET' // 数科企业钱包
    const showSettingIcon = isCORPWALLET ? false : notWallet && payChannel

    const currency = data?.extensions?.currency || []
    const currencyList = []
    currency.forEach(item => {
      let currencyItem = [...allCurrencyRates, standardCurrency].filter(el => el.numCode === item)
      if (currencyItem[0]) {
        currencyList.push(currencyItem[0].strCode)
      }
    })
    const currencyStrCodeStr = currencyList.join(' ')
    const remark = data?.remark || ''
    const _remark = remark?.length < 6 ? remark : remark?.substr(0, 5) + '...'
    return (
      <>
        <div className={classNames('account-content-wrap')}>
          <div className="up-wrap">
            <div className="title-wrap">
              <Tooltip title={accountName}>
                <span className={classNames('account-title', { 'account-large-title': isManangePage })}>
                  {accountName}
                </span>
              </Tooltip>
              {data.code && (
                <span
                  className={classNames('account-code text-nowrap-ellipsis', { 'account-large-code': isManangePage })}
                >
                  ({data.code})
                </span>
              )}
              {isActive && (
                <span className="account-disabled">
                  <T name="已停用" />
                </span>
              )}
              {isWalletAndOpened && (
                <span className="account-disabled">
                  <T name="未开通" />
                </span>
              )}
              {remark && (
                <span className="remark-item ml-8">
                  {remark?.length < 6 ? remark : <Tooltip title={remark}>{_remark}</Tooltip>}
                </span>
              )}
            </div>
            {showSettingIcon && (
              <EKBIcon
                  style={configDisabled ? { opacity: 0 } : {}}
                  className={classNames('item-setting stand-28-icon cur-p', {
                    'cur-n': !isWalletManager && data.sort === 'WALLET'
                  })}
                  onClick={click}
                  name="#EDico-setting-fill1"
                  data-testid="pay-accountListItem-config-icon"
                />
            )}
          </div>
          <div className="bottom-wrap">
            <div className={classNames('bank-content', { 'mt-20': !bankName, 'mt-8': bankName && !notWallet })}>
              {notWallet && (
                <div className="bank-wrap">
                  {data.icon && (
                    <div className="icon-bg">
                      <img
                        className={classNames('stand-16-icon', { 'disable-img': !data.active })}
                        src={data.icon}
                        alt={bankName}
                      />
                    </div>
                  )}
                  {data.sort === 'OVERSEABANK' ? (
                    <>
                      <span className="bank-name text-nowrap-ellipsis">{data.swiftCode}</span>|
                      <Tooltip title={data.bankName}>
                        <span className="bank-name text-nowrap-ellipsis">{data.bankName}</span>
                      </Tooltip>
                    </>
                  ) : (
                    <span className="bank-name">{i18n.get(bankName)}</span>
                  )}
                  <span className="bank-no text-nowrap-ellipsis">{accountNo}</span>
                  <Tooltip title={currencyStrCodeStr}>
                    <span className="currency-str text-nowrap-ellipsis">{currencyStrCodeStr}</span>
                  </Tooltip>
                </div>
              )}
              {payChannel && (
                <>
                  {notWallet && (
                    <div className="channel-wrap">
                      <div className="icon-bg">
                        <EKBIcon
                          className={classNames('stand-16-icon p-2', { 'disable-img': !data.active })}
                          name={dynamicChannelMap[defaultChannel]?.icon}
                        />
                      </div>
                      {<span className="ml-8 channel-name">{dynamicChannelMap[defaultChannel]?.name}</span>}
                    </div>
                  )}
                </>
              )}
              {paymentcChannel && (
                <>
                  <div className="payment-con-wrap" onClick={this.channelClick} data-testid="pay-accountListItem-channelClick">
                    <Select
                      className="w-180 h-32 selecter"
                      disabled={selectDisabled}
                      getPopupContainer={() => (parentID ? document.getElementById(parentID) : document.body)}
                      onChange={this.handleSelectOnChange}
                      dropdownClassName="account-list-item-dropdown"
                      value={newDataChannels.length && newDataChannels.find(v => v === channel)}
                    >
                      {newDataChannels.length &&
                        newDataChannels.map((item: any, idx: number) => {
                          return (
                            <Option key={idx} value={item}>
                              <span className="account-list-item-dropdown-option">
                                <EKBIcon className="mr-8 stand-16-icon" name={dynamicChannelMap?.[item]?.icon} />
                                {i18n.get(dynamicChannelMap[item].name)}
                              </span>
                            </Option>
                          )
                        })}
                    </Select>
                    {FOREIGN_CURRENCY_PAY && !isCNY && (
                      <EKBIcon
                        name="#EDico-help"
                        placement="top"
                        className={'ml-6 mb-8 fs-16'}
                        tooltipTitle={i18n.get('当前仅展示支持外币支付的支付方式')}
                      />
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        {this.renderBackground()}
      </>
    )
  }

  renderBackground = () => {
    const { formChannel, data } = this.props
    const payChannel = formChannel === formChannelEnum.pay
    const paymentcChannel = formChannel === formChannelEnum.paymentc
    let iconName = payeeIconMap[data.sort]
    if (payChannel || paymentcChannel) {
      iconName = accountIconMap[data.sort]
    }
    return (
      <div className="pos-a bg-img">
        <EKBIcon className="" name={iconName} />
      </div>
    )
  }

  render() {
    const {
      formChannel,
      onClick,
      className,
      data,
      isWalletManager = false,
      isSelf,
      payFromChannel,
      inSelectPayMethodModal,
      isSelected,
      payeeConfig
    } = this.props
    // 当前类型是公共账户并且设置了当前账户底色
    const isPublicConfig = data.type === 'PUBLIC' && payeeConfig?.publicAccountConfig?.publicBackgroundCls
    let bgClass = isPublicConfig ? payeeConfig?.publicAccountConfig?.publicBackgroundCls : colorMap[data.sort]
    const isWalletManagerAndOpened = !isWalletManager && data.sort === 'WALLET' && data.isOpened
    const isPersonal = payFromChannelMap.personal === payFromChannel && !isSelf
    const payeeChannel = formChannel === formChannelEnum.payee
    const isSlefPersonal = payFromChannelMap.personal === payFromChannel && isSelf
    const fnOnClick = inSelectPayMethodModal
      ? onClick
      : isSlefPersonal || payFromChannelMap.personal !== payFromChannel
      ? // 企业钱包，数科钱包，数科虚拟卡不可点击
        data.sort === 'WALLET' || data.sort === 'CORPWALLET' || data.sort === 'VIRTUALCARD'
        ? null
        : onClick
      : null

    bgClass = !data.active ? 'gray-bg' : bgClass

    return (
      <div className={classNames('account-list-item-wrap pos-r cur-p', bgClass, className, {
          'cur-n': isWalletManagerAndOpened || isPersonal,
          'payee-list-item-wrap': payeeChannel,
          'account-list-item-not-allowed': data.sort === 'WALLET',
          'selected-item': isSelected
        })}
        data-testid="pay-accountListItem-item"
        onClick={fnOnClick}
      >
        {payeeChannel ? this.renderPayee() : this.renderOther()}
        <div className="account-selected-mask">
          <EKBIcon name="#EDico-check-circle" className="account-selected-icon" />
        </div>
        {/*{formChannel === formChannelEnum.payee ? this.renderPayee() : this.renderOther()}*/}
      </div>
    )
  }
}

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1   ')
}
