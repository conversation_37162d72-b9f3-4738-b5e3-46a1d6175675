import { Alert, Button, Input, Modal } from '@hose/eui'
import { OutlinedEditCopy } from '@hose/eui-icons'
import React, { useState } from 'react'
import style from './IntelligentFillingModal.module.less'

const { TextArea } = Input
const IntelligentFillingModal: React.FC = ({ handleIntelligentFillingValue, disabled }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [value, setValue] = useState('')

  const showModal = () => {
    setIsModalOpen(true)
  }

  const handleOk = () => {
    setIsModalOpen(false)
    setValue('')
    const _value = value?.trim()
    handleIntelligentFillingValue(_value)
  }

  const handleCancel = () => {
    setValue('')
    setIsModalOpen(false)
  }

  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const value = e?.target?.value
    setValue(value)
  }

  return (
    <>
      <Button disabled={disabled} category="secondary" theme="highlight" style={{ marginTop:14}} icon={<OutlinedEditCopy />} onClick={showModal} data-testid="pay-intelligentFillingModal-open-btn">
        {i18n.get('智能填写')}
      </Button>
      <Modal
        className={style['intelligentFillingModal']}
        open={isModalOpen}
        width={520}
        title={i18n.get('智能填写')}
        onCancel={handleCancel}
        data-testid="pay-intelligentFillingModal-modal"
        footer={[
          <Button category="secondary" onClick={handleCancel} data-testid="pay-intelligentFillingModal-cancel-btn">
              {i18n.get('取消')}
            </Button>,
          <Button category="primary" disabled={!value} onClick={handleOk} data-testid="pay-intelligentFillingModal-ok-btn">
              {i18n.get('识别')}
            </Button>
        ]}
      >
        <Alert
          message={
            <div className={style['dialog-wrap']}>
              <span>{i18n.get('户名：合小思')}</span>
              <span>{i18n.get('卡号：6214888888888888')}</span>
              <span>{i18n.get('开户行：招商银行双榆树支行')}</span>
            </div>
          }
          type="info"
        />
        <TextArea
          className="textArea"
          style={{ margin: '12px 0 16px', height: 146, resize: 'none' }}
          value={value}
          placeholder={i18n.get('在银行APP账户管理中查询卡片信息，复制粘贴，自动识别开户名称/银行卡号/银行网点')}
          showCount
          maxLength={100}
          onChange={onChange}
        />
      </Modal>
    </>
  )
}

export default IntelligentFillingModal
