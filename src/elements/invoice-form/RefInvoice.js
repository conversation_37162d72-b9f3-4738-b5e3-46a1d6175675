/**
 *  Created by gym on 2018/7/13 上午11:32.
 */
import React, { PureComponent } from 'react'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import UploadButton from '../../elements/ekbc-business/upload-button'
import { EnhanceConnect } from '@ekuaibao/store'
import { keyMap } from './utils/config'
import { fnInvoicepathTrack } from './utils/invoiceTrack'
import DEFAULT_IMAGE from '../../images/platform-default-icon.png'
import { Button, Tag, Dropdown, Menu } from '@hose/eui'
import { OutlinedDirectionUpload, OutlinedGeneralFolder, OutlinedOtherOverseasBills, FilledGeneralCamera, OutlinedDataImage, FilledEditEdit, TwoToneDataPdf, FilledLogoAlipay } from '@hose/eui-icons'

import './ReInvoice.less'

const menuMap = {
  ocr: {
    key: 'OCR',
    name: i18n.get('智能识票'),
    color: '#FA962A',
    smallIcon: '#EDico-photo-ai',
    icon: <FilledGeneralCamera fontSize={16} color="#FF7D00" />,
    action: 'onImportOCRClick'
  },
  upload: {
    key: 'pdfInvoice',
    name: i18n.get('电子发票文件'),
    smallIcon: '#EDico-PDF',
    color: '#F78885',
    icon: <TwoToneDataPdf fontSize={16} color="#F53F3F" />,
    action: 'onImportInvoiceClick'
  },
  photo: {
    key: 'photoiInvoice',
    name: i18n.get('发票照片'),
    smallIcon: '#EDico-paperclip',
    color: '#52C41A',
    icon: <OutlinedDataImage fontSize={16} color="#F53F3F" />,
    action: 'onImportInvoiceAttachmentClick'
  },
  query: {
    key: 'inputInvoice',
    name: i18n.get('手录发票'),
    smallIcon: '#EDico-edit',
    color: 'var(--brand-base)',
    icon: <FilledEditEdit fontSize={16} color="#3491FA" />,
    action: 'onImportInputInvoiceClick'
  },
  aliPay: {
    key: 'aliPayInvoice',
    name: i18n.get('支付宝发票'),
    smallIcon: '#EDico-payzfb1',
    icon: <FilledLogoAlipay fontSize={16} color="#3491FA" />,
    color: '#0b5fff',
    action: 'onImportAliPayInvoiceClick'
  },
  medical: {
    key: 'medical',
    name: i18n.get('医疗发票'),
    color: '#00C874',
    smallIcon: '#EDico-yiliaofapiao',
    action: 'onImportOCRMedicalClick'
  },
  aifapiao: {
    key: 'aifapiao',
    name: i18n.get('爱发票'),
    smallIcon: '#EDico-aifapiao',
    color: '#0b5fff',
    action: 'onImportAifaPiaoInvoiceClick'
  },
  formPoolSelect: {
    key: 'formPoolSelect',
    name: i18n.get('票池选择'),
    icon: <OutlinedGeneralFolder fontSize={16} color="#665ab5" />,
    action: 'onSelectInvoiceFromPool'
  },
  overseasInvoice: {
    key: 'overseasInvoice',
    name: i18n.get('海外票据识别'),
    icon: <OutlinedOtherOverseasBills fontSize={16} color="#8445d0" />,
    action: 'onImportOverseasInvoiceClick'
  }
}

const didiPersonal = {
  key: 'didiPersonal',
  name: i18n.get('滴滴个人版'),
  smallIcon: '#EDico-didi1',
  color: '#FA962A',
  action: 'onMenuClick',
  subMenus: [
    { key: 'invoicehistory', action: 'onMenuClick', name: i18n.get('导入发票') },
    { key: 'invoiceeplcar', action: 'onMenuClick', name: i18n.get('去开票') }
  ]
}

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data.staff,
  AliPayCardPower: state['@common'].powers.AliPayCard,
  RECEIPT_DOCUMENT: state['@common'].powers.RECEIPT_DOCUMENT,
  OCRMetering: state['@common'].powers.OCRMetering,
  OverseasInvoice:state['@common'].powers.OverseasInvoice,
}))
export default class RefInvoice extends PureComponent {
  constructor(props) {
    super(props)
    const { value } = props
    const fileList = (value && value.attachments) || []
    this.state = {
      fileList
    }
  }
  componentWillMount() {
    let { bus } = this.props
    bus && bus.on('invoice-select-delete', this.handleInvoiceDelete)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus && bus.un('invoice-select-delete', this.handleInvoiceDelete)
  }

  handleInvoiceDelete = () => {
    let { handleInvoiceDelete } = this.props
    handleInvoiceDelete && handleInvoiceDelete()
  }

  handelImportMenu = ({ item, key }) => {
    const { userInfo, importWay } = this.props
    const { bus } = this.props
    bus && bus.emit('change:feedetails:linkDetailEntities:data')
    const imports = keyMap[key]
    // imports没有值时，不需要触发埋点
    if (imports) {
      // 神策埋点
      fnInvoicepathTrack(userInfo, imports, importWay)
    }
    const actionKey = key.includes && key.includes('onImportReceiptClick') ? 'onImportReceiptClick' : key
    const fn = this.props[actionKey]
    if (fn) {
      return fn(item.props.data || {})
    }
    this.props.onMenuClick && this.props.onMenuClick(key)
  }

  handleStart = () => {
    this.setState({ isUpload: true })
  }

  handleFinish = fileList => {
    let { bus } = this.props
    bus && bus.emit('invoice-Attachment-Upload', fileList)
    this.setState({ isUpload: false })
  }

  Refrender = ({img, name, color, smallImg, icon, key}) => {
    const showIcon = () => {
      if (icon) {
        return null; // eui Menu已经支持icon 展示，这里不做自定义展示了
      }
      return smallImg ? (
        <img src={smallImg} style={{ width: 12 }} />
      ) : (
        <svg className="icon mr-12" aria-hidden="true">
          <use xlinkHref={img} style={{ fill: color }} />
        </svg>
      );
    };
    return (
      <div className="horizontal">
        {showIcon()}
        <div>
          {name}
        </div>
      </div>
    )
  }

  RefUpload = (name) => {
    let { fileList, isUpload } = this.state
    let accept = '.png, .jpeg, .jpg'
    let files = fnFormatAttachment(fileList)
    const child = (
      <div className="horizontal">
        <div>{name}</div>
      </div>
    )
    return (
      <UploadButton
        inputStyle={{ width: '90px' }}
        fileList={files}
        accept={accept}
        multiple={true}
        isLoading={isUpload}
        onStart={this.handleStart}
        children={child}
        onFinish={this.handleFinish}
      />
    )
  }

  menu = (menuList, type) => {
    if (!menuList.length) return
    return (
      <Menu key={type} onClick={this.handelImportMenu}>
        {menuList.map((v, idx) => {
          const menuKey = v.action === 'onImportReceiptClick' ? `onImportReceiptClick${idx}` : v.action || v.key
          if (v.subMenus) {
            return (
              <Menu.SubMenu
                key={menuKey}
                className={'invoice_sub_menu'}
                title={this.Refrender({ img: v.smallIcon, name: v.name, color: v.color, icon: v.icon, key: v.key })}
              >
                {v.subMenus.map(subMenu => {
                  return <Menu.Item data-testid={`invoice-menuSubItem-${subMenu.key}`} key={subMenu.key}>{subMenu.name}</Menu.Item>
                })}
              </Menu.SubMenu>
            )
          }
          return (
            <Menu.Item {...v} key={menuKey} className={'invoice_menu'} data-testid={`invoice-menuItem-${v.key}`}>
              {v.key === 'photoiInvoice'
                ? this.RefUpload(v.name)
                : this.Refrender({
                    img: v.smallIcon,
                    name: v.name,
                    color: v.color,
                    smallImg: v.smallImg,
                    icon: v.icon,
                    key: v.key
                  })}
            </Menu.Item>
          )
        })}
      </Menu>
    )
  }

  getMenuList = () => {
    const {
      type,
      OCRPower,
      OCRMetering,
      importMode,
      AliPayCardPower,
      RECEIPT_DOCUMENT,
      feeTypeImportRuleList,
      OverseasInvoice
    } = this.props
    let menuList = []
    if (RECEIPT_DOCUMENT && type === 'receipt') {
      if (feeTypeImportRuleList.length) {
        feeTypeImportRuleList.forEach((ruleItem, index) => {
          menuList.push({
            key: ruleItem.linkId + index,
            name: ruleItem.title,
            smallImg: DEFAULT_IMAGE,
            color: '#52C41A',
            action: 'onImportReceiptClick',
            data: {
              ...ruleItem
            }
          })
        })
      } else {
        menuList.push({
          key: 'no-import',
          name: i18n.get('暂无导入规则，请添加！'),
          color: '#52C41A',
          action: 'noImportantHandle'
        })
      }
    } else if (!importMode) {
      menuList = (OCRPower || OCRMetering) ? Object.values(menuMap) : Object.values(menuMap).filter(o => o.key !== 'OCR')
    } else {
      importMode.ocr && (OCRPower || OCRMetering) && menuList.push(menuMap.ocr)
      importMode.upload && menuList.push(menuMap.upload)
      importMode.photo && menuList.push(menuMap.photo)
      importMode.query && menuList.push(menuMap.query)
      importMode.alipaycard && AliPayCardPower && menuList.push(menuMap.aliPay)
      importMode.aifapiao && menuList.push(menuMap.aifapiao)
      importMode.formPoolSelect && menuList.push(menuMap.formPoolSelect)
      importMode.overseasInvoice && OverseasInvoice && menuList.push(menuMap.overseasInvoice)

      // importMode.medical && OCRMedical && menuList.push(menuMap.medical)
      // importMode.didiPersonal && menuList.push(didiPersonal)
    }
    return menuList
  }

  render() {
    const { text, renderChild, type, disabled } = this.props
    const menuList = this.getMenuList()
    if (!menuList.length) {
      return null
    }
    return (
      <Dropdown disabled={disabled} overlayClassName={'dropdown'} overlay={this.menu(menuList, type)} placement="bottomLeft">
        {text ? (
          <Button category="secondary" theme="default" size='small'>
            {i18n.get(text)}
          </Button>
        ) : renderChild ? (
          renderChild()
        ) : (
          <Button category="secondary" theme="default" size='small'>
            <OutlinedDirectionUpload fontSize={16} className="mr-4" />
            {i18n.get('添加发票')}
          </Button>
        )}
      </Dropdown>
    )
  }
}
