import React, { PureComponent } from 'react'
import styles from './tag-selector.module.less'
import { Tag, Tooltip } from '@hose/eui'
import remove from 'lodash/remove'
import { getItemLabel, getTagLabel } from './util'
import classnames from 'classnames'
export default class TagSelector extends PureComponent {
  constructor(props) {
    super(props)
    this.handleAreaClick = this.handleAreaClick.bind(this)
    this.handleTagClick = this.handleTagClick.bind(this)
  }

  handleClose(item, e) {
    const tags = this.props.value.slice().filter(item => item.id !== 'departmentsIncludeChildren')
    remove(tags, o => (o?.id ? o?.id === item?.id : o?.name === item?.name))
    this.props.onChange && this.props.onChange(tags, item)
  }

  handleTagClick(e) {
    e.stopPropagation()
    e.preventDefault()
  }

  handleAreaClick() {
    const { editable = true } = this.props
    if (editable) {
      this.props.onClick && this.props.onClick()
    }
  }

  render() {
    let { editable = true, closable = true, listRender, tagRender, className, id } = this.props
    let valueList = this.props.value || []
    const cls = classnames(styles['tag-selector'], className, { [styles['tag-selector-disable']]: !editable })
    return (
      <div className={cls} onClick={this.handleAreaClick} data-testid={ id ? `tag-selector-${id}` : undefined} data-cy="TagSelector@Input">
        {valueList.length === 0 && this.props.placeholder && (
          <span className="placeholder">{this.props.placeholder}</span>
        )}
        {!!listRender
          ? listRender(valueList)
          : valueList
              .filter(i => !!i)
              .map(item => {
                if (tagRender) {
                  return tagRender(item)
                }
                return (
                  <Tag
                    key={item.id || item.name}
                    closable={editable && closable}
                    onClose={() => this.handleClose(item)}
                    style={{
                      marginRight: 3,
                      marginBottom: 3,
                      height: '24px',
                      borderRadius: '20px',
                      padding: '0px 8px 0px 4px',
                      ...this.props.tagStyle
                    }}
                    {...item.tagOptions}
                  >
                    {this.renderItem(item)}
                  </Tag>
                )
              })}
      </div>
    )
  }

  renderItem = item => {
    const { renderItem, showAvatar, tagStrLimit = 15 } = this.props
    if (!!renderItem) {
      return renderItem(item)
    }

    const formatItem = {
      ...item,
      label: getItemLabel(item)
    }
    const { toolTipStr, tag } = labelLimit(formatItem, tagStrLimit)
    const tagContent = show => (
      <span style={tag.id !== 'departmentsIncludeChildren' ? { color: 'var(--eui-text-title)' } : {}}>
        {getTagLabel(show, showAvatar)}
      </span>
    )
    return !!toolTipStr ? <Tooltip title={toolTipStr}>{tagContent(tag)}</Tooltip> : tagContent(tag)
  }
}

const labelLimit = (tag, tagStrLimit) => {
  if (tagStrLimit && tag.label?.length > tagStrLimit && !tag.hasOwnProperty('avatar')) {
    const str = `${tag.label.substring(0, tagStrLimit)}...`
    return { toolTipStr: tag.label, tag: { ...tag, label: str } }
  }
  return { toolTipStr: '', tag }
}
