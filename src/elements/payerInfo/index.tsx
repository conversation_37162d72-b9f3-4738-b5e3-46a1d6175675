import React from 'react'
import { Checkbox } from 'antd'
import { InvoicePayer } from '@ekuaibao/ekuaibao_types'
import styles from './PayerInfo.module.less'
import EkbIcon from '../../elements/ekbIcon'

interface PayerInfoProps {
  data?: InvoicePayer[]
  selectType?: 'multiple' | 'none'
  checkedKeys?: string[]
  checkedChange?: (keys: string[]) => void
  handleDelete: (item: InvoicePayer) => void
}

export const PayerInfo: React.FC<PayerInfoProps> = ({
  data,
  handleDelete,
  selectType = 'none',
  checkedKeys = [],
  checkedChange
}) => {
  const deleteAble = selectType === 'none'
  const selectedAble = selectType === 'multiple'

  const handleCheckedChange = (checked, item: InvoicePayer) => {
    console.log('checkedChange', checked, item)
    let result = [...checkedKeys]
    if (checked) {
      result.push(item.id)
    } else {
      result = result.filter(ele => ele !== item.id)
    }
    checkedChange?.(result)
    console.log(result)
  }

  const renderItem = (item: InvoicePayer) => {
    const checked = checkedKeys?.includes(item.id)
    return (
      <div className={styles['payer-info-item']}>
        {selectedAble && (
          <Checkbox
            className={styles['item-check-box']}
            checked={checked}
            onChange={e => handleCheckedChange(e.target.checked, item)}
            data-testid="pay-payerInfo-checkbox"
          />
        )}
        <div className="item-content">
          {deleteAble && (
            <div className="delete-icon" onClick={() => handleDelete(item)} data-testid="pay-payerInfo-delete-btn">
              <EkbIcon name="#EDico-delete" />
            </div>
          )}
          <div className="title">{item?.name}</div>
          <div className="sub-title">
            <div>{item?.payerNo}</div>
            <div>{item?.visibility?.fullVisible ? i18n.get('全员可见') : i18n.get('部分可见')}</div>
          </div>
        </div>
      </div>
    )
  }

  return <div>{data?.map(renderItem)}</div>
}

export default PayerInfo
