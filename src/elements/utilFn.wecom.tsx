import { isString } from 'lodash'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { getStaffName } from './utilFn'
import { getStaffShowExternal } from './utilFn'
import { NameCell } from './name-cell'

/**
 * 根据配置获取员工完整的名称显示（姓名 + 配置的第二字段）
 */
export const getStaffShowByConfig = value => {
  if (!value) {
    return value
  }
  let staffObj = value
  if (isString(value)) {
    const staffMap = api?.getState()['@common']?.allStaffActiveStaffMap || {}
    staffObj = staffMap[value]
  }

  if (window.isInWeComISV) {
    const name = staffObj.id !== 'departmentsIncludeChildren' ? <NameCell type="user" id={staffObj.id} name={staffObj.name} /> : getStaffName(staffObj)
    if (!name) return ''
    const str = getStaffShowExternal(staffObj)
    if (str) {
      return <>{name}{str}</>
    }
    return name
  }

  const name = getStaffName(staffObj)
  if (!name) return ''
  const str = getStaffShowExternal(staffObj)
  return str ? `${name}${str}` : name
}
