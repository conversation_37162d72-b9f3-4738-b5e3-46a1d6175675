/**
 * Created by LinK on 2017/8/29.
 *
 * treeNodeData: 节点数据
 * onlyLeafCanBeSelected: true为只有子节点可以被选
 * refKey: 本组件的ref值，用来固定下拉菜单在页面滚动时的位置
 */
import styles from './TreeSelectSingle.module.less'
import React, { PureComponent, memo } from 'react'
import { findDOMNode } from 'react-dom'
import { Select as AntdSelect, Spin as AntdSpin, TreeSelect as AntdTreeSelect } from 'antd'
import { Select as EUISelect, Spin as EUISpin, TreeSelect as EUITreeSelect, Tooltip } from '@hose/eui'
import TreeSelectRC from './TreeSelectRC'
import { cloneDeep, isEqual, clone, debounce } from 'lodash'
import EkbHighLighter from '../EkbHighLighter'
import classNames from 'classnames'
import { Fetch } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import TreeSelectLoadingNode from './TreeSelectLoadingNode'
import { visit } from '@ekuaibao/helpers'
import { getDisplayName } from '../utilFn'
const customStyle = {
  cursor: 'not-allowed',
  color: '#9c9c9c'
}

let Select
let Spin
let TreeSelect
let TreeNode

class TreeSelectSingle extends PureComponent {
  treeMap = {}
  treeDefaultExpandedKeys = [] // 记录需要默认展开的节点

  state = {
    searchText: undefined,
    valueList: [],
    treeNodes: [],
    accurateTreeNodes: null // 精确搜索时展示的节点
    // curStep: 1   // 懒加载当前的步数
  }
  // timer = null  // 懒加载循环器
  // size = 20  // 懒加载每次条数

  fnGetName = ({ label, enLabel, name, enName, code, hideCode, isShowCode }) => {
    let nameStr = getDisplayName({ label, enLabel, name, enName })
    //判断是否设置了“隐藏自定义档案编码”
    if (hideCode) {
      isShowCode = false
    } else {
      isShowCode = true
    }
    if (isShowCode && code && !label) {
      nameStr = nameStr + i18n.get('(') + code + i18n.get(')')
    }
    return nameStr
  }

  /**
   * @param inAccurateSearch boolean 判断是否仅返回精确搜索的结果
   */
  renderTreeNode(treeNodeData, onlyLeafCanBeSelected = false, isShowCode = true, hideCode, inAccurateSearch) {
    this.treeDefaultExpandedKeys = []
    if (!treeNodeData?.length > 0) return []
    // 兼容新的通讯录接口
    let data = this.props?.isNewContactList ? clone(treeNodeData) : cloneDeep(treeNodeData)
    // 兼容部门根节点不唯一的数据
    data = data.filter(el => !el.hide)
    if (!data.length > 0) return []

    const { searchText } = this.state
    const {
      multiple,
      docId,
      type = [],
      selectAll = true,
      isShowFullPath,
      onChange,
      id: selectedIds,
      entity
    } = this.props.data
    const { isNotExtend, showActive, virtualSelectable = false } = this.props
    let rootPath = ''
    if (Array.isArray(data)) {
      rootPath = getDisplayName(data[0]) || ''

      // 默认展开的节点
      this.props.useEUI &&
        data.forEach(el => {
          this.treeDefaultExpandedKeys.push(el.id)
        })
    }

    // 已选择的节点
    const selectedValue = []
    // 精确搜索结果
    const accurateSearchValue = []
    //  模糊搜索结果
    const fuzzySearchValue = []

    const loop = (arr, groupId) => {
      const result = []
      if (arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          const child = arr[i]
          if (child.hide) continue
          let {
            id,
            name,
            enName,
            code,
            active,
            selectable,
            children = [],
            label,
            enLabel,
            permissions,
            fullPath,
            parentId,
            isGroupItem,
            path, // 后端为分组中的数据提供的路径
            virtual
          } = child

          // 精确搜索时，不展示分组及分组中的节点
          if (isGroupItem && inAccurateSearch) continue

          // 为了不让分组中的id和原数据中的id重复，加了后缀
          if (groupId) id = id + groupId

          let canSelect = true
          if (type?.indexOf('customDimention') === -1) {
            canSelect = selectable !== undefined ? selectable : active
            if (onlyLeafCanBeSelected && children.length) {
              canSelect = false
            }
          }
          //permission 空数组,(只在自定义档案中生效)代表刚下发的默认没有任何权限的, 子租户建的是有一个 all 的
          const isExtend = !!permissions?.find(per => per.name === 'EXTEND' || per.name === 'ALL')?.auth || false
          if (isNotExtend && isExtend != undefined && !isExtend) {
            canSelect = false
          }
          // 分组名称节点不可选中
          if (isGroupItem) canSelect = false

          // 虚拟部门不可选中
          if (virtual && !virtualSelectable) canSelect = false

          let activeName = ''
          if (showActive && !active) {
            activeName = '(已停用)'
          }

          let virtualName = ''
          if (window.__PLANTFORM__ === 'MC' && virtual) {
            virtualName = i18n.get('(虚拟部门)')
          }

          let fullNameStr = fullPath
          //判断是否设置了“隐藏自定义档案编码”
          if (hideCode) {
            isShowCode = false
          } else {
            isShowCode = true
          }
          if (isShowCode && code && !label) {
            fullNameStr = fullNameStr + i18n.get('(') + code + i18n.get(')')
          }
          fullNameStr = fullNameStr + virtualName + activeName
          const nameStr =
            this.fnGetName({ label, enLabel, name, enName, code, hideCode, isShowCode }) + virtualName + activeName
          const departEnPathMap = api.getState('@common.department.noRootPathMap') || {}
          const departPathMap = api.getState('@common.department.noRootPathMap') || {}
          const departFullPathMap = i18n.currentLocale === 'zh-CN' ? departPathMap : departEnPathMap
          let txt = departFullPathMap[child.id] || ''
          if (!txt) txt = getDisplayName(child)
          let fullDepartmentPath = `${rootPath}/${txt}`

          // 精确搜索取值
          let searchResultType
          if (inAccurateSearch && searchText && selectedIds?.includes(id)) {
            searchResultType = 'selected'
          } else if (inAccurateSearch && searchText && (searchText === name || searchText === code)) {
            searchResultType = 'accurate'
          } else if (inAccurateSearch && searchText && (nameStr?.includes(searchText) || code?.includes(searchText))) {
            searchResultType = 'fuzzy'
          }

          const isSelectAll = () => {
            let res = this.loop(child)
              .toString()
              .split(',')
            return res.some(v => !this.props?.data?.id?.includes(v))
          }
          canSelect && (this.treeMap[id] = child)

          const showSelectAll = !this.props?.useEUI && selectAll && multiple && children?.length > 0 && !isGroupItem

          const isVirtualDisabled = virtual && !virtualSelectable
          // 精确搜索相关逻辑
          if (inAccurateSearch) {
            const TreeNodeDom = (
              <TreeNode
                key={id}
                data-testid={`treeNode-searchItem-${id}`}
                fullPath={fullDepartmentPath}
                name={isShowFullPath ? fullNameStr : nameStr}
                value={id}
                disabled={!active || isVirtualDisabled}
                selectable={canSelect}
                data={child}
                title={
                  <div
                    style={!canSelect ? customStyle : {}}
                    className={classNames(
                      { commonGroupNode: isGroupItem },
                      { treeNodeDisable: !canSelect, 'dis-f jc-sb': !canSelect && isNotExtend }
                    )}
                  >
                    <EkbHighLighter
                      highlightClassName="highlight"
                      searchWords={[searchText]}
                      textToHighlight={nameStr}
                    />
                  </div>
                }
              />
            )
            if (children?.length) {
              loop(children)
            }
            if (searchResultType === 'selected') {
              selectedValue.push(TreeNodeDom)
            } else if (searchResultType === 'accurate') {
              accurateSearchValue.push(TreeNodeDom)
            } else if (searchResultType === 'fuzzy') {
              fuzzySearchValue.push(TreeNodeDom)
            }
            continue
          }

          // isGroupItem：如果当前节点是分组组名节点，将组名传入递归方法中
          const groupItemId = isGroupItem || groupId ? id : ''

          result.push(
            <TreeNode
              key={id}
              data-testid={`treeNode-item-${id}`}
              fullPath={fullDepartmentPath}
              name={isShowFullPath ? fullNameStr : nameStr}
              value={id}
              disabled={!active || isVirtualDisabled}
              selectable={canSelect}
              data={child}
              title={
                <div
                  style={!canSelect ? customStyle : {}}
                  className={classNames(
                    { commonGroupNode: isGroupItem },
                    { treeNodeDisable: !canSelect, 'dis-f jc-sb': !canSelect && isNotExtend }
                  )}
                >
                  <span>{nameStr}</span>

                  {showSelectAll ? (
                    <a
                      href={() => {
                        return false
                      }}
                      className="a-select-button"
                      onClick={e => this.selectOrCancelAll(e, child, isSelectAll())}
                    >
                      {isSelectAll() ? '全选' : '取消'}
                    </a>
                  ) : null}
                  {!canSelect && isNotExtend && <span>{i18n.get('不可扩展')}</span>}
                </div>
              }
            >
              {children?.length && loop(children, groupItemId)}
            </TreeNode>
          )
        }
      }
      return result
    }
    const nodes = loop(data)

    if (inAccurateSearch) {
      return selectedValue.concat(accurateSearchValue, fuzzySearchValue)
    }
    return nodes
  }

  componentDidMount() {
    const { treeNodeData, onlyLeafCanBeSelected, isShowCode, hideCode } = this.props.data
    if (this.fnNeedRenderTreeNode()) {
      const treeNodes = this.renderTreeNode(treeNodeData, onlyLeafCanBeSelected, isShowCode, hideCode)
      this.setState({ treeNodes })
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const prevTreeNodeData = prevProps?.data?.treeNodeData || []
    const treeNodeData = this.props?.data?.treeNodeData || []
    if (!isEqual(prevTreeNodeData, treeNodeData)) {
      const { onlyLeafCanBeSelected, isShowCode, hideCode } = this.props.data
      this.treeMap = {}
      if (this.fnNeedRenderTreeNode()) {
        const treeNodes = this.renderTreeNode(treeNodeData, onlyLeafCanBeSelected, isShowCode, hideCode)
        this.setState({ treeNodes })
      }
      if (['7io6l0mVjo0g00', 'jio3KHfB9C0v0_', 'uKG3qw00003azM'].includes(Fetch.ekbCorpId)) {
        const { onCheckValueIsExist } = this.props
        if (this.fnNeedRenderTreeNode() && !this.treeMap[this.props?.data?.id]) {
          onCheckValueIsExist && onCheckValueIsExist(this.props?.data, false)
        }
      }
    }
  }

  componentWillReceiveProps(nextPorps) {
    if (!['7io6l0mVjo0g00', 'jio3KHfB9C0v0_', 'uKG3qw00003azM'].includes(Fetch.ekbCorpId)) {
      //这个地方重新赋值的话this.treeMap是空数组，会清空数据，换一个地方修改，不确定影响范围，先对这个企业开放
      if (nextPorps?.data?.id && this.props?.data?.id !== nextPorps?.data?.id) {
        setTimeout(() => {
          const { onCheckValueIsExist } = this.props
          if (this.fnNeedRenderTreeNode() && !this.treeMap[nextPorps?.data?.id]) {
            onCheckValueIsExist && onCheckValueIsExist(nextPorps?.data, false)
          }
        }, 0)
      }
    }
  }

  fnNeedRenderTreeNode = () => {
    //天阳(bnB3nHI6Fb3qzw)的不进行渲染
    if (Fetch.ekbCorpId !== 'bnB3nHI6Fb3qzw') {
      return true
    }
    const { disabled = false, displayValue } = this.props.data
    if (disabled && displayValue?.id?.length && displayValue?.name?.length) {
      return false
    }
    return true
  }

  selectOrCancelAll = (e, child, flag) => {
    // flag: true  全选 false 取消
    // 阻止合成事件的冒泡
    e.stopPropagation()
    // 阻止与原生事件的冒泡
    e.nativeEvent.stopImmediatePropagation()
    let { id, field } = this.props.data
    let res = this.loop(child)
      .toString()
      .split(',')
    if (flag) {
      if (field.selectRange === 'leaf') {
        const dataMap = {}
        visit(child, node => {
          dataMap[node.id] = node
        })
        res = res.filter(it => !dataMap[it]?.children?.length)
      }
      this.onChange([...new Set([...id, ...res])])
      return
    }
    res.forEach(v => {
      const index = id?.indexOf(v)
      id?.splice(index, 1)
    })
    this.onChange(id)
  }

  loop = arr => {
    let child = []
    if (arr.children && arr.children.length > 0) {
      child = arr.children.map(child => {
        return this.loop(child)
      })
    }
    if (arr.active === false) {
      return child
    }
    return [arr.id, ...child]
  }

  handleSearch = value => {
    const { useAccurateSearch = false } = this.props.data
    if (useAccurateSearch) {
      // 精确搜索逻辑
      const params = { searchText: value }
      if (value) params.loading = true
      this.setState(params, () => {
        this.onSearch(value)
      })
    } else {
      this.setState({ searchText: value })
    }

    const { field } = this.props
    api?.logger?.info('树选择组件搜索', {
      field,
      type: field?.type,
      searchText: value
    })
  }

  onSearch = debounce(value => {
    if (!value) {
      // 搜索框没有值时，将精确搜索结果置为null，展示treeNodes中的数据
      return this.setState({ accurateTreeNodes: null, loading: false })
    }
    const treeNodeData = this.props?.data?.treeNodeData || []
    const { onlyLeafCanBeSelected, isShowCode, hideCode } = this.props.data
    const accurateTree = this.renderTreeNode(treeNodeData, onlyLeafCanBeSelected, isShowCode, hideCode, true)
    this.setState({ accurateTreeNodes: accurateTree, loading: false })
  }, 300)

  getObjById = (selectedId, props) => {
    let { treeNodeData = [], id } = props.data
    let obj = {}
    let fn = data => {
      data.forEach(item => {
        if (item.id === selectedId) {
          obj = item
          return
        }

        fn(item.children || [])
      })
    }

    fn(treeNodeData)
    return obj
  }

  // 此方法只处理清空值的时候
  onChange = (selectedValue, label, extra) => {
    let value = selectedValue
    const { useAccurateSearch = false, onChange = _ => {}, multiple, id, needFullData } = this.props.data
    if (useAccurateSearch) {
      const { triggerNode, triggerValue } = extra || {}
      const triggerNodeId = getV(triggerNode, 'props.data.id')
      // 为了不让分组中的id和原数据中的id重复，加了后缀
      if (triggerNodeId !== triggerValue) {
        if (multiple) {
          let changeValue
          if (id.includes(triggerNodeId)) {
            changeValue = id.filter(el => el !== triggerNodeId)
          } else {
            changeValue = id.concat(triggerNodeId)
          }
          onChange(changeValue)
          return
        }
      }
    }
    if (multiple) {
      let newV = value || []
      const ids = id || []
      if (extra && extra.selected === true) {
        if (needFullData) {
          const fullValue = this.treeMap[extra.triggerValue] || extra.triggerValue
          newV = ids.concat(fullValue)
        } else {
          newV = ids.concat(extra.triggerValue)
        }
      }
      onChange(newV)
      return
    }
    if (!value) {
      onChange(value)
    }
  }

  onSelect = (selectedValue, node, extra) => {
    let value = selectedValue
    const { useAccurateSearch = false, onChange = _ => {}, multiple, optional = false, labelInValue } = this.props.data
    // 如果选中的是分组中的节点，将选中的值换为该节点正确的值
    if (useAccurateSearch && node?.data?.path && node?.data?.id !== selectedValue) {
      value = node?.data?.id
    }

    value = labelInValue ? value.value : value
    if (!optional && !value) {
      return
    }
    let obj = node.props.data
    !multiple && onChange(obj)
  }

  renderLoading = () => {
    if (!this.state.loading) return null
    return <Spin className={styles['loading-wrap']} />
  }

  renderTreeSelect = () => {
    let {
      id, // 此处判空，将不显示placeholder
      refKey = '',
      optional,
      placeholder = '',
      notFoundContent,
      disabled = false,
      isChangePosition,
      onClick,
      labelInValue,
      multiple,
      displayValue = {},
      isShowCode,
      hideCode,
      useTreeSelectRC,
      field: dataField,
      dropdownMatchSelectWidth
    } = this.props.data

    const { dropdownStyle = { maxHeight: 280, overflow: 'auto' }, field, dependenceListOnLoading, useEUI } = this.props

    const { searchText, treeNodes, accurateTreeNodes } = this.state
    // 批量编辑，处于简单模式下，组件高度较小，此时需要将下拉框绑定在body 上，否则无法编辑
    if (field?.isSimple || dataField?.isSimple) {
      isChangePosition = true
    }

    let showAllPath = field && field.editStatePathRule === 'fullPath' ? true : false
    const treeNodeLabelProp = showAllPath ? 'fullPath' : 'name'

    // 当id 为’‘ 不显示placeholder，充值卡成undefined
    id = id || undefined
    if (!this.fnNeedRenderTreeNode()) {
      let { id, name, enName, label, enLabel, code } = displayValue
      const nameStr = this.fnGetName({ label, enLabel, name, enName, hideCode, code, isShowCode })
      return (
        <Select style={{ width: '100%' }} size={'large'} placeholder={placeholder} disabled={disabled} value={id}>
          <Select.Option value={id}>{nameStr}</Select.Option>
        </Select>
      )
    }
    const TreeSelectComponent = useTreeSelectRC ? TreeSelectRC : TreeSelect
    const children = dependenceListOnLoading ? (
      <TreeNode title={<TreeSelectLoadingNode />} disabled />
    ) : (
      accurateTreeNodes || treeNodes
    )
    if (!notFoundContent) {
      notFoundContent = useEUI ? undefined : i18n.get('没有匹配结果')
    }

    const notFoundContentValue = dependenceListOnLoading ? i18n.get('数据加载中，请稍候...') : notFoundContent

    const filedName = dataField ? dataField.name : field ? field.name : undefined
    return (
      <>
        {this.renderLoading()}
        <TreeSelectComponent
          data-testid={filedName ? `tree-selectSingle-${filedName}` : undefined}
          useEUI={useEUI}
          showSearch
          disabled={disabled}
          style={{ width: '100%' }}
          dropdownStyle={dropdownStyle}
          value={id}
          placeholder={placeholder}
          notFoundContent={notFoundContentValue}
          filterTreeNode={(inputValue, treeNode) => {
            const name = treeNode?.props?.data?.name ?? ''
            const enName = treeNode?.props?.data?.enName ?? ''
            const nameSpell = treeNode?.props?.data?.nameSpell ?? ''
            const code = treeNode?.props?.data?.code ?? ''
            const lowerValue = (inputValue ?? '').toLowerCase()
            return (
              code.toLowerCase().includes(lowerValue) ||
              name.toLowerCase().includes(lowerValue) ||
              nameSpell.toLowerCase().includes(lowerValue) ||
              enName.toLowerCase().includes(lowerValue)
            )
          }}
          multiple={multiple}
          treeNodeLabelProp={treeNodeLabelProp}
          allowClear={optional || true} // 是否展示清除按钮
          onSearch={this.handleSearch}
          showCheckedStrategy={'SHOW_ALL'}
          showArrow
          onSelect={this.onSelect}
          onChange={this.onChange}
          onClick={onClick}
          size={useEUI ? 'middle' : 'large'}
          searchValue={searchText}
          labelInValue={labelInValue}
          autoClearSearchValue={true}
          dropdownMatchSelectWidth={dropdownMatchSelectWidth || !isChangePosition}
          treeDefaultExpandedKeys={this.treeDefaultExpandedKeys}
          getPopupContainer={() => document.body}
          placement={'bottomLeft'}
        >
          {children}
        </TreeSelectComponent>
      </>
    )
  }

  render() {
    const { useEUI } = this.props
    Select = useEUI ? EUISelect : AntdSelect
    Spin = useEUI ? EUISpin : AntdSpin
    TreeSelect = useEUI ? EUITreeSelect : AntdTreeSelect
    TreeNode = TreeSelect.TreeNode 

    let { refKey = '' } = this.props.data
    return (
      <div ref={refKey} className={styles['treeSelectSingleWrapper']}>
        {/* {this.state.curStep} */}
        {this.renderTreeSelect()}
      </div>
    )
  }
}

function arePropsEqual(prevProps, nextProps) {
  const isUpdate =
    isEqual(prevProps?.data?.treeNodeData, nextProps?.data?.treeNodeData) &&
    isEqual(prevProps?.data?.id, nextProps?.data?.id) &&
    isEqual(prevProps?.data?.mode, nextProps?.data?.mode) &&
    isEqual(prevProps?.data?.placeholder, nextProps?.data?.placeholder) &&
    isEqual(prevProps?.data?.notFoundContent, nextProps?.data?.notFoundContent) &&
    isEqual(prevProps?.dependenceListOnLoading, nextProps?.dependenceListOnLoading)
  return isUpdate
}

export default memo(TreeSelectSingle, arePropsEqual)
