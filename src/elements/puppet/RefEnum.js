/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/8 下午8:36.
 */
import React, { PureComponent } from 'react'
import { Select as AntdSelect } from 'antd'
import { Select as EUISelect } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import { find, split } from 'lodash'
import { formatLang } from '../../lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'

export default class RefEnum extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { children: [], options: [] }
  }

  componentWillMount = () => {
    this.props.useEUI && this.onFocus()
    this.handleEnum(this.props)
  }

  componentWillReceiveProps(nextProps) {
    let { value } = nextProps
    if (!value) {
      this.setState({
        name: undefined
      })
    }
    if (this.props.value !== nextProps.value) {
      this.handleEnum(nextProps)
    }
  }

  handleEnum(props) {
    let { value, entity, field, useEUI } = props
    if (field?.customData) {
      this.fnBuildItems({ items: field.customData })
      this.setState({ data: { items: field.customData }, name: value?.code || value || undefined })
      return
    }
    if (value) {
      let enumCode = this._getEnumCodeFromField(entity)
      if (enumCode) {
        api.invokeService('@common:get:enumitems', enumCode).then(data => {
          let obj = value.code
            ? find(data.items, { code: value.code })
            : find(data.items, { code: value?.id || value }) || find(data.items, { id: value?.id || value }) //"SecondhandCar" code
          if (obj) {
            // !checked && onChange && onChange(obj) //枚举类型切换时自动和手动填写时会有异步赋值不准确的问题，所以在模版配置的时候不像外通知value了
            this.setState({
              name: !useEUI ? obj[formatLang()] : obj.code
            })
          }
        })
      } else {
        showMessage.error(i18n.get('未找到枚举的Code，请联系易快报'))
      }
    }
  }

  onChange = value => {
    let { onChange, external, updateCell } = this.props
    let { data } = this.state
    let nameData = undefined
    const emitResetFieldsExternals = api.invokeService('@bills:import:emitResetFieldsExternals')

    if (value && data.items) {
      nameData = data.items.filter(o => o.code === value)[0]
    }
    this.setState({
      name: value
    })
    emitResetFieldsExternals && emitResetFieldsExternals(external)
    onChange && onChange(nameData)
    updateCell && updateCell(nameData)
  }

  onFocus = () => {
    //TODO：判断是否有，如果有则不需要重新获取，不过要注意区分type
    let { entity, field } = this.props
    if (field?.customData) {
      this.fnBuildItems({ items: field.customData })
      return
    }
    let enumCode = this._getEnumCodeFromField(entity)
    if (enumCode) {
      api.invokeService('@common:get:enumitems', enumCode).then(data => {
        this.setState({ data })
        data && this.fnBuildItems(data)
      })
    } else {
      showMessage.error(i18n.get('未找到枚举的Code，请联系易快报'))
    }
  }

  _getEnumCodeFromField = entity => {
    let entities = split(entity, '.')
    return entities ? entities[2] : undefined
  }

  fnBuildItems(data) {
    const { useEUI } = this.props
    if (useEUI) {
      let options = []
      data.items.forEach(line => {
        let { code } = line
        options.push({ label: `${line[formatLang()]}（${code}）`, value: code, 'data-testid': `refEnum-item-${line.name}` })
      })
      this.setState({ options: options })
    } else {
      let children = []
      data.items.forEach(line => {
        let { code } = line
        children.push(<AntdSelect.Option key={code}>{line[formatLang()]}</AntdSelect.Option>)
      })
      this.setState({ children: children })
    }
  }

  render() {
    const { placeholder, disabled, optional, useEUI, field, noTriggerNode } = this.props
    const { name, options } = this.state
    const otherProps = {}
    if (!noTriggerNode) {
      otherProps.getPopupContainer = triggerNode => triggerNode.parentNode
    }
    return useEUI ? (
      <EUISelect
        style={{ width: '100%' }}
        onChange={this.onChange}
        onFocus={this.onFocus}
        disabled={disabled}
        placeholder={placeholder}
        value={name}
        data-testid={`field-refEnum-${field.name}`}
        options={options}
        allowClear
        showSearch
        optionFilterProp="label"
        {...otherProps}
      />
    ) : (
      <AntdSelect
        style={{ width: '100%' }}
        onChange={this.onChange}
        onFocus={this.onFocus}
        disabled={disabled}
        data-testid={`field-AntdRefEnum-${field.name}`}
        allowClear={optional}
        placeholder={placeholder}
        value={name}
        size="large"
      >
        {this.state.children}
      </AntdSelect>
    )
  }
}
