/**
 * Created by <PERSON><PERSON><PERSON> on 2017/11/17.
 */
import styles from './gpy-attachment-less/attachment.module.less'
import React, { PureComponent } from 'react'
import AttachmentList from '../../elements/puppet/attachmentList/AttachmentList'
import UploadButton from '../../elements/ekbc-business/upload-button'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { Button, Tooltip } from '@hose/eui'
import { OutlinedGeneralCamera } from '@hose/eui-icons'
import { app as api } from '@ekuaibao/whispered'
import MessageCenter from '@ekuaibao/messagecenter'
import { findIndex } from 'lodash'

export default class Attachment extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = new MessageCenter()
    this.state = {
      orgVisible: false,
      resultList: []
    }
  }

  componentDidMount = async () => {
    const hasOrgCharge =
      (await api.invokeService('@custom-specification:check:power:code', { powerCode: '110410' })) || {}
    const orgVisible = hasOrgCharge.value
    this.setState({ orgVisible })
  }

  handleClickAttachment = line => {
    let { handleClickAttachment } = this.props
    handleClickAttachment && handleClickAttachment(line)
  }
  deleteGpyResultList = line => {
    let { resultList } = this.state
    if (resultList && resultList.length) {
      let index = findIndex(resultList, o => o.name == line.fileName)
      if (index > -1) {
        resultList.splice(index, 1)
      }
    }
    this.setState(resultList)
  }
  handleStart = () => {
    const { handleStart } = this.props
    handleStart && handleStart()
  }

  handleFinish = fileList => {
    let { handleFinish } = this.props
    //上传附件接口返回字段修改 返回fileId变为Id 但我们保存数据接口需要fileId 所以需转换
    //坑爹 新传的id改成了fileId  已经传过了的没有id字段 只有fileId 所以需加上
    fileList.map(item => {
      item.fileId = item.id || item.fileId
    })
    handleFinish && handleFinish(fileList)
  }

  handleRemoveAttachment = line => {
    let { handleRemoveAttachment } = this.props
    handleRemoveAttachment && handleRemoveAttachment(line)
  }

  handleChange = uploaderFileList => {
    let { handleChange } = this.props
    handleChange && handleChange(uploaderFileList)
  }

  handleRetry = (file, fileKey) => {
    if (this.uploadButtonRef && this.uploadButtonRef.handleOnRetry) {
      this.uploadButtonRef.handleOnRetry(file, fileKey)
    }
  }

  gpyChild = () => {
    let { isOCR, useClipboard, gpyClassName } = this.props
    let classNames = isOCR ? 'gpy-invoice' : styles['gpy-upload-invoice']
    let gpyClassNameWrapper = gpyClassName ? gpyClassName : useClipboard ? styles['gpy-upload-clipboard-wrapper'] : ''
    if (i18n?.currentLocale === 'en-US' && !gpyClassName) {
      classNames = `${classNames} ${styles['gpy-upload-invoice-en']}`
      gpyClassNameWrapper = useClipboard ? styles['gpy-upload-en-clipboard-wrapper'] : ''
    }
    return (
      <div className={`${classNames} ${gpyClassNameWrapper}`}>
        <Tooltip placement="topCenter" title={i18n.get('可以接入高拍仪使用')}>
          <Button category="secondary" icon={<OutlinedGeneralCamera />} onClick={this.gpyMachineClick}>
            {i18n.get('拍照上传')}
          </Button>
        </Tooltip>
      </div>
    )
  }
  gpyMachineClick = () => {
    let { resultList } = this.state
    api.open('@bills:ImportGPYModal', { resultList }).then(result => {
      if (result) {
        this.bus.emit('gpyImageList:change', result)
        result.forEach(item => {
          resultList.push(item)
        })
        this.setState({
          resultList
        })
      }
    })
  }

  render() {
    let { orgVisible } = this.state
    const { hideGpy, field } = this.props
    let {
      value,
      uploaderFileList,
      onFilePreview,
      onFileDownload,
      children,
      isOCR,
      accept,
      disable,
      isLoading,
      classNameOCR,
      onFormatFile,
      filesNum,
      filesExceedEvent,
      fileMaxSize,
      canSelectDP,
      onError,
      suffixesPath,
      suffixesFiled,
      uploadLimit,
      useAI,
      onAIResult,
      onApplyAIResult,
      autoApplyAIResult
    } = this.props
    let fileList = fnFormatAttachment(value)
    return (
      <div>
        {orgVisible && !hideGpy ? this.gpyChild() : ''}
        <UploadButton
          ref={el => this.uploadButtonRef = el}
          isOCR={isOCR}
          disable={disable}
          accept={accept}
          fileList={fileList}
          onStart={this.handleStart}
          isLoading={isLoading}
          children={children}
          onFinish={this.handleFinish}
          onChange={this.handleChange}
          onFormatFile={onFormatFile}
          filesNum={filesNum}
          filesExceedEvent={filesExceedEvent}
          fileMaxSize={fileMaxSize}
          bus={this.bus}
          orgVisible={orgVisible && !hideGpy}
          canSelectDP={canSelectDP}
          onError={onError}
          suffixesPath={suffixesPath}
          suffixesFiled={suffixesFiled}
          field={field}
          uploadLimit={uploadLimit}
        />
        <AttachmentList
          isEdit
          isOCR={isOCR}
          disable={disable}
          isRequire={false}
          fileList={fileList}
          uploaderFileList={uploaderFileList}
          onRemoveAttachment={this.handleRemoveAttachment}
          onClickAttachment={this.handleClickAttachment}
          onFilePreview={onFilePreview}
          onFileDownload={onFileDownload}
          classNameOCR={classNameOCR}
          deleteGpyResultList={orgVisible && !hideGpy ? this.deleteGpyResultList : ''}
          useAI={useAI}
          onAIResult={onAIResult}
          onApplyAIResult={onApplyAIResult}
          autoApplyAIResult={autoApplyAIResult}
          onRetry={this.handleRetry}
        />
      </div>
    )
  }
}
