/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/2/23.
 */
import './tree-menu.less'
import { Tree, Tooltip } from 'antd'
import React from 'react'
import EkbHighLighter from '../../EkbHighLighter'
import {
  treeSortBy,
  treeMap,
  searchSubTree,
  recursiveTree,
  getPathByItems,
  insertSubTree,
  insertSubTreeNew,
  clippedList
} from './tree-menu-util'
import { uniq, cloneDeep, get } from 'lodash'
import ClassNames from 'classnames'
import { observer } from 'mobx-react'
import EKBIcon from '../../ekbIcon'
import NoResultViewForSearch from '../../SearchBarForTitle/NoResultViewForSearch'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
import { getDisplayName } from "../../utilFn";
import { NameCell } from '../../name-cell'

const { TreeNode, DirectoryTree } = Tree

@observer
export default class TreeMenuView extends React.PureComponent {
  constructor(props) {
    super(props)

    this._LIST_KEY = props.listKey || 'id'
    this._PARENT_KEY = props.parentKey || 'parentId'
    this._ACTIVE_KEY = props.activeKey
    this._TREE_DATA = props.data

    this._CLIPPED_TREE = recursiveTree(
      { items: this._TREE_DATA, isClipRoot: false, searchHasMore: this.props.searchHasMore },
      this.props.budget,
      this.props.notUseClone,
      this.props.count,
      this.props.isLazyLoad
    )
    this._NODE_PATH = getPathByItems(this._TREE_DATA)
    this._TREE_MAP = treeMap(this._TREE_DATA)

    this.currentParentId = props.onlyBrotherNodeCanBeSelected
      ? props.selectedKeyParentId
      : undefined

    this.state = {
      expandedKeys: props.expandedKeys || [],
      selectedKeys: props.selectedKeys || [],
      checkedKeys: props.checkedKeys || props.expenseStandardCheckedKeys || { checked: [] },
      isShowCode: props.isShowCode || false,
      autoExpandParent: true,
      tree: this._CLIPPED_TREE,
      fold: true,
      searchText: '',
      currId: null
    }
  }

  componentDidMount() {
    this.props.selectedItem && this.setInitData(this.props.selectedItem)
  }

  componentWillReceiveProps(nextProps) {
    const { isNewContactList } = nextProps
    let fn = fnCompareProps(this.props, nextProps)

    fn('selectedItem', selectedItem => {
      this.setInitData(selectedItem)
    })

    fn('isShowCode', isShowCode => {
      this.setState({ isShowCode })
    })

    fn('expandedKeys', expandedKeys => {
      const autoExpandParent = isNewContactList ? false : true
      this.setState({ expandedKeys, autoExpandParent })
    })

    fn('checkedKeys', checkedKeys => {
      this.setState({ checkedKeys })
    })

    fn('dimensionIsExpend', dimensionIsExpend => {
      this.setState({ expandedKeys: [] })
    })

    const searchText = nextProps.searchText || ''

    if (
      this._TREE_DATA !== nextProps.data ||
      Boolean(this.props.multiple) !== Boolean(nextProps.multiple) ||
      Boolean(this.props.isSearch) !== Boolean(nextProps.isSearch) ||
      this.state.searchText !== searchText
    ) {
      let { data, multiple, isSearch, searchText, needObject } = nextProps
      this._TREE_DATA = data
      this._TREE_MAP = treeMap(this._TREE_DATA)
      const { fold } = this.state
      if (multiple || isSearch || searchText) {
        if (!!searchText) {
          this._TREE_DATA = this._TREE_DATA.map(item => {
            const el = cloneDeep(item)
            if (multiple && needObject) return el;
            el.parentId = ''
            return el
          })
        }
        this._CLIPPED_TREE = recursiveTree(
          {
            items: this._TREE_DATA,
            isClipRoot: false,
            isSearch: nextProps.isSearch
          },
          this.props.budget,
          this.props.notUseClone,
          this.props.count,
          this.props.isLazyLoad
        )
      } else {
        this._CLIPPED_TREE = fold
          ? recursiveTree(
              { items: this._TREE_DATA, isSearch: nextProps.isSearch, searchHasMore: nextProps.searchHasMore },
              this.props.budget,
              this.props.notUseClone,
              this.props.count,
              this.props.isLazyLoad
            )
          : this._TREE_DATA
      }
      this._NODE_PATH = getPathByItems(this._TREE_DATA)
      this.setState({ tree: this._CLIPPED_TREE, searchText })
      this.forceUpdate()
    }
    if (nextProps.multiple && nextProps.checkedKeys && nextProps.checkedKeys.length === 0) {
      this.setState({ checkedKeys: { checked: [] } })
    }
  }

  setInitData(selectedItem) {
    if (selectedItem) {
      if (selectedItem.isSuffix) {
        let { subTree } = searchSubTree(this._TREE_DATA, this._NODE_PATH[selectedItem.id])
        let recTree = recursiveTree(
          { items: subTree, isClipRoot: true, searchHasMore: this.props.searchHasMore },
          this.props.budget,
          this.props.notUseClone,
          this.props.count,
          this.props.isLazyLoad
        )
        let { tree } = insertSubTree(this.state.tree, this._NODE_PATH[selectedItem.id], recTree)
        this.setState({ tree })
      } else {
        let selectedKeys = [selectedItem[this._LIST_KEY]]
        let expandedKeys = this.getExpandKeys(this.state.tree, selectedItem)
        let list = this.state.expandedKeys.slice()
        expandedKeys = uniq(list.concat(expandedKeys))
        this.setState({ selectedKeys, expandedKeys })
      }
    } else {
      this.setState({ selectedKeys: [] })
    }
  }

  getExpandKeys(list, selectedItem, result = []) {
    list.forEach(el => {
      let node = undefined
      if (el[this._LIST_KEY] === selectedItem[this._PARENT_KEY]) {
        node = el
        result.push(el[this._LIST_KEY])
      }

      if (node) {
        this.getExpandKeys(this.state.tree, node, result)
      } else {
        this.getExpandKeys(el.children || [], selectedItem, result)
      }
    })
    return result
  }

  handleSelectItem = (selectedKeys, { node }) => {
    const item = get(node, 'props.value')
    if (item?.virtual && !this.props.virtualSelectable) return
    const key = get(selectedKeys, '[0]')
    if (key === 'dimensionAddMore') return
    if (node.props.value.isSuffix) {
      const { onLoadMore } = this.props
      if (this.props.budget || this.props.isLazyLoad) {
        onLoadMore && onLoadMore(node.props.value)
      } else {
        this.clickSuffixItem(node)
        onLoadMore && onLoadMore(node.props.value)
      }
    } else {
      // const id = get(node, 'props.value.id', '')
      // const item = this._TREE_MAP[id]
      const item = get(node, 'props.value')
      this.props.onSelect && this.props.onSelect(item)
      this.setState({selectedKeys})
    }
  }

  clickSuffixItem = node => {
    const { isOriginal, searchType } = this.props
    const { fold } = this.state

    if (isOriginal) {
      this._CLIPPED_TREE = this._TREE_DATA
      this.setState({ tree: this._CLIPPED_TREE, fold: !fold })
      return
    }
    const selectPId = get(node, 'props.value.parentId', '')
    const selectId = get(node, 'props.value.id', '')
    const page = get(node, 'props.value.page') + 1
    let t = []
    if (!selectPId || searchType === 'select_search') {
      t = clippedList({ list: this._TREE_DATA, page })
      t = recursiveTree(
        { items: t, isClipRoot: true },
        this.props.budget,
        this.props.notUseClone,
        this.props.count,
        this.props.isLazyLoad
      )
    } else {
      let parent = cloneDeep(this._TREE_MAP[selectPId])
      parent.children = clippedList({ list: parent.children, page })
      t = insertSubTreeNew(this.state.tree, parent).tree
    }
    const tree = t.slice(0)
    this.setState({ tree })
  }

  handleExpand = (expandedKeys, ev) => {
    this.setState({
      expandedKeys,
      autoExpandParent: false
    })
    if (this.props.budget || this.props.isLazyLoad) {
      const children = get(ev, 'node.props.children') || []
      let oFirst = children.length && children[0]
      if (oFirst && oFirst.constructor === Array) {
        oFirst = oFirst[0]
      }
      const status = oFirst ? oFirst?.props?.value?.status : ''
      const hasTmp = status === 'tmp'
      if (hasTmp) {
        this.props.onExpand && this.props.onExpand(expandedKeys, ev)
      } else {
        if (ev.expanded) {
          let key = ev.node.props.eventKey
          let arr = (this._NODE_PATH[key] || []).concat(key)
          this.setState({ expandedKeys: expandedKeys })
        }
      }
    } else {
      this.props.onExpand && this.props.onExpand(expandedKeys, ev)
    }
  }

  handleCheck = (checkedKeys, e) => {
    const { needObject, onlyBrotherNodeCanBeSelected } = this.props
    let data = cloneDeep(checkedKeys)
    if (Array.isArray(checkedKeys)) {
      data = { checked: data }
    }
    if (needObject) {
      data.checkedData = e.checkedNodes.map(node => node?.props?.value)
      data.currentSelectData = e?.node?.props?.value
    }

    if (onlyBrotherNodeCanBeSelected) {
      this.currentParentId = get(e, 'checkedNodes[0].props.value.parentId')
    }
    this.setState({ checkedKeys })

    this.props.onCheck && this.props.onCheck(data)
  }

  handleTreeData = () => {
    let data = this.state.tree
    let { searchText, isSearch, onSearchCheck } = this.props

    if (searchText && isSearch && onSearchCheck) {
      data = []
      const loop = (item1, item2) => {
        let flag = false
        let subflag = false

        if (onSearchCheck(item1, searchText)) {
          flag = true
        }

        if (item1.children) {
          item1.children.forEach(subitem1 => {
            const children = subitem1.children
            const hasChild = children && children.length
            let subitem2 = { ...subitem1, children: [], hasChild }
            if (loop(subitem1, subitem2)) {
              subflag = true
              if (onSearchCheck(subitem1, searchText)) {
                item2.children.push({ ...subitem1, hasChild })
              } else {
                item2.children.push(subitem2)
              }
            }
          })
        }
        return flag || subflag
      }

      this.props.data.forEach(item1 => {
        const hasChild = item1.children && item1.children.length
        const item2 = { ...item1, children: [], hasChild }
        if (onSearchCheck(item1, searchText)) {
          data.push(item1)
        } else if (loop(item1, item2)) {
          data.push(item2)
        }
      })
    }

    return data
  }

  onMouseEnter = id => {
    this.setState({ currId: id })
  }

  onMouseLeave = id => {
    this.setState({ currId: null })
  }

  renderTitle = item => {
    const { isShowCode, isShowIssuedStatus } = this.props
    item.isExternal = this.props.isExternal
    const label = item.label ? item.label.split('/') : getDisplayName(item)
    let code = ''
    let name = ''
    if (Array.isArray(label)) {
      name = label[0]
      code = label[1]
    } else {
      name = label
      code = isShowCode ? item.code : ''
    }
    name = window.__PLANTFORM__ === 'MC' && item.virtual ? i18n.get('{__k0}（虚拟部门）', { __k0: name }) : name

    if (window.isInWeComISV) {
      name = <NameCell type="department" id={item.id} name={name} />
    }
    const { _ACTIVE_KEY } = this
    const {
      searchText,
      disableSelected,
      renderDropDown,
      modelWidth = false,
      showIcon,
      isShowMinWidth = false,
      virtualSelectable = false
    } = this.props
    const { currId } = this.state
    const cl = modelWidth ? `line-style-model` : `line-style`
    const { belong = {} } = item
    //MC 里展示下发到子租户的状态
    let showIssuedStatus = i18n.get('未下发')
    if (belong?.auth != null && belong?.auth) {
      showIssuedStatus = i18n.get('子企业')
    } else if (belong?.auth != null && !belong?.auth) {
      showIssuedStatus = i18n.get('已下发到{__k0}', { __k0: belong?.name })
    }
    const tooltipTitle = isShowIssuedStatus ? showIssuedStatus : item.fullPath || ''
    const isVirtualDisabled = item.virtual && !virtualSelectable
    return (
      <Tooltip title={tooltipTitle} placement="left">
        <div
          disabled
          className={ClassNames('ant-tree-title-wrapper flex horizontal', {
            'color-gray': item[_ACTIVE_KEY] === false || isVirtualDisabled,
            'min-w': isShowMinWidth
          })}
          style={
            (disableSelected && item[_ACTIVE_KEY] === false) || isVirtualDisabled
              ? { cursor: 'not-allowed' }
              : { cursor: 'pointer' }
          }
          title={name}
          onMouseEnter={showIcon ? this.onMouseEnter.bind(this, item.id) : null}
          onMouseLeave={showIcon ? this.onMouseLeave : null}
        >
          <div>
            <div className={cl}>
              {window.isInWeComISV && item.id
                ? name
                : (
                <EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={name} />
              )}
            </div>
            {code && (
              <div className="color-gray fs-13">
                <EkbHighLighter highlightClassName="highlight" searchWords={[searchText]} textToHighlight={code} />
              </div>
            )}
          </div>

          <div>
            {isShowMinWidth
              ? item.id === currId && renderDropDown && !item.isSuffix && renderDropDown(item)
              : renderDropDown && !item.isSuffix && renderDropDown(item)}
          </div>
        </div>
      </Tooltip>
    )
  }

  renderTreeNode(data, needSort, selectedType) {
    const {
      isDirectoryTree,
      showIcon,
      isShowMinWidth = false,
      disabled,
      isEditBatch,
      showLeaf = false,
      renderLoadMore,
      blackListCanNotSelect = [],
      selectedItem,
      onlyBrotherNodeCanBeSelected,
      notUseParentId = false
    } = this.props
    let arr = data
    if (needSort) {
      arr = treeSortBy(data, 'name') // 名字排序
    }
    if (window.__PLANTFORM__ === 'NBBANK') {
      arr = treeSortBy(data, 'nameSpell') // 宁波银行要求以名字拼音排序
    }

    let { _ACTIVE_KEY } = this

    const { isNewContactList = false } = this.props // 新的通讯录

    return arr.map((item, idx) => {
      const cl = item.isSuffix ? 'disable-checkbox' : ''
      let isDelete = !!item.isDelete || blackListCanNotSelect?.includes(item.id)
      const others = showLeaf ? { isLeaf: item.leaf } : {}
      const useParentId = notUseParentId ? notUseParentId : item.parentId
      const canNotSelected = onlyBrotherNodeCanBeSelected && this.currentParentId !== undefined
        ? item.parentId !== this.currentParentId
        : false

      if ((this.props.budget || this.props.isLazyLoad) && isDelete) {
        return null
      }
      let disableCheckboxCfg = {}
      if (isEditBatch && item.noSyncFeeType) {
        disableCheckboxCfg = { disableCheckbox: true }
      }

      let switcherIcon =
        showIcon && isShowMinWidth && !item.isSuffix
          ? {
              switcherIcon: item.parentId ? (
                <EKBIcon
                  name="#EDico-folder-fill"
                  className={`stand-20-icon icon-color ${isNewContactList ? 'ml-6' : ''}`}
                />
              ) : (
                <div className="icon-bc icon-wc p-4">
                  <EKBIcon name="#EDico-folder-fill" className="stand-16-icon" />
                </div>
              )
            }
          : null

      if (
        ((item.children && item.children.length) ||
          (this.props.isLazyLoad && item.leaf === false) ||
          (this.props.budget && item.hasChild)) &&
        !item.isSearch
      ) {
        const { multiple, notDisableCheckbox, virtualSelectable = false } = this.props
        const isVirtualDisabled = item.virtual && !virtualSelectable
        let config =
          multiple && !item.isSuffix && !notDisableCheckbox
            ? { disableCheckbox: !item[_ACTIVE_KEY] || isVirtualDisabled }
            : {}
        switcherIcon =
          showIcon && isShowMinWidth
            ? {
                switcherIcon: !item.parentId ? (
                  <div className="icon-bc icon-wc p-4">
                    <EKBIcon name="#EDico-folder-fill" className="stand-16-icon" />
                  </div>
                ) : (
                  <EKBIcon name="#EDico-folder-fill" className="stand-20-icon icon-color ml-6" />
                )
              }
            : null

        return (
          <TreeNode
            className={ClassNames(`${cl} set_ellipsis`, {
              'dictory-tree': isDirectoryTree,
              'hide-switcher': isNewContactList ? false : showIcon && !item.parentId,
              'move-after': isNewContactList || (showIcon && useParentId)
            })}
            key={item[this._LIST_KEY]}
            {...config}
            {...others}
            {...disableCheckboxCfg}
            title={this.props.renderTitle ? this.props.renderTitle(item) : this.renderTitle(item)}
            value={item}
            disabled={isDelete || disabled || canNotSelected}
            {...switcherIcon}
          >
            {item.children && item.children.length
              ? this.renderTreeNode(item.children, needSort, selectedType)
              : this.renderTreeNode([{ id: `${item.id}-tmp`, name: '', status: 'tmp' }], needSort, selectedType)}
            {renderLoadMore && renderLoadMore(item)}
          </TreeNode>
        )
      }

      return (
        <TreeNode
          isLeaf
          key={item[this._LIST_KEY]}
          className={ClassNames(`node-1 ${cl} set_ellipsis`, {
            'dictory-tree': isDirectoryTree,
            'show-icon-chilren': showIcon
          })}
          {...disableCheckboxCfg}
          title={this.props.renderTitle ? this.props.renderTitle(item) : this.renderTitle(item)}
          value={item}
          disabled={isDelete || disabled || canNotSelected}
          {...switcherIcon}
          {...others}
        />
      )
    })
  }

  renderNull = () => {
    const { searchText } = this.props
    return <NoResultViewForSearch searchKey={searchText} />
  }

  render() {
    const {
      multiple,
      checkStrictly,
      isSearch,
      isShowAll,
      searchText = '',
      isNeedExpandParentProp = true,
      needSort,
      selectedType = {},
      isDirectoryTree = false,
      showIcon = false,
      selectedKeys = []
    } = this.props
    if (isSearch && searchText === '') return this.renderNull()
    const data = isShowAll ? this.props.data : this.handleTreeData()
    if (isSearch && !data.length) return this.renderNull()
    let obj = isNeedExpandParentProp ? { autoExpandParent: this.state.autoExpandParent } : {}
    const Component = isDirectoryTree ? DirectoryTree : Tree

    return (
      <div className="tree-menu">
        <Component
          className="ekb-tree"
          {...this.props}
          {...obj}
          selectedKeys={selectedKeys}
          showIcon={showIcon}
          multiple={multiple}
          checkStrictly={checkStrictly === undefined ? multiple : checkStrictly}
          checkable={multiple}
          expandedKeys={[...this.state.expandedKeys]}
          checkedKeys={this.state.checkedKeys}
          onSelect={this.handleSelectItem}
          onCheck={this.handleCheck}
          onExpand={this.handleExpand}
        >
          {this.renderTreeNode(data, needSort, selectedType)}
        </Component>
      </div>
    )
  }
}
