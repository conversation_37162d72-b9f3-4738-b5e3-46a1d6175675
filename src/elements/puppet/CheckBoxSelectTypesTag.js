import React, { PureComponent } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { Select } from 'antd'
import { Checkbox } from '@hose/eui'
import FeeTypeSelect from '../feeType-tree-select'
import TripTypeSelect from './TripTypeSelect'
import { app as api } from '@ekuaibao/whispered'
@EnhanceConnect(state => ({
  feeTypes: state['@common'].feetypes.data,
  tripTypeFullList: state['@common'].fullTripTypes
}))
export default class CheckBoxFeetypeTags extends PureComponent {
  state = {
    list: []
  }
  componentWillMount() {
    api.dataLoader('@common.fullTripTypes').load()
    const { parentValue } = this.props
    if (parentValue === 'travel' || parentValue === 'amountAndTravel') {
      api.invokeService('@tpp-v2:get:getTravelManagement').then(templateObj => {
        if (templateObj?.items) {
          let list = templateObj?.items.filter(i => i.active) // @i18n-ignore
          this.setState({
            list
          })
        }
      })
    }
  }

  setChecked = checked => {
    const newValue = {}
    newValue.isAll = !checked
    newValue.ids = []
    return newValue
  }

  setIds = ids => {
    let newValue = {}
    newValue.ids = ids
    newValue.isAll = ids.length == 0
    return newValue
  }

  valueSerialize = value => {
    if (!value) {
      return { checked: false, ids: [] }
    }
    return { checked: !value.isAll, ids: value.ids }
  }

  handleChecked = e => {
    const { onChange, type } = this.props
    const newValue = this.setChecked(e.target.checked)
    onChange && onChange(newValue, type)
  }

  handleChange = checkedKeys => {
    const { onChange, type } = this.props
    const newValue = this.setIds(checkedKeys)
    onChange && onChange(newValue, type)
  }

  renderList(ids) {
    const { className, type, feeTypes, tripTypeFullList, parentValue, disabled } = this.props
    const { list } = this.state
    if (parentValue === 'travel' || parentValue === 'amountAndTravel') {
      return (
        <Select
          maxTagCount={8}
          mode="tags"
          value={ids}
          size={'large'}
          disabled={disabled}
          style={{ width: 300, display: 'block' }}
          placeholder="请选择行程类型"
          onChange={this.handleChange}
        >
          {list.map(item => (
            <Select.Option key={item.id} disabled={!item.active && !value.includes(item.id)}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      )
    } else if (type === 'feeType') {
      return (
        <FeeTypeSelect
          className={className}
          size="large"
          multiple={true}
          treeCheckable={true}
          feeTypes={feeTypes}
          checkedKeys={ids}
          disabled={disabled}
          onChange={this.handleChange}
        />
      )
    } else {
      return (
        <TripTypeSelect
          checkedKeys={ids}
          disabled={disabled}
          onChange={this.handleChange}
          tripTypes={tripTypeFullList}
        />
      )
    }
  }

  render() {
    const { type, value, disabled } = this.props
    const { checked, ids } = this.valueSerialize(value)
    return (
      <div>
        <Checkbox onChange={this.handleChecked} checked={checked} disabled={disabled}>
          {i18n.get(type === 'feeType' ? i18n.get('限制费用类型') : i18n.get('限制行程类型'))}
        </Checkbox>
        {checked && this.renderList(ids)}
      </div>
    )
  }
}
