import React from 'react'
import { debounce } from 'lodash'
import { app } from '@ekuaibao/whispered'
import DislikeButton from './DislikeButton'

/**
 * 点踩按钮组件
 * 使用app.open打开反馈弹框
 * @param {Object} props
 * @param {Object} props.feedbackData - 反馈数据对象
 * @param {boolean} props.isActive - 是否激活状态
 * @param {Function} props.onActiveChange - 激活状态变化回调
 * @returns {JSX.Element}
 */
export default function DislikeButtonWithModal({
  feedbackData,
  isActive = false,
  onActiveChange
}) {
  const handleButtonClick = debounce(async () => {
    const newActiveState = !isActive
    onActiveChange?.(newActiveState)
    if (!newActiveState) return
    await app.open('@bills:AIFeedbackModal', {
      feedbackData,
    })
  }, 500)

  return (
    <DislikeButton
      isActive={isActive}
      onClick={handleButtonClick}
    />
  )
}
