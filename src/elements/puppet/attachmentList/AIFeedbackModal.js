import React from 'react'
import { Button, Input, Tag, message } from '@hose/eui'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import classnames from 'classnames'
import { AIFillFormTracker } from '../../../lib/aiFillFormTracker'
import styles from './AIFeedbackModal.module.less'

const FEEDBACK_OPTIONS = [
  { key: 'info_extract_error', label: i18n.get('信息提取错误') },
  { key: 'not_extracted', label: i18n.get('未提取到') },
  { key: 'format_recognition_error', label: i18n.get('格式识别错误') },
  { key: 'recognition_effect_poor', label: i18n.get('识别效果差') },
  { key: 'no_help', label: i18n.get('没有帮助') }
]

/**
 * 反馈弹窗内容组件
 */
function FeedbackModalContent({
  selectedOptions = [],
  feedbackText = '',
  onToggleOption,
  onTextChange
}) {

  const handleTextChange = (e) => {
    onTextChange?.(e.target.value)
  }

  const handleOptionClick = (optionKey) => {
    onToggleOption?.(optionKey)
  }

  return (
    <div className={styles['feedback-modal-content']}>
      {/* 反馈选项 */}
      <div className={styles['options-section']}>
        <div className={styles['options-wrapper']}>
          {FEEDBACK_OPTIONS.map(option => (
            <Tag
              key={option.key}
              className={classnames(styles['option-tag'], {
                [styles.selected]: selectedOptions.includes(option.key)
              })}
              onClick={() => handleOptionClick(option.key)}
            >
              {option.label}
            </Tag>
          ))}
        </div>
      </div>

      {/* 文本输入区域 */}
      <Input.TextArea
        value={feedbackText}
        onChange={handleTextChange}
        placeholder={i18n.get('感谢您的反馈！您可以告诉我们哪里不准确，帮助我们改进。')}
        autoSize={{ minRows: 4, maxRows: 100 }}
        showCount={false}
      />
    </div>
  )
}

/**
 * AI摘要反馈弹框组件
 * 支持通过app.open调用，使用EnhanceModal装饰器
 */
@EnhanceModal({
  title: i18n.get('反馈'),
  footer: null, // 不使用装饰器的footer
  width: 520,
  maskClosable: false,
  className: styles['ai-feedback-modal-layer']
})
export default class AIFeedbackModal extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      selectedOptions: [],
      feedbackText: '',
      isSubmitting: false
    }
    this.isActiveClose = false // 标记是否为主动关闭（取消/确认按钮）
  }

  /**
   * 切换反馈选项
   */
  toggleFeedbackOption = (optionKey) => {
    const { selectedOptions } = this.state
    this.setState({
      selectedOptions: selectedOptions.includes(optionKey)
        ? selectedOptions.filter(key => key !== optionKey)
        : [...selectedOptions, optionKey]
    })
  }

  /**
   * 更新反馈文本
   */
  updateFeedbackText = (text) => {
    this.setState({ feedbackText: text })
  }

  /**
   * 格式化反馈内容
   */
  formatComment = (modalFeedbackData) => {
    const { selectedOptions: selectedLabels, feedbackText } = modalFeedbackData

    // 格式化comment：用户输入的feedbackText内容，加上逗号（条件：用户有输入且选择了快捷标签），快捷标签前面加上#号
    const userText = feedbackText.trim()
    const optionTags = selectedLabels.map(option => `#${option}`)

    let comment = ''
    if (userText && optionTags.length > 0) {
      // 用户有输入且选择了快捷标签：用户输入 + 逗号 + 标签
      comment = `${userText},${optionTags.join(',')}`
    } else if (userText) {
      // 只有用户输入
      comment = userText
    } else if (optionTags.length > 0) {
      // 只有标签
      comment = optionTags.join(',')
    }

    return comment
  }

  submitFeedback = (isCloseClick = false) => {
    const { selectedOptions, feedbackText } = this.state
    const { feedbackData, onSuccess, layer } = this.props

    try {
      this.setState({ isSubmitting: true })

      const selectedLabels = selectedOptions.map(key =>
        FEEDBACK_OPTIONS.find(option => option.key === key)?.label
      ).filter(Boolean)

      const modalFeedbackData = {
        selectedOptions: selectedLabels,
        feedbackText: feedbackText.trim()
      }

      const comment = isCloseClick ? '' : this.formatComment(modalFeedbackData)

      // 直接调用埋点
      AIFillFormTracker.trackAIFeedback({
        ...feedbackData,
        action: 'dislike',
        feedback: comment,
      })

      message.success(i18n.get('反馈已收到，感谢！'))
      onSuccess?.()
      layer?.emitOk({ success: true })

    } catch (error) {
      console.error('提交反馈失败:', error)
    } finally {
      this.setState({ isSubmitting: false })
    }
  }

  /**
   * 处理取消按钮点击
   */
  handleCancel = () => {
    this.isActiveClose = true // 标记为主动关闭
    this.submitFeedback(true)
    this.props.layer?.emitCancel()
  }

  /**
   * 处理提交
   */
  handleSubmit = async () => {
    this.isActiveClose = true // 标记为主动关闭
    this.submitFeedback()
  }

  componentDidMount() {
    // 初始化时重置状态
    this.setState({
      selectedOptions: [],
      feedbackText: ''
    })
  }

  componentWillUnmount() {
    // 组件卸载时，如果不是主动关闭，则调用submitFeedback
    if (!this.isActiveClose) {
      this.submitFeedback(true)
    }
  }

  render() {
    const { selectedOptions, feedbackText, isSubmitting } = this.state
    const canSubmit = selectedOptions.length > 0 || feedbackText.trim().length > 0

    return (
      <div className={styles['ai-feedback-modal-content']}>
        <FeedbackModalContent
          selectedOptions={selectedOptions}
          feedbackText={feedbackText}
          onToggleOption={this.toggleFeedbackOption}
          onTextChange={this.updateFeedbackText}
        />

        <div className={styles['feedback-modal-footer']}>
          <Button
            category="secondary"
            onClick={this.handleCancel}
            disabled={isSubmitting}
          >
            {i18n.get('取消')}
          </Button>
          <Button
            category="primary"
            onClick={this.handleSubmit}
            disabled={!canSubmit || isSubmitting}
            loading={isSubmitting}
          >
            {i18n.get('提交')}
          </Button>
        </div>
      </div>
    )
  }
}
