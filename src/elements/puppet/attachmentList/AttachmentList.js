import React, { useState } from 'react'
import classnames from 'classnames'
import styles from './AttachmentList.module.less'
import AttachmentItem, { ImagePreview } from './AttachmentItem'
import { uuid } from '@ekuaibao/helpers'
import { getBoolVariation } from '../../../lib/featbit'

const isImage = fileName => {
  return /\.(jpg|jpeg|png|webp|gif|svg|bmp|tif|tiff)$/.test(fileName.toLowerCase())
}
const noop = () => {}

export default function AttachmentList(props) {
  const {
    classNameOCR,
    fileList,
    uploaderFileList,
    isOCR,
    isEdit,
    disable,
    onClickAttachment,
    onFilePreview,
    onFileDownload,
    onRemoveAttachment,
    deleteGpyResultList,
    onAIResult,
    useAI,
    onApplyAIResult,
    autoApplyAIResult,
    itemClassName,
    onRetry
  } = props

  const [images, setImages] = useState([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [open, setOpen] = useState(false)
  const [clickIndex, setClickIndex] = useState(null)

  if (!fileList?.length && !uploaderFileList?.length && !isEdit) {
    return <div>{i18n.get('无')}</div>
  } else if (!fileList?.length && !uploaderFileList.length) {
    return <></>
  }

  const handlePreviewVisible = visible => {
    if (!visible) {
      setImages([])
    }
  }

  const handleFilePreview = file => {
    const clickIndex = fileList.findIndex(item => item.fileId === file.fileId)
    setClickIndex(clickIndex)
    const useDingTalkPreview = getBoolVariation('use-dingtalk-sdk-preview-image')
    // 后面如果要下掉开关了了，针对三方平台的预览还是要使用平台提供的能力
    if (useDingTalkPreview) {
      onFilePreview(file)
      return
    }
    if (isImage(file.fileName) && !file.key?.startsWith('DP:')) {
      const allImages = fileList.filter(file => isImage(file.fileName))
      const currentIndex = allImages.findIndex(item => item.fileId === file.fileId)
      setImages(allImages)
      setCurrentIndex(currentIndex)
      setOpen(uuid(8))
    } else {
      onFilePreview(file)
    }
  }

  const onChangeIndex = () => {
    setClickIndex(null)
  }

  return (
    <div className={styles['attachment-list']} style={isOCR ? {overflow:'auto',maxHeight:'400px'}:{}}>
      <div className={classnames('main', classNameOCR)}>
        {fileList?.map((file, i) => (
          <AttachmentItem
            key={file.id}
            file={file}
            isOCR={isOCR}
            className={itemClassName}
            isEdit={isEdit}
            disable={disable}
            onClickItem={onClickAttachment}
            onFilePreview={handleFilePreview}
            onFileDownload={onFileDownload}
            onRemoveItem={onRemoveAttachment}
            deleteGpyResultList={deleteGpyResultList}
            onChangeIndex={onChangeIndex}
            style={{ color: clickIndex === i ? 'var(--eui-primary-pri-500)' : 'var(--eui-text-title)' }}
            useAI={useAI}
            onAIResult={onAIResult}
            onApplyAIResult={onApplyAIResult}
            autoApplyAIResult={autoApplyAIResult}
          />
        ))}
        {uploaderFileList?.map((file, i) => {
          return (
            <AttachmentItem
              key={file.id}
              file={file}
              isOCR={isOCR}
              isEdit={isEdit}
              style={{ color: clickIndex === i ? 'var(--eui-primary-pri-500)' : '' }}
              useAI={useAI}
              onAIResult={onAIResult}
              onApplyAIResult={onApplyAIResult}
              autoApplyAIResult={autoApplyAIResult}
              onRetry={onRetry}
            />
          )
        })}
      </div>
      <ImagePreview
        open={open}
        images={images}
        current={currentIndex}
        onVisibleChange={handlePreviewVisible}
        onFileDownload={onFileDownload}
      />
    </div>
  )
}

AttachmentList.defaultProps = {
  disable: false,
  isEdit: false,
  isRequire: false,
  fileList: [],
  onAddItem: noop,
  onRemoveItem: noop,
  uploaderFileList: []
}
