.ai-feedback-modal-layer {
	.ai-feedback-modal-content {
		.feedback-modal-content {
			max-height: 480px;
			overflow-y: auto;
			padding: 0px 16px 0 16px;
		
			.options-section {
				margin-bottom: 10px;
		
				.options-wrapper {
					display: flex;
					flex-wrap: wrap;
					gap: 8px;
		
					.option-tag {
						cursor: pointer;
						user-select: none;
						padding: 2px 8px;
						border-radius: 4px;
						background: var(--eui-transparent-n900-10, rgba(29, 33, 41, 0.10));
						border: 1px solid transparent;
						transition: all 0.2s ease;
						color: var(--eui-decorative-neu-600, #6B7785);
						font-family: "PingFang SC";
						font-size: 14px;
						font-style: normal;
						font-weight: 400;
						line-height: 20px; /* 142.857% */
		
						&:hover {
							opacity: 0.8;
						}
		
						&.selected {
							background: var(--eui-primary-pri-100, #C1D7FF);
							color: var(--eui-primary-pri-600, #1739D2);
						}
					}
				}
			}
		}
	
		.feedback-modal-footer {
			display: flex;
			justify-content: flex-end;
			gap: 8px;
			padding: 16px;
		}
	}

	:global {
		.ant-modal-header {
			border-bottom: unset;

			.ant-modal-title {
				color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
				font-family: "PingFang SC";
				font-size: 16px;
				font-style: normal;
				font-weight: 500;
				line-height: 24px; /* 150% */
			}
		}

		.ant-modal-body {
			padding: 0;
		}
	}
}