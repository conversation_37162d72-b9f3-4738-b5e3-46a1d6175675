import React, { useState } from 'react'
import LikeButton from './LikeButton'
import DislikeButtonWithModal from './DislikeButtonWithModal'
import styles from './FeedbackButtons.module.less'

/**
 * 反馈按钮容器组件
 * @param {Object} props
 * @param {Object} props.feedbackData - AI摘要反馈数据
 * @param {string} props.feedbackData.content - AI摘要内容（必需）
 * @param {string|string[]} props.feedbackData.file_id - 附件id（可选，附件填单场景）
 * @param {string|string[]} props.feedbackData.file_name - 附件名称（可选，附件填单场景）
 * @param {string} props.feedbackData.ai_chat - AI对话内容（可选，对话填单场景）
 * @param {string} props.className - 自定义样式类名
 * @returns {JSX.Element}
 */
export default function FeedbackButtons({
  feedbackData = {},
  className = ''
}) {
  const [likeActive, setLikeActive] = useState(false)
  const [dislikeActive, setDislikeActive] = useState(false)

  const normalizedFeedbackData = {
    content: feedbackData.content || '',
    file_id: feedbackData.file_id,
    file_name: feedbackData.file_name,
    ai_chat: feedbackData.ai_chat
  }

  const handleLikeChange = (active) => {
    setLikeActive(active)
    if (active) setDislikeActive(false)
  }

  const handleDislikeChange = (active) => {
    setDislikeActive(active)
    if (active) setLikeActive(false)
  }

  return (
    <div className={`${styles['feedback-buttons']} ${className}`}>
      <LikeButton
        feedbackData={normalizedFeedbackData}
        isActive={likeActive}
        onActiveChange={handleLikeChange}
      />
      <DislikeButtonWithModal
        feedbackData={normalizedFeedbackData}
        isActive={dislikeActive}
        onActiveChange={handleDislikeChange}
      />
    </div>
  )
}
