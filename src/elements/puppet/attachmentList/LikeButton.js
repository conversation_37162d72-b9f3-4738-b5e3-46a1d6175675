import React from 'react'
import { message } from '@hose/eui'
import { OutlinedGeneralThumbsup, FilledGeneralThumbsup } from '@hose/eui-icons'
import { debounce } from 'lodash'
import { AIFillFormTracker } from '../../../lib/aiFillFormTracker'
import styles from './LikeButton.module.less'

/**
 * 点赞按钮组件
 * @param {Object} props
 * @param {Object} props.feedbackData - 反馈数据对象
 * @param {boolean} props.isActive - 是否激活状态
 * @param {Function} props.onActiveChange - 激活状态变化回调
 * @returns {JSX.Element}
 */
export default function LikeButton({
  feedbackData,
  isActive = false,
  onActiveChange
}) {
  const handleClick = debounce(async (e) => {
    e.stopPropagation()
    e.preventDefault()

    const newActiveState = !isActive
    onActiveChange?.(newActiveState)
    if (!newActiveState) return

    AIFillFormTracker.trackAIFeedback({
      ...feedbackData,
      action: 'like',
      feedback: '',
    })
    message.success(i18n.get('反馈已收到，感谢！'))
  }, 500)

  return (
    <div
      className={styles['like-button']}
      onClick={handleClick}
    >
      {isActive ? <FilledGeneralThumbsup /> : <OutlinedGeneralThumbsup />}
      <span className={styles.text}>{i18n.get('赞')}</span>
    </div>
  )
}
