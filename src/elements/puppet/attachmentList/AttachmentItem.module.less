@import '~@ekuaibao/web-theme-variables/styles/default';

:global {
  .eui-popover {
    .eui-popover-content {
      .attachment-item-w {
        min-width: 343px;
      }
    }
  }

  .ant-popover {
    .ant-popover-content {
      .attachment-item-w {
        min-width: 343px;
      }
    }
  }
}

.attachment-item {
  margin-bottom: 4px;
  min-width: 86%;
  background: var(--eui-bg-body-overlay);
  border: 0;
  border-radius: 6px;
  padding: 8px;

  :global {
    .file-AI-result-table {
      .file-AI-result-content-value {
        color: var(--eui-text-caption);
        font: var(--eui-font-note-r2);
      }
      .eui-table-cell{
        font-size: 12px;
        padding: 4px 8px !important;
      }
      th.eui-table-cell{
        padding: 3px 8px !important;
        max-width: 200px;
      }
    }
    .file-AI-result {
      padding: 8px 16px;
      margin-left: 20px;
      margin-top: 8px;
      border-radius: 6px;
      border: 1px solid #C0D5FF;
      background: linear-gradient(270deg, rgba(240, 245, 255, 0.50) 0.35%, rgba(223, 234, 255, 0.50) 97.41%);

      &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 18px;

        &-left {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex: 1;
          gap: 8px;

          &-title-wrapper {
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            gap: 4px;
          }

          &-title {
            color: var(--eui-text-title);
            font: var(--eui-font-note-b2);
          }

          &-percent {
            color: var(--eui-primary-pri-500);
            font: var(--eui-font-note-r2);
          }
        }

        &-right {
          cursor: pointer;

          &-button {
            cursor: pointer;
            border-radius: 4px;
            padding: 4px;
            height: 26px;
            &:hover {
              background: var(--eui-primary-pri-50, #E8F1FF);
            }
            &-text {
              font: var(--eui-font-note-r2);
              background: linear-gradient(275deg, #7347FF 1.83%, #0080FF 98.49%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

      }

      &-content {
        color: var(--eui-text-caption);
        font: var(--eui-font-note-r2);
        max-height: 306px;
        overflow: auto;
        margin-top: 4px;

        &-expand {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 12px;
          margin-top: 4px;
          height: 22px;

          &-left {
            display: flex;
            align-items: center;
            gap: 12px;
          }

          &-right {
            display: flex;
            align-items: center;
            gap: 8px;
          }

          &-text {
            color: var(--eui-primary-pri-500);
            font: var(--eui-font-note-r2);
            cursor: pointer;
          }
        }

        &-collapse-text {
          margin-left: 4px;
          color: var(--eui-primary-pri-500);
          font: var(--eui-font-note-r2);
          cursor: pointer;
          white-space: nowrap;
        }

        &-item {
          display: inline-flex;
          width: 100%;

          &-dom {
            .file-AI-result-content-key{
              margin-bottom: 2px;
            }
          }


          &.collapse-wrapper {
            .file-AI-result-content-value {
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }

          }
        }

        &-key {
          white-space: nowrap;
        }
      }

      .ai-result-tip {
        color: var(--eui-text-disabled);
        font: var(--eui-font-note-r1);
      }
    }
  }
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 24px;
  position: relative;
  z-index: 0;

  :global {
    .file-wrapper {
      width: calc(100% - 100px);
      display: flex;
      align-items: center;
      .eui-image-mask{
        border-radius: 4px;
        .eui-icon-OutlinedGeneralView{
          margin-left: 3px;
        }
      }
    }

    .file-img {
      width: 20px;
      height: 20px;
      border-radius: 4px;
    }

    .file-preview-out {
      position: absolute;
      left: 0px;
      right: 0px;
      z-index: 1;
      width: 100%;
      text-indent: -9999px;
    }

    .action-wrapper {
      display: flex;
      align-items: center;
      color: var(--eui-icon-n2);
      gap: 8px;

      .eui-icon {
        cursor: pointer;
        border-radius: 4px;
        padding: 4px;
        font-size: 14px;

        &:hover {
          background-color: var(--eui-fill-pressed);
        }
      }

      .file-preview, .file-download, .del-btn {
        height: 24px;
      }

      .file-AI, .file-AI-disabled {
        display: flex;
        align-items: center;
        gap: 2px;
        cursor: pointer;
        border-radius: 4px;
        white-space: nowrap;
        padding: 4px;

        >span {
          background: linear-gradient(275deg, #7347FF 1.83%, #0080FF 98.49%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font: var(--eui-font-note-r2);
        }

        .eui-icon {
          font-size: 16px;
          padding: 0;
          &:hover {
            background-color: transparent;
          }
        }

        &:hover {
          background: var(--eui-primary-pri-50);
        }

        &:selected {
          background: var(--eui-primary-pri-100);
        }
      }

      .file-AI-disabled {
        cursor: not-allowed;
        color: var(--eui-text-disabled);
        font: var(--eui-font-note-r2);

        &:hover {
          background: transparent;
        }

        >span {
          background: transparent;
          -webkit-text-fill-color: var(--eui-text-disabled);
        }
        .eui-icon {
          filter: opacity(0.5);
          cursor: not-allowed;
        }
      }

      .not-allow {
        cursor: not-allowed !important;
        color: #bcbcb2;
      }

      .file-retry {
        cursor: pointer;
        height: 24px;
      }

      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
        margin: 0;
      }

    }

    .file-name {
      width: 100%;
      margin-left: 8px;
      padding-right: 12px;
      cursor: pointer;
      font: var(--eui-font-body-r1);
    }
    .upload-error {
      color: var(--eui-function-danger-500);
    }
  }
}

.attachment-toolbar-wrapper {
  margin-left: 340px;
  background-color: var(--eui-static-black-60);
  border-radius: 8px;
  transform: translateX(-50%);

  :global {
    .eui-space-item {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--eui-static-white-60);

      .eui-icon:hover {
        color: var(--eui-static-white);
      }

      .eui-icon:focus {
        color: var(--eui-static-white);
      }

      .disabled {
        color: var(--eui-static-white-20);
        cursor: not-allowed;
        background-color: inherit;
      }

      .disabled:hover {
        cursor: not-allowed;
        color: var(--eui-static-white-20);
      }

      &:first-of-type {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
      }

      &:last-of-type {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
      }

      >span {
        cursor: pointer;
        font-size: 18px;
      }
    }
  }
}