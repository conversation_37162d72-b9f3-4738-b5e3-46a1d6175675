import React from 'react'
import { OutlinedGeneralThumbdown, FilledGeneralThumbdown } from '@hose/eui-icons'
import styles from './DislikeButton.module.less'

/**
 * 点踩按钮组件
 * @param {Object} props
 * @param {boolean} props.isActive - 是否激活状态
 * @param {Function} props.onClick - 点击回调
 * @returns {JSX.Element}
 */
export default function DislikeButton({
  isActive = false,
  onClick
}) {
  const handleClick = (e) => {
    e.stopPropagation()
    e.preventDefault()
    onClick?.()
  }

  return (
    <div
      className={styles['dislike-button']}
      onClick={handleClick}
    >
      {isActive ? <FilledGeneralThumbdown /> : <OutlinedGeneralThumbdown />}
      <span className={styles.text}>{i18n.get('踩')}</span>
    </div>
  )
}
