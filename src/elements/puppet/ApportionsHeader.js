/**
 * Created by <PERSON><PERSON> on 2017/9/13.
 */
import React, { PureComponent } from 'react'
import { Checkbox, Col } from 'antd'
import { Switch } from '@hose/eui'
import styles from './ApportionsHeader.module.less'

export default class ApportionsHeader extends PureComponent {
  render() {
    const { onCheckedValueChanged, checked, label = i18n.get('开启费用分摊'), style } = this.props
    return (
      <Col className={styles['apportion-header']} style={style}>
        <div className="desc">{i18n.get('若本明细的费用由多部门、多项目等共同承担，请开启')}</div>
        <Switch className={styles['switch']} checked={checked} data-testid="bill-apportion-switch" onChange={onCheckedValueChanged} title={label} />
      </Col>
    )
  }
}
