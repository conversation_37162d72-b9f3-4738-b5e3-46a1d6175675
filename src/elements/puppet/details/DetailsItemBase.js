/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/7/13.
 */
import styles from './DetailsItem.module.less'
import React from 'react'
import { Popover } from 'antd'
import { Popover as EUIPopover, Space, Tag } from '@hose/eui'
import { OutlinedGeneralIntelligentService } from '@hose/eui-icons'
import classNames from 'classnames'
import { toJS } from 'mobx'
import AttachmentList from '../attachmentList/AttachmentList'
import InvoiceCard from '../../invoice-card'
import ThirdPartyCard from 'ekbc-thirdParty-card/esm/thirdCard'
import ThirdCard from '../../thirdCard/third-card'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import Money from '../Money'
import IconTag from '../IconTag'
import { map, get } from 'lodash'
import DetailItemExpandFields from './DetailItemExpandFields'
import { fnGetDetailsByType, renderTitle, renderContent, renderStyle, GetInvoiceStateText, getInvoiceTagColorByType } from './FormatDateUtils'
import { parseAsShowValue } from '../../../elements/invoice-form/utils/config'
import { app as api } from '@ekuaibao/whispered'
import EKBIcon from '../../../elements/ekbIcon'
import { getV } from '@ekuaibao/lib/lib/help'
import { isObject, isArray } from '@ekuaibao/helpers'
import { T } from '@ekuaibao/i18n'
import { getFeetypeName } from '../../../lib/lib-util'
import RiskWarningV2 from '../../../plugins/bills/riskWarning/DetailRiskTipV2'
import { moneyReceivingAmountDisplay } from '../../../components/utils/fnCurrencyObj'
const parseAsMeta = api.invokeServiceAsLazyValue('@bills:import:parseAsMeta')
const renderPayDetail = api.invokeServiceAsLazyValue('@audit:import:renderPayDetail')
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')
const download = api.invokeServiceAsLazyValue('@bills:file:download')
const orderInfo = i18n.get('订单信息')
import AITextIcon from './AITextIcon'


const accountTypes = {
  public: 'PUBLIC',
  personal: 'PERSONAL'
}

const getAccountType = type => {
  switch (type) {
    case accountTypes.public:
      return i18n.get('公共账户')
    case accountTypes.personal:
      return i18n.get('个人账户')
    default:
      return ''
  }
}

const svg = (
  <span style={{ display: 'inline-flex' }}>
    <EKBIcon name="#EDico-didi" style={{ color: '#1E96FA', width: '16px', height: '16px' }} />
  </span>
)

export function fnFilePreview(attachmentsData, clickItem) {
  preview()({ value: attachmentsData, line: clickItem })
}

export function fnFileDownload(line) {
  download()(line)
}

const DetailBase = props => {
  const {
    dataSource,
    allTemplate,
    isReadFirstNoBorder,
    flowRulePerformLogs,
    index,
    baseDataProperties,
    external,
    riskInfo,
    submitterId,
    isForbid,
    RiskPromptOptimization,
    detailId,
    field,
    isInvoiceManagePermissions,
    onChange,
    isRecordExpends,
    billState,
    isOwn,
    ownerName,
    showPayPlan,
    sharedStaffs,
    sharedIds,
    sharedName,
    checkSharedStaffs,
    fnShareAction,
    nodeId,
    multiplePayeesMode,
    allowSelectionReceivingCurrency
  } = props

  let ruleErrorMsg = ((flowRulePerformLogs && flowRulePerformLogs.results) || [])
    .filter(e => e.loc === index && e.dataFrom === 'details')
    .map(e => e.errorMsg)
    .join(';')
  if (!dataSource) {
    return
  }

  let {
    feeTypeId,
    feeType,
    feeTypeForm,
    feeTypeForm: { feeDetailPayeeId = {}, amortizes = [] },
    specificationId = {},
    errorMsg = {},
    fromAIChat
  } = dataSource
  if (!feeTypeId) {
    feeTypeId = feeType
  }

  let code = feeTypeId.code
  if (code) code = i18n.get('（') + code + i18n.get('）')

  let isStop = !feeTypeId.active //是否已停用

  let { amount, ordersData = [], thirdPartyOrders = [], receivingAmount } = feeTypeForm

  let { invoiceForm } = feeTypeForm
  if (invoiceForm && invoiceForm.details && !invoiceForm.invoiceDetail) {
    let invoiceDetail = {}
    invoiceDetail.details = invoiceForm.details
    invoiceDetail.master = invoiceForm.master
    invoiceForm.invoiceDetail = invoiceDetail
    delete invoiceForm.details
    delete invoiceForm.master
  }
  let invoiceData = ordersData.find(o => o && o.platform === 'fp')
  let ekbmallDataV1 = ordersData.find(o => o && o.platform === 'ekbmall')
  let transactData = ordersData.find(o => o && o.platform === 'transact')

  let components = parseAsMeta()(specificationId, baseDataProperties)
  let { feeDateString, cityStr } = fnGetDetailsByType(feeTypeForm, components)
  let attachField = components.filter(v => ['attachments','aiAttachments'].includes(v.type))
  let attachMap = {}

  !isRecordExpends &&
    attachField.forEach(v => {
      let att = fnFormatAttachment(feeTypeForm[v.field])
      if (att.length) {
        attachMap[v.field] = {
          leng: att.length,
          attachmentsContent: (
            <AttachmentList
              itemClassName={styles['attachment-item']}
              onFilePreview={fnFilePreview.bind(this, att)}
              onFileDownload={fnFileDownload}
              fileList={att}
            />
          ),
          label: (i18n?.currentLocale === 'en-US' ? v.enLabel : v.cnLabel) || v.label
        }
      }
    })

  const thirdOrder = thirdPartyOrders[0]
  if (thirdOrder && thirdOrder.platform === 'YEEGO') {
    thirdOrder.platformName = i18n.get('易快报自营')
  }
  let invoiceContent = <InvoiceCard style={{ width: 400 }} invoice={invoiceData} submitterId={submitterId} />
  let thirdCardContent = (
    <ThirdPartyCard dataSource={thirdOrder} iconFont={thirdOrder && thirdOrder.platform === 'DIDI' ? svg : null} />
  )
  let ekbMallContentV1 = <ThirdCard orders={ekbmallDataV1} />
  let transactContent = <ThirdCard orders={transactData} />

  let errorMessage = ''
  if (isArray(errorMsg)) {
    errorMessage = errorMsg.join(' ')
  } else if (isObject(errorMsg)) {
    errorMessage = errorMsg?.completed || errorMsg?.visible
  } else {
    errorMessage = errorMsg
  }
  if (ruleErrorMsg) {
    errorMessage += ruleErrorMsg
  }

  let reasonLabel = '',
    comp = components.find(o => o.field === 'consumptionReasons')
  if (comp) {
    reasonLabel = comp.label + i18n.get('：')
  }
  let invoiceFormType = ''
  if (invoiceForm && (invoiceForm.type !== 'noWrite' || invoiceForm.invoiceDetail)) {
    if (invoiceForm.type === 'exist') {
      invoiceFormType = invoiceForm.invoiceConfirm
        ? invoiceForm.invoiceConfirm === 'false'
          ? 'existConfirm'
          : invoiceForm.type
        : invoiceForm.type
    } else {
      invoiceFormType = invoiceForm.type
    }
  }

  const wordBudgetMoney = amount => {
    return (
      <div className="budget-money" style={{ top: amount.foreignStrCode ? '42px' : '24px' }}>
        <span className="text mr-4">
          <span className="mr-4">{i18n.get('预算币')}</span>
          {amount.budgetStrCode}
        </span>
        <Money
          currencySize={16}
          valueSize={16}
          value={amount.budget}
          withoutStyle={true}
          showSymbol={false}
          isShowForeign={false}
          showForeignNum={false}
        />
      </div>
    )
  }

  /* 根据外币显示折合的本位币 */
  const showBaseCurrencyOfQuote = (amount) => {
    // const foreignStrCode = getV(amount, 'foreignStrCode', '')
    const baseCurrencyCode = getV(amount, 'standardStrCode', '')
    return <span className={styles['base-currency-of-quote']}>
      {/* <span className={styles.label}>{i18n.get('折合')}</span> */}
      <span className={styles['currency-code']}>{baseCurrencyCode}</span>
      <Money
        className={styles['currency-account']}
        value={amount}
        withoutStyle={true}
        showSymbol={false}
        isShowForeign={false}
        showForeignNum={false}
      />
    </span>
  }

  const wordAndMoney = (amount, isSecond, noPrefix = false) => {
    const foreignStrCode = getV(amount, 'foreignStrCode', '')
    const standardStrCode = getV(amount, 'standardStrCode', '')
    // const mustAllFeeTypeCurrencyEqual = getV(field, 'mustAllFeeTypeCurrencyEqual', false)
    let isShowForeign = false
    if (isSecond) {
      isShowForeign = foreignStrCode ? true : false
    } else {
      isShowForeign = false
    }

    let strCode = isShowForeign && foreignStrCode ? foreignStrCode : standardStrCode
    const isBlod = isShowForeign || (!foreignStrCode && standardStrCode)
    const text = allowSelectionReceivingCurrency ? i18n.get('折合') : ''
    return (
      <>
        {!noPrefix && <span className="mr-4 prefix">{isShowForeign ? '' : text}</span>}
        {<span className={classNames('mr-4 f-fd', { 'color-black-1': isBlod && !noPrefix }, 'ignore-translate')}>{strCode}</span>}
        <Money
          className={classNames({ 'color-black-1': isBlod && !noPrefix })}
          value={amount}
          withoutStyle={true}
          showSymbol={false}
          isShowForeign={isShowForeign}
          showForeignNum={false}
        />
      </>
    )
  }

  const fnGetSharedTxt = () => {
    if (!isOwn && isRecordExpends && ownerName) {
      return (
        <div className="money" style={{ fontSize: '12px' }}>
          <T name={`来自${ownerName}的共享`} />
        </div>
      )
    }
    if (isOwn && sharedName) {
      return (
        <div className="shared">
          <span
            onClick={e => {
              e.stopPropagation()
              e.preventDefault()
              fnShareAction({ sharedOwnerIds: sharedIds, ownerId: submitterId.id, nodeId })
            }}
          >
            {sharedName}
          </span>
        </div>
      )
    }
    return (
      <>
        <div className="money">
          {amount?.foreignStrCode && showBaseCurrencyOfQuote(amount, false)}
        </div>
        {amount?.budgetStrCode && wordBudgetMoney(amount)}
      </>
    )
  }
  const showPayInfo = multiplePayeesMode ? feeDetailPayeeId?.accountName || feeDetailPayeeId?.name : false
  const detailNo = get(dataSource, 'feeTypeForm.detailNo', '')

  const handlePopoverClick = event => {
    if (!event) return
    event.stopPropagation()
    event.preventDefault()
  }

  const typeName = getFeetypeName(feeTypeId)
  const renderRreceivingAmount = () => {
    if (allowSelectionReceivingCurrency && receivingAmount) {
      // 外币code 不等于当前本位币
      const { isShowForeign, isShowReceivingAmount } = moneyReceivingAmountDisplay(amount, receivingAmount)
      return (
        <div className="new-money">
          <div className="new-money-first">{wordAndMoney(amount, isShowForeign, true)}</div>
          {isShowReceivingAmount && <div className="new-money-other">{wordAndMoney(receivingAmount, false)}</div>}
          {isShowForeign && <div className="new-money-other">{wordAndMoney(amount, false, false)}</div>}
        </div>
      )
    }
  }

  return (
    <div className={`${styles['item-wrap']} ${isStop && styles['isStop']}`}>
      <div className="item-icon" style={{ background: feeTypeId.color }}>
        <img width={24} height={24} src={feeTypeId.icon} alt="" />
      </div>
      <section className="item-main" style={{ borderTop: isReadFirstNoBorder ? 'none' : '' }}>
        <div className="content">
          <div className="configurations">
            <Space direction='vertical' size={4}>
              <div className="name">
                <div className="feeType-name">
                  {!!amortizes?.length && <span className="amortize-type">{i18n.get('摊销')}</span>}
                  {typeName}
                  {code && <span className='code'>{code}</span>}
                  {detailNo && <span className="detailNo">{`（${i18n.get('序号')} ${detailNo}）`}</span>}
                  {feeTypeId.actionValue && <Tag color='danger' size='small'>{feeTypeId.actionValue}</Tag>}
                </div>
                {fromAIChat && <AITextIcon/>}
              </div>
              <div className="date" style={{ marginBottom: amount?.budgetStrCode ? '22px' : '' }}>
                {cityStr} {feeDateString}
              </div>
              <DetailItemExpandFields
                feeTypeForm={feeTypeForm}
                components={components}
                allTemplate={allTemplate}
                isRecordExpends={isRecordExpends}
              />
              <div className={styles['tip-group']}>
                <Space size={4} className={styles['tip-group-space']}>
                  {invoiceFormType ? (
                    <div
                      onClick={e => {
                        e.stopPropagation()
                        e.nativeEvent.stopImmediatePropagation()
                        return false
                      }}
                    >
                      <EUIPopover
                        overlayClassName={styles.popover_third_card + ' invoice-popover'}
                        overlayStyle={renderStyle(invoiceForm)}
                        title={renderTitle(invoiceForm)}
                        content={renderContent(
                          toJS(invoiceForm),
                          submitterId,
                          [],
                          isInvoiceManagePermissions,
                          onChange,
                          billState
                        )}
                      >
                        <IconTag color={getInvoiceTagColorByType(invoiceFormType)} text={<GetInvoiceStateText invoiceFormState={invoiceFormType} invoiceForm={invoiceForm}/>} />
                      </EUIPopover>
                    </div>
                  ) : null}
                  {invoiceData && (
                    <Popover
                      overlayClassName={styles.popover_invoice_card}
                      title={i18n.get('发票信息')}
                      content={invoiceContent}
                    >
                      <IconTag text={i18n.get('增值税发票')} />
                    </Popover>
                  )}
                  {showPayInfo && (
                    <EUIPopover overlayClassName={styles.popover_account_card} content={renderPayDetail()(feeDetailPayeeId)}>
                      <IconTag
                        src={feeDetailPayeeId?.icon}
                        className={styles['account-info']}
                        text={
                          <>
                            {feeDetailPayeeId?.accountName || feeDetailPayeeId?.name || '-'}
                            <span className="account-type">{i18n.get('（')}{getAccountType(feeDetailPayeeId?.type)}{i18n.get('）')}</span>
                          </>
                        }
                      />
                    </EUIPopover>
                  )}
                  {thirdOrder && (
                    <Popover overlayClassName={styles.popover_third_card} title={orderInfo} content={thirdCardContent}>
                      <IconTag text={orderInfo} />
                    </Popover>
                  )}
                  {thirdOrder && <IconTag text={getV(thirdOrder, 'form.payType')} />}
                  {ekbmallDataV1 && (
                    <Popover overlayClassName={styles.popover_third_card} title={orderInfo} content={ekbMallContentV1}>
                      <IconTag text={orderInfo} />
                    </Popover>
                  )}
                  {transactData && (
                    <Popover
                    overlayClassName={styles.popover_third_card}
                    title={i18n.get('公务卡交易记录')}
                    content={transactContent}
                  >
                    <IconTag text={i18n.get('公务卡')} />
                  </Popover>
                )}
                {map(attachMap, (v, key) => {
                  return (
                    <EUIPopover
                      key={key}
                      title={v.label}
                      content={<div onClick={handlePopoverClick}>{v.attachmentsContent}</div>}
                    >
                      <IconTag
                        color="success"
                        text={`${i18n.get('附件')}*${v.leng}`}
                      />
                    </EUIPopover>
                  )
                })}
              </Space>
            </div>
            {errorMessage && <div className="text-error">{errorMessage}</div>}
            </Space>
            <RiskWarningV2
              className={styles['risk-warning']}
              external={external}
              riskInfo={riskInfo}
              isForbid={isForbid}
              RiskPromptOptimization={RiskPromptOptimization}
            />
          </div>
          <div className="cost">
            {!allowSelectionReceivingCurrency && <div className={styles['currency-wrapper']}>
              <div className="money">{amount ? wordAndMoney(amount, true) : <span className={'fs-16'}>{i18n.get('无金额')}</span>}</div>
              {fnGetSharedTxt()}
            </div>}
          </div>
        </div>
        {/* @link https://hose2019.feishu.cn/wiki/wikcnBJnLq5N9bMA3PlrrWL3gZA */}
        {feeTypeForm.systemGenerationDetail && (
          <div className="is-system">
            <OutlinedGeneralIntelligentService style={{marginRight: 2}} fontSize={16} />
            {i18n.get('系统创建')}
          </div>
        )}
      </section>
      {renderRreceivingAmount()}
    </div>
  )
}

DetailBase.defaultProps = {
  source: {}, //每一条消费记录的数据
  isShowBorder: true //是否显示下边的border,最后一个不显示
}

export default DetailBase
