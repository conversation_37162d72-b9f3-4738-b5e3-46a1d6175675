@root-entry-name: 'default';
@import (reference) '~@hose/eui/es/components/style/themes/default.less';

@pro-table-prefix-cls: ~'@{eui-prefix}-pro-table';
@pro-column-setting-prefix-cls: ~'@{eui-prefix}-pro-table-column-setting';

.@{pro-table-prefix-cls} {
  &-group-name {
    position: sticky !important;
    left: 0;
    display: block;
    width: calc(var(--pro-table-width));

    &::after {
      display: none;
    }
  }

  // 下述样式设置中，-48px 是 pro-card 卡片内边距，-40px 是选择列的宽度，-1px 是单元格右边宽度
  .@{eui-prefix}-pro-card &-group-name {
    width: calc(var(--pro-table-width) - 48px);
  }

  .@{eui-prefix}-pro-card .@{eui-prefix}-table-selection-column+&-group-name {
    width: calc(var(--pro-table-width) - 48px - 40px);
  }

  .@{eui-prefix}-table-bordered &-group-name {
    width: calc(var(--pro-table-width) - 1px);
  }

  .@{eui-prefix}-table-bordered .@{eui-prefix}-table-selection-column+&-group-name {
    width: calc(var(--pro-table-width) - 1px - 40px);
  }

  .@{eui-prefix}-pro-card .@{eui-prefix}-table-bordered &-group-name {
    width: calc(var(--pro-table-width) - 48px - 1px);
  }

  .@{eui-prefix}-pro-card .@{eui-prefix}-table-bordered .@{eui-prefix}-table-selection-column+&-group-name {
    width: calc(var(--pro-table-width) - 48px - 1px - 40px);
  }
}

.@{pro-column-setting-prefix-cls} {
  width: auto;

  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .@{eui-prefix}-checkbox-wrapper {
      color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      font: var(--eui-font-body-r1);
    }
  }

  &-action-rest-button {
    color: var(--eui-primary-pri-500, #2555ff);
    font: var(--eui-font-body-r1);
  }

  &-overlay {
    width: 200px;
    padding: 4px 0;
    background: var(--eui-bg-float, #fff);
    border-radius: 6px;
    box-shadow: var(---eui-shadow-down-3);

    .@{eui-prefix}-popover-arrow {
      display: none;
    }

    .@{eui-prefix}-popover-inner {
      padding: 4px 0;
      border-radius: 8px;
    }

    .@{eui-prefix}-popover-title {
      padding: 6px 12px;
    }

    .@{eui-prefix}-popover-inner-content {
      padding: 0px 4px;
    }

    .@{eui-prefix}-popover-title {
      margin-bottom: 0;
    }

    .@{eui-prefix}-tree-node-content-wrapper {
      padding: 0;

      &:hover {
        background-color: transparent;
      }
    }

    .@{eui-prefix}-tree-draggable-icon {
      width: 16px !important;
      height: 16px !important;
      color: var(--eui-icon-n3);
      line-height: 16px !important;
      cursor: grab;
      opacity: 1 !important;
    }

    .@{eui-prefix}-tree-treenode {
      display: flex;
      flex: 1 0 0;
      align-items: center;
      padding: 4px 8px;
      border-radius: 4px;

      &:hover {
        background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));

        .@{pro-column-setting-prefix-cls}-list-item-option {
          display: block;
          color: var(--eui-primary-pri-500);
          font-size: 16px;
        }
      }

      .@{eui-prefix}-tree-switcher {
        display: none;
      }

      .@{eui-prefix}-tree-checkbox {
        top: 0;
        margin: 0 8px;
      }
    }
  }
}

.@{pro-column-setting-prefix-cls}-list {
  display: flex;
  flex-direction: column;
  width: 100%;

  &.@{pro-column-setting-prefix-cls}-list-group {
    padding-top: 0;
  }

  &-title {
    padding: 6px 8px;
    color: var(--eui-text-placeholder);
    font: var(--eui-font-body-b1);
  }

  &-item {
    display: flex;
    align-items: center;

    &-title {
      flex: 1;
    }

    &-option {
      display: none;
      float: right;
      cursor: pointer;

      >span {
        >span.anticon {
          color: @primary-color;
        }
      }

      >span+span {
        margin-left: 8px;
      }
    }
  }
}