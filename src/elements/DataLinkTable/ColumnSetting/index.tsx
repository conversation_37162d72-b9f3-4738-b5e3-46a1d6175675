import {
  OutlinedGeneralSetting,
  OutlinedDirectionSetRight,
  OutlinedDirectionCenterAlign,
  OutlinedDirectionSetLeft,
  OutlinedEditDrag,
} from '@hose/eui-icons';
import type { TableColumnType } from '@hose/eui';
import { Checkbox, ConfigProvider, Popover, Space, Tooltip, Button, Tree } from '@hose/eui';
import type { CheckboxChangeEvent } from '@hose/eui/es/components/checkbox';
import type { DataNode } from '@hose/eui/es/components/tree';
import classNames from 'classnames';

import omit from 'omit.js';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';


import './index.less';
export const genColumnKey = (key?: React.Key | undefined, index?: number): string => {
  if (key) {
    return Array.isArray(key) ? key.join('-') : key.toString();
  }
  return `${index}`;
};
type ColumnSettingProps<T = any> = {
  columns: TableColumnType<T>[];
  draggable?: boolean;
  checkable?: boolean;
  extra?: React.ReactNode;
  checkedReset?: boolean;
  children?: React.ReactNode;
  listsHeight?: number;
  filterColumns?: any[];
  onColumnsChange?: (columnsMap: any) => void;
  filterColumnsMap?: any;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
};

const ToolTipIcon: React.FC<{
  title: string;
  columnKey: string | number;
  show: boolean;
  fixed: 'left' | 'right' | boolean;
  children?: React.ReactNode;
  columnsMap: any;
  setColumnsMap: (columnsMap: any) => void;
}> = ({ title, show, children, columnKey, fixed, columnsMap, setColumnsMap }) => {
  if (!show) {
    return null;
  }
  return (
    <Tooltip title={title}>
      <span
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          const config = columnsMap[columnKey] || {};
          const disableIcon = typeof config.disable === 'boolean' && config.disable;
          if (disableIcon) return;
          const columnKeyMap = {
            ...columnsMap,
            [columnKey]: { ...config, fixed } as any,
          };
          setColumnsMap(columnKeyMap);
        }}
      >
        {children}
      </span>
    </Tooltip>
  );
};

const CheckboxListItem: React.FC<{
  columnKey: string | number;
  className?: string;
  title?: React.ReactNode;
  fixed?: boolean | 'left' | 'right';
  isLeaf?: boolean;
  columnsMap: any;
  setColumnsMap: (columnsMap: any) => void;
}> = ({ columnKey, isLeaf, title, className, fixed, columnsMap, setColumnsMap }) => {
  const dom = (
    <span className={`${className}-list-item-option`}>
      <ToolTipIcon
        columnKey={columnKey}
        fixed="left"
        title={i18n.get('固定在左侧')}
        show={fixed !== 'left'}
        columnsMap={columnsMap}
        setColumnsMap={setColumnsMap}
      >
        <OutlinedDirectionSetLeft />
      </ToolTipIcon>
      <ToolTipIcon
        columnKey={columnKey}
        fixed={false}
        title={i18n.get('不固定')}
        show={!!fixed}
        columnsMap={columnsMap}
        setColumnsMap={setColumnsMap}
      >
        <OutlinedDirectionCenterAlign />
      </ToolTipIcon>
      <ToolTipIcon
        columnKey={columnKey}
        fixed="right"
        title={i18n.get('固定在右侧')}
        show={fixed !== 'right'}
        columnsMap={columnsMap}
        setColumnsMap={setColumnsMap}
      >
        <OutlinedDirectionSetRight />
      </ToolTipIcon>
    </span>
  );
  return (
    <span className={`${className}-list-item`} key={columnKey}>
      <div className={`${className}-list-item-title`}>{title}</div>
      {!isLeaf && columnKey !== 'action' ? dom : null}
    </span>
  );
};

const CheckboxList: React.FC<{
  list: any[];
  className?: string;
  title: string;
  draggable: boolean;
  checkable: boolean;
  showTitle?: boolean;
  listHeight?: number;
  columnsMap: any;
  setColumnsMap: (columnsMap: any) => void;
  sortKeyColumns: any[];
  setSortKeyColumns: (sortKeyColumns: any[]) => void;
}> = ({
  list,
  draggable,
  checkable,
  className,
  showTitle = true,
  title: listTitle,
  listHeight = 280,
  columnsMap,
  setColumnsMap,
  sortKeyColumns,
  setSortKeyColumns,
}) => {
    const show = list && list.length > 0;

    const treeDataConfig = useMemo(() => {
      if (!show) return {};
      const checkedKeys: string[] = [];

      const loopData = (data: any[]): DataNode[] =>
        data.map(({ key, dataIndex, ...rest }) => {
          const columnKey = dataIndex;
          const config = columnsMap[columnKey] || {};
          if (config.show !== false) {
            checkedKeys.push(columnKey);
          }
          const item: DataNode = {
            key: columnKey,
            ...omit(rest, ['className']),
            selectable: true,
            disabled: dataIndex === 'action',
            disableCheckbox: dataIndex === 'action',
            icon: dataIndex === 'action' ? null : <OutlinedEditDrag />,
            isLeaf: false,
          };

          return item;
        });
      return { list: loopData(list), keys: checkedKeys };
    }, [list, show]);
    const onDrop = (info: any) => {
      const targetId = info.node.key;
      const id = info.dragNode.key;
      const { dropPosition, dropToGap } = info;
      const position = dropPosition === -1 || !dropToGap ? dropPosition + 1 : dropPosition;
      const newMap = { ...columnsMap };
      const newColumns = Array.from(sortKeyColumns);
      const findIndex = newColumns.findIndex((columnKey) => columnKey === id);
      const targetIndex = newColumns.findIndex((columnKey) => columnKey === targetId);
      const isDownWord = position > targetIndex;
      if (findIndex < 0) return;
      const targetItem = newColumns[findIndex];
      newColumns.splice(findIndex, 1);

      if (position === 0) {
        newColumns.unshift(targetItem);
      } else {
        newColumns.splice(isDownWord ? targetIndex : targetIndex + 1, 0, targetItem);
      }
      // 重新生成排序数组
      newColumns.forEach((key, order) => {
        newMap[key] = { ...(newMap[key] || {}), order };
      });
      // 更新数组
      setColumnsMap(newMap);
      setSortKeyColumns(newColumns);
    }

    /** 选中反选功能 */
    const onCheckTree: any = (checkInfo) => {

      const newColumnsMap = { ...columnsMap };
      const cKeys = checkInfo?.checkedNodes?.map(node => node.key);
      Object.keys(newColumnsMap).forEach(key => {
        if (cKeys.includes(key)) {
          newColumnsMap[key] = { ...newColumnsMap[key], show: true };
        } else if (list.find(item => item.dataIndex === key)) {
          newColumnsMap[key] = { ...newColumnsMap[key], show: false };
        }
      });
      setColumnsMap(newColumnsMap);
    };

    if (!show) {
      return null;
    }

    const listDom = (
      <Tree
        itemHeight={24}
        draggable={
          draggable && !!treeDataConfig.list?.length && treeDataConfig.list?.length > 1
            ? {
              icon: <OutlinedEditDrag />,
            }
            : false
        }
        checkable={checkable}
        onDrop={onDrop}
        blockNode
        onCheck={(_, e) => onCheckTree(e)}
        checkedKeys={treeDataConfig.keys}
        showLine={false}
        titleRender={(_node) => {
          const node = { ..._node, children: undefined };
          return (
            <CheckboxListItem
              className={className}
              {...node}
              title={node.title}
              columnKey={node.key}
              columnsMap={columnsMap}
              setColumnsMap={setColumnsMap}

            />
          );
        }}
        treeData={treeDataConfig.list}
      />
    );
    return (
      <>
        {showTitle && <span className={`${className}-list-title`}>{listTitle}</span>}
        {listDom}
      </>
    );
  };

const GroupCheckboxList: React.FC<{
  localColumns: any[];
  className?: string;
  draggable: boolean;
  checkable: boolean;
  listsHeight?: number;
  setColumnsMap: (columnsMap: any) => void;
  columnsMap: any;
  sortKeyColumns: any[];
  setSortKeyColumns: (sortKeyColumns: any[]) => void;
}> = ({ localColumns, className, draggable, checkable, listsHeight, columnsMap, setColumnsMap, sortKeyColumns, setSortKeyColumns }) => {
  const rightList: any[] = [];
  const leftList: any[] = [];
  const list: any[] = [];

  localColumns.forEach((item) => {
    /** 不在 setting 中展示的 */
    if (item.hideInSetting) {
      return;
    }
    const { fixed } = item;
    if (fixed === 'left') {
      leftList.push(item);
      return;
    }
    if (fixed === 'right' || item.dataIndex === 'action') {
      rightList.push(item);
      return;
    }
    list.push(item);
  });

  const showRight = rightList && rightList.length > 0;
  const showLeft = leftList && leftList.length > 0;
  return (
    <div
      className={classNames(`${className}-list`, {
        [`${className}-list-group`]: showRight || showLeft,
      })}
    >
      <CheckboxList
        title={i18n.get('固定在左侧')}
        list={leftList}
        draggable={draggable}
        checkable={checkable}
        className={className}
        listHeight={listsHeight}
        columnsMap={columnsMap}
        setColumnsMap={setColumnsMap}
        sortKeyColumns={sortKeyColumns}
        setSortKeyColumns={setSortKeyColumns}
      />
      {/* 如果没有任何固定，不需要显示title */}
      <CheckboxList
        list={list}
        draggable={draggable}
        checkable={checkable}
        title={i18n.get('不固定')}
        showTitle={showLeft || showRight}
        className={className}
        listHeight={listsHeight}
        columnsMap={columnsMap}
        setColumnsMap={setColumnsMap}
        sortKeyColumns={sortKeyColumns}
        setSortKeyColumns={setSortKeyColumns}
      />
      <CheckboxList
        title={i18n.get('固定在右侧')}
        list={rightList}
        draggable={draggable}
        checkable={checkable}
        className={className}
        listHeight={listsHeight}
        columnsMap={columnsMap}
        setColumnsMap={setColumnsMap}
        sortKeyColumns={sortKeyColumns}
        setSortKeyColumns={setSortKeyColumns}
      />
    </div>
  );
};

function ColumnSetting<T>(props: ColumnSettingProps<T>) {
  const { columns, filterColumnsMap, onColumnsChange, children, open: openProps, onOpenChange } = props
  const columnRef = useRef({});
  const [columnsMap, setColumnsMap] = useState({});
  const [sortKeyColumns, setSortKeyColumns] = useState([]);
  const [localColumns, setLocalColumns] = useState([]);
  const [unCheckedKeys, setUnCheckedKeys] = useState([]);
  const [open, setOpen] = useState(openProps);
  useEffect(() => {
    if (!open) return
    let sortedColumns = columns;
    const defaultColumnsMap = columns.reduce((acc, item: any, index: number) => {
      acc[item.dataIndex] = {
        show: true,
        fixed: item.fixed,
        order: index
      }
      return acc
    }, {})

    const fMap = Object.keys(filterColumnsMap || {})?.length ? filterColumnsMap : defaultColumnsMap
    sortedColumns = columns.sort((x: any, y: any) => {
      const ox = fMap[x.dataIndex]?.order ?? Number.MAX_SAFE_INTEGER;
      const oy = fMap[y.dataIndex]?.order ?? Number.MAX_SAFE_INTEGER;
      return ox - oy;
    })
    const unCheckedList = []
    const sortedKeys = []
    sortedColumns.forEach((item: any) => {
      if (!fMap[item.dataIndex]?.show) {
        unCheckedList.push(item.dataIndex)
      }
      sortedKeys.push(item.dataIndex)
    })
    setUnCheckedKeys(unCheckedList)
    columnRef.current = defaultColumnsMap
    setLocalColumns(sortedColumns)
    setSortKeyColumns(sortedKeys);
    setColumnsMap(fMap)
  }, [columns, open]);

  useEffect(() => {
    const showList = []
    const unCheckedList = []
    if (sortKeyColumns.length > 0 && localColumns.length > 0) {
      const sortedColumns = localColumns.sort((x: any, y: any) => {
        const ox = columnsMap[x.dataIndex]?.order ?? Number.MAX_SAFE_INTEGER;
        const oy = columnsMap[y.dataIndex]?.order ?? Number.MAX_SAFE_INTEGER;
        return ox - oy;
      })

      // 根据 columnsMap 更新 fixed 属性
      const updatedColumns = sortedColumns.map(column => {
        const columnConfig = columnsMap[column.dataIndex];
        if (!columnConfig?.show) {
          unCheckedList.push(column.dataIndex)
        } else {
          showList.push(column.dataIndex)
        }
        if (columnConfig && columnConfig.fixed !== undefined) {
          return {
            ...column,
            fixed: columnConfig.fixed
          };
        }
        return column;
      });
      setUnCheckedKeys(unCheckedList)
      setLocalColumns(updatedColumns);
    }

  }, [sortKeyColumns, columnsMap]);


  const setAllSelectAction = (show: boolean = true) => {
    const columnKeyMap = {};
    const loopColumns = (columns: any) => {
      columns.forEach(({ fixed, dataIndex }: any) => {
        columnKeyMap[dataIndex] = {
          show: dataIndex !== 'action' ? show : true,
          fixed,
        };
      });
    };
    loopColumns(localColumns);
    setColumnsMap(columnKeyMap);
  }

  /** 全选和反选 */
  const checkedAll = (e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      setAllSelectAction();
    } else {
      setAllSelectAction(false);
    }
  }

  /** 重置项目 */
  const clearClick = () => {
    setColumnsMap(columnRef.current);
  }

  const saveClick = (open: boolean) => {
    if (!open) {
      onColumnsChange?.(columnsMap);
    }
    onOpenChange?.(open)
    setOpen(open)
  }

  const indeterminate = unCheckedKeys.length > 0 && unCheckedKeys.length !== localColumns.length
  const { getPrefixCls } = useContext(ConfigProvider.ConfigContext);
  const className = getPrefixCls('pro-table-column-setting');
  return (
    <Popover
      title={
        <div className={`${className}-title`}>
          <Checkbox
            indeterminate={indeterminate}
            checked={unCheckedKeys.length === 0 && unCheckedKeys.length !== localColumns.length}
            onChange={(e) => checkedAll(e)}
          >
            {i18n.get('列展示')}
          </Checkbox>
          <Space size={8} align="center">
            <a onClick={clearClick} className={`${className}-action-rest-button`}>
              {i18n.get('重置')}
            </a>
          </Space>
        </div>
      }
      overlayClassName={`${className}-overlay`}
      trigger="click"
      placement="bottomRight"
      open={open}
      onOpenChange={saveClick}
      content={
        <GroupCheckboxList
          checkable={true}
          draggable={true}
          className={className}
          localColumns={localColumns}
          listsHeight={props.listsHeight}
          setColumnsMap={setColumnsMap}
          columnsMap={columnsMap}
          sortKeyColumns={sortKeyColumns}
          setSortKeyColumns={setSortKeyColumns}
        />
      }
    >
      {children || <Button category="text" icon={<OutlinedGeneralSetting />} onClick={() => setOpen(true)}>
        {i18n.get('列设置')}
      </Button>}
    </Popover>
  );
}

export default ColumnSetting;
