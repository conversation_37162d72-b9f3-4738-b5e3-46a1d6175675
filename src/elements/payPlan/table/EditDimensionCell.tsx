/*
 * @Author: Onein 
 * @Date: 2019-09-20 17:43:49 
 * @Last Modified by: Onein
 * @Last Modified time: 2020-10-23 18:14:34
 */

import React from 'react'
import './EditDimensionCell.less'
import PayPlanStore from './table.store'
import CreditTableStore from './credit.store'
import { formatDisplayValue } from '../helper/formatTableData'
import { ObjFormIF, FieldType } from '../types'
import { funcMap, getColumnType } from '../helper/tableHelper'
import { observer } from 'mobx-react'
import classnames from 'classnames'
import { isObject } from '@ekuaibao/helpers'

interface Props {
  currentSpecification?: any
  editable: boolean
  dataIndex: string
  entity: string
  type: FieldType
  payPlanStore: PayPlanStore | CreditTableStore
  value: any
  title: React.ReactNode
  record: ObjFormIF
  idx: number
  flowId: string
  isModify?: boolean
  model?: string
  billSpecification?: any
}

interface State {
  editing: boolean
}

@observer
export default class EditDimensionCell extends React.Component<Props, State> {
  handleClick = () => {
    const { type, entity, payPlanStore, title, dataIndex, flowId, editable, isModify, value, billSpecification } = this.props
    const itemType = getColumnType(entity || type)
    // @ts-ignore
    const func = funcMap[itemType]
    const templateid = billSpecification.id
    const { record } = this.props
    return func({ flowId, isModify, templateid, data: value }).then((result: any) => {
      if (result && result.id) {
        const { key } = record
        payPlanStore.updateOneObjValue(key, dataIndex, result)
      }
    })
  }

  render() {
    const { value, type, dataIndex, model, editable, isModify } = this.props
    const displayValue = formatDisplayValue(value, type, dataIndex, model)
    const isCredit = model === 'credit' ? (!isModify ? isModify : editable) : isModify
    const click = isCredit ? this.handleClick : void 0
    return (
      <div className={classnames('edit_dimension_cell_wrapper', {'hover': isCredit, 'cur-p': isCredit })} onClick={click} data-testid="pay-plan-dimension-cell">
        {displayValue}
      </div>
    )
  }
}
