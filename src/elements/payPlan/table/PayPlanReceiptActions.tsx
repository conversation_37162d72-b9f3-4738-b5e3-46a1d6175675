/**************************************
 * Created By LinK On 2021/2/2 10:32.
 **************************************/
import React from 'react'
import styles from './PayPlanReceiptActions.module.less'
import { downloadReceipt, printReceipt, previewReceipt } from '../helper/fetchUtil'
import { ObjFormIF } from '../types'
import { getV } from '@ekuaibao/lib/lib/help'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'

interface Props {
  receiptArr: any
  dataSource: any
  record: ObjFormIF
  idx: number
  BankReceiptPdfModel?: any
}
@EnhanceConnect(state => ({
  BankReceiptPdfModel: state['@common'].powers.BankReceiptPdfModel
}))
export default class PayPlanReceiptActions extends React.Component<Props, any> {
  openModal = () => {
    const { record, BankReceiptPdfModel } = this.props
    api.open('@bills:ReceiptFileModal', {
      payPlanId: record.key,
      ids: record.receiptIds,
      BankReceiptPdfModel,
      line: record
    })
  }
  handlePreview = () => {
    const { record } = this.props
    printReceipt(record.receiptIds)
  }
  handleDowload = () => {
    const { record } = this.props
    downloadReceipt(record.receiptIds)
  }
  handlePrint = () => {
    const { record } = this.props
    printReceipt(record.receiptIds)
  }

  handlePreviewNTSC = () => {
    const ids = getV(this.props, 'record.receiptIds', [])
    if (ids.length > 0) {
      previewReceipt(ids[0], this.props.BankReceiptPdfModel)
    }
  }

  renderPreview = () => {
    const { dataSource, receiptArr } = this.props
    if (!(receiptArr.length > 0)) {
      return <span />
    }
    const paymentChannel = getV(dataSource, 'form.paymentChannel')
    if (paymentChannel === 'NSTC') {
      return <span onClick={this.handlePreviewNTSC} data-testid="pay-plan-preview-ntsc">{i18n.get('预览')}</span>
    }
    return <span onClick={this.openModal} data-testid="pay-plan-preview">{i18n.get('查看')}</span>
  }

  render() {
    const { receiptArr } = this.props
    if (!receiptArr) {
      return null
    }
    return <div className={styles['PayPlanReceiptActions-wrapper']}>{this.renderPreview()}</div>
  }
}
