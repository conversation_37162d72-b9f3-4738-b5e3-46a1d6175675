import React from 'react'
import { Input } from '@hose/eui'
import TagSelector from './tag-selector'

class InputSelector extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      key: props.value ? props.value.key : '',
      value: props.value ? props.value.value : '',
      tag: props.tag
    }
    this.handleInputClick = this.handleInputClick.bind(this)
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      key: nextProps.value ? nextProps.value.key : '',
      value: nextProps.value ? nextProps.value.value : '',
      tag: nextProps.value?.tag
    })
  }

  handleInputClick() {
    if (this.props.onSelect) {
      this.props.onSelect(value => {
        this.setState({ ...value }, () => {
          this.props.onChange && this.props.onChange(value)
        })
      })
    }
  }

  handleTagRemove = () => {
    const value = {key: '', value: undefined, tag: null}
    this.setState(value)
    this.props.onChange?.(value)
  }

  render() {
    // tagShow 控制是否用tag样式展示
    let { pass = { isPass: false }, placeholder, disabled, noticeDom, tagShow, closable } = this.props
    return (
      <div className={this.props.className}>
        {tagShow ? (
          <TagSelector
            value={this.state.tag ? [this.state.tag] : []}
            onClick={this.handleInputClick}
            showAvatar={true}
            placeholder={placeholder}
            editable={!pass.isPass && !disabled}
            onChange={this.handleTagRemove}
            closable={closable}
            id={this.props.id}
          />
        ) : (
          <Input
            style={{ width: '100%', cursor: 'pointer' }}
            disabled={pass.isPass || disabled}
            value={this.state.value}
            onClick={this.handleInputClick}
            data-testid={this.props.id ? `input-selector-${this.props.id}` : undefined}
            onFocus={e => {
              e.target.blur()
            }}
            placeholder={placeholder}
          />
        )}
        {!!noticeDom && noticeDom}
      </div>
    )
  }
}

export default InputSelector
