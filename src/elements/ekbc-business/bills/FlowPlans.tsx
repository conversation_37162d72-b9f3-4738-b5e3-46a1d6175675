import React, { useEffect, useState } from 'react'
import classnames from 'classnames'
import { app as api } from '@ekuaibao/whispered'
import EkbIcon from '../../ekbIcon'
import CountersignAvatar from '../../countersign/countersign-avatar-config'
import SkipTag from '../../skip-tag'
import SVG_USER_PIC from '../../../images/no-user-avator.svg'
import SVG_USER_PIC_SQUARE from '../../../images/no-user-avator-square.svg'
import { CountersignTooltip } from '../../countersign/countersign-selector'
import { getCounterProgress, isAllCountersignerAgree } from '@ekuaibao/lib/lib/lib-util'
import { Popover } from 'antd'
import { Tooltip } from '@hose/eui'
import { isString } from '@ekuaibao/helpers'
import { getStaffName, isPayNode } from "./../../utilFn";
import { get } from 'lodash'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import { getAIAgentObj, fetchNodesAIAgentMap, getAIAgentLabelDom } from '../../ai-agent-utils'
export interface FlowPlansProps {
  nodes: any[]
  logs?: any[]
  taskId?: any
  nodeClick?: (node: any) => void
  handleSelectCountersigners?: () => void
  selectCountersigners?: any[]
  handleModifiedStaff?: (node: any) => void
  isEditConfig: boolean
  isAlterFlag?: boolean
  staffDisplayConfigField?: any
  isSimpleMode?: boolean
  nodesAIAgentMap?: any
}

export const FlowPlans: React.FunctionComponent<FlowPlansProps> = props => {
  const [nodesAIAgentMap, setNodesAIAgentMap] = useState(props.nodesAIAgentMap || {})
  const {
    nodes,
    taskId,
    nodeClick,
    logs,
    selectCountersigners,
    handleSelectCountersigners,
    handleModifiedStaff,
    isEditConfig,
    // 处于变更中的申请单 审批流程是置空状态
    isAlterFlag,
    staffDisplayConfigField,
    isSimpleMode
  } = props

  useEffect(() => {
    api.dataLoader('@common.authStaffStaffMap').load()
  }, [])

  const fnGetCountersignerAvatar = node => {
    const counterSigners =
      selectCountersigners && selectCountersigners.length ? selectCountersigners : node.counterSigners
    const imageList = counterSigners.map(line => {
      return line.signerId.avatar && line.signerId.avatar.length ? line.signerId.avatar : SVG_USER_PIC_SQUARE
    })
    return imageList.length ? imageList : [SVG_USER_PIC_SQUARE]
  }
  const fnGetCarbonCopyAvatar = node => {
    const staffIds = get(node, 'carbonCopy[0].staffIds', [])
    const staffMap = api.getState()['@common']?.authStaffStaffMap || {}
    const staffs = staffIds.map(id => staffMap[id])
    return staffs.length ? staffs.map(staff => staff?.avatar || SVG_USER_PIC_SQUARE) : [SVG_USER_PIC_SQUARE]
  }
  const renderNodeImgae = (node: any) => {
    if (isSimpleMode) {
      if (node.stateEx === 'done') {
        return <div />
      }
      return <EkbIcon name="#EDico-xingzhuangjiehe" style={{ width: 20, height: 20 }} />
    }
    const type = get(node, 'type')
    if (type === 'countersign') {
      return <CountersignAvatar imageList={fnGetCountersignerAvatar(node)} />
    }
    if (type === 'carbonCopy') {
      return <CountersignAvatar imageList={fnGetCarbonCopyAvatar(node)} />
    }
    if (type === 'ebot' || type === 'invoicingApplication') {
      return <EkbIcon name="#EDico-EBot-AI" style={{ width: 48, height: 48 }} />
    }
    if (type === 'aiApproval' && node?.ebotConfig?.setting?.aiAgent ) {
      const {avatar} = getAIAgentObj(node,nodesAIAgentMap)
      return avatar
    }
    if (type === 'recalculate') {
      return <EkbIcon name="#EDico-sys-recount" style={{ width: 48, height: 48 }} />
    }
    return <img width="48" height="48" src={(node.approverId && node.approverId.avatar) || SVG_USER_PIC} />
  }

  const showNodes = nodes.filter(
    line => get(line, 'ebotConfig.type') !== 'costControlCheck' && get(line, 'config.isNeedCashierNode', true)
  )

  useEffect(() => {
    (async () => {
      if(!props.nodesAIAgentMap){
        const map = await fetchNodesAIAgentMap(nodesAIAgentMap, nodes, logs)
        setNodesAIAgentMap(map)
      } else {
        setNodesAIAgentMap(props.nodesAIAgentMap)
      }
    })()
  }, [nodes, props.nodesAIAgentMap])
  const renderCountersign = (node: any, flowLogs: any[]) => {
    const counterSigners =
      selectCountersigners && selectCountersigners.length ? selectCountersigners : node.counterSigners
    let counterSigner
    if (node.stateEx === 'done' && node.policy === 'ANY' && node.policy === 'ALL') {
      counterSigner = counterSigners.find(line => line.state === 'PROCESSED')
    }
    return (
      <div className={'dis-f'}>
        <CountersignTooltip
          node={node}
          flowLogs={flowLogs}
          getSelectCounterSigners={handleSelectCountersigners}
          staffDisplayConfigField={staffDisplayConfigField}
          component={
            <div className="countersign">
              <span className="count">
                {counterSigner
                  ? counterSigner.signerId.name
                  : i18n.get('counterSignNode', { count: counterSigners.length })}
              </span>
              <span className="progress">
                {node.policy === 'ALL' &&
                  !isAllCountersignerAgree(counterSigners) &&
                  `(${getCounterProgress(counterSigners)})`}
              </span>
            </div>
          }
        />
        {renderUserApproveTip(node)}
      </div>
    )
  }

  const renderStaff = (node: any, flowLogs: any[]) => {
    if (node.skippedType !== 'NO_SKIPPED') {
      return null
    }
    const isSubmitterChoice = get(node, 'config.isSubmitterChoice')
    const isEdit = !isSubmitterChoice && isEditConfig
    if (isEdit && !['ebot', 'recalculate', 'invoicingApplication', 'aiApproval'].includes(node.type)) {
      return node.stateEx ? (
        <>
          {renderUserName(node, flowLogs)}
          {renderUserDisplayConfig(node)}
        </>
      ) : (
        <>
          <a className="user-name-container" onClick={() => handleModifiedStaff(node)}>
            {node.approverId ? getStaffName(node.approverId) : i18n.get('选择审批人')}
          </a>
          {node?.approverId?.name && renderUserDisplayConfig(node)}
        </>
      )
    } else {
      return (
        <>
          {renderUserName(node, flowLogs)}
          {renderUserDisplayConfig(node)}
        </>
      )
    }
  }

  const renderSignature = url => {
    return <img className="w-240 h-140" src={url} />
  }

  const getName = line => {
    const { type, crossCorpNode } = line
    if (type === 'ebot') {
      return 'EBot'
    }
    if (type === 'invoicingApplication') {
      return i18n.get('开票申请')
    }
    if (type === 'recalculate') {
      return i18n.get('重算节点')
    }
    if (type === 'aiApproval' && line?.ebotConfig?.setting?.aiAgent) {
      const {agent} = getAIAgentObj(line, nodesAIAgentMap)
      return agent?.name
    }
    if (crossCorpNode) {
      const {
        approverId,
        config: { crossCorpApprovers = [] }
      } = line
      const id = isString(approverId) ? approverId : approverId.id
      const item = crossCorpApprovers.find(v => v.id === id)
      return item?.name || i18n.get('未选择')
    }
    return line.approverId ? getStaffName(line.approverId) : i18n.get('未选择')
  }

  const getStaffDisplayConfig = (line, field, staff = undefined) => {
    if (staff) {
      return staff[field] || ''
    }

    const { crossCorpNode } = line

    if (crossCorpNode) {
      const {
        approverId,
        config: { crossCorpApprovers = [] }
      } = line
      const id = isString(approverId) ? approverId : approverId?.id
      const item = crossCorpApprovers.find(v => v.id === id)
      return item?.[field] || ''
    }
    return line?.approverId?.[field] || ''
  }

  const renderUserName = (node: any, flowLogs: any[]) => {
    const curLogs = flowLogs.filter(o => o.attributes && o.attributes.nodeId && o.attributes.nodeId === node.id)
    const curLog = curLogs.length > 0 ? curLogs[curLogs.length - 1] : {}
    const name = getName(node)
    // 前加签、转交不显示签名 后加签才显示
    const isShow =
      (curLog.action === 'freeflow.addnode' && curLog.attributes.addNodeType === 'AFT_ADD_NODE') ||
      curLog.action !== 'freeflow.addnode'
    return (
      <div className="user-name-container">
        <div className="user-name">{name}</div>
        {isShow && curLog.attributes && curLog.attributes.autographImageId && !isAlterFlag && (
          <Popover placement="bottomLeft" content={renderSignature(curLog.attributes.autographImageId.url)}>
            <div className="user-signature">
              {i18n.get('（')}
              {i18n.get('签名')}
              {i18n.get('）')}
            </div>
          </Popover>
        )}
        {renderUserApproveTip(node)}
      </div>
    )
  }

  const renderUserDisplayConfig = (node: any, staff = undefined) => {
    const { staffDisplayConfigField } = props
    const displayConfig = getStaffDisplayConfig(node, staffDisplayConfigField, staff)

    return (
      <div className="user-display-container">
        {displayConfig && isString(displayConfig) && (
          <Tooltip placement="bottom" title={displayConfig}>
            <div className="user-display">{displayConfig}</div>
          </Tooltip>
        )}
      </div>
    )
  }

  const renderUserApproveTip = node => {
    const approveMsg = {
      REJECT_FORCE_APPROVALS: '驳回',
      ROLLBACK_FORCE_APPROVALS: '回退',
      EDIT_FORCE_APPROVALS: '修改'
    }
    const msg = approveMsg[node.agreeType]
    if (!msg) {
      return null
    }
    return (
      <Tooltip placement="bottom" title={i18n.get(`因单据被${msg}，此节点需再次审批`)}>
        <OutlinedTipsInfo className={'flow-node-user-approve-tip'} />
      </Tooltip>
    )
  }

  const renderCondition = (node: any) => {
    const userInfo = api.getState('@common.userinfo.data')
    let conditionText = node.conditionalDescription
    if (node.approverId && conditionText) {
      conditionText =
        userInfo.staff.id === node.approverId.id
          ? conditionText.replace(i18n.get('需要此环节审批'), i18n.get('需要您审批'))
          : conditionText
    }
    return (
      node.conditionalDescription && (
        <Tooltip arrowPointAtCenter placement="bottomLeft" title={conditionText}>
          <div className="conditionWrapper">
            <div className="condition">{i18n.get('条件')}</div>
          </div>
        </Tooltip>
      )
    )
  }

  const renderCarbonCopy = (node, logs) => {
    const staffIds = get(node, 'carbonCopy[0].staffIds', [])
    const staffMap = api.getState()['@common']?.authStaffStaffMap || {}
    const staffs = staffIds.map(id => staffMap[id])
    if (staffs.length > 1) {
      const customStaffs = staffs.map(staff => { return { signer: staff } })
      return (
        <CountersignTooltip
          node={node}
          flowLogs={logs}
          staffDisplayConfigField={staffDisplayConfigField}
          customStaffs={customStaffs}
          customDesc={i18n.get('抄送至{__k0}名成员', { __k0: staffs.length })}
          component={
            <div className="countersign">
              <span className="count">
                {i18n.get('抄送{__k0}人', { __k0: staffs.length })}
              </span>
            </div>
          }
        />
      )
    } else {
      return <>
        <div className="user-name-container">
          <div className="user-name">{getStaffName(staffs[0])}</div>
        </div>
        {renderUserDisplayConfig(node, staffs[0])}
      </>
    }
  }

  const renderName = (node, logs) => {
    if (node.type === 'countersign') {
      return renderCountersign(node, logs)
    } else if (node.type === 'carbonCopy') {
      return renderCarbonCopy(node, logs)
    } else {
      return renderStaff(node, logs)
    }
  }

  return (
    <div className={!isSimpleMode ? 'flow-config-wrap' : 'flow-config-wrap simple-mode'} data-platform-wx2-hidden={window.isInWeComISV}>
      <div className="flow-config-con">
        {showNodes.map(node => {
          const { skippedType, type } = node
          if ((type === 'ebot' || type === 'invoicingApplication' || type === 'aiApproval') && node.ebotConfig && node.ebotConfig.hiddenNode) {
            if ((node?.ebotConfig?.hiddenModule || ['feeflow']).includes('feeflow')) {
              return <></>
            }
          } else if (type === 'recalculate' && node.config && node.config.hiddenNode) {
            return <></>
          } else if (skippedType !== 'NO_SKIPPED' && node.config && node.config.hiddenNode) {
            return <></>
          }
          let showConditionInBill = get(node, 'config.showConditionInBill')
          showConditionInBill = showConditionInBill === undefined ? true : showConditionInBill
          // @i18n-ignore
          const name = isPayNode(node) ? (node.label ? node.label : node.name) : node.name
          let langName = (i18n.currentLocale === 'en-US' && node?.enName) ? node.enName : name
          const tipName = langName
          if(node.type === 'aiApproval'){
            langName = getAIAgentLabelDom(langName)
          }
          const staffs = get(node, 'carbonCopy[0].staffIds', [])
          const showSkip = node?.type === 'carbonCopy' ? staffs.length === 0 : true

          return (
            <div
              key={node.id}
              className={classnames('flow-config-repeat', node.stateEx ? node.stateEx : 'normal', {
                current: node.id === taskId
              })}
              onClick={nodeClick && nodeClick.bind(null, node)}
            >
              <div className="user-pic">
                <div className={classnames('user-pic-img', { 'user-pic-radius': node.type !== 'recalculate' })}>
                  {renderNodeImgae(node)}
                </div>
                <div className="user-status" />
                {node.stateEx === 'now' && !isAlterFlag && <EkbIcon name={'#icon-icon_more'} />}
                {node.stateEx === 'done' && !isAlterFlag && <EkbIcon name="#icon-icon_suc" />}
              </div>
              <Tooltip placement="bottom" title={tipName}>
                <div className={classnames('flow-name', { 'ai-agent-name': node.type === 'aiApproval' && node.stateEx})}>{langName}</div>
              </Tooltip>
              {node.id ? (
                <>
                  {renderName(node, logs)}
                  {showSkip && <SkipTag {...node} />}
                  {showConditionInBill && renderCondition(node)}
                </>
              ) : (
                <>
                  {renderUserName(node, [])}
                  {renderUserDisplayConfig(node)}
                </>
              )}
              {showNodes.length > 1 && <div className="flow-config-bg" />}
            </div>
          )
        })}
      </div>
    </div>
  )
}
