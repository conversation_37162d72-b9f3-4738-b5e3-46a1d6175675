import React, { ReactNode } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

import styles from './FlowLogItemWrapper.module.less'
import { Popover, Avatar, Tooltip, Ellipsis, Popconfirm, Button, message, Tag, Checkbox, Divider } from '@hose/eui'
import {
  FilledGeneralRecord,
  FilledGeneralPosition,
  FilledGeneralIntelligentService,
  OutlinedEditDeleteTrash,
  OutlinedGeneralAttachment,
  OutlinedGeneralSignature1
} from '@hose/eui-icons'
import classNames from 'classnames'
import Highlighter from 'react-highlight-words'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { comment2MentionContent, formatRegStr, containsMarkdownSyntax } from './util/parse'
import { getMoney } from '../../../lib/misc'
import { skippedTypeMap, agreeType, printLogMap } from '@ekuaibao/lib/lib/enums'
import AttachmentList from '../../puppet/attachmentList/AttachmentList'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import EkbIcon from '../../../elements/ekbIcon'
import { getDisplayName, getStaffShowByConfig, isPayNode, getStaffName as getStaffNameOfConfig } from './../../utilFn'
import Big from 'big.js'
import { isString } from '@ekuaibao/helpers'
import { CountersignTooltip } from '../../countersign/countersign-selector'
import CountersignAvatar from '../../countersign/countersign-avatar-config'
import { PayeeInfoPopover } from './FlowPlanLog/FlowPlanViewHelper'
import { skipTypeMap } from '../../../lib/enums'
import moment from 'moment'
import { deleteComment } from '../../../plugins/bills/bills.action'
import { getBoolVariation } from '../../../lib/featbit'
import { getAIAgentObj, isAIAgentNode, getSkippedNodeStaff } from '../../ai-agent-utils'
import { NameCell } from '../../name-cell'
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')
const download = api.invokeServiceAsLazyValue('@bills:file:download')
export const ModifyCreateType = 'CREATE'
const ModifyBillAction = 'freeflow.modify'
let lastVersionNum = 0
const emptyStr = '' // @i18n-ignore
const EBotName = 'Ebot'

function fnFilePreview(attachmentsData, clickItem) {
  preview()({ value: attachmentsData, line: clickItem })
}

function fnFileDownload(line) {
  download()(line)
}

const formatAttachments = attachments => {
  const items = fnFormatAttachment(attachments)
  return {
    length: items.length,
    attachmentsContent: (
      <AttachmentList
        onFilePreview={fnFilePreview.bind(this, items)}
        onFileDownload={fnFileDownload}
        fileList={items}
      />
    ),
    label: i18n.get('附件')
  }
}

// 预处理markdown文本，将表格、分隔符等保持为原始格式
const preprocessMarkdown = (text) => {
  if (!text) return text

  let processedText = text

  // 1. 处理markdown表格
  const tableRegex = /^\|[^\r\n]*\|[\r\n]+\|[-:\s|]+\|[\r\n]+((?:\|[^\r\n]*\|[\r\n]*)*)/gm
  processedText = processedText.replace(tableRegex, (match) => {
    return `\`\`\`\n${match}\`\`\`\n`
  })

  // 2. 处理分隔符 - 横线分隔符
  const separatorRegex = /^([-*]{3,}|_{3,})$/gm
  processedText = processedText.replace(separatorRegex, (match) => {
    return `\`\`\`\n${match}\n\`\`\``
  })

  return processedText
}
const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
const riskMarkdownComponents = {
  // 加粗所有标题
  ...Object.fromEntries(
    headingTags.map(tag => [tag, ({ node, ...props }) => <div style={{ fontWeight: 'bold' }} {...props} />])
  ),
  // 行内代码样式
  code: ({ node, inline, className, children, ...props }) => {
    if (inline) {
      return (
        <code {...props}>
          <Tag size="small">{children}</Tag>
        </code>
      )
    }

    // 非行内代码块渲染为纯文本
    return (
      <div
        style={{
          whiteSpace: 'pre-wrap',
          fontFamily: 'monospace',
          backgroundColor: 'transparent'
        }}
        {...props}
      >
        {children}
      </div>
    )
  },
  img: ({ src, alt }) => <span>{`![${alt}](${src})`}</span>,
  a: ({ children, ...props }) => (
    <a
      {...props}
      href="javascript:void(0)"
      onClick={() => {
        api.sdk.openLink(props.href)
      }}
    >
      {children}
    </a>
  ),
  ol: ({ children }) => (
    <ul style={{ paddingLeft: '12px' }}>
      {children
        .filter(child => child?.type === 'li')
        .map((child, index) => (
          <li key={index} style={{ display: 'flex' }}>
            <span style={{ marginRight: '2px' }}>{index + 1}. </span>
            {child}
          </li>
        ))}
    </ul>
  ),
  ul: ({ children }) => (
    <ul style={{ paddingLeft: '12px' }}>
      {children
        .filter(child => child?.type === 'li')
        .map((child, index) => {
          const isTask = get(child, 'props.children[0].type', '') === 'input' // 代办项
          const isCheckedTask = isTask && get(child, 'props.children[0].props.checked', false) // 已完成的代办项
          const taskText = isTask && get(child, 'props.children[2]', '')
          return (
            <li key={index} style={{ display: 'flex' }}>
              {isTask ? (
                <>
                  <Checkbox defaultChecked={isCheckedTask} disabled>
                    <span
                      style={{
                        textDecoration: isCheckedTask && 'line-through',
                        color: !isCheckedTask && 'var(--eui-text-caption)',
                        font: 'var(--eui-font-note-r2)'
                      }}
                    >
                      {taskText}
                    </span>
                  </Checkbox>
                </>
              ) : (
                <>
                  <span style={{ marginRight: '4px' }}>• </span>
                  {child}
                </>
              )}
            </li>
          )
        })}
    </ul>
  )
}

export function renderCommentWithMarkdown(comment, element, options = {}) {
  const isRisk = options?.type === 'risk'
  const markdownComponents = isRisk ? riskMarkdownComponents : null
  const showComment = isRisk ? preprocessMarkdown(comment) : comment

  try {
    if (isRisk) {
      if (!getBoolVariation('ao-46-bill-risk-markdown')) {
        return element
      }
    } else {
      if (!getBoolVariation('ao-46-markdown-render')) {
        return element
      }
    }

    return containsMarkdownSyntax(comment, isRisk) ? (
      <div className={styles['markdown-container']}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={markdownComponents || {
            a: ({ node, ...props }) => <a {...props} href="javascript:void(0)" onClick={() => {
              api.sdk.openLink(props.href)
            }} />,
            h1: ({ node, ...props }) => <h1 className={styles['markdown-h1']} {...props} />
          }}
        >
          {showComment}
        </ReactMarkdown>
      </div>
    ) : (
      element
    )
  } catch (error) {
    return element
  }
}

export const renderComment = item => {
  let {
    attributes: { participants = [], comment }
  } = item
  participants = participants ? participants : []
  const com = comment2MentionContent(comment, participants, flowStateMap.staffDisplayConfigField)
  let highlighterWords = participants.map(item => ` @${item.name} `)
  highlighterWords = parseReg(highlighterWords)
  const element = (
    <Highlighter highlightClassName={styles['highlight']} searchWords={highlighterWords} textToHighlight={com} />
  )
  return renderCommentWithMarkdown(com, element)
}

export const renderAttachments = (attachments, props = {}) => {
  const data = formatAttachments(attachments)
  return (
    <Popover title={data.label} content={data.attachmentsContent} placement={props.placement}>
      <div className={classNames(styles['attachment-wrapper'], props.className, 'cur-p mr-8')}>
        <OutlinedGeneralAttachment fontSize={12} className="mr-4" />
        {i18n.get(`{__k0} 个{__k1}`, { __k0: data.length, __k1: data.label })}
      </div>
    </Popover>
  )
}

type AutographImageType = {
  id: string
  key: string
  lastHabit: string
  name: string
  platform: string
  regionUrl: string
  thumbUrl: string
  url: string
  use: string
}

export const AutographImage = React.memo((props: { autographImageId: AutographImageType }) => {
  const { autographImageId } = props
  const style = { width: 204, height: 115, borderRadius: 4 }
  const content = <img style={style} src={autographImageId.url} alt={i18n.get('电子签名')} />

  return (
    <Popover placement="bottomLeft" content={content}>
      <div className={classNames(styles['attachment-wrapper'], 'cur-p')}>
        <OutlinedGeneralSignature1 fontSize={12} className="mr-4" />
        {i18n.get('签名')}
      </div>
    </Popover>
  )
})

const renderVersion = (item, click) => {
  const action = () => {
    click && click(item)
  }
  const versionConfig = api.getState('@common.specificationVersionConfig')
  if (versionConfig && versionConfig.hideVersion) {
    return null
  }
  return (
    <div className={styles['version-wrapper']} onClick={action}>
      {i18n.get('查看')}
    </div>
  )
}

const renderAvatar = (avatar: string | string[], className = '') => {
  if (Array.isArray(avatar)) {
    return <div className={classNames(styles['avatar-imgs'])}>
      <CountersignAvatar imageList={avatar} />
    </div>
  }
  if (avatar === 'EBotIconNode') {
    return (
      <div className={`${styles['user-icon']} ${className}`}>
        <FilledGeneralIntelligentService fontSize={12} color={'var(--eui-bg-body)'} />
      </div>
    )
  }
  if (avatar === 'RecalculateIconNode') {
    return <EkbIcon name="#EDico-sys-recount" className={styles['user-img']} />
  }
  if (avatar === 'CarbonCopyIconNode') {
    return (
      <div className={`${styles['user-icon']} ${styles['usr-icon-rotate-neu-300']}`}>
        <FilledGeneralPosition fontSize={10} color={'var(--eui-bg-body)'} />
      </div>
    )
  }
  const src = avatar ? avatar : null
  return <Avatar size="xmini" src={src} className={styles['user-img']} />
}

const renderApproveContent = (
  { content, deleteCommentIcon, highlighterWords = null, popoverContent = [], comments = null },
  subDom = null
) => {
  const contentClassName = `${styles['approve-content-wrapper']}`
  return (
    <div className={`${styles['horizontal']}`}>
      {popoverContent.length > 0 ? (
        <Popover overlayClassName={styles['flow-log-item-popover']} content={popoverContent} trigger="hover">
          <div className={classNames(contentClassName, { 'cur-p': !!popoverContent }, 'translate-ignore-class')}>
            {highlighterWords ? (
              <Highlighter
                highlightClassName={styles['content-highlight']}
                searchWords={highlighterWords}
                textToHighlight={content}
              />
            ) : (
              content
            )}
            {!!subDom && subDom}
          </div>
        </Popover>
      ) : (
        <div className={contentClassName}>
          {highlighterWords ? (
            <Highlighter
              highlightClassName={styles['content-highlight']}
              searchWords={highlighterWords}
              textToHighlight={content}
            />
          ) : (
            content
          )}
          {!!subDom && subDom}
        </div>
      )}
      {deleteCommentIcon}
    </div>
  )
}

function fnGetName(item, staff = undefined) {
  let name = getNameWithoutDelegate(item, staff)
  let byDelegateName = item?.byDelegateId ? getStaffShowByConfig(item?.byDelegateId) : ''
  name = byDelegateName ? i18n.get(`{__k0}代{__k1}`, { __k0: byDelegateName, __k1: name }) : name
  return name
}

function getNameWithoutDelegate(item, staff = undefined) {
  let { staffName } = getStaffNameWithConfig(item, staff)
  return staffName
}

/**
 * 获取员工姓名信息的通用函数
 * @param item - 流程项目对象
 * @param staff - 可选的员工对象
 * @param useFullConfig - 是否使用完整配置（包含第二字段），默认为 false
 * @returns 包含 staffName 和 byDelegateName 的对象
 */
function getStaffNameInfo(item, staff = undefined, useFullConfig = false) {
  const getStaffNameFunc =  window.isInWeComISV
    ? (item: any) => <NameCell type="user" id={item.id} name={item.name} />
    : useFullConfig
      ? getStaffShowByConfig : getStaffNameOfConfig

  let staffName = ''
  if (staff) {
    staffName = getStaffNameFunc(staff)
  } else if (get(item, 'operatorId') === 'ebot') {
    // ebot节点operatorId为ebot, 没有operatorId.name
    staffName = get(item, 'operatorId')
  } else if (get(item, 'operatorId.isAIAgentNode')) {
    staffName = get(item, 'operatorId.name')
  } else {
    staffName = item?.operatorId ? getStaffNameFunc(item.operatorId) : ''
  }

  const byDelegateName = item?.byDelegateId ? getStaffNameFunc(item.byDelegateId) : ''
  return { staffName, byDelegateName }
}

/**
 * 获取员工基本姓名（仅姓名）
 * @param item - 流程项目对象
 * @param staff - 可选的员工对象
 * @returns 包含 staffName 和 byDelegateName 的对象
 */
function getStaffName(item, staff = undefined) {
  return getStaffNameInfo(item, staff, false)
}

/**
 * 获取员工完整显示名称（姓名 + 配置字段）
 * @param item - 流程项目对象
 * @param staff - 可选的员工对象
 * @returns 包含 staffName 和 byDelegateName 的对象
 */
function getStaffNameWithConfig(item, staff = undefined) {
  return getStaffNameInfo(item, staff, true)
}

function addAtHighlighter(item, userInfo) {
  let {
    // tslint:disable-next-line:prefer-const
    attributes: { participants = [], comment }
  } = item
  participants = participants ? participants : []
  const isAtMe = userInfo && !!~participants.map(e => e.id).indexOf(userInfo.id)
  const content = comment2MentionContent(comment, participants, flowStateMap.staffDisplayConfigField)
  let highlighterWords = participants.map(item => ` @${item.name} `)
  highlighterWords = parseReg(highlighterWords)
  const atDom = isAtMe && (
    <span>
      {i18n.get('，')}
      {i18n.get('并')}
      <span className={styles['refer-me']}>{i18n.get('@我')}</span>
    </span>
  )

  return {
    atDom,
    content,
    highlighterWords
  }
}

const UpdateApproverPopover: React.FC<{
  name: string
  nodeName: string
  oldOperatorAvatar: string
  preName: string
  nextOperatorAvatar: string
  nextName: string
  children?: React.ReactNode
  title?: string
}> = ({
  name,
  nodeName,
  oldOperatorAvatar,
  preName,
  nextOperatorAvatar,
  nextName,
  children,
  title = i18n.get('重新指派审批人')
}) => {
    return (
      <Popover
        title={`${name}${title}`}
        placement={'bottom'}
        content={
          <div className={styles['flow-log-select-approver-wrapper']}>
            <div className="title">{`${i18n.get('节点名称')}:`}</div>
            <div className="content">{nodeName}</div>
            <div className="title title-mt-16">{`${i18n.get('审批人变更')}:`}</div>
            <div className="sub-title sub-title-mt-10">{i18n.get('修改前')}</div>
            <div className="content">
              {preName ? (
                <>
                  {renderAvatar(oldOperatorAvatar)}
                  <span className="ml-4">{preName}</span>
                </>
              ) : (
                i18n.get('无')
              )}
            </div>
            <div className="sub-title">{i18n.get('修改后')}</div>
            <div className="content">
              {renderAvatar(nextOperatorAvatar)}
              <span className="ml-4">{nextName}</span>
            </div>
          </div>
        }
      >
        {children}
      </Popover>
    )
  }

const showEBotSkippedTypes = [
  'NO_ABILITY',
  'NO_ABILITY_TRUE',
  'NO_ABILITY_FALSE',
  'REQUISITION_NO_ABILITY',
  'PAY_AMOUNT_IS_0',
  'PAY_AMOUNT_IS_0_TRUE',
  'PAY_AMOUNT_IS_0_FALSE',
  'APPROVER_NOT_FOUND',
  'APPROVER_NOT_FOUND_BY_ROLE'
]

const flowStateMap = {
  staffDisplayConfigField: '', // 通讯录显示配置字段
  retract: {
    approveContent({ item }) {
      const name = getNameWithoutDelegate(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('撤回了单据'))}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <StaffNameComponent
                staffName={byDelegateName || staffName}
                actionClassName={'approve-content-wrapper-content-reject'}
              />
              <span className="text-static approve-content-wrapper-content-reject flex-s-0 ml-4">
                {i18n.get('撤回了单据')}
              </span>
            </div>
          </div>
        </Tooltip>
      )

      return { content }
    },
    render({ item }) {
      const { content } = this.approveContent({ item })
      return renderApproveContent({ content })
    }
  },
  withdraw: {
    approveContent({ item }) {
      const name = getNameWithoutDelegate(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('撤回了单据'))}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <StaffNameComponent
                staffName={staffName}
                byDelegateName={byDelegateName}
                actionClassName={'approve-content-wrapper-content-reject'}
              />
              <span className="text-static approve-content-wrapper-content-reject flex-s-0 ml-4">
                {i18n.get('撤回了单据')}
              </span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const { content } = this.approveContent({ item })
      return renderApproveContent({ content })
    }
  },
  addnode: {
    approveContent({ item }) {
      const {
        nextOperatorId,
        nextOperatorIds = [],
        attributes: { nextName: nodeName, oldApproverId, isEbotNode }
      } = item
      const name = fnGetName(item)
      const isShiftEbot = get(item, 'attributes.isShiftEbot')
      const nextNames = !!nextOperatorIds.length && getStaffShowByConfig(nextOperatorIds[0])
      const nextStaffNames = !!nextOperatorIds.length && getStaffName({ operatorId: nextOperatorIds[0] }).staffName
      const nextStaffDisplay = get(item, `nextOperatorId.${flowStateMap.staffDisplayConfigField}`)
      const nextStaffNameDisplay = getStaffName({ operatorId: get(item, `nextOperatorId`) }).staffName
      const nextOpName = getStaffShowByConfig(nextOperatorId)
      const nextOpStaffName = getStaffName(nextOperatorId).staffName
      const avatar = get(item, 'operatorId.avatar')
      let nextOperatorAvatar = get(nextOperatorId, 'avatar')
      if (isEbotNode && !nextOperatorAvatar) {
        nextOperatorAvatar = 'EBotIconNode'
      }
      const nextName = isShiftEbot
        ? EBotName
        : nextNames
          ? <>{nextNames}等{nextOperatorIds.length}人</>
          : nextStaffDisplay
            ? nextOpName + ` (${nextStaffDisplay}) `
            : nextOpName
      const nextOpStaffNameString = nextOpStaffName
        ? nextOpStaffName + ` (${nextStaffNameDisplay}) `
        : nextStaffNameDisplay
      const nextStaffName = isShiftEbot
        ? EBotName
        : nextStaffNames
          ? <>{nextStaffNames}等{nextOperatorIds.length}人</>
          : nextStaffNameDisplay
            ? nextOpStaffNameString
            : nextOpStaffName

      const addNodeType = get(item, 'attributes.addNodeType')
      let content: ReactNode = ''
      // 加签和转交的所有可能类型
      const transferNodeTypes = ['PRE_ADD_NODE', 'AFT_ADD_NODE', 'SHIFT_NODE', 'COMBINE_ADD_NODE']
      if (transferNodeTypes.includes(addNodeType)) {
        const actionName = addNodeType === 'SHIFT_NODE' ? i18n.get('转交给') : i18n.get('加签给')
        let showActionName = actionName
        let toolTipTitle: ReactNode = ''
        if (needTipContent(name) || needTipContent(nextName)) {
          toolTipTitle = <span>
            {name} {actionName} {nextName}
          </span>
        }
        const { staffName, byDelegateName } = getStaffName(item)
        let showNextName = true
        if (byDelegateName) {
          showNextName = false
          showActionName = addNodeType === 'SHIFT_NODE' ? i18n.get('转交') : i18n.get('加签')
        }
        content = (
          <Tooltip placement={'bottom'} title={toolTipTitle}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-info-content']}`}>
              {renderAvatar(avatar, styles['usr-icon-info-500'])}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-info'}
                />
                <span className="text-static approve-content-wrapper-content-info flex-s-0 ml-4 mr-4">
                  {showActionName}
                </span>
                {showNextName ? <span className="text-ellipsis">{nextStaffName}</span> : null}
              </div>
            </div>
          </Tooltip>
        )
      } else {
        const preName = getStaffShowByConfig(oldApproverId)
        const oldOperatorAvatar = get(oldApproverId, 'avatar')
        content = (
          <UpdateApproverPopover
            name={name}
            nextName={nextName}
            nextOperatorAvatar={nextOperatorAvatar}
            oldOperatorAvatar={oldOperatorAvatar}
            nodeName={nodeName}
            preName={preName}
          >
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']} cur-p`}>
              <div className="text-container">
                {renderAvatar(avatar, styles['usr-icon-info-500'])}
                <span className="text-ellipsis">{name}</span>
                <span className="text-static approve-content-wrapper-content-info ml-4">
                  {i18n.get('重新指派审批人')}
                </span>
              </div>
            </div>
          </UpdateApproverPopover>
        )
      }
      return { content }
    },
    render({ item, userInfo }) {
      const {
        attributes: { comment, autographImageId },
        attachments
      } = item
      const { content } = this.approveContent({ item })
      const hasAttachments = attachments && attachments.length > 0
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)
      const element = comment ? (
        <Highlighter
          highlightClassName={styles['highlight']}
          searchWords={highlighterKey}
          textToHighlight={commentContent}
        />
      ) : (
        comment
      )
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderCommentWithMarkdown(commentContent, element)}</div>}
          <div className="dis-f">
            {hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  },
  print: {
    approveContent({ item }) {
      const {
        attributes: { action, autoRemind }
      } = item
      let name = fnGetName(item)
      const state = autoRemind ? 'autoRemind' : action
      let avatar = get(item, 'operatorId.avatar')
      let msg = printLogMap('')[state]
      // tslint:disable-next-line:prefer-const
      let { staffName, byDelegateName } = getStaffName(item)
      let avatarClassName = ''
      if (state === 'autoRemind') {
        avatar = 'EBotIconNode'
        staffName = EBotName
        name = staffName
        avatarClassName = styles['usr-icon-neu-400']
        msg = i18n.get('自动提醒打印单据')
      }
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
            {renderAvatar(avatar, avatarClassName)}
            <div className="text-container">
              <StaffNameComponent
                staffName={byDelegateName || staffName}
                actionClassName={'approve-content-wrapper-content'}
              />
              <span className="text-static approve-content-wrapper-content flex-s-0 ml-4">{msg}</span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const { content } = this.approveContent({ item })
      return renderApproveContent({ content })
    }
  },
  submit: {
    approveContent({ item }) {
      const { attributes } = item
      const { resubmitMethod } = attributes
      let name = ''
      let content: React.ReactNode = ''
      const avatar = get(item, 'operatorId.avatar')
      if (resubmitMethod === 'TO_REJECTOR') {
        if (attributes.resubmitOperatorIds && Array.isArray(attributes.resubmitOperatorIds)) {
          name = attributes?.isToInvoiceApplicationNode
            ? i18n.get('开票申请')
            : attributes.resubmitOperatorIds
              .map(item => {
                const itemCopy = { ...item }
                const name = getDisplayName(itemCopy)
                const itemStaffDisplay = get(itemCopy, `${flowStateMap.staffDisplayConfigField}`)
                itemStaffDisplay &&
                  isString(itemStaffDisplay) &&
                  (itemCopy.name = name + ` (${fnSubstr16(itemStaffDisplay)}) `)
                return name
              })
              .join(',') || EBotName
        }
      } else {
        name = getNameWithoutDelegate(item)
      }
      let toolTipTitle: ReactNode = ''
      if (needTipContent(name)) {
        toolTipTitle = <>{name} {i18n.get('提交送审')}</>
      }
      const { staffName } = getStaffName(item)
      content = (
        <Tooltip placement={'bottom'} title={toolTipTitle}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <span className="text-ellipsis">{staffName}</span>
              <span className="text-static approve-content-wrapper-content flex-s-0 ml-4">{i18n.get('提交送审')}</span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment }
      } = item

      const { content, highlighterWords } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
        </div>
      )
    }
  },
  // 变更
  alter: {
    approveContent({ item }) {
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('发起变更申请'))}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-info']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <StaffNameComponent
                staffName={staffName}
                byDelegateName={byDelegateName}
                actionClassName={'approve-content-wrapper-content-info'}
              />
              <span className="text-static approve-content-wrapper-content-info flex-s-0 ml-4">
                {i18n.get('发起变更申请')}
              </span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item, userInfo }) {
      const {
        attachments,
        attributes: { comment, autographImageId }
      } = item
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = this.approveContent({ item })
      const { atDom } = addAtHighlighter(item, userInfo)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          <div className="dis-f">
            {hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  },
  agree: {
    approveContent({ item }) {
      const name = fnGetName(item)
      // tslint:disable-next-line:prefer-const
      let { staffName, byDelegateName } = getStaffName(item)
      const isEbotNode = get(item, 'attributes.isEbotNode')
      const isInvoiceApplicationNode = get(item, 'attributes.isInvoiceApplicationNode')
      const isRecalNode = get(item, 'attributes.isRecalNode')
      const aiAgentNode = isAIAgentNode(item)
      const result = get(item, 'attributes.extras.result')
      const showGray = aiAgentNode && !!result
      let avatar = get(item, 'operatorId.avatar')
      let msg = result || i18n.get('审批同意')
      if (isEbotNode || isInvoiceApplicationNode || isRecalNode) {
        avatar = 'EBotIconNode'
        msg = i18n.get('执行完成')
        staffName = EBotName
      }
      const isAutoNode = isEbotNode || isInvoiceApplicationNode || isRecalNode
      const actionClassName = isAutoNode
        ? 'approve-content-wrapper-content-cyan'
        : showGray
          ? 'approve-content-wrapper-content'
          : 'approve-content-wrapper-content-green'
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
          <div
            className={classNames(styles['flow-log-action-wrapper'], {
              [styles['flow-log-action-wrapper-cyan']]: isAutoNode,
              [styles['flow-log-action-wrapper-green']]: !showGray,
              [styles['flow-log-action-wrapper-gray']]: showGray
            })}
          >
            {renderAvatar(avatar, styles['usr-icon-cyan-500'])}
            <div className="text-container">
              <StaffNameComponent
                staffName={staffName}
                byDelegateName={byDelegateName}
                actionClassName={actionClassName}
              />
              <span className={classNames('text-static', 'ml-4', actionClassName)}>{msg}</span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item, userInfo }) {
      const {
        attachments,
        attributes: { comment, autographImageId }
      } = item
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = this.approveContent({ item })
      const { atDom } = addAtHighlighter(item, userInfo)
      const tips = get(item, 'attributes.extras.tips')
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords }, atDom)}
          {comment && (
            <div className={classNames(styles['content-msg'], 'translate-ignore-class', { [styles['has-content-msg-tips']]: !!tips })}>
              {!!tips && <div className={styles['content-msg-tips']}>{tips}</div>}
              {renderComment(item)}
            </div>
          )}
          <div className="dis-f">
            {hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  },
  autoAgree: {
    approveContent({ item }) {
      const {
        attributes: { autoAgreeType }
      } = item
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const resion = agreeType()[autoAgreeType] || i18n.get('审批流程配置')
      const agreeReasion = autoAgreeType === 'NO_AUTO_AGREE' ? '' : resion
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('自动同意'))}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content'}
                />
                <span className="text-static approve-content-wrapper-content flex-s-0 ml-4">
                  {i18n.get('自动同意')}
                </span>
              </div>
            </div>
          </Tooltip>
          {agreeReasion?.length ? (
            <div className="mt-8 flex-1">
              <div
                className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
              >
                <div className="approve-content-wrapper-content-caption">{agreeReasion}</div>
              </div>
            </div>
          ) : null}
        </div>
      )

      return { content }
    },
    render({ item }) {
      const {
        attachments,
        attributes: { comment, autographImageId }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length
      const { content, highlighterWords } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          <div className="dis-f">
            {!!hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  },
  reject: {
    approveContent({ item, commentInfo, commentElement }) {
      const { attributes } = item
      const { resubmitMethod, nodeName, isEbotNode } = attributes
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const isCostControlCheck = get(item, 'attributes.isCostControlCheck')
      let content: ReactNode = ''
      const { staffName, byDelegateName } = getStaffName(item)
      let actionMsg = i18n.get('驳回至')
      if (byDelegateName) {
        actionMsg = i18n.get('驳回')
      }
      if (isCostControlCheck) {
        let toolTipTitle: React.ReactNode = ''
        const msg = i18n.get('提交人')
        if (needTipContent(name)) {
          toolTipTitle = <>
            {name} {i18n.get('驳回至')} {msg}
          </>
        }
        content = (
          <Tooltip placement={'bottom'} title={toolTipTitle}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
              {renderAvatar(avatar, styles['usr-icon-danger-500'])}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-reject'}
                />
                <span className="text-static approve-content-wrapper-content-reject ml-4">{actionMsg}</span>
                {byDelegateName ? null : <span className="text-ellipsis approve-content-wrapper-name ml-4">{msg}</span>}
              </div>
            </div>
          </Tooltip>
        )
      } else if (resubmitMethod === 'TO_REJECTOR') {
        let toolTipTitle: ReactNode = ''
        const msg = nodeName || i18n.get('提交人')
        if (needTipContent(name)) {
          toolTipTitle = <>{name} {i18n.get('驳回至')} {msg}</>
        }
        content = (
          <div className="dis-f fd-c flex-1">
            <Tooltip placement={'bottom'} title={toolTipTitle}>
              <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
                {renderAvatar(avatar, styles['usr-icon-danger-500'])}
                <div className="text-container">
                  <StaffNameComponent
                    staffName={staffName}
                    byDelegateName={byDelegateName}
                    actionClassName={'approve-content-wrapper-content-reject'}
                  />
                  <span className="text-static approve-content-wrapper-content-reject ml-4">{actionMsg}</span>
                  {byDelegateName ? null : (
                    <span className="text-ellipsis approve-content-wrapper-name ml-4">{msg}</span>
                  )}
                </div>
              </div>
            </Tooltip>
            <div className="mt-8 flex-1">
              <div
                className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']} ${styles['display-block']}`}
              >
                <div className={styles['content-msg-placeholder']}>
                  { window.isInWeComISV
                    ? <>重新提交后由{name}继续审批</>
                    : i18n.get('重新提交后由{__k0}继续审批', { __k0: name })}
                </div>
                <Divider dashed className={styles['reject-divider']} />
                <div className={styles['flow-log-action-wrapper-gray-text']}>{commentElement}</div>
              </div>
            </div>
          </div>
        )
      } else if (nodeName) {
        let toolTipTitle: ReactNode = ''
        if (needTipContent(name) || needTipContent(nodeName)) {
          toolTipTitle = <>{name} {i18n.get('驳回至')} {nodeName}</>
        }
        content = (
          <Tooltip placement={'bottom'} title={toolTipTitle}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
              {renderAvatar(avatar, styles['usr-icon-danger-500'])}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-reject'}
                />
                <span className="text-static approve-content-wrapper-content-reject ml-4">{actionMsg}</span>
                {byDelegateName ? null : <span className="text-ellipsis ml-4">{nodeName}</span>}
              </div>
            </div>
          </Tooltip>
        )
      } else {
        content = (
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('驳回了单据'))}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
              {renderAvatar(avatar, styles['usr-icon-danger-500'])}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-reject'}
                />
                <span className="text-static approve-content-wrapper-content-reject ml-4">
                  {i18n.get('驳回了单据')}
                </span>
              </div>
            </div>
          </Tooltip>
        )
      }
      return { content }
    },
    render({ item, userInfo, config }) {
      const {
        attachments,
        attributes: { comment, autographImageId, resubmitMethod }
      } = item
      const hasAttachments = attachments && attachments.length
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)
      const element = comment ? (
        <Highlighter
          highlightClassName={styles['highlight']}
          searchWords={highlighterKey}
          textToHighlight={commentContent}
        />
      ) : (
        comment
        )
      const commentElement = comment && renderCommentWithMarkdown(commentContent, element)
      const { content, highlighterWords } = this.approveContent({ item, config, commentElement })
      const tips = get(item, 'attributes.extras.tips')
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords }, atDom)}
          {comment &&
            (!!tips ? ( // 智能审批提示
              <div className={classNames(styles['content-msg'], 'translate-ignore-class', { [styles['has-content-msg-tips']]: !!tips })}>
                <div className={styles['content-msg-tips']}>{tips}</div>
                {renderCommentWithMarkdown(commentContent, element)}
              </div>
            ) : (
              resubmitMethod !== 'TO_REJECTOR' && <div className={`${styles['content-msg']} translate-ignore-class`}>{commentElement}</div>
            ))}
          <div className="dis-f">
            {!!hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  },
  nullify: {
    approveContent({ item }) {
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('作废了单据'))}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <StaffNameComponent
                staffName={byDelegateName || staffName}
                actionClassName={'approve-content-wrapper-content-reject'}
              />
              <span className="text-static approve-content-wrapper-content-reject flex-s-0 ml-4">
                {i18n.get('作废了单据')}
              </span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const { content } = this.approveContent({ item })
      return renderApproveContent({ content })
    }
  },
  activate: {
    approveContent({ item }) {
      // const name = get(item, 'operatorId.name')
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('激活了单据'))}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-green']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <StaffNameComponent staffName={staffName} actionClassName={'approve-content-wrapper-content-green'} />
              <span className="text-static approve-content-wrapper-content-green ml-4">{i18n.get('激活了单据')}</span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const { content } = this.approveContent({ item })
      return renderApproveContent({ content })
    }
  },
  pay: {
    approveContent({ item, billInfo }) {
      const name = getNameWithoutDelegate(item)
      const {
        form: { payMoney }
      } = billInfo
      const avatar = get(item, 'operatorId.avatar')
      const isPayZero = payMoney && Number(getMoney(payMoney)) === 0
      let msg = ''
      if (isPayZero) {
        msg = i18n.get('（支付金额为0，无实际支付行为发生）')
      }
      let payMethod = ''
      let account = ''
      // 后台没法，只能让判断文案
      if (item?.attributes?.comment === '实际付款账户') {
        account = item?.attributes?.accountCompany?.name
      } else if (item?.attributes?.paymentChannel === 'OFFLINE' && item?.attributes?.comment) {
        payMethod = i18n.get('线下支付')
        account = item?.attributes?.comment
      } else if (item?.attributes?.paymentChannel === 'OFFLINE') {
        payMethod = i18n.get('线下支付')
        account = item?.attributes?.accountCompany?.name
      }
      const { staffName } = getStaffName(item)
      const hasOtherMsg = !!payMethod || !!account || !!msg
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('支付成功'))}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-green']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <span className="text-ellipsis">{staffName}</span>
                <span className="text-static approve-content-wrapper-content-green flex-s-0 ml-4">
                  {i18n.get('支付成功')}
                </span>
              </div>
            </div>
          </Tooltip>
          {hasOtherMsg ? (
            <div className="mt-8 flex-1">
              <div
                className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
              >
                <div className="dis-f fd-c flex-1">
                  {payMethod ? (
                    <div className="approve-content-wrapper-content-caption">{`${i18n.get('方式')}：${payMethod}`}</div>
                  ) : null}
                  {account ? (
                    <div className="approve-content-wrapper-content-caption">{`${i18n.get('账户')}：${account}`}</div>
                  ) : null}
                  {msg ? <div className="approve-content-wrapper-content-caption">{msg}</div> : null}
                </div>
              </div>
            </div>
          ) : null}
        </div>
      )
      return { content }
    },
    render({ item, billInfo }) {
      const { content } = this.approveContent({ item, billInfo })
      return renderApproveContent({ content })
    }
  },
  paying: {
    approveContent({ item }) {
      const {
        attributes: { accountCompany, paymentChannel },
        dynamicChannelMap
      } = item
      const payName = accountCompany ? accountCompany?.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap?.[paymentChannel]?.name || i18n.get('无')

      let msg = i18n.get('发起支付')
      const payMethod = i18n.get(payChannel)
      const account = i18n.get(payName)

      if (item.attributes.hasOwnProperty('reviewResult')) {
        if (item.attributes?.reviewResult) {
          msg = i18n.get('同意支付')
        } else {
          msg = i18n.get('驳回支付')
        }
      }
      const { staffName } = getStaffName(item)
      const avatar = get(item, 'operatorId.avatar')
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <span className="text-ellipsis">{staffName}</span>
                <span className="text-static approve-content-wrapper-content ml-4">{msg}</span>
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="dis-f fd-c flex-1">
                {payMethod ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('方式')}：${payMethod}`}</div>
                ) : null}
                {account ? (
                  <Ellipsis
                    className={'approve-content-wrapper-content-caption'}
                    direction="end"
                    rows={2}
                    content={`${i18n.get('账户')}：${account}`}
                    expandText={i18n.get('展开')}
                    collapseText={i18n.get('收起')}
                  />
                ) : // <div className="approve-content-wrapper-content-caption">{`${i18n.get('账户')}：${account}`}</div>
                  null}
                <PayeeInfoPopover paymentInfos={item.attributes.paymentInfos}>
                  <div className="approve-content-wrapper-content-payInfo">{i18n.get('查看收款信息')}</div>
                </PayeeInfoPopover>
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && (
            <div className={`${styles['content-msg']} translate-ignore-class`}>
              {item.attributes?.hasOwnProperty?.('reviewResult') && !item.attributes?.reviewResult
                ? `${i18n.get('原因')}：`
                : ''}
              {comment}
            </div>
          )}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  repaying: {
    approveContent({ item }) {
      const {
        attributes: { accountCompany, paymentChannel, repayReason = '' },
        dynamicChannelMap
      } = item
      const payName = accountCompany ? accountCompany.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap[paymentChannel].name || i18n.get('无')
      const reason = repayReason || emptyStr
      const msg = i18n.get('发起重新支付')
      const payMethod = i18n.get(payChannel)
      const account = i18n.get(payName)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
            <div className="text-container">
              <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
                {renderAvatar(avatar)}
                <span className="text-ellipsis">{staffName}</span>
                <span className="text-static approve-content-wrapper-content ml-4">{msg}</span>
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="dis-f fd-c flex-1">
                {payMethod ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('方式')}：${payMethod}`}</div>
                ) : null}
                {account ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('账户')}：${account}`}</div>
                ) : null}
                {reason ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('账户')}：${account}`}</div>
                ) : null}
                <PayeeInfoPopover paymentInfos={item.attributes.paymentInfos}>
                  <div className="approve-content-wrapper-content-payInfo">{i18n.get('查看收款信息')}</div>
                </PayeeInfoPopover>
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content, highlighterWords } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  'pay.by.offline': {
    approveContent({ item }) {
      const {
        attributes: { paymentInfos }
      } = item
      const { amount, payeeId = {}, feeTypeId = {} } = (paymentInfos && paymentInfos[0]) || {}
      const _amount = amount?.standard ? amount?.standard : amount
      const payName = payeeId.name || i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = i18n.get('线下支付')
      const feeTypeName = i18n.get((feeTypeId && feeTypeId.name) || i18n.get('无'))
      const payMoney = new Big(_amount || 0).toFixed(2)
      const msg = i18n.get('修改支付方式')
      const payMethod = i18n.get(payChannel)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <span className="text-ellipsis">{staffName}</span>
                <span className="text-static approve-content-wrapper-content ml-4">{msg}</span>
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="dis-f fd-c flex-1">
                <div className="approve-content-wrapper-content-caption">{`${i18n.get('方式')}：${payMethod}`}</div>
                <div className="approve-content-wrapper-content-caption">{`${i18n.get('账户')}：${payName}`}</div>
                <div className="approve-content-wrapper-content-caption">{`${i18n.get(
                  '费用类型'
                )}：${feeTypeName}`}</div>
                <div className="approve-content-wrapper-content-caption">{`${i18n.get('支付金额')}：${payMoney}`}</div>
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const { content } = this.approveContent({ item })
      return <div className={styles['content-wrapper']}>{renderApproveContent({ content })}</div>
    }
  },
  failure: {
    approveContent({ item }) {
      const {
        attributes: { failureReason }
      } = item
      const name = getNameWithoutDelegate(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
            {renderAvatar(avatar, styles['usr-icon-danger-500'])}
            <div className="text-container">
              <Tooltip placement={'bottom'} title={name}>
                <span className="text-ellipsis">{staffName}</span>
              </Tooltip>
              <span className="text-static approve-content-wrapper-content-reject ml-4">{i18n.get('支付失败')}</span>
            </div>
          </div>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="dis-f fd-c flex-1">
                <div className="approve-content-wrapper-content-caption">{failureReason}</div>
                <PayeeInfoPopover paymentInfos={item.attributes.paymentInfos} showFailureReason={true}>
                  <div className="approve-content-wrapper-content-payInfo">{i18n.get('查看收款信息')}</div>
                </PayeeInfoPopover>
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  'select.approver': {
    getNewApproverNames(newApproverIds = [], dataSource) {
      const planNode = dataSource?.plan?.nodes || []
      const newApproverNames = newApproverIds.map(id => {
        const node = planNode.find(item => item.approverId?.id === id)
        return node ? getStaffShowByConfig(node.approverId) : ''
      })
      return newApproverNames.join(i18n.get('，'))
    },
    approveContent({ item, billInfo }) {
      const {
        attributes: { label: nodeLabel, oldApproverId, newApproverId, newApproverNames, newApproverIds }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      // 修复oldApproverId为null时，报错，同时修改文案： "change-no-approver": "{name} changed the approver of {nodeName} to {nextName}"
      let preName = oldApproverId ? getStaffShowByConfig(oldApproverId) : ''
      const oldOperatorAvatar = get(oldApproverId, 'avatar')
      const nextOperatorAvatar = get(newApproverId, 'avatar')
      const nextName = newApproverIds
        ? this.getNewApproverNames(newApproverIds, billInfo)
        : newApproverNames?.join(i18n.get('，')) || ''
      const name = fnGetName(item)
      const nodeName = isPayNode(item.attributes)
        ? nodeLabel
          ? nodeLabel
          : item.attributes.name
        : item.attributes.name
      const preStaffDisplay = oldApproverId?.[flowStateMap.staffDisplayConfigField]
      preStaffDisplay && isString(preStaffDisplay) && (preName = `${preName} (${fnSubstr16(preStaffDisplay)}) `)
      const actionName = i18n.get('指派审批人')
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <UpdateApproverPopover
          title={actionName}
          name={name}
          nextName={nextName}
          nextOperatorAvatar={nextOperatorAvatar}
          oldOperatorAvatar={oldOperatorAvatar}
          nodeName={nodeName}
          preName={preName}
        >
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-info']} cur-p`}>
            <div className="text-container">
              {renderAvatar(avatar)}
              <StaffNameComponent
                staffName={staffName}
                byDelegateName={byDelegateName}
                actionClassName={'approve-content-wrapper-content-info'}
              />
              <span className="text-static approve-content-wrapper-content-info ml-4">{actionName}</span>
            </div>
          </div>
        </UpdateApproverPopover>
      )

      return { content }
    },
    render(obj) {
      const { content } = this.approveContent(obj)
      return renderApproveContent({ content })
    }
  },
  skipped: {
    approveContent({ item }) {
      const {
        attributes: { skippedType }
      } = item
      let name = item?.attributes?.name
      const skipDetail = skippedTypeMap()[skippedType]
      let avatar = get(item, 'operatorId.avatar')
      // tslint:disable-next-line:prefer-const
      let { staffName, byDelegateName } = getStaffName(item)
      let avatarIconClass = ''
      if (showEBotSkippedTypes.includes(skippedType)) {
        avatar = 'EBotIconNode'
        staffName = EBotName
        name = staffName
        avatarIconClass = styles['usr-icon-neu-400']
      }
      if (skippedType === 'AI_APPROVAL') {
        const staffs = getSkippedNodeStaff(item) || []
        avatar = staffs[0]?.avatar
        staffName = staffs[0]?.name
        name = staffName
        if (staffs.length > 1) {
          avatar = staffs.map(item => item.avatar)
          staffName = `${staffs.length}名成员`
          name = staffName
        }
      }
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('自动跳过'))}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
              {renderAvatar(avatar, avatarIconClass)}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content'}
                />
                <span className="text-static approve-content-wrapper-content ml-4">{i18n.get('自动跳过')}</span>
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="approve-content-wrapper-content-caption">
                {skipDetail ? i18n.get(skipDetail) : skipDetail}
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  modify: {
    approveContent({ item, text, onViewVersionClick }) {
      let defaultText = ''
      let contentInfoClassName = 'approve-content-wrapper-content-info'
      let showVersion = true
      let logWrapperClassName = styles['flow-log-action-wrapper-info']
      const { staffName, byDelegateName } = getStaffName(item)
      if (item.operatorState) {
        const { content, className, showVersion: showVersionButton, wrapperClassName } = fnGetOperatorStateStr(
          item.operatorState
        )
        contentInfoClassName = className
        defaultText = content
        showVersion = showVersionButton
        logWrapperClassName = wrapperClassName
      }

      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const { flowVersionedId } = item
      const subDom = showVersion && flowVersionedId && renderVersion(item, onViewVersionClick)
      let toolTipTitle: ReactNode = ''
      if (needTipContent(name)) {
        toolTipTitle = <>{name} {text}</>
      }
      const content = (
        <div className="dis-f jc-sb flex-1 ai-c">
          <Tooltip placement={'bottom'} title={toolTipTitle}>
            <div className={`${styles['flow-log-action-wrapper']} ${logWrapperClassName}`}>
              {renderAvatar(avatar, styles['usr-icon-info-500'])}
              <div className="text-container text-container-middle-130">
                <StaffNameComponent staffName={byDelegateName || staffName} actionClassName={contentInfoClassName} />
                <span className={`text-static ${contentInfoClassName} ml-4`}>{text || defaultText}</span>
              </div>
            </div>
          </Tooltip>
          {subDom}
        </div>
      )
      return { content }
    },
    render({ item, onViewVersionClick }) {
      let text = i18n.get('修改了单据')
      if (item.operatorState) {
        const { content } = fnGetOperatorStateStr(item.operatorState)
        text = content
      }
      if (item.operatorId === 'ebot') {
        item.operatorId = { name: EBotName, avatar: 'EBotIconNode' }
      }
      const { editeReason } = item
      const { content, highlighterWords } = this.approveContent({ item, text, onViewVersionClick })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords })}
          {editeReason && <div className={`${styles['content-msg']} translate-ignore-class`}>{editeReason}</div>}
        </div>
      )
    }
  },
  comment: {
    approveContent({ item, config, billInfo, bus }) {
      const { logIndex, privilegeId } = config
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      // const participants = get(item, 'attributes.participants', [])
      // const currentStaffId = get(userInfo, 'id') || get(userInfo, 'staff.id')
      // const isAtMe = currentStaffId && !!~participants.map(e => e.id).indexOf(currentStaffId)
      // const msg = isAtMe ? `${i18n.get('评论了单据并@我')}` : i18n.get('评论了单据')
      const msg = i18n.get('发表评论')
      const { staffName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <span className="text-ellipsis">{staffName}</span>
              <span className="text-static approve-content-wrapper-content flex-s-0 ml-4">{msg}</span>
            </div>
          </div>
        </Tooltip>
      )

      const fnDeleteComment = async () => {
        const res = await deleteComment({
          flowId: billInfo.id,
          logIndex: item.index,
          privilegeId,
          id: item.id,
          time: item.time,
        })
        if (res?.value) {
          message.success(i18n.get('评论删除成功'))
          bus.invoke('bills:update:flow')
        } else {
          message.error(i18n.get('评论删除失败'))
        }
      }

      const deleteCommentIcon = (
        <Popconfirm
          title={i18n.get('确认删除此评论？')}
          content={i18n.get('删除操作不可撤回')}
          placement="bottomRight"
          onConfirm={fnDeleteComment}
          overlayStyle={{ width: 258 }}
          align={{ targetOffset: [-16, 10] }}
        >
          <Button category="text" icon={<OutlinedEditDeleteTrash />} size="mini"></Button>
        </Popconfirm>
      )

      return { content, deleteCommentIcon }
    },
    render({ item, config, billInfo, bus }) {
      const {
        attachments,
        attributes: { participants = [], comment, receiverIds = [], autographImageId, delOperatorId, delTime }
      } = item
      const { canDeleteComment } = config
      const com = comment2MentionContent(comment, participants, flowStateMap.staffDisplayConfigField)
      let highlighterWords = participants.map(item => ` @${getStaffShowByConfig(item)} `)
      highlighterWords = parseReg(highlighterWords)
      const receivers = receiverIds.map(item => getStaffShowByConfig(item)).join(i18n.get('、'))
      const hasAttachments = attachments && attachments.length > 0
      const { content, deleteCommentIcon } = this.approveContent({ item, config, billInfo, bus })
      const delOperator = delOperatorId && getStaffShowByConfig(delOperatorId)
      const delTimeFormat = delTime && moment(delTime).format('YYYY-MM-DD HH:mm')
      const element = comment ? (
        <Highlighter highlightClassName={styles['highlight']} searchWords={highlighterWords} textToHighlight={com} />
      ) : (
        comment
      )
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({
            content,
            deleteCommentIcon: canDeleteComment && !delOperatorId && deleteCommentIcon
          })}
          <div className={`${styles['content-msg']} translate-ignore-class fd-c`}>
            {comment && renderCommentWithMarkdown(com, element)}
            {!comment && delOperatorId && (
              <Tooltip
                placement="bottom"
                title={i18n.get('评论已被管理员{__k0}于{__k1}删除', { __k0: delOperator, __k1: delTimeFormat })}
              >
                <div className={styles['content-msg-placeholder']}>
                  {i18n.get('评论已被管理员删除（{__k0}）', { __k0: delTimeFormat })}
                </div>
              </Tooltip>
            )}
            {receivers.length > 0 && (
              <div className={styles['dashed-separator']}>{i18n.get('mentioned', { name: receivers })}</div>
            )}
          </div>
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  carbonCopy: {
    approveContent({ item }) {
      const {
        attributes: { carbonCopy }
      } = item
      if (carbonCopy?.length > 1) {
        const content = (
          <Popover
            placement={'bottom'}
            title={i18n.get('抄送单据给')}
            content={
              <div className={`${styles['flow-log-carbonCopy-wrapper']} cur-p`}>
                {carbonCopy.map(item => {
                  const avatar = get(item, 'avatar')
                  const name = fnGetName({ operatorId: item })
                  return (
                    <div key={item.id} className={'flow-log-carbonCopy-row'}>
                      {renderAvatar(avatar)} <span className="ml-4">{name}</span>
                    </div>
                  )
                })}
              </div>
            }
          >
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
              <div className={`${styles['user-icon']} ${styles['usr-icon-neu-500']} p-2`}>
                <FilledGeneralPosition style={{ marginBottom: 2 }} fontSize={10} color={'var(--eui-bg-body)'} />
              </div>
              <div className="text-container">
                <span className="text-static approve-content-wrapper-content mr-4">{i18n.get('抄送单据给')}</span>
                <span className="text-ellipsis ml-4">{i18n.get('{__k0}名成员', { __k0: carbonCopy.length })}</span>
              </div>
            </div>
          </Popover>
        )
        return { content }
      }

      let msg: string = ''
      let toolTipTitle: ReactNode = ''
      if (carbonCopy?.length === 1) {
        const { staffName } = getStaffName({ operatorId: carbonCopy[0] })
        msg = staffName
        toolTipTitle = <>{i18n.get('抄送单据给')} {fnGetName({ operatorId: carbonCopy[0] })}</>
      } else if (needTipContent(toolTipTitle)) {
        toolTipTitle = <>{i18n.get('抄送单据给')} {toolTipTitle}</>
      }

      const content = (
        <Tooltip placement={'bottom'} title={toolTipTitle}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
            <div className={`${styles['user-icon']} ${styles['usr-icon-neu-500']}`}>
              <FilledGeneralPosition fontSize={10} color={'var(--eui-bg-body)'} />
            </div>
            <div className="text-container">
              {!carbonCopy?.length ? (
                <>
                  <span className="text-ellipsis mr-4">{i18n.get('匹配不到抄送人')}</span>
                  <span className="text-static approve-content-wrapper-content">{i18n.get('自动跳过')}</span>
                </>
              ) : (
                <>
                  <span className="text-static approve-content-wrapper-content mr-4">{i18n.get('抄送单据给')}</span>
                  <span className="text-ellipsis">{msg}</span>
                </>
              )}
            </div>
          </div>
        </Tooltip>
      )

      return { content }
    },
    render({ item }) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>{renderApproveContent({ content, highlighterWords, avatar })}</div>
      )
    }
  },
  back: {
    approveContent({ item }) {
      const name = fnGetName(item)
      const type = get(item, 'attributes.type')
      const avatar = get(item, 'operatorId.avatar')
      let nodeName = ''
      let backNodeMsg = i18n.get('撤回支付')
      let actionName = i18n.get('撤回支付')
      let className = styles['flow-log-action-wrapper-red']
      let actionNameClassName = 'approve-content-wrapper-content-reject'
      if (type === 'rollback') {
        className = styles['flow-log-action-wrapper-gray']
        actionNameClassName = 'approve-content-wrapper-content'
        actionName = i18n.get('回退至')
        nodeName = get(item, 'attributes.nodeName')
        backNodeMsg = nodeName ? `${actionName}${nodeName}` : nodeName
      }
      let toolTipTitle: ReactNode = ''
      if (needTipContent(name) || needTipContent(nodeName)) {
        toolTipTitle = <>{name} {backNodeMsg}</>
      }
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={toolTipTitle}>
          <div className={`${styles['flow-log-action-wrapper']} ${className}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <StaffNameComponent
                staffName={staffName}
                byDelegateName={type === 'rollback' ? null : byDelegateName}
                actionClassName={actionNameClassName}
              />
              <span className={`text-static ${actionNameClassName} ml-4`}>{actionName}</span>
              {nodeName ? <span className="text-ellipsis approve-content-wrapper-name ml-4">{nodeName}</span> : null}
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const {
        attachments,
        attributes: { comment, autographImageId }
      } = item
      const hasAttachments = attachments && attachments.length > 0
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          <div className="dis-f">
            {hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  },
  send: renderExpress('send'),
  receive: renderExpress('receive'),
  receiveExcep: renderExpress('receiveExcep'),
  cancelReceiveExcep: renderExpress('cancelReceiveExcep'),
  compliance: {
    approveContent({ item }) {
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('完成了合规性确认'))}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <StaffNameComponent
                staffName={staffName}
                byDelegateName={byDelegateName}
                actionClassName={'approve-content-wrapper-content'}
              />
              <span className="text-static approve-content-wrapper-content ml-4">{i18n.get('完成了合规性确认')}</span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  'signing.failed': {
    approveContent({ item }) {
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName } = getStaffName(item)
      const content = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('签署失败'))}>
          <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
            {renderAvatar(avatar)}
            <div className="text-container">
              <span className="text-ellipsis">{staffName}</span>
              <span className="text-static approve-content-wrapper-content-reject ml-4">{i18n.get('签署失败')}</span>
            </div>
          </div>
        </Tooltip>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  'pay.partial.paying': {
    approveContent({ item }) {
      const {
        attributes: { accountCompany, paymentChannel },
        dynamicChannelMap
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const payName = accountCompany ? accountCompany?.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap?.[paymentChannel]?.name || i18n.get('无')
      const msg = i18n.get('发起部分支付')
      const payMethod = i18n.get(payChannel)
      const account = i18n.get(payName)
      const { staffName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <span className="text-ellipsis">{staffName}</span>
                <span className="text-static approve-content-wrapper-content flex-s-0 ml-4">{msg}</span>
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="dis-f fd-c flex-1">
                {payMethod ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('方式')}：${payMethod}`}</div>
                ) : null}
                {account ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('账户')}：${account}`}</div>
                ) : null}
                <PayeeInfoPopover paymentInfos={item.attributes.paymentInfos} showCode={true}>
                  <div className="approve-content-wrapper-content-payInfo">{i18n.get('查看收款信息')}</div>
                </PayeeInfoPopover>
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  'pay.partial.success': {
    approveContent({ item }) {
      const {
        attributes: { accountCompany, paymentChannel },
        dynamicChannelMap
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const payName = accountCompany ? accountCompany?.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap?.[paymentChannel]?.name || i18n.get('无')
      const msg = i18n.get('部分支付成功')
      const payMethod = i18n.get(payChannel)
      const account = i18n.get(payName)
      const { staffName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-gray']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <span className="text-ellipsis">{staffName}</span>
                <span className="text-static approve-content-wrapper-content flex-s-0 ml-4">{msg}</span>
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="dis-f fd-c flex-1">
                {payMethod ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('方式')}：${payMethod}`}</div>
                ) : null}
                {account ? (
                  <div className="approve-content-wrapper-content-caption">{`${i18n.get('账户')}：${account}`}</div>
                ) : null}
                <PayeeInfoPopover paymentInfos={item.attributes.paymentInfos} showCode={true}>
                  <div className="approve-content-wrapper-content-payInfo">{i18n.get('查看收款信息')}</div>
                </PayeeInfoPopover>
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  'pay.partial.failure': {
    approveContent({ item }) {
      const {
        attributes: { failureReason }
      } = item
      const name = getNameWithoutDelegate(item)
      const avatar = get(item, 'operatorId.avatar')
      const { staffName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('部分支付失败'))}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <span className="text-ellipsis">{staffName}</span>
                <span className="text-static approve-content-wrapper-content-reject ml-4">
                  {i18n.get('部分支付失败')}
                </span>
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']}`}
            >
              <div className="dis-f fd-c flex-1">
                <div className="approve-content-wrapper-content-caption">{failureReason}</div>
                <PayeeInfoPopover paymentInfos={item.attributes.paymentInfos} showCode={true} showFailureReason={true}>
                  <div className="approve-content-wrapper-content-payInfo">{i18n.get('查看收款信息')}</div>
                </PayeeInfoPopover>
              </div>
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item }) {
      const {
        attributes: { comment, autographImageId }
      } = item
      const { content } = this.approveContent({ item })
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
        </div>
      )
    }
  },
  'admin.skipnode': {
    approveContent({ item }) {
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      const nodeName = item?.attributes?.nodeName || item?.attributes?.name
      const msg = nodeName ? <>{name} {i18n.get('设置跳过')} {nodeName}</> : name
      let toolTipTitle: ReactNode = ''
      if (needTipContent(name) || needTipContent(item.attributes.name)) {
        toolTipTitle = msg
      }
      const { staffName, byDelegateName } = getStaffName(item)
      const content = (
        <div className="dis-f fd-c flex-1">
          <Tooltip placement={'bottom'} title={toolTipTitle}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-info-content']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-info'}
                />
                <span className="text-static approve-content-wrapper-content-info ml-4">{i18n.get('设置跳过')}</span>
                {nodeName ? (
                  <span className="text-ellipsis approve-content-wrapper-content ml-4">{nodeName}</span>
                ) : null}
              </div>
            </div>
          </Tooltip>
          <div className="mt-8 flex-1">
            <div
              className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-radius-4']} ${styles['flow-log-action-wrapper-gray']} ${styles['flow-log-action-wrapper-gray-text']}`}
            >
              {i18n.get(`原因：管理员({__k0})手动跳过`, { __k0: name })}
            </div>
          </div>
        </div>
      )
      return { content }
    },
    render({ item, userInfo }) {
      const {
        attachments,
        attributes: { comment, autographImageId }
      } = item
      const hasAttachments = attachments && attachments.length > 0
      const { content } = this.approveContent({ item })
      const { atDom } = addAtHighlighter(item, userInfo)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          <div className="dis-f">
            {hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  },
  'current-approving': {
    approveContent({ item, config, billInfo }) {
      const { node } = item
      let name = fnGetName(item)
      // tslint:disable-next-line:prefer-const
      let { staffName, byDelegateName } = getStaffName(item)
      let avatar = get(item, 'operatorId.avatar')
      let approvingCounterSingers = []
      if (node?.type === 'countersign') {
        approvingCounterSingers = node.counterSigners.filter(counterSigner => {
          return counterSigner.state === 'APPROVING'
        })
      }

      if (node?.type === 'ebot' || node?.type === 'invoicingApplication' || node?.type === 'recalculate') {
        avatar = 'EBotIconNode'
        name = EBotName
        staffName = name
      }

      if (isAIAgentNode(item)) {
        const { agent } = getAIAgentObj(item.node, config.nodesAIAgentMap)
        avatar = agent?.icon
        name = agent?.name
        staffName = name
      }

      let content: React.ReactNode = ''
      if (node?.type === 'countersign') {
        const msg = i18n.get('{__k0}名成员', { __k0: approvingCounterSingers.length })
        content = (
          <CountersignTooltip
            showLogView={true}
            onlyShowProcessing={true}
            placement={'bottom'}
            node={node}
            flowLogs={config?.flowLogs || []}
            staffDisplayConfigField={flowStateMap.staffDisplayConfigField}
            component={
              <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-info']} cur-p`}>
                <div className={styles['user-icon']}>
                  <FilledGeneralRecord fontSize={12} color={'var(--eui-bg-body)'} />
                </div>
                <div className="text-container">
                  <span className="text-ellipsis">{msg}</span>
                  <span className="text-static approve-content-wrapper-content-approving ml-4">
                    {`${i18n.get('正在处理')}...`}
                  </span>
                </div>
              </div>
            }
          />
        )
      } else {
        const msg = `${i18n.get('正在处理')}...`
        content = (
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-info']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-approving'}
                />
                <span className="text-static approve-content-wrapper-content-approving ml-4">{msg}</span>
              </div>
            </div>
          </Tooltip>
        )
      }

      return { content }
    },
    render({ item, config, billInfo }) {
      const { content } = this.approveContent({ item, config, billInfo })
      return renderApproveContent({ content })
    }
  },
  feature: {
    render({ item, config, staffMap }) {
      const { node, isEditConfig } = item
      let name = fnGetName(item)
      let { staffName } = getStaffName(item)
      let fullName = name
      let avatar = get(item, 'operatorId.avatar')
      let msg = i18n.get('等待处理')
      let useCountersignTooltip = false

      const isSubmitterChoice = get(node, 'config.isSubmitterChoice')
      const isAuto = get(node, 'config.isAuto')
      let isEdit = false
      if (
        !isSubmitterChoice &&
        !isAuto &&
        isEditConfig &&
        !['ebot', 'recalculate', 'invoicingApplication', 'aiApproval'].includes(node?.type)
      ) {
        isEdit = true
      }
      // 是否有审批人
      let noApprover = false
      if (node?.type === 'countersign') {
        let approvingCounterSingers = []
        if (node?.policy === 'ANY') {
          approvingCounterSingers = node.counterSigners
        } else {
          approvingCounterSingers = node.counterSigners.filter(counterSigner => {
            return counterSigner.state === 'APPROVING' || counterSigner.action === 'freeflow.reject'
          })
        }
        useCountersignTooltip = true
        name = `${approvingCounterSingers.length}名成员`
        staffName = name
        fullName = approvingCounterSingers
          .map(counterSigner => fnGetName({ operatorId: counterSigner.signerId }))
          .join('、')
      } else if (!node.approverId) {
        name = isEdit ? i18n.get('选择审批人') : i18n.get('暂无审批人')
        staffName = name
        noApprover = true
        fullName = ''
      }

      let tipMsg = msg
      let avatarIconClass = ''
      const paySkippedTypes = [
        'PAY_AMOUNT_IS_0',
        'NO_ABILITY',
        'REQUISITION_NO_ABILITY',
        'NO_ABILITY_TRUE',
        'NO_ABILITY_FALSE'
      ]
      if (paySkippedTypes.includes(node?.skippedType)) {
        avatar = 'EBotIconNode'
        msg = i18n.get('将自动跳过')
        fullName = i18n.get('单据无需支付')
        name = EBotName
        staffName = name
        tipMsg = ''
        isEdit = false
        avatarIconClass = styles['usr-icon-neu-300']
      }

      if (node?.type === 'ebot' || node?.type === 'invoicingApplication' || node?.type === 'recalculate') {
        avatar = 'EBotIconNode'
        name = EBotName
        staffName = name
        isEdit = false
        avatarIconClass = styles['usr-icon-neu-300']
      }

      if (node?.type === 'aiApproval') {
        const { agent } = getAIAgentObj(node, config.nodesAIAgentMap)
        avatar = agent?.icon
        name = agent?.name
        staffName = name
        isEdit = false
      }
      const skippedTypes = [
        'NO_SKIPPED_TRUE',
        'NO_SKIPPED_FALSE',
        'APPROVER_NOT_FOUND_BY_ROLE',
        'APPROVER_SAME_AS_SUBMITTER_BY_ROLE',
        'APPROVER_NOT_FOUND',
        'NO_AUTO_AGREE',
        'APPROVER_SAME_AS_SUBMITTER',
        'APPROVER_REPEATED',
        'APPROVER_SAME_AS_OWNER'
      ]
      if (skippedTypes.includes(node?.skippedType)) {
        avatar = 'EBotIconNode'
        msg = i18n.get('将自动跳过')
        name = EBotName
        staffName = name
        tipMsg = ''
        isEdit = false
        avatarIconClass = styles['usr-icon-neu-300']
        fullName = skipTypeMap()[node?.skippedType]
      }

      let customStaffs
      let customDesc
      if (node?.type === 'carbonCopy') {
        const ccStaffIds = get(node, 'carbonCopy[0].staffIds', [])
        customStaffs = ccStaffIds.map(staffId => {
          return { signer: staffMap[staffId] }
        })
        customDesc = i18n.get('抄送至{__k0}名人员', { __k0: customStaffs.length })
        if (customStaffs.length > 1) {
          useCountersignTooltip = true
          staffName = i18n.get('将抄送单据给')
          msg = i18n.get('{__k0}名成员', { __k0: customStaffs.length })
        } else if (customStaffs.length === 1) {
          useCountersignTooltip = false
          staffName = i18n.get('将抄送单据给')
          fullName = staffName
          tipMsg = fnGetName(item, staffMap[ccStaffIds[0]])
          msg = getStaffName(item, staffMap[ccStaffIds[0]]).staffName
        } else {
          useCountersignTooltip = false
          msg = i18n.get('将自动跳过')
          staffName = i18n.get('匹配不到抄送人')
        }
        avatar = 'CarbonCopyIconNode'
        isEdit = false
      }

      const handleModifiedStaff = () => {
        if (!isEdit) {
          return
        }
        config.onModifiedStaff && config.onModifiedStaff(node)
      }
      let className = ''
      let canModifyUserClassName = ''
      if (isEdit) {
        className = noApprover
          ? 'text-ellipsis-select text-ellipsis-no-select-hover'
          : 'text-ellipsis-disabled text-ellipsis-select-hover'
        canModifyUserClassName = styles['flow-log-action-wrapper-feature-content']
      } else {
        className = 'text-ellipsis-disabled'
      }

      if (!avatar?.length) {
        avatarIconClass = styles['usr-icon-neu-300']
      }

      const tooltipContent = (
        <div
          className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-feature']} ${canModifyUserClassName}`}
        >
          {renderAvatar(avatar, avatarIconClass)}
          <div className="text-container">
            <span className={classNames('text-ellipsis', className)} onClick={handleModifiedStaff}>
              {staffName}
            </span>
            <span className="text-static approve-content-wrapper-content-disabled ml-4">{msg}</span>
          </div>
        </div>
      )
      const baseTooltip = (
        <Tooltip placement={'bottom'} title={formatToolTipContent(fullName, tipMsg)}>
          {tooltipContent}
        </Tooltip>
      )
      const countersignTooltip = (
        <CountersignTooltip
          showLogView={true}
          onlyShowProcessing={true}
          placement={'bottom'}
          node={node}
          flowLogs={config?.flowLogs || []}
          staffDisplayConfigField={flowStateMap.staffDisplayConfigField}
          component={tooltipContent}
          customStaffs={customStaffs}
          customDesc={customDesc}
        />
      )
      const content = useCountersignTooltip ? countersignTooltip : baseTooltip
      return <div className={styles['content-wrapper']}>{renderApproveContent({ content })}</div>
    }
  }
}

function renderExpress(type) {
  return {
    approveContent({ item }) {
      const name = fnGetName(item)
      const avatar = get(item, 'operatorId.avatar')
      let content: React.ReactNode = ''
      const { staffName, byDelegateName } = getStaffName(item)
      if (type === 'receive') {
        content = (
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('确认收单'))}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-green']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-green'}
                />
                <span className="text-static approve-content-wrapper-content-green ml-4">{i18n.get('确认收单')}</span>
              </div>
            </div>
          </Tooltip>
        )
      } else if (type === 'receiveExcep') {
        content = (
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, i18n.get('收单异常'))}>
            <div className={`${styles['flow-log-action-wrapper']} ${styles['flow-log-action-wrapper-red']}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={'approve-content-wrapper-content-reject'}
                />
                <span className="text-static approve-content-wrapper-content-reject ml-4">{i18n.get('收单异常')}</span>
              </div>
            </div>
          </Tooltip>
        )
      } else {
        let msg = ''
        let className = styles['flow-log-action-wrapper-gray']
        let contentClassName = 'approve-content-wrapper-content'
        let actionClassName = 'approve-content-wrapper-content'
        if (type === 'cancelReceiveExcep') {
          msg = i18n.get('取消收单异常')
        } else if (item.attributes.expressNum) {
          className = styles['flow-log-action-wrapper-info-content']
          contentClassName = 'approve-content-wrapper-content-info'
          actionClassName = 'approve-content-wrapper-content-info'
          msg = i18n.get('寄送了单据')
        } else {
          msg = i18n.get('跳过了寄送')
        }
        content = (
          <Tooltip placement={'bottom'} title={formatToolTipContent(name, msg)}>
            <div className={`${styles['flow-log-action-wrapper']} ${className}`}>
              {renderAvatar(avatar)}
              <div className="text-container">
                <StaffNameComponent
                  staffName={staffName}
                  byDelegateName={byDelegateName}
                  actionClassName={contentClassName}
                />
                <span className={`text-static ${actionClassName} ml-4`}>{msg}</span>
              </div>
            </div>
          </Tooltip>
        )
      }
      return { content }
    },
    render({ item, userInfo }) {
      const {
        attachments,
        attributes: { comment, autographImageId }
      } = item
      const hasAttachments = attachments && attachments.length > 0
      const { content } = this.approveContent({ item })
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)
      let _comment = comment
      // 收单异常 || 取消收单异常 不显示评论
      if (comment === i18n.get('收单异常') || comment === i18n.get('取消收单异常')) {
        _comment = ''
      }

      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content }, atDom)}
          {_comment && (
            <div className={`${styles['content-msg']} translate-ignore-class`}>
              <Highlighter
                highlightClassName={styles['highlight']}
                searchWords={highlighterKey}
                textToHighlight={commentContent}
              />
            </div>
          )}
          <div className="dis-f">
            {hasAttachments && renderAttachments(attachments)}
            {!!autographImageId?.url && <AutographImage autographImageId={autographImageId} />}
          </div>
        </div>
      )
    }
  }
}

const fnMapLogs = logs => {
  let flowLogs = []
  logs.forEach((item, index) => {
    flowLogs.push({ ...item, index })
    let { modifyFlowLog } = item
    if (modifyFlowLog) {
      lastVersionNum = modifyFlowLog.length ? modifyFlowLog[modifyFlowLog.length - 1].version : 0
      let mLogs = modifyFlowLog
        .filter(item => item.operatorState !== ModifyCreateType)
        .map(line => {
          const operatorMap = {
            openapi: { name: 'OpenAPI' },
            '320-corporateReceipt': { name: i18n.get('收款业务'), id: '320-corporateReceipt' }
          }
          let operatorState = line.operatorState
          if (line.operatorId === '320-corporateReceipt' && line.operatorState === 'REPLENISH_INVOICE') {
            operatorState = 'REPLENISH_INVOICE_AUTO'
          }

          return {
            ...line,
            time: line.operatorTime,
            action: ModifyBillAction,
            attributes: { comment: line.editeReason },
            operatorState,
            operatorId: operatorMap[line.operatorId] ?? line.operatorId
          }
        })
      flowLogs = flowLogs.concat(mLogs)
    }
  })
  return flowLogs
}

const needTipContent = (content: string): boolean => {
  if (!content) {
    return false
  }
  if (window.isInWeComISV) {
    return true
  }
  return content.length > 4
}

const formatToolTipContent = (name: string = '', action: string = ''): ReactNode => {
  return needTipContent(name) ? <>{name} {action}</> : ''
}

function parseReg(items = []) {
  return items.map(item => {
    return formatRegStr(item)
  })
}

function fnGetOperatorStateStr(operatorState) {
  let content = ''
  let className = ''
  const showVersion = true
  let wrapperClassName = styles['flow-log-action-wrapper-info']
  switch (operatorState) {
    case 'REPLENISH_INVOICE':
      content = i18n.get('补充了发票')
      wrapperClassName = styles['flow-log-action-wrapper-feature']
      className = 'approve-content-wrapper-content'
      break
    case 'REPLENISH_INVOICE_AUTO':
      content = i18n.get('自动补充了发票')
      wrapperClassName = styles['flow-log-action-wrapper-feature']
      className = 'approve-content-wrapper-content'
      break
    case 'CONFIRM_INVOICE':
      content = i18n.get('确认了发票')
      wrapperClassName = styles['flow-log-action-wrapper-feature']
      className = 'approve-content-wrapper-content'
      break
    case 'DELETE_INVOICE':
      content = i18n.get('删除了发票')
      wrapperClassName = styles['flow-log-action-wrapper-feature']
      className = 'approve-content-wrapper-content'
      break
    default:
      content = i18n.get('修改了单据')
      className = 'approve-content-wrapper-content-info'
      break
  }
  return { content, className, showVersion, wrapperClassName }
}

const StaffNameComponent: React.FC<{ staffName: string; byDelegateName?: string; actionClassName?: string }> = ({
  staffName,
  byDelegateName,
  actionClassName = ''
}) => {
  if (staffName === EBotName) {
    return <span className="text-ellipsis">{staffName}</span>
  }
  return (
    <>
      {byDelegateName ? <span className="text-ellipsis">{byDelegateName}</span> : null}
      {byDelegateName ? <span className={`text-static ${actionClassName} ml-4 mr-4`}>{i18n.get('代')}</span> : null}
      <span className="text-ellipsis">{staffName}</span>
    </>
  )
}

function fnSubstr16(str) {
  if (str.length > 16) {
    return str.substring(0, 16) + '...'
  }
  return str
}

export { fnMapLogs, flowStateMap, formatAttachments, fnSubstr16, addAtHighlighter }
