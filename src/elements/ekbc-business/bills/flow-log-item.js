import React from 'react'
import styles from './flow-log-item.module.less'
import { Popover } from 'antd'
import { Popover as EUIPopover } from '@hose/eui'
import classNames from 'classnames'
import Highlighter from 'react-highlight-words'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import { comment2MentionContent, formatRegStr } from './util/parse'
import { getMoney } from '../../../lib/misc'
import { skippedTypeMap, agreeType, printLogMap } from '@ekuaibao/lib/lib/enums'
import Money from '../../../elements/puppet/Money'
import AttachmentList from '../../puppet/attachmentList/AttachmentList'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import SVG_USER_PIC from '../../../images/no-user-avator.svg'
import EkbIcon from '../../../elements/ekbIcon'
import { getDisplayName, getStaffName, isPayNode } from './../../utilFn'
import Big from 'big.js'
import { isString } from '@ekuaibao/helpers'
import { renderCommentWithMarkdown } from './FlowLogItemWrapper'
import { isAIAgentNode } from '../../../elements/ai-agent-utils'
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')
const download = api.invokeServiceAsLazyValue('@bills:file:download')
export const ModifyCreateType = 'CREATE'
export const ModifyUpdateType = 'UPDATE'
const ModifyBillAction = 'freeflow.modify'
let lastVersionNum = 0
const emptyStr = '' // @i18n-ignore

function fnFilePreview(attachmentsData, clickItem) {
  preview()({ value: attachmentsData, line: clickItem })
}

function fnFileDownload(line) {
  download()(line)
}

const formatAttachments = attachments => {
  let items = fnFormatAttachment(attachments)
  let popoverContent = {
    length: items.length,
    attachmentsContent: (
      <AttachmentList
        onFilePreview={fnFilePreview.bind(this, items)}
        onFileDownload={fnFileDownload}
        fileList={items}
      />
    ),
    label: i18n.get('附件')
  }

  return popoverContent
}

export async function openOldFlowPlan(id) {
  const value = await api.invokeService('@audit:getVersionFlowPlanById', { planId: id })
  api.open('@bills:FlowPlanModal', { flowPlan: value.flowPlan, logs: [] })
}

export const renderComment = item => {
  const isRecalNode = get(item, 'attributes.isRecalNode')
  let {
    attributes: { participants = [], comment, keepItSecret }
  } = item
  participants = participants ? participants : []
  const com = comment2MentionContent(comment, participants, flowStateMap.staffDisplayConfigField)
  let highlighterWords = participants.map(item => ` @${item.name} `)
  highlighterWords = parseReg(highlighterWords)

  if (isRecalNode) {
    const oldFlowPlanId = get(item, 'attributes.oldFlowPlanId')
    comment = (
      <div className={styles['recalculate-comment-wrapper']}>
        <Highlighter highlightClassName={styles['highlight']} searchWords={highlighterWords} textToHighlight={com} />
        <div className="old-flow-action" onClick={() => openOldFlowPlan(oldFlowPlanId)}>
          {i18n.get('点击查看')}
        </div>
      </div>
    )
  }
  const element = (
    <Highlighter highlightClassName={styles['highlight']} searchWords={highlighterWords} textToHighlight={com} />
  )
  return renderCommentWithMarkdown(com, element)
}

export const renderAttachments = (attachments, props = {}) => {
  const data = formatAttachments(attachments)
  return (
    <EUIPopover title={data.label} content={data.attachmentsContent} placement={props.placement}>
      <div className={classNames(styles['attachment-wrapper'], props.className)}>
        {i18n.get(`{__k0} 个{__k1}`, { __k0: data.length, __k1: data.label })}
      </div>
    </EUIPopover>
  )
}

const isShowComment = (item, userInfo) => {
  let {
    attributes: { participants = [], keepItSecret }
  } = item
  participants = participants ? participants : []

  if (keepItSecret) {
    const isCurrent = userInfo && !!~participants.map(e => e.id).indexOf(userInfo.id)
  }
  return false
}

const renderVersion = (item, click) => {
  const action = () => {
    click && click(item)
  }
  const versionConfig = api.getState('@common.specificationVersionConfig')
  if (versionConfig && versionConfig.hideVersion) {
    return null
  }
  return (
    <div className={styles['version-wrapper']} onClick={action}>
      {i18n.get('查看本次修改内容 >')}
    </div>
  )
  return (
    <div className={styles['version-wrapper']} onClick={action}>
      {`${i18n.get('查看版本')}${item.version}` + `${item.version === lastVersionNum ? i18n.get('「最新」') : ''}`}
    </div>
  )
}

const renderApproveContent = ({ content, highlighterWords, hasAvatar = true, avatar, popoverContent = [] }, subDom) => {
  const renderAvatar = () => {
    if (avatar === 'EBotIconNode') {
      return <EkbIcon name="#EDico-EBot-AI" className={styles['user-img']} />
    }
    if (avatar === 'RecalculateIconNode') {
      return <EkbIcon name="#EDico-sys-recount" className={styles['user-img']} />
    }
    return <img src={avatar || SVG_USER_PIC} alt="" className={styles['user-img']} />
  }
  return (
    <div className={`${styles['horizontal']} mt-5`}>
      {hasAvatar && renderAvatar()}
      {popoverContent.length > 0 ? (
        <Popover overlayClassName={styles['flow-log-item-popover']} content={popoverContent} trigger="hover">
          <div className={classNames(styles['approve-content-wrapper'], { 'cur-p': !!popoverContent })}>
            {highlighterWords ? (
              <Highlighter
                highlightClassName={styles['content-highlight']}
                searchWords={highlighterWords}
                textToHighlight={content}
              />
            ) : (
              content
            )}
            {!!subDom && subDom}
          </div>
        </Popover>
      ) : (
        <div className={styles['approve-content-wrapper']}>
          {highlighterWords ? (
            <Highlighter
              highlightClassName={styles['content-highlight']}
              searchWords={highlighterWords}
              textToHighlight={content}
            />
          ) : (
            content
          )}
          {!!subDom && subDom}
        </div>
      )}
    </div>
  )
}

function fnGetName(item) {
  let name = getNameWithoutDelegate(item)
  // let byDelegateName = get(item, 'byDelegateId.name')
  let byDelegateName = item?.byDelegateId ? getStaffName(item.byDelegateId) : ''
  const byDelegateSatffDisplay = get(item, `byDelegateId.${flowStateMap.staffDisplayConfigField}`)

  byDelegateSatffDisplay &&
    isString(byDelegateSatffDisplay) &&
    (byDelegateName = `${byDelegateName} (${fnSubstr16(byDelegateSatffDisplay)})`)
  name = byDelegateName ? i18n.get(`{__k0}代{__k1}`, { __k0: byDelegateName, __k1: name }) : name

  return name
}

function getNameWithoutDelegate(item) {
  let name = ''
  if (get(item, 'operatorId') === 'ebot') {
    // ebot节点operatorId为ebot, 没有operatorId.name
    name = get(item, 'operatorId')
  } else {
    name = getStaffName(item?.operatorId)
  }
  const staffDisplay = get(item, `operatorId.${flowStateMap.staffDisplayConfigField}`)
  staffDisplay && isString(staffDisplay) && (name = name + ` (${fnSubstr16(staffDisplay)}) `)
  return name
}

function addAtHighlighter(item, userInfo) {
  let {
    attributes: { participants = [], comment }
  } = item
  participants = participants ? participants : []
  const isAtMe = userInfo && !!~participants.map(e => e.id).indexOf(userInfo.id)
  const content = comment2MentionContent(comment, participants, flowStateMap.staffDisplayConfigField)
  let highlighterWords = participants.map(item => ` @${item.name} `)
  highlighterWords = parseReg(highlighterWords)
  const atDom = isAtMe && (
    <span>
      {i18n.get('，')}
      {i18n.get('并')}
      <span className={styles['refer-me']}>{i18n.get('@我')}</span>
    </span>
  )

  return {
    atDom,
    content,
    highlighterWords
  }
}

const flowStateMap = {
  staffDisplayConfigField: '', // 通讯录显示配置字段
  retract: {
    approveContent(item) {
      const name = getNameWithoutDelegate(item)
      const content = `${name} ${i18n.get('撤回了单据')}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return renderApproveContent({ content, highlighterWords, avatar })
    }
  },
  withdraw: {
    approveContent(item) {
      const name = getNameWithoutDelegate(item)
      const content = `${name} ${i18n.get('撤回了单据')}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return renderApproveContent({ content, highlighterWords, avatar })
    }
  },
  addnode: {
    approveContent(item) {
      const { nextOperatorId, nextOperatorIds = [] } = item
      const name = fnGetName(item)
      const isShiftEbot = get(item, 'attributes.isShiftEbot')
      const nextNames = !!nextOperatorIds.length && getStaffName(nextOperatorIds[0])
      const nextStaffDsiplay = get(item, `nextOperatorId.${flowStateMap.staffDisplayConfigField}`)
      const nextOpName = getStaffName(nextOperatorId)
      const nextName = isShiftEbot
        ? 'EBot'
        : nextNames
        ? `${nextNames}等${nextOperatorIds.length}人`
        : nextStaffDsiplay
        ? nextOpName + ` (${nextStaffDsiplay}) `
        : nextOpName
      const addNodeType = get(item, 'attributes.addNodeType')
      let content = ''
      if (addNodeType === 'PRE_ADD_NODE' || addNodeType === 'AFT_ADD_NODE' || addNodeType === 'COMBINE_ADD_NODE') {
        content = `${name} ${i18n.get('选择')} ${nextName} ${i18n.get('加签审批')}`
      } else {
        content = `${name} ${i18n.get('将单据转交给')} ${nextName} ${i18n.get('审批')}`
      }
      const highlighterWords = parseReg([name, nextName])
      return { content, highlighterWords }
    },
    render(item, _, userInfo) {
      const {
        attributes: { comment },
        attachments
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      const hasAttachments = attachments && attachments.length > 0
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)

      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, atDom)}
          {comment && (
            <div className={`${styles['content-msg']} translate-ignore-class`}>
              {renderCommentWithMarkdown(commentContent, <Highlighter
                highlightClassName={styles['highlight']}
                searchWords={highlighterKey}
                textToHighlight={commentContent}
              />)}
            </div>
          )}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  print: {
    approveContent(item) {
      const {
        attributes: { action, autoRemind }
      } = item
      const name = fnGetName(item)
      const state = autoRemind ? 'autoRemind' : action
      const content = printLogMap(name)[state]
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return renderApproveContent({ content, highlighterWords, avatar })
    }
  },
  submit: {
    approveContent(item) {
      const { attributes } = item
      const { resubmitMethod } = attributes
      let name = ''
      let content = ''
      let commitName = ''
      if (resubmitMethod === 'TO_REJECTOR') {
        commitName = getNameWithoutDelegate(item)
        if (attributes.resubmitOperatorIds && Array.isArray(attributes.resubmitOperatorIds)) {
          name = attributes?.isToInvoiceApplicationNode
            ? i18n.get('开票申请')
            : attributes.resubmitOperatorIds
                .map(item => {
                  const itemCopy = { ...item }
                  const name = getStaffName(itemCopy)
                  const itemStaffDisplay = get(itemCopy, `${flowStateMap.staffDisplayConfigField}`)
                  itemStaffDisplay &&
                    isString(itemStaffDisplay) &&
                    (itemCopy.name = name + ` (${fnSubstr16(itemStaffDisplay)}) `)
                  return name
                })
                .join(',') || 'Ebot'
        }
        content = i18n.get('{__k0}直接送审给{__k1}', {
          __k0: commitName,
          __k1: name
        })
      } else {
        name = getNameWithoutDelegate(item)
        content = `${name} ${i18n.get('提交送审')}`
      }

      const highlighterWords = parseReg([commitName, name])
      return { content, highlighterWords }
    },
    render(item, click) {
      const {
        modifyFlowLog,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const creatVersion = modifyFlowLog && modifyFlowLog.find(item => item.operatorState === ModifyUpdateType)
      const { content, highlighterWords } = this.approveContent(item)
      const subDom = creatVersion && renderVersion(creatVersion, click)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, subDom)}
          {/* {comment && <div className={styles['content-msg']}>{comment}</div>} */}
        </div>
      )
    }
  },
  // 变更
  alter: {
    approveContent(item) {
      const name = fnGetName(item)
      let content = `${name} ${i18n.get('发起变更')}`
      return { content }
    },
    render(item, _, userInfo) {
      const {
        attachments,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = this.approveContent(item)
      const { atDom } = addAtHighlighter(item, userInfo)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  agree: {
    approveContent(item) {
      const name = fnGetName(item)
      const isEbotNode = get(item, 'attributes.isEbotNode')
      const isInvoiceApplicationNode = get(item, 'attributes.isInvoiceApplicationNode')
      const isRecalNode = get(item, 'attributes.isRecalNode')
      const result = get(item, 'attributes.extras.result')
      const msg = result || i18n.get('审批同意')
      let content = `${name} ${msg}`
      if (isEbotNode || isInvoiceApplicationNode) {
        content = `${name} ${i18n.get('执行完成')}`
      }
      if (isRecalNode) {
        content = `${name} ${i18n.get('重算完成')}`
      }
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item, _, userInfo) {
      const {
        attachments,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = this.approveContent(item)
      const { atDom } = addAtHighlighter(item, userInfo)
      const tips = get(item, 'attributes.extras.tips')
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, atDom)}
          {comment && <div className={classNames(styles['content-msg'], 'translate-ignore-class', { [styles['has-content-msg-tips']]: !!tips })}>
            {!!tips && <div className={styles['content-msg-tips']}>{tips}</div>}
            {renderComment(item)}
          </div>}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  autoAgree: {
    approveContent(item) {
      const {
        attributes: { autoAgreeType, skippedType }
      } = item
      const name = fnGetName(item)
      const resion =
        i18n.get(`，{__k0}：`, { __k0: i18n.get('原因') }) + (agreeType()[autoAgreeType] || i18n.get('审批流程配置'))
      let agreeResion = autoAgreeType === 'NO_AUTO_AGREE' ? '' : resion
      agreeResion = skippedType === 'AI_APPROVAL' ? '已由 AI 代为同意' : agreeResion
      const content = ` ${name} ${i18n.get('自动同意')}${agreeResion}`
      const highlighterWords = parseReg([name, agreeResion])
      return { content, highlighterWords }
    },
    render(item) {
      const {
        attachments,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length
      const { content, highlighterWords } = this.approveContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  reject: {
    approveContent(item) {
      const { attributes } = item
      const { resubmitMethod, nodeName } = attributes
      const name = fnGetName(item)
      const isCostControlCheck = get(item, 'attributes.isCostControlCheck')
      let desc = name + i18n.get('驳回了单据')
      if (resubmitMethod === 'TO_REJECTOR') {
        desc = i18n.get('{__k0}驳回了单据，重新提交后由{__k1}继续审批', { __k0: name, __k1: name })
      } else if (nodeName) {
        desc = i18n.get('{__k0}驳回至{__k1}节点', { __k0: name, __k1: nodeName })
      }
      const content = `${isCostControlCheck ? name + i18n.get('单据提交失败') : desc}`
      const highlighterWords = parseReg([name, nodeName])
      return { content, highlighterWords }
    },
    render(item, _, userInfo) {
      const {
        attachments,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length
      const { content, highlighterWords } = this.approveContent(item)
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)
      const element = comment ? (
        <Highlighter
          highlightClassName={styles['highlight']}
          searchWords={highlighterKey}
          textToHighlight={commentContent}
        />
      ) : (
        comment
      )
      const tips = get(item, 'attributes.extras.tips')
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, atDom)}
          {comment && (
            <div className={classNames(styles['content-msg'], 'translate-ignore-class', { [styles['has-content-msg-tips']]: !!tips })}>
              {!!tips && <div className={styles['content-msg-tips']}>{tips}</div>}
              {renderCommentWithMarkdown(commentContent, element)}
            </div>
          )}
          {!!hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  nullify: {
    approveContent(item) {
      const name = fnGetName(item)
      const content = `${name} ${i18n.get('作废了单据')}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return renderApproveContent({ content, highlighterWords, avatar })
    }
  },
  activate: {
    approveContent(item) {
      // const name = get(item, 'operatorId.name')
      const name = fnGetName(item)
      const content = `${name} ${i18n.get('激活了单据')}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return renderApproveContent({ content, highlighterWords, avatar })
    }
  },
  pay: {
    approveContent(item, doc) {
      const name = getNameWithoutDelegate(item)
      const {
        form: { payMoney }
      } = doc
      const isPayZero = payMoney && Number(getMoney(payMoney)) === 0
      let content = `${name} ${i18n.get('完成了支付')}`
      if (isPayZero) {
        content = content + i18n.get('（支付金额为0，无实际支付行为发生）')
      }
      // 后台没法，只能让判断文案
      if (item?.attributes?.comment === '实际付款账户') {
        const name = item?.attributes?.accountCompany?.name
        content = content + `（实际付款账户：${name}）`
      } else if (item?.attributes?.paymentChannel === 'OFFLINE' && item?.attributes?.comment) {
        content = content + item?.attributes?.comment
      }
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item, click, userinfo, doc) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item, doc)
      return renderApproveContent({ content, highlighterWords, avatar })
    }
  },
  paying: {
    approveContent(item) {
      const {
        attributes: { accountCompany, paymentChannel },
        dynamicChannelMap
      } = item
      const payName = accountCompany ? accountCompany?.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap[paymentChannel]?.name || i18n.get('无')
      let content = i18n.get(
        `{__k0} {__k1}
                      （{__k2}：{__k3}，{__k4}：{__k5}）`,
        {
          __k0: name,
          __k1: i18n.get('发起支付'),
          __k2: i18n.get('方式'),
          __k3: i18n.get(payChannel),
          __k4: i18n.get('账户'),
          __k5: i18n.get(payName)
        }
      )
      let highlighterWords = parseReg([name, payChannel, payName])

      if (item.attributes.hasOwnProperty('reviewResult')) {
        if (item.attributes?.reviewResult) {
          content = name + i18n.get('同意支付')
        } else {
          content = name + i18n.get('驳回支付')
        }
        highlighterWords = parseReg([name])
      }

      return { content, highlighterWords }
    },
    popoverContent(item) {
      const {
        attributes: { paymentInfos = [] }
      } = item
      const popoverContent = paymentInfos?.map(payment => {
        return (
          <div key={Math.random()}>
            {payment.feeTypeId
              ? i18n.get(
                  `{__k0}：{__k1}，
          {__k2}：`,
                  { __k0: i18n.get('费用类型'), __k1: get(payment, 'feeTypeId.name'), __k2: i18n.get('支付金额') }
                )
              : i18n.get(`{__k0}：`, { __k0: i18n.get('支付金额') })}
            <Money className="dis-ib" value={payment.amount || 0} />
            {i18n.get(`，{__k0}：{__k1}`, { __k0: i18n.get('收款账户'), __k1: get(payment, 'payeeId.name') })}
          </div>
        )
      })
      return { popoverContent }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      const { popoverContent } = this.popoverContent(item)

      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar, popoverContent })}
          {comment && (
            <div className={`${styles['content-msg']} translate-ignore-class`}>
              {item.attributes?.hasOwnProperty?.('reviewResult') && !item.attributes?.reviewResult
                ? i18n.get('原因：')
                : ''}
              {comment}
            </div>
          )}
        </div>
      )
    }
  },
  repaying: {
    approveContent(item) {
      const {
        attributes: { accountCompany, paymentChannel, repayReason = '' },
        dynamicChannelMap
      } = item
      const payName = accountCompany ? accountCompany.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap[paymentChannel].name || i18n.get('无')
      const content = i18n.get(
        `{__k0} {__k1}
                      （{__k2}：{__k3}，{__k4}：{__k5}）
                        {__k6}: {__k7}`,
        {
          __k0: name,
          __k1: i18n.get('发起重新支付'),
          __k2: i18n.get('方式'),
          __k3: i18n.get(payChannel),
          __k4: i18n.get('账户'),
          __k5: i18n.get(payName),
          __k6: i18n.get('原因'),
          __k7: repayReason || emptyStr
        }
      )
      const highlighterWords = parseReg([name, payChannel, payName])
      return { content, highlighterWords }
    },
    popoverContent(item) {
      const {
        attributes: { paymentInfos = [] }
      } = item
      const popoverContent = paymentInfos?.map(payment => {
        return (
          <div>
            {payment.feeTypeId
              ? i18n.get(
                  `{__k0}：{__k1}，
          {__k2}：`,
                  { __k0: i18n.get('费用类型'), __k1: get(payment, 'feeTypeId.name'), __k2: i18n.get('支付金额') }
                )
              : i18n.get(`{__k0}：`, { __k0: i18n.get('支付金额') })}
            <Money className="dis-ib" value={payment.amount || 0} />
            {i18n.get(`，{__k0}：{__k1}`, { __k0: i18n.get('收款账户'), __k1: get(payment, 'payeeId.name') })}
          </div>
        )
      })
      return { popoverContent }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      const { popoverContent } = this.popoverContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar, popoverContent })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
        </div>
      )
    }
  },
  'pay.by.offline': {
    approveContent(item) {
      const {
        attributes: { accountCompany, paymentChannel, paymentInfos },
        dynamicChannelMap
      } = item
      const { amount, payeeId = {}, feeTypeId = {} } = (paymentInfos && paymentInfos[0]) || {}
      const _amount = amount?.standard ? amount?.standard : amount
      const payName = accountCompany ? accountCompany.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap[paymentChannel].name || i18n.get('无')
      const content = `
          ${i18n.get(
            `{__k0} {__k1}
                        ({__k2}：{__k3}，{__k4}：{__k5}，
                        {__k6}:`,
            {
              __k0: name,
              __k1: i18n.get('修改了支付方式'),
              __k2: i18n.get('方式'),
              __k3: i18n.get('线下支付'),
              __k4: i18n.get('费用类型'),
              __k5: i18n.get((feeTypeId && feeTypeId.name) || i18n.get('无')),
              __k6: i18n.get('支付金额')
            }
          )}
          ${new Big(_amount || 0).toFixed(2)}
          ${i18n.get(`，{__k0}: {__k1}）`, { __k0: i18n.get('收款账户'), __k1: payeeId.name || i18n.get('无') })}
          `
      const highlighterWords = parseReg([name, payChannel, payName])
      return { content, highlighterWords }
    },
    popoverContent(item) {
      const {
        attributes: { paymentInfos = [] }
      } = item
      const popoverContent = paymentInfos?.map(payment => {
        return (
          <div>
            {payment.feeTypeId
              ? i18n.get(
                  `{__k0}：{__k1}，
          {__k2}：`,
                  { __k0: i18n.get('费用类型'), __k1: get(payment, 'feeTypeId.name'), __k2: i18n.get('支付金额') }
                )
              : i18n.get(`{__k0}：`, { __k0: i18n.get('支付金额') })}
            <Money className="dis-ib" value={payment.amount || 0} />
            {i18n.get(`，{__k0}：{__k1}`, {
              __k0: i18n.get('收款账户'),
              __k1: get(payment, 'payeeId.name', i18n.get('无'))
            })}
          </div>
        )
      })
      return { popoverContent }
    },
    render(item) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return (
        <div className={styles['content-wrapper']}>{renderApproveContent({ content, highlighterWords, avatar })}</div>
      )
    }
  },
  failure: {
    approveContent(item) {
      const {
        attributes: { failureReason }
      } = item
      const content = i18n.get(`{__k0}，{__k1}：{__k2}`, {
        __k0: i18n.get('支付失败'),
        __k1: i18n.get('原因'),
        __k2: failureReason
      })
      const highlighterWords = parseReg([failureReason])
      return { content, highlighterWords }
    },
    popoverContent(item) {
      const {
        attributes: { paymentInfos = [] }
      } = item
      const popoverContent = paymentInfos?.map(payment => {
        return (
          <div>
            <p>
              {payment.feeTypeId
                ? i18n.get(
                    `{__k0}：{__k1}，
            {__k2}：`,
                    { __k0: i18n.get('费用类型'), __k1: payment.feeTypeId.name, __k2: i18n.get('支付金额') }
                  )
                : i18n.get(`{__k0}：`, { __k0: i18n.get('支付金额') })}
              <Money className="dis-ib" value={payment.amount || 0} />
              {i18n.get(`，{__k0}：{__k1}`, {
                __k0: i18n.get('收款账户'),
                __k1: payment?.payeeId?.name
              })}
            </p>
            <p>{i18n.get(`{__k0}：{__k1}`, { __k0: i18n.get('原因'), __k1: payment.respMsg || emptyStr })}</p>
          </div>
        )
      })
      return { popoverContent }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      const { popoverContent } = this.popoverContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar, popoverContent })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
        </div>
      )
    }
  },
  'select.approver': {
    // 在logs中只保存了审批人的id或者name，但在审批流节点中保存了完整的信息
    // 这里通过id在node中查找对应的节点，然后获取审批人的完整信息
    getNewApproverNames(newApproverIds = [], dataSource) {
      const planNode = dataSource?.plan?.nodes || []
      const newApproverNames = newApproverIds.map(id => {
        const node = planNode.find(item => item.approverId?.id === id)
        return node ? getStaffName(node.approverId) : ''
      })
      return newApproverNames.join(i18n.get('，'))
    },
    approveContent(item, userInfo, dataSource) {
      let {
        attributes: { name: nodeName, label: nodeLabel, oldApproverId, newApproverId, newApproverNames, newApproverIds }
      } = item
      // 修复oldApproverId为null时，报错，同时修改文案： "change-no-approver": "{name} changed the approver of {nodeName} to {nextName}"
      let preName = oldApproverId ? getStaffName(oldApproverId) : ''
      const nextName = newApproverIds
        ? this.getNewApproverNames(newApproverIds, dataSource)
        : newApproverNames?.join(i18n.get('，')) || ''
      const name = fnGetName(item)
      const key = preName ? 'change-approver' : 'change-no-approver'
      nodeName = isPayNode(item.attributes) ? (nodeLabel ? nodeLabel : nodeName) : nodeName
      const preStaffDisplay = oldApproverId?.[flowStateMap.staffDisplayConfigField]
      preStaffDisplay && isString(preStaffDisplay) && (preName = `${preName} (${fnSubstr16(preStaffDisplay)}) `)
      const content = i18n.get(key, { name, nodeName, preName, nextName })
      const highlighterWords = parseReg([name, nodeName, preName, nextName])
      return { content, highlighterWords }
    },
    render(item, _, userInfo, dataSource) {
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item, userInfo, dataSource)
      return renderApproveContent({ content, highlighterWords, avatar })
    }
  },
  skipped: {
    approveContent(item) {
      const {
        attributes: { name, skippedType }
      } = item

      const skipDetail = skippedTypeMap()[skippedType]
      const content = i18n.get(`{__k0} {__k1} {__k2}，{__k3}：{__k4}`, {
        __k0: i18n.get('自动跳过'),
        __k1: name,
        __k2: i18n.get('环节'),
        __k3: i18n.get('原因'),
        __k4: skipDetail ? i18n.get(skipDetail) : skipDetail
      })
      const highlighterWords = parseReg([name, skipDetail])
      return { content, highlighterWords }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const { content, highlighterWords } = this.approveContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, hasAvatar: false })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
        </div>
      )
    }
  },
  modify: {
    approveContent(item, text) {
      let defaultText = item.operatorState && fnGetOperatorStateStr(item.operatorState)
      const name = fnGetName(item)
      const content = `${name} ${text || defaultText}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item, click) {
      let { flowVersionedId } = item
      let defaultText = i18n.get('修改了单据')
      let text = (item.operatorState && fnGetOperatorStateStr(item.operatorState)) || defaultText
      if (item.operatorId === 'ebot') {
        item.operatorId = { name: 'EBot', avatar: 'EBotIconNode' }
      }
      const { editeReason } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item, text)
      const subDom = flowVersionedId && renderVersion(item, click)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, subDom)}
          {editeReason && <div className={`${styles['content-msg']} translate-ignore-class`}>{editeReason}</div>}
        </div>
      )
    }
  },
  comment: {
    approveContent(item) {
      const name = fnGetName(item)
      const content = `${name} ${i18n.get('添加了评论')}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item, _, userInfo) {
      const {
        attachments,
        attributes: { participants = [], comment, receiverIds = [] }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const isAtMe = userInfo && !!~participants.map(e => e.id).indexOf(userInfo.id)
      const com = comment2MentionContent(comment, participants, flowStateMap.staffDisplayConfigField)
      let highlighterWords = participants.map(item => ` @${getStaffName(item)} `)
      highlighterWords = parseReg(highlighterWords)
      const receivers = receiverIds.map(item => getStaffName(item)).join(i18n.get('、'))
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords: contentHighlight } = this.approveContent(item)
      const subDom = isAtMe && (
        <span>
          {i18n.get('，')}
          {i18n.get('并')}
          <span className={styles['refer-me']}>{i18n.get('@我')}</span>
        </span>
      )

      const element = comment ? (
        <Highlighter highlightClassName={styles['highlight']} searchWords={highlighterWords} textToHighlight={com} />
      ) : (
        comment
      )
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords: contentHighlight, avatar }, subDom)}
          {comment && (
            <div className={`${styles['content-msg']} translate-ignore-class`}>
              {renderCommentWithMarkdown(com, element)}
              {receivers.length > 0 && (
                <div className={styles['dashed-separator']}>{i18n.get('mentioned', { name: receivers })}</div>
              )}
            </div>
          )}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  carbonCopy: {
    approveContent(item) {
      const {
        attributes: { carbonCopy }
      } = item
      const staffNames =
        (carbonCopy &&
          carbonCopy.map(
            line =>
              `${getDisplayName(line)}  ${
                line?.[flowStateMap.staffDisplayConfigField] && isString(line?.[flowStateMap.staffDisplayConfigField])
                  ? ` (${fnSubstr16(line?.[flowStateMap.staffDisplayConfigField])}) `
                  : ''
              } `
          )) ||
        []

      let content = i18n.get('根据流程配置，抄送单据给') + staffNames.join(',')
      if (staffNames.length === 0) {
        content = i18n.get('自动跳过，原因：匹配不到抄送人')
      }
      const highlighterWords = parseReg(staffNames)
      return { content, highlighterWords }
    },
    render(item) {
      const avatar = get(item, 'operatorId.avatar') || 'EBotIconNode'
      const { content, highlighterWords } = this.approveContent(item)
      return (
        <div className={styles['content-wrapper']}>{renderApproveContent({ content, highlighterWords, avatar })}</div>
      )
    }
  },
  back: {
    approveContent(item) {
      const name = fnGetName(item)
      const type = get(item, 'attributes.type')
      let str = i18n.get('将单据重置到待支付状态')
      if (type === 'rollback') {
        const nodeName = get(item, 'attributes.nodeName')
        str = i18n.get(`{__k0}「{__k1}」{__k2}`, {
          __k0: i18n.get('将单据回退到'),
          __k1: nodeName,
          __k2: i18n.get('节点')
        })
      }
      const content = `${name} ${str}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const {
        attachments,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = this.approveContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  send: renderExpress('send'),
  receive: renderExpress('receive'),
  receiveExcep: renderExpress('receiveExcep'),
  cancelReceiveExcep: renderExpress('cancelReceiveExcep'),
  compliance: {
    approveContent(item) {
      const name = fnGetName(item)
      let content = `${name} ${i18n.get('完成了合规性确认')}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
        </div>
      )
    }
  },
  'signing.failed': {
    approveContent(item) {
      const name = fnGetName(item)
      let content = `${name} ${i18n.get('签署失败')}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
        </div>
      )
    }
  },
  'pay.partial.paying': {
    approveContent(item) {
      const {
        attributes: { accountCompany, paymentChannel },
        dynamicChannelMap
      } = item
      const payName = accountCompany ? accountCompany.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap[paymentChannel].name || i18n.get('无')
      const content = i18n.get(
        `{__k0} {__k1}
                      （{__k2}：{__k3}，{__k4}：{__k5}）`,
        {
          __k0: name,
          __k1: i18n.get('发起部分支付'),
          __k2: i18n.get('方式'),
          __k3: i18n.get(payChannel),
          __k4: i18n.get('账户'),
          __k5: i18n.get(payName)
        }
      )
      const highlighterWords = parseReg([name, payChannel, payName])
      return { content, highlighterWords }
    },
    popoverContent(item) {
      const {
        attributes: { paymentInfos = [] }
      } = item
      const popoverContent = paymentInfos?.map(payment => {
        return (
          <div key={Math.random()}>
            {payment.code ? `${payment.code}，` : ''}
            {i18n.get(`{__k0}：`, { __k0: i18n.get('支付金额') })}
            <Money className="dis-ib" value={payment.amount || 0} />
            {i18n.get(`，{__k0}：{__k1}`, { __k0: i18n.get('收款账户'), __k1: get(payment, 'payeeId.name') })}
          </div>
        )
      })
      return { popoverContent }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      const { popoverContent } = this.popoverContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar, popoverContent })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
        </div>
      )
    }
  },
  'pay.partial.success': {
    approveContent(item) {
      const {
        attributes: { accountCompany, paymentChannel },
        dynamicChannelMap
      } = item
      const payName = accountCompany ? accountCompany.name : i18n.get('无')
      const name = getNameWithoutDelegate(item)
      const payChannel = dynamicChannelMap[paymentChannel].name || i18n.get('无')
      const content = i18n.get(
        `{__k0} {__k1}
                      （{__k2}：{__k3}，{__k4}：{__k5}）`,
        {
          __k0: `${name}`,
          __k1: i18n.get('部分支付完成'),
          __k2: i18n.get('方式'),
          __k3: i18n.get(payChannel),
          __k4: i18n.get('账户'),
          __k5: i18n.get(payName)
        }
      )
      const highlighterWords = parseReg([name, payChannel, payName])
      return { content, highlighterWords }
    },
    popoverContent(item) {
      const {
        attributes: { paymentInfos = [] }
      } = item
      const popoverContent = paymentInfos?.map(payment => {
        return (
          <div key={Math.random()}>
            {payment.code ? `${payment.code}，` : ''}
            {i18n.get(`{__k0}：`, { __k0: i18n.get('支付金额') })}
            <Money className="dis-ib" value={payment.amount || 0} />
            {i18n.get(`，{__k0}：{__k1}`, { __k0: i18n.get('收款账户'), __k1: get(payment, 'payeeId.name') })}
          </div>
        )
      })
      return { popoverContent }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      const { popoverContent } = this.popoverContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar, popoverContent })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
        </div>
      )
    }
  },
  'pay.partial.failure': {
    approveContent(item) {
      const {
        attributes: { failureReason }
      } = item
      const content = i18n.get(`{__k0}，{__k1}：{__k2}`, {
        __k0: i18n.get('部分支付失败'),
        __k1: i18n.get('原因'),
        __k2: failureReason
      })
      const highlighterWords = parseReg([failureReason])
      return { content, highlighterWords }
    },
    popoverContent(item) {
      const {
        attributes: { paymentInfos = [] }
      } = item
      const popoverContent = paymentInfos?.map(payment => {
        return (
          <div>
            <p>
              {payment.code ? `${payment.code}，` : ''}
              {payment.feeTypeId
                ? i18n.get(
                    `{__k0}：{__k1}，
            {__k2}：`,
                    { __k0: i18n.get('费用类型'), __k1: payment.feeTypeId.name, __k2: i18n.get('支付金额') }
                  )
                : i18n.get(`{__k0}：`, { __k0: i18n.get('支付金额') })}
              <Money className="dis-ib" value={payment.amount || 0} />
              {i18n.get(`，{__k0}：{__k1}`, { __k0: i18n.get('收款账户'), __k1: payment?.payeeId?.name })}
            </p>
            <p>{i18n.get(`{__k0}：{__k1}`, { __k0: i18n.get('原因'), __k1: payment.respMsg || emptyStr })}</p>
          </div>
        )
      })
      return { popoverContent }
    },
    render(item) {
      const {
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const { content, highlighterWords } = this.approveContent(item)
      const { popoverContent } = this.popoverContent(item)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar, popoverContent })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
        </div>
      )
    }
  },
  'admin.skipnode': {
    approveContent(item) {
      const name = fnGetName(item)
      let content = `${name} ${i18n.get('跳过审批')} ${i18n.get(`原因：管理员({__k0})手动跳过`, { __k0: name })} `
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item, _, userInfo) {
      const {
        attachments,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = this.approveContent(item)
      const { atDom } = addAtHighlighter(item, userInfo)

      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  }
}

function renderExpress(type) {
  return {
    approveContent(item) {
      let text = ''
      if (type === 'receive') {
        text = i18n.get('确认收单')
      } else if (type === 'receiveExcep') {
        text = i18n.get('收单异常')
      } else if (type === 'cancelReceiveExcep') {
        text = i18n.get('取消收单异常')
      } else {
        if (item.attributes.expressNum) {
          text = i18n.get('寄送单据')
        } else {
          text = i18n.get('跳过寄送')
        }
      }
      const name = fnGetName(item)
      const content = `${name} ${i18n.get(text)}`
      const highlighterWords = parseReg([name])
      return { content, highlighterWords }
    },
    render(item, _, userInfo) {
      const {
        attachments,
        attributes: { comment }
      } = item
      const avatar = get(item, 'operatorId.avatar')
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = this.approveContent(item)
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)

      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, avatar }, atDom)}
          {comment && (
            <div className={`${styles['content-msg']} translate-ignore-class`}>
              <Highlighter
                highlightClassName={styles['highlight']}
                searchWords={highlighterKey}
                textToHighlight={commentContent}
              />
            </div>
          )}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  }
}

const fnMapLogs = logs => {
  let flowLogs = []
  logs.forEach(item => {
    const { modifyFlowLog, ...rest } = item
    if (modifyFlowLog) {
      lastVersionNum = modifyFlowLog.length ? modifyFlowLog[modifyFlowLog.length - 1].version : 0
      let mLogs = modifyFlowLog
        .filter(item => item.operatorState !== ModifyCreateType)
        .map(line => {
          const operatorMap = {
            openapi: { name: 'OpenAPI' },
            '320-corporateReceipt': { name: i18n.get('收款业务'), id: '320-corporateReceipt' }
          }
          let operatorState = line.operatorState
          if (line.operatorId === '320-corporateReceipt' && line.operatorState === 'REPLENISH_INVOICE') {
            operatorState = 'REPLENISH_INVOICE_AUTO'
          }

          return {
            ...line,
            time: line.operatorTime,
            action: ModifyBillAction,
            attributes: { comment: line.editeReason },
            operatorState,
            operatorId: operatorMap[line.operatorId] ?? line.operatorId
          }
        })
      mLogs.length ? flowLogs.push(rest) : flowLogs.push(item)
      flowLogs = flowLogs.concat(mLogs)
    } else {
      flowLogs.push(item)
    }
  })
  return flowLogs
}

function parseReg(items = []) {
  return items.map(item => {
    return formatRegStr(item)
  })
}

function fnGetOperatorStateStr(operatorState) {
  let str = ''
  switch (operatorState) {
    case 'REPLENISH_INVOICE':
      str = i18n.get('补充了发票')
      break
    case 'REPLENISH_INVOICE_AUTO':
      str = i18n.get('自动补充了发票')
      break
    case 'CONFIRM_INVOICE':
      str = i18n.get('确认了发票')
      break
    case 'DELETE_INVOICE':
      str = i18n.get('删除了发票')
      break
    default:
      str = i18n.get('修改了单据')
      break
  }
  return str
}

function fnSubstr16(str) {
  return fnSubstrByCount(str, 16)
}

const fnSubstrByCount = (str, count) => {
  if (str.length > count && !window.isInWeComISV) {
    return str.substring(0, count) + '...'
  }
  return str
}

export { fnMapLogs, flowStateMap, formatAttachments, fnSubstr16, fnSubstrByCount, addAtHighlighter }
