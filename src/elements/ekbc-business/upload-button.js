/**************************************************
 * Created by nany<PERSON>ing<PERSON> on 08/06/2017 19:01.
 **************************************************/
import React, { PureComponent } from 'react'
import { Modal } from 'antd'
import { showModal } from '@ekuaibao/show-util'
import { Fetch } from '@ekuaibao/fetch'
import { FilesUploader, fixData, generateFileName, getFileSuffix, getFileKey } from '@ekuaibao/uploader'
import { showMessage } from '@ekuaibao/show-util'
import MessageCenter from '@ekuaibao/messagecenter'
import styles from './upload-button.module.less'
import TokenData from '../../lib/token-data'
import { logUploadFileError } from '../../lib/logs'
import { app as api } from '@ekuaibao/whispered'
import { uuid } from '@ekuaibao/helpers'
import { Button, message, Tooltip } from '@hose/eui'
import { OutlinedDirectionUpload, OutlinedTipsMaybe } from '@hose/eui-icons'
import { defaultInvalidSuffixes, fetchAttachmentConfig, parseAttachmentTipText } from '../../lib/fnParseTemplateField'
import { getVariation, getBoolVariation } from "../../lib/featbit"

const MAX_RETRY_COUNT = 3

function getFileId(keys) {
  return Fetch.POST(
    '/api/v1/attachment/attachments',
    {},
    {
      body: {
        keys: keys
      }
    }
  )
}

function buildData(file, tokenData, isOCR, uploadServiceUrl) {
  let { name } = file
  if (isOCR && IS_STANDALONE) {
    // 私有化的OCR不用上传图片的名称，如果有特殊字符切割服务有问题
    name = uuid(12) + getFileSuffix(name)
  }
  const partSize = getVariation('multipart-upload-partsize-new', '0')
  return {
    key: generateFileName(name),
    'x:originalname': name,
    token: tokenData && tokenData.token,
    uploadType: uploadServiceUrl?.fileServiceType,
    partSize: partSize
  }
}

const maxSize = 64

export default class UploadButton extends PureComponent {
  token = null
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = new MessageCenter()
    this.tokenData = TokenData.getInstance()
    this.state = { status: 'done', attachmentSetting: {},  fileRetryMap: {} }
  }

  componentDidMount() {
    this.bus.on('over', this.handleOver)
    this.getConfig()
  }

  componentWillMount() {
    this.props.bus && this.props.bus.on('gpyImageList:change', this.extHandleOnchage.bind(this))
  }

  componentWillUnmount() {
    const uploadServiceUrl = api.getState()['@common'].uploadServiceUrl
    const fileServiceType = uploadServiceUrl?.fileServiceType
    if (fileServiceType !== 'alioss') {
      this.tokenData.cleanToken()
    }
    this.props.bus && this.props.bus.un('gpyImageList:change', this.extHandleOnchage.bind(this))
    this.setState = (state, callback) => {
      return
    }
  }

  getConfig = async () => {
    const { suffixesPath, suffixesFiled } = this.props
    const attachmentSetting = await fetchAttachmentConfig()
    let invalidSuffixesConfig = {}

    if (attachmentSetting?.onFields?.includes(suffixesFiled) || attachmentSetting?.onPath?.includes(suffixesPath)) {
      attachmentSetting?.fileType?.forEach(item => {
        item.name?.forEach(suffix => {
          invalidSuffixesConfig[suffix] = item.size
        })
      })
    }

    this.setState({
      attachmentSetting: {
        onPath: attachmentSetting?.onPath || [],
        onFields: attachmentSetting?.onFields || [],
        invalidSuffixesConfig: invalidSuffixesConfig || {}
      }
    })
  }

  uploadPrepare = async () => {
    try {
      const uploadServiceUrl = api.getState()['@common'].uploadServiceUrl
      const fileServiceType = uploadServiceUrl?.fileServiceType
      this.token = await this.tokenData.data(fileServiceType)
    } catch (e) {
      Modal.warning({
        title: i18n.get('获取上传验证码失败,请稍候重试'),
        content: e.msg || e.message
      })
      return Promise.reject(e)
    }
  }

  extHandleOnchage(gpyImageList) {
    const handleSelect = this.refs.filesUpLoader.handleSelect
    if (handleSelect) {
      handleSelect({ target: { files: gpyImageList } })
      this.handleOnChange(gpyImageList)
    }
  }

  fnFixData = (file, params = {}) => {
    const uploadServiceUrl = api.getState()['@common'].uploadServiceUrl
    const { fileName, uploadLimit } = this.props
    const fileServiceType = uploadServiceUrl && uploadServiceUrl.fileServiceType
    const partSize = getVariation('multipart-upload-partsize-new', '0')
    const enableCheckpoint = getBoolVariation('multipart-upload-enable-checkpoint', false)
    const checkpointStorageType = getVariation('multipart-upload-checkpoint-storage-type', 'indexedDB')
    const parallel = getVariation('multipart-upload-parallel')
    if (fileName) {
      const type = file.type
      const newFile = new File([file], fileName, { type })
      return fixData(newFile, fileServiceType, this.token, this.tokenErrorAction, {
        isCompress: uploadLimit?.isCompress,
        quality: uploadLimit?.quality,
        partSize: partSize,
        enableCheckpoint,
        checkpointStorageType,
        parallel,
        ...params
      })
    }
    return fixData(file, fileServiceType, this.token, this.tokenErrorAction, {
      isCompress: uploadLimit?.isCompress,
      quality: uploadLimit?.quality,
      partSize: partSize,
      enableCheckpoint,
      checkpointStorageType,
      parallel,
      ...params
    })
  }

  tokenErrorAction = () => {
    message.warning(i18n.get('获取上传验证码失败,请稍候重试'))
    return this.uploadPrepare()
  }

  fnGetFileType = file => {
    // 部分类型的文件 file 实例无 type类型 现以文件名后缀为 type
    const fileNameArr = file?.name?.split('.')
    return fileNameArr?.pop()?.toLocaleLowerCase()
  }

  handleOver = data => {
    const { invalidSuffixesConfig } = this.state.attachmentSetting
    let { fileMaxSize } = this.props
    if (data && data.length) {
      let Content = () => {
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            {data.map(o => {
              const type = this.fnGetFileType(o)
              const limitSize = invalidSuffixesConfig?.[type]
              return (
                <div>
                  <span>{o.name}</span>
                  {limitSize && <span>，{`上限为${limitSize}MB`}</span>}
                </div>
              )
            })}
          </div>
        )
      }

      showModal.error({
        title: i18n.get('file-max', { maxSize: fileMaxSize || maxSize - 4 }), // 比最大的小4M
        content: <Content />,
        onOk: () => {}
      })
    }
  }

  handleOnStart = file => {
    const { onStart } = this.props
    if (file && file.length > 0) {
      this.setState({ status: 'start' })
      onStart && onStart()
    }
  }

  handleOnDone = uploaderFileList => {
    let { fileList = [], onFinish, showSuccessTip = true } = this.props
    let cloneList = fileList.slice(0)
    let keys = []
    let fileNames = {}
    let hashMap = {}
    const enableCheckpoint = getBoolVariation('multipart-upload-enable-checkpoint', false)
    const { fileRetryMap } = this.state
    // 标记是否有需要重试的文件
    let hasRetrying = false
    uploaderFileList.forEach(file => {
      if (file.status === 'error') {
        this.setState({ status: 'done' })
        const name = file.name || ''
        if (enableCheckpoint) {
          const retryObj = fileRetryMap[file.fileKey]
          if (retryObj && retryObj.retryCount) {
            hasRetrying = true
            return
          }
        }
        logUploadFileError(file)
        showMessage.error(i18n.get(`「{__k0}」{__k1}!`, { __k0: name, __k1: i18n.get('上传失败') }))
      } else {
        let { response } = file
        let key = response.key

        keys.push({ key, filename: response['x:originalname'] })
        fileNames[key] = response['x:originalname']
        hashMap[key] = response.hash
      }
    })
    if(keys.length > 0){
      getFileId(keys).then(({ items }) => {
        if (items && items.length) {
          items.forEach(item => {
            let { id, url, key, thumbUrl } = item
            let f = {
              key,
              id,
              url,
              fileName: fileNames[key],
              thumbUrl,
              platform: item.platform,
              hash: hashMap[key]
            }
            cloneList.push(f)
          })
          this.setState({ status: 'done' })
          onFinish && onFinish(cloneList)
          if (showSuccessTip) {
            showMessage.success(i18n.get('上传成功'))
          }
          let filesUpLoader = this.refs['filesUpLoader']
          let input = filesUpLoader && filesUpLoader.refs['input']
          if (input) input.value = ''
        }
      })
      return
    }
    // 没有任何成功的文件，如果还有文件在重试，直接 return，不触发 onFinish
    if (hasRetrying) {
      return
    }
    // 没有成功的文件，也没有文件在重试，说明所有文件都失败且重试用尽，返回 fileList
    onFinish && onFinish(cloneList) 
  }

  handleOnChange = uploaderFileList => {
    let { onChange } = this.props
    onChange && onChange(uploaderFileList)
  }

  handleOnRetry = (file, fileKey) => {
    const filesUpLoader = this.refs['filesUpLoader']
    if (filesUpLoader && typeof filesUpLoader.handleSelect === 'function') {
      filesUpLoader.handleSelect({ target: { files: [file] }, fileKey, retryUploadFile: true });
    }
  }

  handleOnError = (error, file, fileKey) => {
    const { onError } = this.props
    const { fileRetryMap } = this.state
    const enableCheckpoint = getBoolVariation('multipart-upload-enable-checkpoint', false)
    // 最多重试3次
    if (enableCheckpoint) {
      // 重新上传同一个文件
      let retryObj = fileRetryMap[fileKey]
      if (!retryObj) {
        retryObj = { retryCount: 0 }
      }
      api?.logger?.info('multipart-upload-enable-checkpoint', {
        error,
        fileKey,
        retryCount: retryObj.retryCount
      })
      if (retryObj.retryCount < MAX_RETRY_COUNT) {
        retryObj.retryCount += 1
        fileRetryMap[fileKey] = retryObj
        this.setState({ fileRetryMap })
        this.handleOnRetry(file, fileKey)
        return
      }
    }
    // 超过重试次数或无断点信息，才回调onError
    onError && onError(error)
  }

  handleOnCheckpointInfo = (checkpointInfo) => {
    const { onCheckpointInfo } = this.props
    onCheckpointInfo && onCheckpointInfo(checkpointInfo)
  }

  handleDingTalkUpload = async () => {
    const { disable } = this.props
    if (disable) return
    const spaceId = await api.invokeService('@common:get:space:id')
    await api.invokeService('@common:ding:pan:space:authorize')
    const result = await api.invoke('@vendor:dingtalk:uploadAttachment', {
      types: ['file', 'space', 'photo'],
      space: { corpId: Fetch.corpId, spaceId, multiple: true },
      image: { multiple: true, compression: true, quality: 50, resize: 50, spaceId, max: 9 },
      file: { spaceId, multiple: true, max: 9 },
      multiple: true
    })
    if (result) {
      const data = result.data.map(extNode => {
        const key = `DP:${JSON.stringify(extNode)}`
        return {
          key,
          name: extNode.fileName,
          fileName: extNode.fileName,
          fileId: { id: extNode.fileId, key }
        }
      })
      const { onFinish, fileList = [] } = this.props
      onFinish && onFinish(fileList.concat(data))
    }
  }

  handleInvalidFIle = files => {
    const { invalidSuffixes = defaultInvalidSuffixes } = this.props
    message.error(i18n.get(`不支持{__k0}格式的文件`, { __k0: invalidSuffixes.map(str => '.' + str).join('、') }))
  }

  getUploadUrl = (file, generateFileName) => {
    return Fetch.GET(`/api/v1/attachment/attachments/presign/$${generateFileName}`)
  }

  renderText = () => {
    const { field, suffixesPath } = this.props
    if (field?.useSelfConfig && field?.tooltipmsg) {
      // 退汇回单中上传组件进行自定义配置
      // {
      //   name: 'attachments',
      //   label: i18n.get('退汇凭证'),
      //   type: 'attachments',
      //   dataType: { type: 'list', elemType: { type: 'attachment'}},
      //   optional: false,
      //   useSelfConfig: true, // 是否使用自己template中的配置
      //   btnText: i18n.get('上传退汇凭证'),
      //   tooltipmsg: i18n.get('仅支持上传「jpg,jpeg,bnp,pdf」格式文件'),
      // },
      return field?.tooltipmsg
    } else {
      return parseAttachmentTipText(suffixesPath, field)
    }
  }

  render() {
    let { status } = this.state
    const { invalidSuffixesConfig = {} } = this.state.attachmentSetting
    const uploadServiceUrl = api.getState()['@common'].uploadServiceUrl
    const dpPower = api.getState()['@common'].powers.DP
    let {
      disabled,
      disable,
      children,
      isLoading,
      accept,
      multiple,
      inputStyle,
      onFormatFile,
      filesNum,
      fileMaxSize,
      filesExceedEvent = () => {},
      orgVisible,
      canSelectDP = false,
      isOCR,
      invalidSuffixes = defaultInvalidSuffixes,
      onError,
      field,
      validatorFiles,
      uploadLimit
    } = this.props

    if (dpPower && canSelectDP) {
      return (
        <div onClick={this.handleDingTalkUpload} data-testid="upload-button-business">
          {children ? (
            children
          ) : (
            <Button category="secondary" size="small" disabled={disabled || status !== 'done'}>
              <OutlinedDirectionUpload fontSize={16} className="mr-4" />
              {i18n.get('上传附件')}
            </Button>
          )}
        </div>
      )
    }

    let multiples = multiple ? true : multiple
    let uploadUrl = uploadServiceUrl && uploadServiceUrl.uploadUrl
    const config = invalidSuffixesConfig && Object.keys(invalidSuffixesConfig)
    const sizes = invalidSuffixesConfig && Object.values(invalidSuffixesConfig)
    const minLimitSize = Math.min(...sizes) || 60
    const validAccept = config?.map(item => '.' + item)?.join(',') || accept
    const validAcceptText = validAccept?.replace('.', '')
    const text = this.renderText(validAccept, validAcceptText, minLimitSize, invalidSuffixes)

    if (disable) {
      return (
        <div>
          {children ? (
            children
          ) : (
            <>
              <Button
                category="secondary"
                size="small"
                disabled={disabled || status !== 'done'}
                style={{ pointerEvents: 'none' }}
              >
                <OutlinedDirectionUpload fontSize={16} className="mr-4" />
                {field?.useSelfConfig && field?.btnText ? field?.btnText : i18n.get('上传附件')}
              </Button>
              {this.props.suffixesPath !== 'BILL' && (
                <Tooltip placement="bottom" className="uploader-tooltip" title={text}>
                  <OutlinedTipsMaybe fontSize={16} className={styles['uploader-tooltip']} />
                  {/* <Icon className={styles['uploader-tooltip']} type="question-circle-o" /> */}
                </Tooltip>
              )}
            </>
          )}
        </div>
      )
    }
    return (
      <FilesUploader
        name="file"
        bus={this.bus}
        action={IS_STANDALONE ? this.getUploadUrl : uploadUrl}
        type={IS_STANDALONE}
        maxSize={fileMaxSize || maxSize}
        ref="filesUpLoader"
        multiple={multiples}
        accept={validAccept}
        disabledUpload={isLoading}
        disabled={disabled || status !== 'done'}
        data={IS_STANDALONE ? (file, tokenData) => buildData(file, tokenData, isOCR, uploadServiceUrl) : this.fnFixData}
        onDone={this.handleOnDone}
        onStart={this.handleOnStart}
        onChange={this.handleOnChange}
        filesNum={filesNum}
        filesExceedEvent={filesExceedEvent}
        inputStyle={inputStyle}
        format={onFormatFile}
        uploadPrepare={this.uploadPrepare}
        invalidSuffixes={invalidSuffixes}
        onInvalidFile={this.handleInvalidFIle}
        onError={this.handleOnError}
        onCheckpointInfo={this.handleOnCheckpointInfo}
        invalidSuffixesConfig={invalidSuffixesConfig}
        validatorFiles={validatorFiles}
      >
        {children ? (
          children
        ) : (
          <>
            <Button category="secondary" size="small" disabled={disabled || status !== 'done'}>
              <OutlinedDirectionUpload fontSize={16} className="mr-4" />
              {field?.useSelfConfig && field?.btnText ? field?.btnText : i18n.get('上传附件')}
            </Button>
            {this.props.suffixesPath !== 'BILL' && (
              <Tooltip placement="bottom" className="uploader-tooltip" title={text}>
                <OutlinedTipsMaybe fontSize={16} className={styles['uploader-tooltip']} />
                {/* <Icon className={styles['uploader-tooltip']} type="question-circle-o" /> */}
              </Tooltip>
            )}
          </>
        )}
      </FilesUploader>
    )
  }
}

UploadButton.defaultProps = {
  action: 'https://up.qbox.me'
}
