/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 21/09/2017 15:08.
 **************************************************/
import React from 'react'
import {
  stateMap,
  INVOICE_TYPE_BASE_ENUM,
  PRINT_STATE,
  RECEIPT_STATE,
  AI_SAMPLING_DATASOURCE,
  AI_SAMPLING_DATASOURCE_LIST,
  AI_APPROVAL_RESULT_DATASOURCE,
  AI_APPROVAL_RESULT_DATASOURCE_LIST
} from '@ekuaibao/lib/lib/enums'
import { NullCell } from 'ekbc-datagrid/lib/puppet/Cells'
import { Button, Divider, Popover, Space, Tooltip, Spin } from '@hose/eui'
import Money from '../puppet/Currency'
import moment from 'moment'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { filterChannels } from '../../components/utils/fnFilterPaymentChannel'
import {
  isBaseData,
  isCity,
  isStaffs,
  isMoney,
  isPayeeInfos,
  isSwitch,
  isDataLink,
  isInvoiceType,
  isDepartments
} from '@ekuaibao/lib/lib/propertySetIs'
import styles from './column.module.less'
import { getDisplayName, getStaffShowByConfig } from '../utilFn'
import { isZhongYing } from '@ekuaibao/lib/lib/checkZY'
import { Fetch } from '@ekuaibao/fetch'
import StaffFilter from './StaffFilter'
import DepartmentFilter from './DepartmentFilter'
import { getSpecificationName, getFeetypeName } from '../../lib/lib-util'
import { setPagination } from '../../lib/expenseMangerUtils'
import classNames from 'classnames'
import { enableHidingFinishedBills } from '../../lib/featbit'
import { NameCell } from '../name-cell'

export const SwitcherData = [
  { label: i18n.get('是'), value: true },
  { label: i18n.get('否'), value: false }
]

const getState = {
  draft: i18n.get('待提交'), //草稿
  sending: i18n.get('待寄送'), //待寄送
  receiving: i18n.get('待收单') //待收单
}

export function fixEnums(enumSource, filterDataSource, defaultLabel = undefined) {
  return {
    filterType: 'list',
    filterDataSource: filterDataSource,
    render(text, record) {
      if (!text) {
        return <NullCell />
      }
      let state = enumSource[text] || defaultLabel
      return (
        <span className={styles.urgentWrapper}>
          {record.isUrgent && window.isNewHome && <i className={styles.urgent} />}
          {state ? state : getState[text]}
        </span>
      )
    }
  }
}

export function fixPaymentAccountId() {
  return {
    render(line) {
      return line && line.detail ? (
        <Tooltip placement="topLeft" title={line.detail.name}>
          <span className="text-nowrap-ellipsis ws-nowrap">{line.detail.name}</span>
        </Tooltip>
      ) : (
        <NullCell />
      )
    }
  }
}

export function fixPaymentChannel(filterDataSource, dynamicChannelMap) {
  return {
    filterType: 'list',
    filterDataSource: filterDataSource,
    render(paymentChannel) {
      if (!paymentChannel || !dynamicChannelMap[paymentChannel]?.name) {
        return <NullCell />
      }
      return <span>{dynamicChannelMap[paymentChannel]?.name}</span>
    }
  }
}

export function fixInvoiceEnums(enumSource, filterDataSource) {
  return {
    filterType: 'list',
    filterDataSource: filterDataSource,
    render(line) {
      return line ? <span>{i18n.get(enumSource[line.id])}</span> : <NullCell />
    }
  }
}

/**
 * 档案名称显示函数工厂函数
 */
export function fixBaseData(property) {
  return {
    render(text) {
      if (!text) {
        return <NullCell />
      }
      if (typeof text === 'string') {
        return (
          <Tooltip placement="topLeft" title={text}>
            <span className="translate-ignore-class" style={{
              wordBreak: 'break-all',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}>{text}</span>
          </Tooltip>
        )
      }
      const { name, code, enName } = text
      let _name = name
      // 如果是档案，根据当前的语言环境，显示对应的名称
      if (i18n?.currentLocale === 'en-US' && enName) {
        _name = enName
      }
      const value = `${_name}(${code})`
      return <span className="translate-ignore-class" style={{
        wordBreak: 'break-all',
        display: '-webkit-box',
        WebkitLineClamp: 3,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}>{value}</span>
    }
  }
}

function fixReceiptState() {
  return {
    filterType: 'list',
    filterDataSource: [...Object.values(RECEIPT_STATE())],
    render(value, record) {
      if (RECEIPT_STATE()[value]) {
        return i18n.get(RECEIPT_STATE()[value].label)
      } else {
        return '-'
      }
    }
  }
}

function fixPrintState() {
  return {
    filterType: 'list',
    filterDataSource: [...Object.values(PRINT_STATE())],
    render(value, record) {
      if (PRINT_STATE()[value]) {
        return i18n.get(PRINT_STATE()[value].label)
      } else {
        return '-'
      }
    }
  }
}

export function fixCity() {
  return {
    render(text, record) {
      if (!text) {
        return <NullCell />
      }

      const arr = JSON.parse(text)
      const label = arr
        .map(line => (i18n.currentLocale === 'en-US' ? line?.enLabel || line?.label : line?.label))
        .join(',')
      return <span>{label}</span>
    }
  }
}

function renderPayDetail(doc = {}) {
  return (
    <div className={classNames('ekb-account-info-popover', styles['ekb-account-info-popover'])}>
      <div className="header">
        {i18n.get(doc.type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户'))} |{' '}
        {doc.accountName || doc.name}
      </div>
      <div className="body">
        <div className="line1">
          <img className="stand-20-icon" src={doc.icon} />
          {doc.bank || doc.unionBank}
        </div>
        <div className="line2 text-nowrap-ellipsis">{doc.cardNo || doc.accountNo}</div>
        <div className="line3">{doc.branch}</div>
      </div>
    </div>
  )
}

export function fixPayeeInfo() {
  return {
    render(payeeInfo, record) {
      if (!payeeInfo) {
        const multiplePayeesMode =
          get(record, 'flowId.form.multiplePayeesMode') || get(record, 'form.multiplePayeesMode')
        if (multiplePayeesMode) {
          return i18n.get('多收款人')
        }
        return <NullCell />
      }

      return (
        <Popover content={renderPayDetail.call(this, payeeInfo)} placement="topLeft">
          <div className="account">
            <div>{payeeInfo.accountName || payeeInfo.name}</div>
            <div className="color-gray">
              {i18n.get(payeeInfo.type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户'))}
            </div>
          </div>
        </Popover>
      )
    }
  }
}

export function fixPayeeInfoNew() {
  return {
    render(payeeInfo, record) {
      if (!payeeInfo) {
        const multiplePayeesMode =
          get(record, 'flowId.form.multiplePayeesMode') || get(record, 'form.multiplePayeesMode')
        if (multiplePayeesMode) {
          return i18n.get('多收款人')
        }
        return <NullCell />
      }

      const { icon, accountName, name, type } = payeeInfo
      return (
        <Popover content={renderPayDetail.call(this, payeeInfo)} placement="topLeft">
          <div className={styles['pay-info-new']}>
            {icon && <img className={styles.icon} src={icon} alt="bank icon" />}
            <div className={styles.detail}>
              <div className={styles.name}>{accountName || name}</div>
              <div className={styles.type}>
                {i18n.get(type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户'))}
              </div>
            </div>
          </div>
        </Popover>
      )
    }
  }
}

export function fixMoney({ valueSize = 12, canFilter = true, moneyTextAlign = 'right' }) {
  return {
    filterType: canFilter ? 'money' : undefined,
    sorter: canFilter,
    render(money, record) {
      if (!money && money !== 0) {
        return <NullCell />
      }
      if (window.IS_SMG || isZhongYing(Fetch.ekbCorpId)) {
        const isSmgMoney = Number(money.standard) >= **********9.0 //SMG（11位9）
        const isZYMoney = Number(money.standard) >= **********.0 //中影企业（10位9）
        if (isSmgMoney || isZYMoney) {
          return '-'
        } else {
          // return <Money currencySize={12} valueSize={valueSize} color="#333333" value={money} />
          return <Money value={money} style={{ textAlign: moneyTextAlign }} styleTwo={{ fontSize: '12px' }} />
        }
      }
      return <Money value={money} style={{ textAlign: moneyTextAlign }} styleTwo={{ fontSize: '12px' }} />
      // return <Money currencySize={12} valueSize={valueSize} color="#333333" value={money} onlyForeign={(money && money.foreignStrCode)} isShowForeign={(money && money.foreignStrCode)}  showSymbol={false} showStrCode  />
    }
  }
}

export function fixPayDate() {
  return {
    render(value, record) {
      return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-'
    }
  }
}

export function fixRepaymentDate() {
  return {
    render(value, record) {
      let newValue = value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-'
      return newValue === 'Invalid date' ? i18n.get('无还款日期') : newValue
    }
  }
}

export function fixSwitch(filterDataSource) {
  return {
    filterType: 'list',
    filterDataSource: filterDataSource,
    lookup: {
      dataSource: filterDataSource,
      displayExpr: 'label',
      valueExpr: 'value'
    },
    render(value) {
      let title = ''
      if (value === undefined || value === null) {
        title = '-'
      } else {
        title = i18n.get(value ? i18n.get('是') : i18n.get('否'))
      }
      return <span>{title}</span>
    }
  }
}

export function fixDataLink() {
  return {
    render(value) {
      let title = ''
      if (value === undefined || value === null) {
        title = '-'
      } else if (typeof value === 'string') {
        title = value
      } else {
        const { form } = value
        let refCount = 0
        Object.keys(form).forEach(key => {
          if (key.endsWith('_name')) {
            title = form[key] + title
            refCount++
          } else if (key.endsWith('_code')) {
            title = `${title}(${form[key]})`
            refCount++
          }
          if (refCount === 2) return title
        })
      }
      return <span>{title}</span>
    }
  }
}

export function fixInvoiceType() {
  const filterDataSource = Object.keys(INVOICE_TYPE_BASE_ENUM()).map(key => {
    return { label: INVOICE_TYPE_BASE_ENUM()[key], value: key }
  })
  return fixInvoiceEnums(INVOICE_TYPE_BASE_ENUM(), filterDataSource)
}

export function fixLastPrintTime() {
  return {
    render(value) {
      return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-'
    }
  }
}

export function fixSubmitter() {
  return {
    render(value, record) {
      const { flowId, form } = record
      let ownerId = ''
      let submitterId = ''
      let owner = ''
      let submitter = ''
      if (flowId) {
        ownerId = get(flowId, 'ownerId.id', '')
        submitterId = get(flowId, 'form.submitterId.id', '')
        owner = getStaffShowByConfig(get(flowId, 'ownerId'))
        submitter = getStaffShowByConfig(get(flowId, 'form.submitterId'))
      } else {
        ownerId = get(record, 'ownerId.id', '')
        if (!ownerId) ownerId = get(record, 'form.ownerId.id', '')
        submitterId = get(form, 'submitterId.id', '')
        let recordOwnerId = get(record, 'ownerId')
        if (!recordOwnerId) recordOwnerId = get(record, 'form.ownerId', '')
        owner = getStaffShowByConfig(recordOwnerId)
        submitter = getStaffShowByConfig(get(form, 'submitterId'))
      }

      if (window.isInWeComISV && value.id) {
        return <NameCell type='user' id={value.id} name={value.name} />
      }

      let str =
        ownerId?.length > 0 &&
        submitterId?.length > 0 &&
        (ownerId === submitterId
          ? getStaffShowByConfig(value)
          : i18n.get('submitted-by', {
            name: submitter,
            ownerName: owner
          }))
      str = str ? str : getStaffShowByConfig(value)
      return <div className="translate-ignore-class">{str || '-'}</div>
    }
  }
}

export function fixStaffShow() {
  return {
    render(value) {
      return value ? <span className="translate-ignore-class">{getStaffShowByConfig(value)}</span> : '-'
    }
  }
}

const FLOW_REVIEW_STATUS = () => ({
  ALL: i18n.get('全部收到'),
  NONE: i18n.get('暂未收到'),
  PART: i18n.get('部分收到')
})

const FLOW_REVIEW_STATUS_COLOR = {
  ALL: { color: '#18B694' },
  NONE: { color: '#F4526B' },
  PART: { color: '#FAA442' }
}

const FLOW_REVIEW_FILTER_DATA = () => {
  return [
    { label: i18n.get('暂未收到'), value: 'NONE' },
    { label: i18n.get('部分收到'), value: 'PART' },
    { label: i18n.get('全部收到'), value: 'ALL' }
  ]
}

export function fixReviewStatus() {
  return {
    filterType: 'list',
    filterDataSource: FLOW_REVIEW_FILTER_DATA(),
    render(value, line) {
      return <div style={FLOW_REVIEW_STATUS_COLOR[value]}>{FLOW_REVIEW_STATUS()[value] || '-'}</div>
    }
  }
}

export function fixDepartment() {
  const pathMap = api.getState('@common.department').pathMap
  const enPathMap = api.getState('@common.department').enPathMap
  return {
    render: (item, row) => {
      if (!item) {
        return <NullCell />
      }
      // let depts = deptMap[item.id]
      let pathName = i18n.currentLocale === 'zh-CN' ? pathMap[item.id] : enPathMap[item.id]
      let name = getDisplayName(item)
      if (window.isInWeComISV && item.id) {
        return <NameCell type="department" id={item.id} name={name} />
      }
      if (typeof item === 'string') {
        name = item
      }
      return (
        <Tooltip placement="topLeft" title={pathName}>
          <div className="td-text-wrap translate-ignore-class">{name}</div>
        </Tooltip>
      )
    }
  }
}

export function fixExpenseLinks() {
  return {
    render(value) {
      if (!value) {
        return '-'
      }
      if (!Array.isArray(value)) {
        value = [value]
      }
      return (
        <div>
          {value.map(v => (
            <div>{(v && v.name) || '-'}</div>
          ))}
        </div>
      )
    }
  }
}

export function fixPartialPayState() {
  return {
    filterType: 'list',
    filterDataSource: [
      {
        value: 'PartialPaying',
        label: '部分支付中'
      },
      {
        value: 'PartialPaied',
        label: '部分支付成功'
      }
    ]
  }
}

export function fixSpecificationId() {
  return {
    render(value) {
      return <span className="translate-ignore-class">{getSpecificationName(value)}</span>
    }
  }
}

export function fixFeeTypeId() {
  return {
    render(value) {
      return getFeetypeName(value)
    }
  }
}

export function fixVoucherStatus() {
  return {
    render(value) {
      if (!value) {
        return i18n.get('未生成')
      }
      return value
    }
  }
}

const getBillStateV2 = (stateMap, Express, scenesType) => {
  const oo = {}
  Object.keys(stateMap).forEach(key => {
    // 我的单据表格模式：
    if (scenesType === 'MyBill') {
      const myBillBlackList = ['nullify', 'failure']
      if (myBillBlackList.includes(key)) return null
    }

    if (['paid', 'archived', 'PROCESS'].includes(key)) {
      oo[key] = i18n.get('已完成')
    } else if (key === 'draft') {
      oo[key] = i18n.get('草稿')
    } else if (key === 'pending' && scenesType !== 'MyBill') {
      // 去掉列选中的待提交和提交中
      return null
    } else if (!Express && (key === 'sending' || key === 'receiving')) {
      return null
    } else {
      oo[key] = stateMap[key].text
    }
  })
  return oo
}
const billState = (stateMap, Express, scenesType) => {
  if (enableHidingFinishedBills()) {
    return getBillStateV2(stateMap, Express, scenesType)
  }
  const oo = {}
  //TODO 让佟P泽该需求,去掉俩 if
  Object.keys(stateMap).forEach(key => {
    // 我的单据表格模式：
    if (scenesType === 'MyBill') {
      const myBillBlackList = ['PROCESS', 'nullify', 'failure', 'paid', 'archived']
      if (myBillBlackList.includes(key)) return null
    }

    if (key === 'paid') {
      oo[key] = i18n.get('已完成(待确认)')
    } else if (key === 'archived') {
      oo[key] = i18n.get('已完成(已确认)')
    } else if (key === 'draft') {
      oo[key] = i18n.get('草稿')
    } else if (key === 'pending' && scenesType !== 'MyBill') {
      // 去掉列选中的待提交和提交中
      return null
    } else if (!Express && (key === 'sending' || key === 'receiving')) {
      return null
    } else {
      oo[key] = stateMap[key].text
    }
  })
  return oo
}

export function parseMeta2ColumSpecials(name, specialColumn = {}) {
  if (specialColumn[name]) {
    return specialColumn[name]
  }
}

const EXPENSE_STATUS = {
  PROCESSING: i18n.get('报销中'),
  WAITING: i18n.get('待报销'),
  PROCESSED: i18n.get('已报销')
}

const CAR_TYPE = {
  TAXI: i18n.get('出租'),
  SPECIAL: i18n.get('专车'),
  FAST: i18n.get('快车'),
  BEHALF: i18n.get('代驾')
}

const EXPENSE_STATUS_STATUS_FILTERS_DATASOURCE = Object.keys(EXPENSE_STATUS).map(key => ({
  label: EXPENSE_STATUS[key],
  value: key
}))

const CAR_TYPE_STATUS_FILTERS_DATASOURCE = Object.keys(CAR_TYPE).map(key => ({
  label: CAR_TYPE[key],
  value: key
}))

export function parseMeta2ColumnOthers(name, property, dynamicChannelMap = {}, Express, scenesType) {
  if (name === 'expenseStatus') {
    return fixEnums(EXPENSE_STATUS, EXPENSE_STATUS_STATUS_FILTERS_DATASOURCE)
  }

  if (name === 'useCarType') {
    return fixEnums(CAR_TYPE, CAR_TYPE_STATUS_FILTERS_DATASOURCE)
  }

  if (name === 'state') {
    const stateMapRes = stateMap()
    if (!api.getState()['@common'].powers.Express) {
      // 如果没有寄送单据管理charge，去除该选项
      delete stateMapRes.receivingExcep
    }
    const BILL_STATUS = billState(stateMapRes, Express, scenesType)
    const { archived, PROCESS, ...rest } = BILL_STATUS
    let BILL_STATUS_FILTERS_DATASOURCE = enableHidingFinishedBills() ? rest : BILL_STATUS
    BILL_STATUS_FILTERS_DATASOURCE = Object.keys(BILL_STATUS_FILTERS_DATASOURCE).map(key => ({
      label: BILL_STATUS_FILTERS_DATASOURCE[key],
      value: key
    }))
    return fixEnums(BILL_STATUS, BILL_STATUS_FILTERS_DATASOURCE, '-')
  }

  if (name === 'paymentChannel') {
    const channelList = Object.values(dynamicChannelMap) || []
    const arr = filterChannels(channelList)
    return fixPaymentChannel(arr, dynamicChannelMap)
  }

  if (name === 'paymentAccountId') {
    return fixPaymentAccountId()
  }

  if (
    name === 'payDate' ||
    name === 'timeToEnterPendingPayment' ||
    name === 'preNodeApprovedTime' ||
    name === 'payScanTime'
  ) {
    return fixPayDate()
  }

  if (name === 'repaymentDate') {
    return fixRepaymentDate()
  }

  if (name === 'receiptState') {
    return fixReceiptState()
  }

  if (name === 'printState') {
    return fixPrintState()
  }

  if (name === 'description') {
    return {
      render: (text) => <span className="translate-ignore-class" style={{
        wordBreak: 'break-all',
        display: '-webkit-box',
        WebkitLineClamp: 3,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}>{text || '-'}</span>
    }
  }

  if (isBaseData(property)) {
    return fixBaseData(property)
  }

  if (isCity(property)) {
    return fixCity()
  }

  if (isPayeeInfos(property)) {
    return fixPayeeInfo()
  }
  if (isBaseData(property)) {
    return fixBaseData()
  }

  if (isMoney(property)) {
    return fixMoney({ valueSize: 12 })
  }

  if (isSwitch(property)) {
    return fixSwitch(SwitcherData)
  }

  if (isDataLink(property)) {
    return fixDataLink()
  }

  if (isInvoiceType(property)) {
    return fixInvoiceType()
  }

  if (isDepartments(property)) {
    return fixDepartment()
  }

  if (name === 'lastPrintTime') {
    return fixLastPrintTime()
  }

  if (name === 'submitterId') {
    return fixSubmitter()
  }
  if (name === 'reviewStatus') {
    return fixReviewStatus()
  }
  if (name === 'expenseLinks') {
    return fixExpenseLinks()
  }
  if (name === 'partialPayState') {
    return fixPartialPayState()
  }
  if (name === 'specificationId') {
    return fixSpecificationId()
  }

  if (name === 'feeTypeId') {
    return fixFeeTypeId()
  }

  if (name === 'voucherStatus') {
    return fixVoucherStatus()
  }

  if (isStaffs(property)) {
    return fixStaffShow()
  }

  // 智能审批_人工抽检
  if (name === 'AI_Sampling') {
    return fixEnums(AI_SAMPLING_DATASOURCE(), AI_SAMPLING_DATASOURCE_LIST(), '-')
  }

  // 智能审批_AI审核结果
  if (name === 'AI_Approval_Result') {
    return fixEnums(AI_APPROVAL_RESULT_DATASOURCE(), AI_APPROVAL_RESULT_DATASOURCE_LIST(), '-')
  }
}

export const parseColumnFilter = (property, scenesType) => {
  if (isStaffs(property)) {
    return {
      filterType: 'custom',
      filterStyles: {
        wrapperStyle: {
          border: 'none',
          backgroundColor: 'transparent'
        },
        bodyStyle: {
          padding: '0'
        }
      },
      hiddenFilterAction: true,
      renderFilter: props => <StaffFilter {...props} scenesType={scenesType} />
    }
  }
  if (isDepartments(property)) {
    return {
      filterType: 'custom',
      filterStyles: {
        wrapperStyle: {
          border: 'none',
          backgroundColor: 'transparent'
        },
        bodyStyle: {
          padding: '0'
        }
      },
      hiddenFilterAction: true,
      renderFilter: props => <DepartmentFilter {...props} scenesType={scenesType} />
    }
  }
  return {}
}

export const DropdownRender = ({ className = '', originNode, onConfirm, onReset }) => {
  return (
    <div className={className}>
      {originNode}
      <Divider style={{ margin: '0' }} />
      <div className={styles['action-wrapper']}>
        <Space size={8}>
          <Button size="small" category="secondary" onClick={onReset}>
            {i18n.get('重置')}
          </Button>
          <Button size="small" category="primary" onClick={onConfirm}>
            {i18n.get('确定')}
          </Button>
        </Space>
      </div>
    </div>
  )
}

export const Loading = () => {
  return <Spin style={{ padding: 16 }} text="加载中..." direction="vertical" />
}

export const MaxTagPlaceholder = ({ omittedValues, titleKey = 'label' }) => {
  const title = omittedValues.map(item => item[titleKey]).join('，')
  return <Tooltip title={title}>{`+${omittedValues.length}..`}</Tooltip>
}
