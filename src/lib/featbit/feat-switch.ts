import { getBoolVariation, getVariation } from './init'

/**
 * 是否启用新版城市选择器
 */
export const enableNewCityPicker = (defaultValue?: boolean) => {
  return getBoolVariation('city-picker-2.0', defaultValue)
}

/**
 * 明细保存时增加费用金额<=发票总金额校验
 */
export const getIfCheckInvoicesMoney = () => {
  return getBoolVariation('mfrd-3469-invoices-total-less-than-amount-validate', false)
}

/**
 * 单据编辑页是否展示”本位币“标识
 */
export const isDismissLocalCurrencyLabel = () => {
  return getBoolVariation('mfrd-2995-dismiss-local-currency-label', false)
}

/**
 * 报销金额使用接口数据进行展示
 */
export const useApiExpenseMoney = () => {
  return getBoolVariation('cyxq-70702-use-api-expense-money', false)
}
export const newVersionOPaymenAccount = () => {
  return getBoolVariation('new_version_of_payment_account', false)
}

/**
 * 1、是否隐藏本位币  2、隐藏海外本位币币种
 */
export const onlyOneCurrencyHideSeaLocalCurrrency = () => {
  return getBoolVariation('only-one-hide-local-currrency', false)
}

export const enableNewBillOptimization = () => {
  return getBoolVariation('web-new-bill-optimization', false)
}

/**
 * 部门可见性
 */
export const useDepartmentVisible = () => {
  return getBoolVariation('mfrd-3133-department-visible', false)
}

export const asyncOCRinvoice = () => {
  return getBoolVariation('async-ocr-invoice', false)
}



/**
 * 自定义档案加载优化
 */
export const enableRecordOptimization = () => {
  return getBoolVariation('mfrd-3830-record-optimization', false)
}


/**
 * 是否启用员工API切换
 * @link https://jira.hosecloud.com/browse/MFRD-4278
 */
export const enableStaffApiSwitch = () => {
  return getBoolVariation('mfrd-4278-staff-api-switch', false)
}


export const enableOtherInvoiceByDimension = () => {
  return getBoolVariation('fird-4844-invoice-by-dimension', false)
}

/**
 * 【无需测试】【在线客服】web端客户入口和移动端顶部导航栏客服入口对接天润客服离线消息
 * @link https://jira.hosecloud.com/browse/MFRD-3923
 */
export const enableSupportOfflineMessage = () => {
  return getBoolVariation('mfrd-3923-support-offline', false)
}

/**
 * 【单据焕新】【二期】【查看态】「我的单据」去除已完成操作
 * @link https://jira.hosecloud.com/browse/MFRD-3156
 */
export const enableHidingFinishedBills = () => {
  return window.isInWeComISV || getBoolVariation('mfrd-3156-get-rid-of-finished-bills', false)
}




/**
 * 单据审批意见输入框增大
 */
export const enableCommentOptimization = () => {
  return getBoolVariation('mfrd-4689', false)
}

export const enableFlowOptimization = () => {
  return getBoolVariation('cyxq-77804-flow-optimization', false)
}

/**
 * 单据焕新】切换单据且保持外部加载，先关闭单据—>加载—>打开新单据
 * 因为依赖于自定义按钮的逻辑，所以公用一个featbit
 * @link https://jira.hosecloud.com/browse/MFRD-4163
*/
export const supportBillDetailsSwitchingInDrawer = () => {
  return getBoolVariation('custom-extend-button', false)
}


/**
 * 自定义按钮
*/
export const enableCustomExtendButton = () => {
  return getBoolVariation('custom-extend-button', false)
}

/** 【赢家】更新单据模板收款信息可见性清空校验
 * @link https://jira.hosecloud.com/browse/MFRD-4595
*/
export const enableCheckingVisibilityBeforeClearingAccounts = () => {
  return getBoolVariation('mfrd-4595', false)
}

export const enableHomepageOptimization = () => {
  return getBoolVariation('mfrd-4800-homepage-optimization', false)
}

export const enableShowingApprovalFlowByDefault = () => {
  return getBoolVariation('mfrd-2937-approval-flow-by-default', false)
}
/**
 * 重新查验
 * @link https://jira.hosecloud.com/browse/FIRD-5365
 */
export const enableReCheck = () => {
  return getBoolVariation('fird-5365', false)
}

export const enableRecordSearchOptimization = () => {
  return getBoolVariation('cyxq-78696-record-search', false)
}

export const enableFlowHiddenFields = () => {
  const value = getVariation('mfrd-3670-flow-hidden-field', 'UNOPEN')
  const hasDetail = ['READORWRITE', 'READORWRITE_DETAIL'].includes(value)

  return {
    value,
    handleDetail: !!hasDetail
  }
}
