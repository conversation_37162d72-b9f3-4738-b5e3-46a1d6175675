/**
 *  Created by pw on 2020-01-13 20:30.
 */

import {
  DetailValueSource,
  GlobalFieldMapIF,
  ComponentIF,
  FeeTypeIF,
  InvoiceIF,
  MoneyIF,
  InvoiceMappingRuleItemIF,
  InvoiceRuleIF,
  InvoiceRuleMapIF,
  SpecificationIF
} from '@ekuaibao/ekuaibao_types'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { array2object, isNumber, isObject, isString } from '@ekuaibao/helpers'
import { MoneyMath } from '@ekuaibao/money-math'
import { INVOICE_TYPE } from '@ekuaibao/lib/lib/enums'
import {
  formatMoneyWithDiffCurrency,
  foreignConversionStandard,
  getAcountKey,
  isStandardCurrencyDiff,
  standardValueMoney
} from './misc'
import {
  getDisableStateByInvoiceID,
  getMarkStateByInvoiceID,
  getOriginalFileDownload
} from '../plugins/bills/bills.action'
import { showMessage } from '@ekuaibao/show-util'
import Big from 'big.js'
import { cloneDeep } from 'lodash'
import { initBudgetCurrentInfo } from '../components/utils/fnCurrencyObj'
import { getBoolVariation } from './featbit'
import { enableOtherInvoiceByDimension } from './featbit/feat-switch'
import moment from 'moment'

interface Invoice2DetailProps {
  feeType: FeeTypeIF
  invoices: InvoiceIF[]
  specification: SpecificationIF
  isBatch: boolean
  attachmentList?: any[]
  currentFeeTypeForm: any
  billData: any
  billSpecification: any
}

export default class InvoiceMappingValue {
  invoiceRuleMap: InvoiceRuleMapIF
  invoiceRules: InvoiceMappingRuleItemIF[]

  public async invoice2Detail(param: Invoice2DetailProps) {
    if (!this.invoiceRuleMap) {
      await this.lazy()
    }
    const {
      feeType,
      invoices,
      specification = {} as SpecificationIF,
      isBatch,
      attachmentList = [],
      currentFeeTypeForm,
      billData,
      billSpecification
    } = param

    const { KA_DISABLE_INVOICE } = api.getState()['@common'].powers || {}
    await this.originalFileDownload(invoices)

    if (KA_DISABLE_INVOICE) {
      let arr = []
      invoices.forEach(i => {
        if (!('disableInfo' in i) && i.master?.id) {
          arr.push(i.master.id)
        }
      })
      //根据发票id 查询发票状态
      if (arr.length) {
        const disableInfoResult = await api.dispatch(getDisableStateByInvoiceID(arr))
        const markInfoResult = await getMarkStateByInvoiceID(arr)

        disableInfoResult?.items?.forEach(disable => {
          invoices.forEach((i: any) => {
            if (i.master?.id === disable?.invoiceId) {
              i.disableInfo = { disable: true }
            }
          })
        })
        markInfoResult?.items?.forEach(mark => {
          invoices.forEach((i: any) => {
            if (i.master?.id === mark?.invoiceId) {
              i.markInfo = { mark: true }
            }
          })
        })
      }
    }
    const { components = [] } = specification
    const rule2InvoiceValue = this.processInvoice(invoices, components, currentFeeTypeForm)
    let feeTypeForm = this.processFieldMappingValue(components, rule2InvoiceValue, invoices)
    feeTypeForm = this.processSummaryFields(components, feeTypeForm, rule2InvoiceValue)
    feeTypeForm = await this.processFormCityField(components, feeTypeForm)
    feeTypeForm = await this.processEnumField(components, feeTypeForm)
    // TODO: 在费用上添加发票埋点
    if (billSpecification?.id) {
      const invoiceIds = attachmentList?.map(it => {
        return it?.master?.id
      })
      api?.logger?.info('在费用上添加发票', {
        specificationId: billSpecification?.id,
        specificationName: billSpecification?.name,
        flowId: billData?.flowId,
        code: billData?.code,
        sceneName: '发票',
        feeTypeId: feeType?.id,
        feeTypeName: feeType?.name,
        operateType: '添加',
        invoiceIds,
        resultForm: feeTypeForm
      })
    }
    const formData = {
      feeTypeId: feeType,
      feeTypeForm,
      specificationId: specification,
      attachmentList
    }
    if (isBatch) {
      return await api
        .invoke('generate:DetailByAutoCalculate', { detailFormValue: formData, components }, true)
        .catch(() => {
          return formData
        })
    }
    return formData
  }
  public async clearInvoicesForm(components) {
    await this.lazy()
    return this.processNoInvoices(components)
  }
  public async invoice2FeeTypeForm(invoices: InvoiceIF[] = [], components: ComponentIF[], currentFeeTypeForm?: any) {
    if (!this.invoiceRuleMap) {
      await this.lazy()
    }
    if (!invoices.length) {
      return this.processNoInvoices(components)
    }
    const rule2InvoiceValue = this.processInvoice(invoices, components, currentFeeTypeForm)
    let feeTypeForm = this.processFieldMappingValue(components, rule2InvoiceValue, invoices)
    feeTypeForm = await this.processFormCityField(components, feeTypeForm)
    feeTypeForm = await this.processEnumField(components, feeTypeForm)
    return this.processSummaryFields(components, feeTypeForm, rule2InvoiceValue)
  }

  public originalFileDownload = async invoices => {
    const { DIGITAL_ORIGINAL_FILE } = api.getState()['@common'].powers || {}
    const digitalInvoices = invoices.filter(item => {
      const type = getV(item, 'master.form.E_system_发票主体_发票类别', '')
      const isFullDigital = [
        'FULL_DIGITAl_SPECIAL',
        'FULL_DIGITAl_NORMAL',
        'ELECTRONIC_TRAIN_INVOICE',
        'ELECTRONIC_AIRCRAFT_INVOICE',
        'ELECTRONIC_PAPER_CAR'
      ].includes(type)
      return isFullDigital
    }, [])
    if (DIGITAL_ORIGINAL_FILE && digitalInvoices.length) {
      const invoiceIds = digitalInvoices.map(v => v.master.id)
      await getOriginalFileDownload({ invoiceIds }).catch(() => {})
    }
  }

  private async lazy() {
    const { items = [] } = await api.invokeService('@invoice-manage:get:invoice:mapping:rule')
    this.invoiceRules = items
    this.invoiceRuleMap = array2object(items, 'entityId')
  }

  private processInvoice(invoices, components, currentFeeTypeForm?) {
    const invoiceMapList: { [key: string]: InvoiceIF[] } = {}
    const invoiceEntityKeys = []
    const baseDataPropertiesMap = api.getState('@common.globalFields.baseDataPropertiesMap') || {}
    invoices.forEach(invoice => {
      let list = invoiceMapList[invoice.master.entityId]
      if (!list) {
        list = []
        invoiceMapList[invoice.master.entityId] = list
        invoiceEntityKeys.push(invoice.master.entityId)
      }
      list.push(invoice)
    })
    let rule2InvoiceValue = {}
    invoiceEntityKeys.forEach(entityId => {
      const invoiceList = invoiceMapList[entityId]
      const invoiceRule = this.invoiceRuleMap[entityId]
      invoiceRule.rules = this.mergeRules(invoiceRule?.rules, this.addDefaultRules(entityId))
      invoiceList.forEach((invoice, curIndex) => {
        let detailResult: any
        if (invoice.details && invoice.details.length) {
          detailResult = this.processDetail(invoice)
        }
        rule2InvoiceValue = this.conver2DetailByRule(
          invoice,
          detailResult,
          invoiceRule,
          rule2InvoiceValue,
          baseDataPropertiesMap,
          curIndex,
          invoiceList.length,
          components,
          currentFeeTypeForm
        )
      })
    })
    return rule2InvoiceValue
  }

  private conver2DetailByRule(
    invoice: InvoiceIF,
    detailResult: any,
    ruleItem: InvoiceMappingRuleItemIF,
    rule2InvoiceValue,
    globalFieldMap: GlobalFieldMapIF,
    curIndex: number,
    invoiceListLength: number,
    components: any,
    currentFeeTypeForm: any
  ) {
    return ruleItem.rules.reduce((result, rule) => {
      const globalField = globalFieldMap[rule.detailField]
      let type = getV(globalField, 'dataType.type')
      if (rule.detailField === 'sumAmountAndTax') {
        type = 'money'
      }
      const entity = getV(globalField, 'dataType.entity')

      const prev = result[rule.detailField]
      let current = invoice.master.form[rule.mappingValue as string]
      if (rule.mappingType === DetailValueSource.INVOICEDETAIL) {
        current = detailResult?.[rule.mappingValue as string]
      }
      // @i18n-ignore
      if (rule.mappingValue === 'E_税额' && invoice.taxAmount) {
        current = invoice.taxAmount
      }
      // @i18n-ignore
      if (rule.mappingValue === 'E_税率' && invoice.taxRate !== undefined) {
        // 价税分离的字段要特殊处理
        current = invoice.taxRate
      }
      if (rule.mappingType === DetailValueSource.FIXED) {
        current = rule.mappingValue
      }
      if (type === 'money') {
        if (rule.detailField === 'amount' && getV(invoice, 'approveAmount') && getV(invoice, 'comment')) {
          // 表示用户修改过核发金额
          result[rule.detailField] = this.processMoney(
            getV(invoice, 'approveAmount'),
            prev,
            rule.mappingValue as string,
            false,
            rule.detailField,
            currentFeeTypeForm
          )
          return result
        }
        result[rule.detailField] = this.processMoney(
          current,
          prev,
          rule.mappingValue as string,
          detailResult,
          rule.detailField,
          currentFeeTypeForm
        )
        return result
      }
      if (type === 'number') {
        result[rule.detailField] = this.processNumber(
          current,
          prev,
          rule.mappingValue as string,
          detailResult,
          invoice.taxRate
        )
        return result
      }
      if (entity === 'basedata.city' && current && rule.mappingType === DetailValueSource.INVOICEINFO) {
        result[rule.detailField] = { isFixed: false, value: current }
        return result
      }
      if (entity === 'organization.Staff' && current && rule.mappingType === DetailValueSource.INVOICEINFO) {
        const tempResult = Object.assign({}, { ...result })
        const loginStaff = api.getState('@common.userinfo.staff')
        const activesStaffs = api.getState('@common.staffsVisibility')
        let showTipMessage = false
        components.map(component => {
          if (component.field == rule.detailField) showTipMessage = true
        })

        const filterStaff =
          activesStaffs?.filter(staff => {
            return staff.name === current
          }) || []
        if (filterStaff?.length > 1) {
          if (loginStaff?.name == filterStaff[0].name) {
            tempResult[rule.detailField] = loginStaff
          } else {
            tempResult[rule.detailField] = ''
            if (curIndex + 1 == invoiceListLength && showTipMessage)
              showMessage.error(i18n.get('在该企业找到多位同名乘车人员，请手动选择'))
          }
        } else if (filterStaff?.length == 1) {
          tempResult[rule.detailField] = filterStaff[0]
        } else {
          tempResult[rule.detailField] = ''
          if (curIndex + 1 == invoiceListLength && showTipMessage)
            showMessage.error(i18n.get('在该企业未找到对应乘车人员'))
        }
        return tempResult
      }
      if (rule.detailField === 'invoiceType' && current && rule.mappingType === DetailValueSource.INVOICEINFO) {
        result[rule.detailField] = { isFixed: false, value: current }
        return result
      }

      const ENUM_VALUE_TO_TEXT = api.getState('@common').powers.ENUM_VALUE_TO_TEXT
      if (
        ENUM_VALUE_TO_TEXT &&
        type === 'text' &&
        rule.mappingValue === 'E_system_发票主体_发票类别' &&
        current &&
        rule.mappingType === DetailValueSource.INVOICEINFO
      ) {
        result[rule.detailField] = INVOICE_TYPE()[current]
        return result
      }
      const curFeild = components.find(component=>component.field === rule.detailField)
      if(type === 'date' && curFeild && current){
        if(curFeild.dateTimeType === 'YEAR_MONTH'){
          current = moment(current || new Date()).startOf('month').valueOf()
        }else if(curFeild.dateTimeType === 'YEAR_MONTH_DAY_TIME'){
          current = current
        }else{
          // 模板配置默认是年月日
          current = moment(current || new Date()).startOf('day').valueOf()
        }
      }

      result[rule.detailField] = current
      return result
    }, rule2InvoiceValue)
  }

  private processDetail(invoice: InvoiceIF) {
    const deduction = invoice.master.form['E_是否抵扣'] // @i18n-ignore
    const masterEntityId = invoice.master.entityId
    return invoice.details.reduce((result, current, currentIndex) => {
      const unitPriceKey = `E_${current.entityId}_单价` // @i18n-ignore
      const taxAmountKey = `E_${current.entityId}_税额` // @i18n-ignore
      const noTaxAmountKey = `E_${current.entityId}_金额` // @i18n-ignore
      const taxRateKey = `E_${current.entityId}_税率` // @i18n-ignore
      const detail = {}
      for (let key in current.form) {
        if ([unitPriceKey, taxAmountKey, noTaxAmountKey].includes(key)) {
          const money = result[key] || 0
          const sumMoney = new MoneyMath(money).add(current.form[key] || 0).value
          detail[key] = sumMoney
        } else if (key === taxRateKey) {
          const currentTaxRate = current.form[key]
          let taxRate = result.hasOwnProperty(key) ? result[key] : currentTaxRate
          if (taxRate !== undefined && taxRate !== currentTaxRate) {
            taxRate = undefined
          }
          detail[key] = taxRate
        } else {
          detail[key] = currentIndex === 0 ? current.form[key] : result[key]
        }
      }
      const sumTaxAmount = detail[taxAmountKey]
      const sumNoTaxAmount = detail[noTaxAmountKey]
      const sumAmountAndTax = new MoneyMath(sumTaxAmount).add(sumNoTaxAmount).value
      result = {
        ...detail,
        [`E_税率`]: detail[taxRateKey], // @i18n-ignore
        [`E_${masterEntityId}_税额`]: sumTaxAmount, // @i18n-ignore
        [`E_不计税金额`]: sumNoTaxAmount, // @i18n-ignore
        [`E_${masterEntityId}_价税合计`]: sumAmountAndTax, // @i18n-ignore
        sumAmountAndTax
      }
      if (deduction) {
        result[`E_税额`] = invoice.taxAmount || sumTaxAmount // @i18n-ignore
      }
      return result
    }, {})
  }

  private processMoney(
    current: MoneyIF,
    prev: MoneyIF = standardValueMoney(0),
    sourceField: string,
    detailResult: any,
    detailField: string,
    currentFeeTypeForm: any
  ): MoneyIF {
    const dimensionCurrency = api.getState()['@bills']?.dimentionCurrencyInfo
    const hasForeignNumCode = prev?.foreignNumCode && current?.foreignNumCode
    if (detailResult) {
      current = detailResult[sourceField] || current
    }
    // 判断金额的本位币和法人实体或系统本位币是否一致
    // 排除海外发票
    if (!!current && isStandardCurrencyDiff(current, standardValueMoney(0, dimensionCurrency?.currency))) {
      if (sourceField?.startsWith('E_system_海外发票')) {
        current = foreignConversionStandard(current)
      } else {
        current = formatMoneyWithDiffCurrency(
          current,
          standardValueMoney(0, dimensionCurrency?.currency),
          dimensionCurrency?.rates
        )
      }
    }
    let money: MoneyIF
    if (hasForeignNumCode && current?.foreignNumCode !== prev?.foreignNumCode) {
      const standardAaddForeign = new Big(current.standard).plus(new Big(prev.standard))
      const standard = standardAaddForeign.toFixed(Number(current.standardScale))
      const foreign = standardAaddForeign.div(current.rate).toFixed(Number(current.standardScale))
      money = { ...current, foreign: foreign.valueOf(), standard }
    } else if (hasForeignNumCode && current?.foreign && current?.foreignStrCode) {
      const foreign = new Big(prev.foreign || new Big(prev.standard).div(current.rate)).plus(current.foreign)
      const standard = new Big(foreign).times(current.rate).toFixed(Number(current.standardScale))
      money = { ...current, foreign: foreign.valueOf(), standard }
    } else if (!prev?.standard && current?.foreign) {
      // standard 初始值为0 并且计算值有外币情况下，第一次不参与累加（因为 standard 0 + 外币计算有问题）
      money = current
    } else {
      money = new MoneyMath(prev).add(current).value as MoneyIF
    }
    if (
      dimensionCurrency?.currency &&
      !money.foreignStrCode &&
      money?.standardNumCode !== dimensionCurrency.currency?.numCode
    ) {
      money = standardValueMoney(money.standard, dimensionCurrency.currency)
    }
    if (money.foreignStrCode && money.foreign) {
      money.foreign = Number(money.foreign).toFixed(Number(money.foreignScale))
    }

    // 修复场景：生成明细后，金额budget字段消失导致币种切换异常
    let currentFieldValue = currentFeeTypeForm?.[detailField]
    // 使用开关控制是否要赋值给预算币
    const useInvoiceAssignBudgetValue = getBoolVariation('invoice-assign-budget-money')
    if (
      useInvoiceAssignBudgetValue &&
      currentFeeTypeForm &&
      !currentFieldValue &&
      !!dimensionCurrency?.rates?.length &&
      currentFeeTypeForm.hasOwnProperty(detailField)
    ) {
      const budgetMoney = initBudgetCurrentInfo(money, dimensionCurrency?.rates)
      currentFieldValue = { ...money, ...budgetMoney }
    }
    if (currentFieldValue) {
      const moneyObj = currentFieldValue

      if (!dimensionCurrency || typeof moneyObj !== 'object') {
        return money
      }

      moneyObj.standard = money.standard

      if (
        moneyObj.budgetStrCode &&
        moneyObj.standard !== undefined &&
        moneyObj.budgetRate &&
        Number(moneyObj.budgetRate) !== 0
      ) {
        moneyObj.budget =
          Number(moneyObj.standard) === 0
            ? '0'
            : new Big(moneyObj.standard).div(moneyObj.budgetRate).toFixed(Number(moneyObj.budgetScale))
      }
      if (moneyObj.foreignStrCode && moneyObj.standard !== undefined && moneyObj.rate && Number(moneyObj.rate) !== 0) {
        moneyObj.foreign =
          Number(moneyObj.standard) === 0
            ? '0'
            : new Big(moneyObj.standard).div(moneyObj.rate).toFixed(Number(moneyObj.foreignScale))
      }

      return { ...money,...moneyObj }
    }
    return money
  }

  private processNumber(current: string, prev: string, sourceField: string, detailResult: any, taxRate = ''): string {
    // @i18n-ignore
    if (sourceField.indexOf('税率') >= 0) {
      current = detailResult ? detailResult[sourceField] : current
      let result = prev !== undefined && current !== prev ? '' : current
      if (isString(result) && result?.length && taxRate) {
        result = taxRate
      }
      return result
    }
    return String(Number(current) + Number(prev || 0))
  }

  private processFieldMappingValue(components: ComponentIF[], rule2InvoiceValue, invoices) {
    return components.reduce((result, current) => {
      let value = rule2InvoiceValue[current.field]
      if (value !== undefined) {
        if (current.field === 'invoiceType' && isObject(value) && !value.isFixed) {
          result[current.field] = invoiceTypeMapping[value.value]
          return result
        }
        if (current.field === 'taxRate') {
          const rate = isNumber(value) ? String(value) : value
          value = rate?.replace('%', '')
          value = isNaN(Number(value)) ? '0' : value
        }
        result[current.field] = value
        return result
      }
      if (current.field === 'invoice') {
        result[current.field] = '1'
        return result
      }

      if (current.field === 'invoiceForm') {
        result[current.field] = {
          invoices,
          type: 'exist'
        }
      }
      return result
    }, {})
  }

  private processSummaryFields(component: ComponentIF[], feeTypeForm, rule2InvoiceValue) {
    const summaryFields = component.filter(line => getV(line, 'defaultValue.type') === 'invoiceSum')
    if (summaryFields.length) {
      const cloneInvoiceValue = cloneDeep(rule2InvoiceValue)
      summaryFields.forEach(({ defaultValue, field }) => {
        const v = getV(defaultValue, 'value')
        const value = getAcountKey(v)
        if (v === 'taxTotal') {
          feeTypeForm[field] = new MoneyMath(cloneInvoiceValue.sumAmountAndTax).value
        } else {
          feeTypeForm[field] = cloneInvoiceValue[value]
        }
      })
    }
    return feeTypeForm
  }

  private processNoInvoices(component: ComponentIF[]) {
    const fields = this.invoiceRules.reduce((result, current) => {
      return current.rules.reduce((ruleResult: string[], ruleCurrent) => {
        ruleResult.push(ruleCurrent.detailField)
        return ruleResult
      }, result)
    }, [])
    return component.reduce((feeTypeForm: any, c) => {
      if (!!~fields.indexOf(c.field)) {
        feeTypeForm[c.field] = undefined
      }
      return feeTypeForm
    }, {})
  }

  private async processFormCityField(components: ComponentIF[], feeTypeForm: any) {
    const valueFields = components.reduce((result, c) => {
      if (getV(c, 'type') === 'city') {
        const city = feeTypeForm[c.field]
        if (city && city.isFixed !== undefined && !city.isFixed) {
          const fields = result[city.value] || []
          fields.push(c.field)
          result[city.value] = fields
        }
      }
      return result
    }, {} as any)
    const cityKeys = Object.keys(valueFields)
    const encodeCityKeys = cityKeys.map(oo => encodeURIComponent(oo))
    if (cityKeys.length) {
      const { value: cityValue = {} } = await api.invokeService('@invoice-manage:get:invoice:city', {
        cities: encodeCityKeys
      })
      return cityKeys.reduce((form, key) => {
        const value = cityValue[key]
        const fields = valueFields[key]
        fields.reduce((fieldForm: any, field: string) => {
          fieldForm[field] = value ? JSON.stringify([value]) : undefined
          return fieldForm
        }, feeTypeForm)
        return form
      }, feeTypeForm)
    }
    return Promise.resolve(feeTypeForm)
  }

  private async processEnumField(components: ComponentIF[], feeTypeForm: any) {
    const baseDataPropertiesMap = api.getState('@common.globalFields.baseDataPropertiesMap') || {}
    const result = components.reduce((result, c) => {
      const globalField = baseDataPropertiesMap[c.field]
      const entity = getV(globalField, 'dataType.entity', '') || getV(globalField, 'dataType.elemType.entity', '')
      const value = feeTypeForm[c.field]
      if (entity.startsWith('basedata.Enum') && isString(value)) {
        const code = entity.split('.').pop()
        const { fields } = result[code] || { fields: [] }
        fields.push(c.field)
        result[code] = { fields, value }
      }
      return result
    }, {} as { [key: string]: { fields: string[]; value?: string } })
    const enumCodes = Object.keys(result)
    if (enumCodes.length) {
      const promises = enumCodes.map(code => api.invokeService('@common:get:enumitems', code))
      const data = await Promise.all(promises)
      const items = data.reduce((result, item) => {
        result = result.concat(item.items)
        return result
      }, [])
      if (items.length) {
        return enumCodes.reduce((form, code) => {
          const { fields, value: name } = result[code]
          const value = items.find((item: any) => item.id === name || item.code === name || item.name === name)
          fields.reduce((fieldForm: any, field: string) => {
            fieldForm[field] = value
            return fieldForm
          }, feeTypeForm)
          return form
        }, feeTypeForm)
      }
    }
    return feeTypeForm
  }

  private addDefaultRules(entityId: string): InvoiceRuleIF[] {
    const baseRules = [
      { detailField: 'taxAmount', mappingType: 'invoiceInfo', mappingValue: 'E_税额' }, // @i18n-ignore
      { detailField: 'noTaxAmount', mappingType: 'invoiceInfo', mappingValue: 'E_不计税金额' } // @i18n-ignore
    ]
    const defaultRulesMap = {
      // @i18n-ignore
      system_发票主体: baseRules.concat(
        { detailField: 'taxRate', mappingType: 'invoiceInfo', mappingValue: 'E_税率' }, // @i18n-ignore
        { detailField: 'sumAmountAndTax', mappingType: 'invoiceInfo', mappingValue: `E_${entityId}_价税合计` } // @i18n-ignore
      ),
      // @i18n-ignore
      system_出租车票: baseRules.concat(
        { detailField: 'sumAmountAndTax', mappingType: 'invoiceInfo', mappingValue: 'E_system_出租车票_金额' } // @i18n-ignore
      ),
      // @i18n-ignore
      system_火车票: baseRules.concat(
        { detailField: 'sumAmountAndTax', mappingType: 'invoiceInfo', mappingValue: 'E_system_火车票_金额' } // @i18n-ignore
      ),
      // @i18n-ignore
      system_客运汽车发票: baseRules.concat(
        { detailField: 'sumAmountAndTax', mappingType: 'invoiceInfo', mappingValue: 'E_system_客运汽车发票_金额' } // @i18n-ignore
      ),
      // @i18n-ignore
      system_航空运输电子客票行程单: baseRules.concat(
        {
          detailField: 'sumAmountAndTax',
          mappingType: 'invoiceInfo',
          mappingValue: 'E_system_航空运输电子客票行程单_金额'
        } // @i18n-ignore
      ),
      // @i18n-ignore
      system_过路费发票: baseRules.concat(
        { detailField: 'sumAmountAndTax', mappingType: 'invoiceInfo', mappingValue: 'E_system_过路费发票_金额' } // @i18n-ignore
      ),
      // @i18n-ignore
      system_定额发票: baseRules.concat(
        { detailField: 'sumAmountAndTax', mappingType: 'invoiceInfo', mappingValue: 'E_system_定额发票_金额' } // @i18n-ignore
      ),
      // @i18n-ignore
      system_其他: baseRules
    }
    return defaultRulesMap[entityId] || []
  }

  private mergeRules(rules: InvoiceRuleIF[], toMergeRules: InvoiceRuleIF[]): InvoiceRuleIF[] {
    const map: { [key: string]: InvoiceRuleIF } = array2object(rules, 'detailField')

    return toMergeRules.reduce((array, rule) => {
      if (!map[rule.detailField]) {
        array.push(rule)
      }
      return array
    }, rules)
  }
}
// 发票导入的映射关系对应值
const invoiceTypeMapping = {
  DIGITAL_NORMAL: 'VATElectronicInvoice',
  DIGITAL_SPECIAL: 'VATElectronicSpecialInvoice',
  PAPER_NORMAL: 'VATOrdinaryInvoice',
  PAPER_SPECIAL: 'VATSpecialInvoice',
  FULL_DIGITAl_PAPER: 'FullDigitalSpecialPaper',
  FULL_DIGITAl_PAPER_NORMAL: 'FullDigitalSpecialPaperNormal',
  PAPER_CAR: 'MotorInvoice',
  PAPER_ROLL: 'VATVolumeTicket',
  PAPER_FEE: 'TollInvoice',
  BLOCK_CHAIN: 'BlockchainElectronicInvoice',
  FULL_DIGITAl_NORMAL: 'FullDigitalNormal',
  FULL_DIGITAl_SPECIAL: 'FullDigitalSpecial',
  OverseasInvoice: 'OverseasInvoice',
  ELECTRONIC_AIRCRAFT_INVOICE: 'FullDigitalAircraftInvoice',
  ELECTRONIC_TRAIN_INVOICE: 'FullDigitalTrainInvoice',
  ELECTRONIC_PAPER_CAR: 'FullDigitalMotorInvoice' // todo 待确认
}
