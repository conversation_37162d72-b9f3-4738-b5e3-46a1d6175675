import { app } from '@ekuaibao/whispered'
import { addRumAction } from '../hosting/dataflux-rum'

export enum DeviceType {
  PC = 'pc',
  APP = 'app'
}

export enum FillMode {
  CREATE = 'create',
  EDITDRAFT = 'editdraft'
}

interface AIFillFormTrackingData {
  // 基础字段
  form_type?: string // 单据类型
  form_template_name?: string // 单据模板名称
  device_type?: DeviceType // 设备类型（app、pc）
  form_create_time?: number // 单据创建时间
  
  // AI相关标识
  ai_used_flag?: boolean // 是否在此单据使用了AI摘要功能（true、false）
  
  // 时长统计
  total_fill_duration?: number // 本次会话的有效填写时长（毫秒）
  form_lifecycle_duration?: number // 单据总生命周期（毫秒）
  
  // 附件统计
  total_attachments_count?: number // 单据中总附件数
  ai_attachments_count?: number // 单据中可使用AI摘要的附件数
  
  // AI使用统计
  ai_used_count?: number // 单据中点击AI摘要的次数
  ai_fields_applied_count?: number // 单据中通过AI填单应用成功的字段总数
  
  // 其他字段
  fieldCount?: number // 字段总数
  success?: boolean // 操作是否成功
  errorMsg?: string // 错误信息
  hash_path?: string // 单据页面hash路径
  timestamp?: number // 事件发生时间戳
  title?: string // 事件标题
}

interface TrackAIFillFormStartData extends AIFillFormTrackingData {
  form_id: string  // 单据实例id（单据编号，若新建则生成唯一ID）
  form_template_id: string // 单据模板id
  form_type: string // 单据类型
  form_template_name: string // 单据模板名称
  event_start_time: number // 事件开始时间
  form_create_time: number // 单据创建时间
  device_type: DeviceType // 设备类型（app、pc）
  fill_mode: FillMode // 填写模式（create、editdraft）
}

interface TrackAIFillFormSubmitData extends AIFillFormTrackingData {
  form_id: string
  form_template_id: string
  form_type: string // 单据类型【已埋点】
  form_template_name: string // 单据模板名称
  event_end_time: number // 事件发生时间戳【已埋点】
  device_type: DeviceType // 设备类型（app、pc）【已埋点】
  ai_used_flag: boolean // 是否在此单据使用了AI摘要功能（true、false)
  total_fill_duration: number // 本次会话的有效填写时长【已埋点】
  // 关联"开始填写单据"事件计算，event_end_time - event_start_time(最新）
  // 注意：此处只考虑用户新建/打开单据直接提交的填写时长，其他时长均不考虑。
  form_lifecycle_duration: number // 单据总生命周期
  // 关联"开始填写单据"事件计算，event_end_time - form_create_time
  total_attachments_count: number // 单据中总附件数
  ai_attachments_count: number // 单据中可使用AI摘要的附件数
  ai_used_count: number // 单据中点击AI摘要的次数
  // 关联"点击AI摘要按钮"事件计算，count(event_id)
  ai_fields_applied_count: number // 单据中通过AI填单应用成功的字段总数
}

interface TrackAIFillFormSummaryData extends AIFillFormTrackingData {
  form_id: string 
  file_id: string // 附件ID
  form_template_id: string // 单据模板id
  form_expense_id: string // 单据关联的费用明细 id（若附件在费用明细上，则记录明细id）
  form_type: string // 单据类型
  form_template_name: string // 单据模板名称
  form_expense_name: string // 单据关联的费用明细名称（若附件在费用明细上，则记录明细名称）
  file_name: string // 附件名称
  file_type: string // 附件类型（.pdf、.jpg、.png）
  file_size: number // 附件大小（字节）
  event_click_time: number // 点击时间戳
}

interface TrackAIFillFormSummaryResultData extends AIFillFormTrackingData {
  form_id: string
  file_id: string // 附件ID
  form_template_id: string // 单据模板id
  form_expense_id: string // 单据关联的费用明细 id（若附件在费用明细上，则记录明细id）
  form_template_name: string // 单据模板名称
  form_expense_name: string // 单据关联的费用明细名称（若附件在费用明细上，则记录明细名称）
  file_name: string // 附件名称
  file_type: string // 附件类型（.pdf、.jpg、.png）
  file_size: number // 附件大小（字节）
  event_display_time: number // 点击时间戳
  recognition_status: string // 识别状态（success、failed）
  error_msg: string // 错误信息
  extracted_fields_list: any // ai摘要提取结果
  is_apply_all: boolean // 是否应用全部字段
  processing_duration_ms: number // AI识别处理时长，关联“点击AI摘要按钮”事件，event_display_time - event_click_time
  extracted_fields_count: number // AI提取的字段数量
}

interface TrackAIFillFormModifyData extends AIFillFormTrackingData {
  form_id: string
  field_id: string // 字段id
  field_name: string // 被修改的字段名
  field_type: string // 字段类型
  ai_original_value: string // AI原始填充值
  user_modified_value: string // 用户修改后的值
  event_modified_time: number // 修改时间戳
}

interface TrackAIFeedbackData {
  ai_id?: string // AI摘要id（基于content的固定hash）
  action: 'like' | 'dislike' // 行为
  feedback?: string // 详细说明（仅点踩时有值）
  ai_chat?: string // AI对话内容（对话填单场景）
  chat_id?: string // 对话ID（对话填单场景）
  file_id?: string // 附件id（附件填单场景，多个文件时为JSON字符串）
  file_name?: string // 附件名称（附件填单场景，多个文件时为JSON字符串）
  content?: string // AI摘要内容
}

export class AIFillFormTracker {

  /****************** 用户填写单据埋点 *****************/
  // 用户填写单据开始
  /**
   * 触发时机：
   * 用户创建新单据，进入编辑界面时。
   * 用户打开草稿单据，进入编辑界面时。
   */
  // form_id：单据实例id（单据编号，若新建则生成唯一ID）
  // form_template_id：单据模板id
  // form_type: 单据类型
  // form_template_name：单据模板名称
  // event_start_time：事件发生时间戳【已埋点】
  // form_create_time：单据创建时间戳（单据的创建时间）
  // device_type：设备类型（app、pc）【已埋点】
  // fill_mode：填写模式（create、editdraft）
  static trackAIFillFormStart(data: TrackAIFillFormStartData) {
    addRumAction('ai_fill_form_start', {
      title: 'AI填单开始',
      hash_path: location.hash,
      timestamp: Date.now(),
      device_type: DeviceType.PC,
      event_start_time: Date.now(),
      ...data
    })
  }

  /**
   * 成功提交单据
   * 触发时机： 用户成功提交单据后。
   */
  static trackAIFillFormSubmit(data: TrackAIFillFormSubmitData) {
    addRumAction('ai_fill_form_submit', {
      title: 'AI填单提交',
      hash_path: location.hash,
      timestamp: Date.now(),
      device_type: DeviceType.PC,
      event_end_time: Date.now(),
      ...data
    })
  }

  /****************** 用户与AI附件填单功能交互埋点 *****************/

  /**
   * 点击"AI摘要"按钮
   * 触发时机： 用户点击某个附件上的"AI摘要"按钮时。
   */
  static trackAIFillFormSummary(data: TrackAIFillFormSummaryData) {
    addRumAction('ai_fill_form_summary', {
      title: 'AI填单摘要',
      hash_path: location.hash,
      timestamp: Date.now(),
      ...data
    })
  }

  /**
   * AI摘要识别结果展示
   * 触发时机： AI处理完成，并将识别出的信息展示给用户时。
   */
  static trackAIFillFormSummaryResult(data: TrackAIFillFormSummaryResultData) {
    addRumAction('ai_fill_form_summary_result', {
      title: 'AI填单摘要结果',
      hash_path: location.hash,
      timestamp: Date.now(),
      event_display_time: Date.now(),
      ...data
    })
  }

  /**
   * 用户修改AI填充的字段
   * 触发时机： 用户修改了一个由AI摘要功能填充的字段值。
   */
  static trackAIFillFormModify(data: TrackAIFillFormModifyData) {
    addRumAction('ai_fill_form_modify', {
      title: 'AI填单字段人工修改',
      hash_path: location.hash,
      timestamp: Date.now(),
      event_modified_time: Date.now(),
      ...data
    })
  }

  /****************** AI摘要反馈埋点 *****************/

  /**
   * 基于AI摘要内容生成固定的ai_id
   */
  private static generateAiId(content: string): string {
    let hash = 0
    if (content.length === 0) return hash.toString()

    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }

    return `ai_${Math.abs(hash).toString(36)}`
  }

  /**
   * AI摘要反馈 - 点赞/点踩
   * 触发时机： 用户对AI摘要结果进行点赞或点踩操作时。
   */
  static trackAIFeedback(data: TrackAIFeedbackData) {
    const trackingData: TrackAIFeedbackData = {
      ...data,
      ai_id: data.content ? this.generateAiId(data.content) : ''
    }

    const userInfo = app.getState()['@common']?.userinfo

    addRumAction('ai_feedback', {
      title: data.action === 'like' ? 'AI摘要反馈点赞' : 'AI摘要反馈点踩',
      corpName: userInfo?.corporation?.name,
      staff_id: userInfo?.staff?.id,
      staff_name: userInfo?.staff?.name,
      hash_path: location.hash,
      timestamp: Date.now(),
      ...trackingData
    })
  }
}