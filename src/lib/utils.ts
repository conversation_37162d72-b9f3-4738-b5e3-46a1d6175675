import { app } from '@ekuaibao/whispered'

import { FMPMonitorOptions, FMP_MONITOR_CONFIG_MAP } from './FMPConfigMap'
import { addRumAction } from '../hosting/dataflux-rum'


export const getTrueKey = (key: string = '') => {
  const arr = key.split('_')
  if (arr.length === 3) {
    return arr[2]
  } else if (arr.length > 3) {
    return arr.splice(2).join('_')
  } else {
    return key
  }
}

export const fixEntityObj = (it: any) => {
  if (!it) return it
  const obj: any = {}
  Object.keys(it).forEach(key => {
    const tKey = getTrueKey(key)
    obj[tKey] = it[key]
  })
  return obj
}

// FMP监控状态管理
let fmpMonitorState = {
  isActive: false,
  hasCollectedPages: new Set<string>(), // 记录已经采集过FMP的页面
  observer: null as MutationObserver | null,
  timeoutId: null as NodeJS.Timeout | null,
  currentPage: '', // 当前监控的页面
  startTime: 0, // 监控开始时间，用于判断是否是首次加载
}

// 清理FMP监控
function cleanupFMP() {
  if (fmpMonitorState.observer) {
    fmpMonitorState.observer.disconnect()
    fmpMonitorState.observer = null
  }

  if (fmpMonitorState.timeoutId) {
    clearTimeout(fmpMonitorState.timeoutId)
    fmpMonitorState.timeoutId = null
  }

  app.un('hash:change', handleRouteChange)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('pagehide', handleVisibilityChange)

  fmpMonitorState.isActive = false
  console.log('FMP监控已清理:', fmpMonitorState.currentPage)
}

// 页面可见性变化处理
function handleVisibilityChange() {
  if (document.visibilityState === 'hidden') {
    cleanupFMP()
  }
}

// 路由变化处理 - 如果用户快速跳转，立即停止监控
function handleRouteChange() {
  const currentHash = location.hash.slice(1)

  // 如果路由变化了，且当前有监控在运行
  if (fmpMonitorState.isActive && fmpMonitorState.currentPage !== currentHash) {
    console.log('路由从', fmpMonitorState.currentPage, '变化到', currentHash, '，停止FMP监控')
    cleanupFMP()
  }
}

const monitorFMP = (options: Omit<FMPMonitorOptions, 'rule'>, pageHash: string) => {
  if (!('MutationObserver' in window)) {
    return
  }

  // 检查是否已经采集过该页面的FMP数据
  if (fmpMonitorState.hasCollectedPages.has(pageHash)) {
    console.log('页面FMP数据已采集，跳过重复监控:', pageHash)
    return
  }

  // 如果已有监控在运行，先清理
  if (fmpMonitorState.isActive) {
    cleanupFMP()
  }

  const { isFMPNode, params } = options

  try {
    if(document.visibilityState === 'hidden') return

    // 记录当前监控的页面和开始时间
    fmpMonitorState.currentPage = pageHash
    fmpMonitorState.startTime = performance.now()

    app.on('hash:change', handleRouteChange)

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('pagehide', handleVisibilityChange)

    fmpMonitorState.observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        for (const node of Array.from(mutation.addedNodes)) {
          if (node.nodeType !== Node.ELEMENT_NODE) continue

          if (isFMPNode(node as Element)) {
            const args = typeof params === 'function' ? params() : params
            const loadedDuration = Math.floor(performance.now())

            addRumAction('first_meaning_paint', {
              ...args,
              loadedDuration,
              pageHash,
              monitorStartTime: fmpMonitorState.startTime,
            })
            console.log(args.title, 'FMP - 首次加载完成:', {
              pageHash,
              duration: loadedDuration,
              startTime: fmpMonitorState.startTime,
              node
            })

            // 标记该页面已采集，防止重复采集
            fmpMonitorState.hasCollectedPages.add(pageHash)
            cleanupFMP()
            return
          }
        }
      }
    })

    fmpMonitorState.observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // 设置超时断开连接
    fmpMonitorState.timeoutId = setTimeout(() => {
      console.log('FMP监控超时:', pageHash)
      cleanupFMP()
    }, 60 * 1000)

    fmpMonitorState.isActive = true
    console.log('FMP监控已启动:', pageHash)

  } catch (error) {
    console.error('FMP监控启动失败:', error)
  }
}

export const trackFMP = async (hash: string) => {
  if(!hash) return

  if(typeof hash === 'string') {
    hash = hash.replace('#', '')
  }

  const config = Object.values(FMP_MONITOR_CONFIG_MAP).find(config => config.rule(hash))

  if (config) {
    monitorFMP({
      isFMPNode: config.isFMPNode,
      params: config.params
    }, hash)
  }
}

// 导出工具函数供调试使用
export const FMPUtils = {
  // 获取当前状态
  getState: () => ({
    isActive: fmpMonitorState.isActive,
    hasCollectedPages: Array.from(fmpMonitorState.hasCollectedPages),
    currentPage: fmpMonitorState.currentPage
  }),

  // 重置状态（用于测试或特殊情况）
  reset: () => {
    cleanupFMP()
    fmpMonitorState.hasCollectedPages.clear()
  },

  // 手动清理
  cleanup: cleanupFMP
}


/**
 * 注册钉钉性能监控日志
 */
export const registerDingtalkPerformance = () => {
  try {
    if (process.env.NODE_ENV === 'production' && location.hostname === 'dd2.hosecloud.com') {
      window.__BIRD_CONFIG = window.__BIRD_CONFIG || {}
      window.__BIRD_CONFIG.tags = {
        isvAppId: '2297' // isv应用id
      }

      const reportPerformance = () => {
        setTimeout(() => {
          const { send } = window.__BIRD || {}

          window.performance && window.performance.mark('FMP')
          typeof send === 'function' && send()
        }, 500) // 延迟500ms确保脚本初始化
      }

      if (document.readyState === 'complete') {
        // 页面已加载完成，直接执行
        reportPerformance()
      } else {
        // 页面还在加载，等待load事件
        window.addEventListener('load', reportPerformance)
      }
    }
  } catch(e) {}
}