/**
 *  Created by pw on 2021/8/18 下午4:14.
 */
import { app } from '@ekuaibao/whispered'
import { FeeTypeIF, InvoiceIF } from '@ekuaibao/ekuaibao_types'
import { flatten } from 'lodash'
import { standardValueMoney } from './misc'
import { enableOtherInvoiceByDimension } from './featbit/feat-switch'
import Big from 'big.js'
import { calculateAble } from '../components/utils/fnCurrencyObj'

export async function updateInvoiceDeduction(
  feetypes: FeeTypeIF[] = [],
  invoiceList: InvoiceIF[] = [],
  isMerge: boolean = false
): Promise<boolean> {
  let isUpdate = false
  const params = feetypes.map((feeType, index) => {
    const invoices = isMerge ? invoiceList : [invoiceList[index]]
    const invoiceIds = invoices
      .map(invoice => {
        return { masterId: invoice.master?.id, detailIds: invoice?.details?.map(detail => detail?.id) }
      })
      .filter(invoice => !!invoice?.masterId?.length)
    return { feeTypeId: feeType?.id, invoiceIds }
  })
  const invoiceIds = params.map(param => param.invoiceIds).filter(invoice => invoice?.length)
  if (!invoiceIds.length) {
    return isUpdate
  }
  const appInstance: any = app
  appInstance?.logger?.info('开始价税分离', params)
  const value = await app.invokeService('@bills:update:invoice:tax', params)
  appInstance?.logger?.info('价税分离后台返回值', value)
  if (value && Object.keys(value)?.length) {
    isUpdate = true
    const invoiceData = flatten(invoiceList)
    invoiceData.forEach(invoice => {
      const priceSeparated = value[invoice?.master?.id]
      if (priceSeparated) {
        updatePriceSeparatedValue(invoice, priceSeparated)
      }
    })
  }
  return isUpdate
}

interface IPriceSeparated {
  taxAmount: number
  taxRate: string
}

function updatePriceSeparatedValue(invoice: InvoiceIF, priceSeparated: IPriceSeparated) {
  const amount = invoice.master.form[`E_system_${invoice.master.entityId}_金额`] || invoice.master.form[`E_税额`]
  // 发票上是否存在金额多币种形态
  if(enableOtherInvoiceByDimension() && amount?.foreignNumCode && amount?.foreignNumCode ==='156' && calculateAble([amount?.rate, amount?.foreignScale, priceSeparated.taxAmount])){
    // 法人实体是本位币
    const  { currency } = app.getState()['@bills'].dimentionCurrencyInfo || {}
    // 价税分离 priceSeparated.taxAmount 字段始终返回人民币，所以前端需要换算，后期可优化成后端返回多币种金额字段
    if(currency?.numCode !== '156'){
      const standard = new Big(priceSeparated.taxAmount).times(amount?.rate).toFixed(Number(amount?.foreignScale))
      invoice.taxAmount = {...amount, standard: standard, foreign: priceSeparated.taxAmount}
    }else{
      const foreign = new Big(priceSeparated.taxAmount).div(amount?.rate).toFixed(Number(amount?.foreignScale))
      invoice.taxAmount = {...amount, standard: priceSeparated.taxAmount, foreign: foreign}
    }
  }else{
    invoice.taxAmount = standardValueMoney(priceSeparated.taxAmount)
  }
  invoice.taxRate = priceSeparated.taxRate
}
