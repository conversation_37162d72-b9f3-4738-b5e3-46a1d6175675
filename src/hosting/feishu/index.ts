import '../polyfill'

import { reportHomePageLoadedDuration, reportHomePagePaintDuration } from '../../logger/homePageLoad'

import { fetchHandleError } from '../FetchConfig'
import { appDataInitialize, bootstrap } from './bootstrap'

fetchHandleError()

window.addEventListener('load', () => {
  reportHomePageLoadedDuration()
  reportHomePagePaintDuration()
})

appDataInitialize()

window.addEventListener('DOMContentLoaded', async () => {
  bootstrap()
})
