/***************************************************
 * Created by nanyuantingfeng on 2019/9/2 12:16. *
 ***************************************************/
import { Fetch } from '@ekuaibao/fetch'
import { Store } from '@ekuaibao/i18n'

import brandData from '../i18n/brand'
import { enableSettingTranslate } from '../i18n/utils'

const appendLocaleToBody = (locale: string) => {
  document.body.dataset.locale = locale
}

export async function initialize() {
  const store = Store.getInstance()
  store.useDefaultLocale(determineLocale())
  store.useLocaleDataLoader(language => {
    appendLocaleToBody(language)
    if (['zh-CN', 'en-US'].includes(language)) {
      return Fetch.GET(`locales/${language}.json`, null, { isText: true } as any)
    }
    return Promise.resolve()
  })

  const scope = IS_HSFK
    ? 'hose'
    : IS_ICBC
      ? 'icbc'
      : 'ekuaibao'
  store.addScopeVariables(brandData)
  store.changeScope(scope)

  enableSettingTranslate();

  return store.initialize()
}

export function determineLocale() {
  // @ts-ignore
  let backLanguage = Fetch.staffSetting && Fetch.staffSetting.language
  /**
   * wJEazW8N980000: 安永（中国）企业咨询有限公司企业ID
   * hotfix:PRP-15802 工单03#4606
   * 需求：将该企业的默认语言修改为英文，即新用户加入后，也是英文
   */
  if (Fetch.ekbCorpId === 'wJEazW8N980000') backLanguage = 'en-US'

  // 此处会在reports模块中读取 Fetch.lang 以作为语言包使用
  if (backLanguage) {
    Fetch.lang = backLanguage
    Fetch.defaultLanguage = backLanguage
    return backLanguage
  } else {
    backLanguage = window.navigator && window.navigator.language
    backLanguage = /en/.test(backLanguage) ? 'en-US' : 'zh-CN'
    Fetch.lang = backLanguage
    Fetch.defaultLanguage = backLanguage
    return backLanguage
  }
}

// 未登录的语言设置
export async function initializeI18n() {
  const store = Store.getInstance()
  const browerLang = window.navigator && window.navigator.language
  const language = /en/.test(browerLang) ? 'en-US' : 'zh-CN'
  store.useDefaultLocale(language)
  store.useLocaleDataLoader(language => {
    appendLocaleToBody(language)
    if (['zh-CN', 'en-US'].includes(language)) {
      return Fetch.GET(`locales/${language}.json`, null, { isText: true } as any)
    }
    return Promise.resolve()
  })

  const scope = IS_HSFK
    ? 'hose'
    : IS_ICBC
      ? 'icbc'
      : 'ekuaibao'
  store.addScopeVariables(brandData)
  store.changeScope(scope)
  return store.initialize()
}

export function setStaffDefaultLanguage() {
   // @ts-ignore
   let staffLanguage = Fetch.staffSetting && Fetch.staffSetting.language
   /**
    * wJEazW8N980000: 安永（中国）企业咨询有限公司企业ID
    * hotfix:PRP-15802 工单03#4606
    * 需求：将该企业的默认语言修改为英文，即新用户加入后，也是英文
    */
   if (Fetch.ekbCorpId === 'wJEazW8N980000') staffLanguage = 'en-US'

   Fetch.lang = staffLanguage
   enableSettingTranslate();
   if (!!staffLanguage && staffLanguage !== i18n.currentLocale) {
     i18n.changeLanguage(staffLanguage)
   }
}
