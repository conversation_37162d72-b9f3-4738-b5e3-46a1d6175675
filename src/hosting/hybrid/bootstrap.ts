import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { callback, preloadMfePlugin } from '../index.v0'
import { newHomeConfig, getPreviewUrl } from '../newHomeConfig'
import { HybridSDK } from '@ekuaibao/sdk-bridge/sdk/hybrid'
import registerAPI from '../registerAPI'
import registerHandler from '../registerHandler'
import initPlatformInfo from '../initPlatformInfo'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import initializeTitle from '../initializeTitle'
require('../../styles/app.less')
import initLogger from '../../logger/initLogger'
import { initDatafluxRum } from '../dataflux-rum'
import { anonymousUser, initFeatBit, switchFeatBitUser } from '../../lib/featbit'
import { initializeI18n, setStaffDefaultLanguage } from '../i18n-initialize'

initDatafluxRum();
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'HYBRID'

function startup() {
  app.use({
    id: '@app',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}

const init = async () => {
  initColors()

  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(HybridSDK)
  registerHandler()
  registerAPI()

  preloadMfePlugin()

  try {
    await Promise.all([
      initPlatformInfo(),
      (app.sdk as HybridSDK).initialize(),
      initFeatBit(anonymousUser()),
      initializeI18n(),
      getPreviewUrl()
    ])
    await switchFeatBitUser()
    initRoute()
    setStaffDefaultLanguage()
    await newHomeConfig({ needInitFeatBit: false, needInitI18n: false, needInitPreviewUrl: false })
    setNetworkAdapter()
    initializeTitle()
  } catch (e) {
    console.error('init error', e)
  }
}

let initPromise = null
export const appDataInitialize = async () => {
  initPromise = init()
  await initPromise
}

export async function bootstrap() {
  if (initPromise) {
    await initPromise
    initPromise = null
  }

  startup()
}
