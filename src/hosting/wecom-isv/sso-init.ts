/**
 * WeChat Work ISV SSO Login Flow
 *
 * Flow Description:
 * 1. User accesses Hose platform
 * 2. Check if authorization code exists in URL
 * 3. If NO code: Redirect to WeChat OAuth authorization page
 * 4. If YES code: Exchange code for access token via WeChat API
 * 5. Initialize token manager with received access token
 * 6. Configure fetch instance for authenticated requests
 *
 * Flow Diagram:
 * user → Hose → [if Exist Code?]
 *                    ↓ No: redirect to open.weixin.qq.com + suite_id + redirectURL
 *                    ↓ Yes: get IDP code by api/wecom/public/oauth/login
 *                           ↓ get Ekuaibao accessToken by @hose/idp-jslib
 *                           ↓ Visit Hose By AccessToken
 */

import { createTokenManager } from '@hose/idp-jslib'
import { useFetchInstance, Fetch, configure, } from '@ekuaibao/fetch'

export const getWecomServerHostname = () => {

  const getByCurrentHost = () => {
    let host = 'https://wecom.ekuaibao.net'
    if (window.location.hostname.includes('ekuaibao.com')) {
      host = 'https://wecom.ekuaibao.com'
    }

    return host
  }

  if (!window.ENV_CONFIG) {
    return getByCurrentHost()
  }

  return window.ENV_CONFIG['wecomServerHostname'] || getByCurrentHost()
}

const WECOM_SERVER_HOSTNAME = window._WECOM_SERVER_HOSTNAME_ = getWecomServerHostname()

const SUITE_ID = window.ENV_CONFIG['wecomISVSuiteId'] || 'wwafcaadcea40e9595'

const appendQuery = (queryList: {key: string, value: string}[]) => {
  const url = new URL(window.location.href)
  queryList.forEach(item => {
    url.searchParams.set(item.key, item.value)
  })
  window.history.replaceState(null, '', url.toString());
}


const getCodeOfQuery = () => {
  const qs = new URLSearchParams(window.location.search)
  return qs.get('code')
}

const redirectToAuthUrl = (url?: string) => {
  // 重定向 URL - Current page URL encoded for OAuth callback
  const redirect_uri = encodeURI(url || window.location.href)
  // 【应用详情】-【回调配置】对应的 suite_id 和 suite_secret 信息
  // 授权链接 URL - WeChat OAuth authorization endpoint
  const login_url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${SUITE_ID}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
  // 向授权链接 URL 跳转 - Redirect to WeChat OAuth page
  window.location.href = login_url
}

/**
 * Exchange code for IDP code
 * @param {string} code - Authorization code from WeChat callback
 * @returns {Promise<string>} IDP code
 */
const getIDPInfo = async (code: string) => {
  const res = await window.fetch(`${WECOM_SERVER_HOSTNAME}/api/wecom/public/oauth/login?code=${encodeURIComponent(code)}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    keepalive: false,
  })
  return res.json()
}

/**
 * IDP (Identity Provider) configuration
 * Contains client credentials and endpoint information
 */
const idpInfo = {
  clientId: 'whispered-app',
  // idp测试环境只支持费控的release测试环境
  // Dynamic IDP base URI selection based on environment
  idpBaseUri: location.origin.includes('release.ekuaibao.net')
    ? 'https://idp.ekuaibao.net'
    : 'https://idp.hosecloud.com'
}

export const getSignature = async (url: string) => {
  const res = await fetch(`${WECOM_SERVER_HOSTNAME}/api/wecom/public/js-sdk/signature`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      corpId: Fetch.wxCorpId,
      url: url,
    })
  })
  return res.json()
}

/**
 * Initialize token manager with access token
 * Creates and configures token manager for authenticated API calls
 * @param {string} code - Access token received from WeChat API
 */
const initTokenManager = async (code: string) => {
  try {
    // Create token manager instance with IDP configuration
    const tokenManager = createTokenManager({
      clientId: idpInfo.clientId,
      idpBaseUri: idpInfo.idpBaseUri,
      enableCORS: true,
      fetch: useFetchInstance(),
      // Callback to update Fetch instance with new token
      onTokenChange: token => {
        Fetch.accessToken = encodeURIComponent(token)
      }
    })

    // Finalize login process with the access token
    await tokenManager.finishLogin(code as string)

    const fetcher = tokenManager.decorateFetch(useFetchInstance())
    configure({ fetcher, appendAccessTokenToQuery:false })
  } catch (error) {
    console.error(error)
    throw error
  }


}

/**
 * Main SSO login initialization function
 * Orchestrates the entire OAuth flow based on current URL state
 *
 * Flow:
 * 1. Check if authorization code exists in URL
 * 2. If no code: Redirect to WeChat OAuth
 * 3. If code exists: Exchange for access token and initialize session
 */
export const loginInit = async () => {
  const code = getCodeOfQuery()
  if (code) {
    const options = await getIDPInfo(code)
    const { access_token, hoseCorpId, wecomCorpId } = options
    if (!access_token && hoseCorpId === 'INVALID_CODE') {
      const url = new URL(window.location.href)
      url.searchParams.delete('code')
      redirectToAuthUrl(url.toString())
    }
    Fetch.corpId = hoseCorpId
    Fetch.wxCorpId = wecomCorpId
    Fetch.ekbCorpId = hoseCorpId

    // 这里的access_token并不是费控的token，所以不能直接放到query上
    appendQuery([{key: 'corpId', value: hoseCorpId}])
    const [_, signatureInfo] = await Promise.all([initTokenManager(access_token), getSignature(location.href)])
    return {options, signatureInfo};
  } else {
    redirectToAuthUrl()
  }
}
