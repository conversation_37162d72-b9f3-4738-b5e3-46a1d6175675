import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { app, callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { initDatafluxRum } from '../dataflux-rum'
import QYWeiXinSDK from '@ekuaibao/sdk-bridge/sdk/qyweixin-v2'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import initLogger from '../../logger/initLogger'
import { loginInit } from './sso-init'
import { designate, PLATFORM_KEYWORD } from './designate'
import('../../styles/invisible.less')

initLogger()
/********注册平台类型*******/
designate()

initDatafluxRum();

// window.UPLOAD_INVOICE_FILE_URL = UPLOAD_INVOICE_FILE_URL
function startup() {
  app.use({
    id: `@${PLATFORM_KEYWORD.toLowerCase()}`,
    onready() {
      addNcpcCode()
    }
  })

  callback()
}



export const bootstrap = async () => {
  let opts
  try {
    opts = await loginInit()
  } catch (error) {
    console.error('loginInit failed', error)
    throw error
  }

  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  // @ts-ignore
  app.sdk = app.container.get(QYWeiXinSDK)

  registerHandler()

  const { options, signatureInfo } = opts || {}
  await (app.sdk as any as QYWeiXinSDK).initialize({
    wxCorpId: options.wecomCorpId,
    agentId: options.agentId,
    getJSConfig: (url: string, type: 'config' | 'agentConfig') => {
      try {
        const fields = {
          config: 'configSignature',
          agentConfig: 'agentConfigSignature'
        }
        return {
          timestamp: signatureInfo.timeStamp,
          nonceStr: signatureInfo.nonceStr,
          signature: signatureInfo[fields[type]]
        }
      } catch (e) {
        console.error("get signature error", e)
        return {}
      }
    }
  } as any)

  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  await newHomeConfig()

  initializeTitle()
  registerAPI()
  setNetworkAdapter()
  await startup()
  setNetworkAdapter()
}
