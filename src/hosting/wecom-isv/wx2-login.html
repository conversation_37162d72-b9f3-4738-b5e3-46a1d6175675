<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HOSE</title>
  <style>
    body, div {
      width: 100%;
      height: 100%;
    }
    .wx2-login-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      color: rgba(29,33,41,0.5);
      font-size: 14px;
    }
  </style>
  <script>
    ;(function() {
        // 重定向 URL - Current page URL encoded for OAuth callback
      const redirect_uri = encodeURI(location.origin + '/web/wx2.html')
      const SUITE_ID = 'wwafcaadcea40e9595'
      // 授权链接 URL - WeChat OAuth authorization endpoint
      const login_url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${SUITE_ID}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
      // 向授权链接 URL 跳转 - Redirect to WeChat OAuth page
      window.location.href = login_url
    })()
  </script>
</head>
<body>
  <!-- to jump for login wecom isv -->
  <div class="wx2-login-container">
    跳转中...
  </div>
</body>
</html>