/***************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 2019/11/4 16:45. *
 ***************************************************/
import { Fetch } from '@ekuaibao/fetch'

export default initPlatformInfo
export async function initPlatformInfo() {
  try {
    const platformInfo = await Fetch.GET('/api/platformInfo', undefined, undefined, { hiddenLoading: true })
    window.PLATFORMINFO = platformInfo
    window.isZhongDian = platformInfo?.name?.includes( 'zhongdian')
  }catch (e) {
    console.log(e)
  }
}