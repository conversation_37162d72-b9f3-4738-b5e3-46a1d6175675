/*!
 * Copyright 2019 yangjunbao <<EMAIL>>. All rights reserved.
 * @since 2019-07-09 11:56:12
 */

import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { callback, preloadMfePlugin } from '../index.v0'
import { newHomeConfig, getPreviewUrl } from '../newHomeConfig'
import DingtalkSDK from '@ekuaibao/sdk-bridge/sdk/dingtalk'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import { initDatafluxRum } from '../dataflux-rum'
import { localCacheSet } from '@ekuaibao/session-info'
import initLogger from '../../logger/initLogger'

import { anonymousUser, initFeatBit, switchFeatBitUser } from '../../lib/featbit'
import { initializeI18n } from '../i18n-initialize'

/********注册平台类型*******/
window.__PLANTFORM__ = 'DING_TALK'

initDatafluxRum();
initLogger()

function startup(hash) {
  app.use({
    id: '@hosting',
    onready() {
      addNcpcCode()
    }
  })
  if (hash) {
    app.useHistory({
      search: location.search.slice(1),
      initialEntries: [hash]
    })
  }
  callback()
}

function modifierKey(e: KeyboardEvent) {
  let modifier = 0
  if (e.metaKey) {
    modifier |= 1
  }
  if (e.ctrlKey) {
    modifier |= 2
  }
  if (e.altKey) {
    modifier |= 4
  }
  if (e.shiftKey) {
    modifier |= 8
  }
  return modifier
}

window.addEventListener('keydown', function (e: KeyboardEvent) {
  const modifier = modifierKey(e)
  if (e.keyCode === 82 && (modifier === 1 || modifier === 2)) {
    // meta+r or ctrl+r
    window.location.reload()
  }
})
function redirect() {
  const params = (() => {
    const arr = location.search.slice(1).split('&')
    const obj: any = {}
    for (let i = 0; i < arr.length; i++) {
      const arr2 = arr[i].split('=')
      obj[arr2[0]] = decodeURIComponent(arr2[1])
    }
    return obj
  })()

  if (params.roleDefId) {
    localCacheSet('beforePage', 'dimensionMap', true)
    return `/dimension-map?roleDefId=${params.roleDefId}`
  }
  return ''

}

const init = async () => {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(DingtalkSDK)
  registerHandler()

  preloadMfePlugin()

  try {
    await Promise.all([
      initPlatformInfo(),
      (app.sdk as DingtalkSDK).initialize(),
      initFeatBit(anonymousUser()),
      initializeI18n(),
      getPreviewUrl()
    ])
    await switchFeatBitUser()

    initRoute()

    await newHomeConfig({ needInitFeatBit: false, needInitI18n: false, needInitPreviewUrl: false })
    setNetworkAdapter()
    systemLimitLogout()
    initializeTitle()
    registerAPI()
  } catch (e) {
    console.error('init error', e)
  }
}

let initPromise = null
export const appDataInitialize = async () => {
  initPromise = init()
  await initPromise
}

// 延迟处理，已确保jssdk执行完成
export const bootstrap = async () => {
  if (initPromise) {
    await initPromise
    initPromise = null
  }

  const hash = redirect()

  await startup(hash)
}

if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'beta') {
  require('../development')
}
