/*!
 * Copyright 2019 yangjunbao <<EMAIL>>. All rights reserved.
 * @since 2019-07-09 12:08:12
 */

import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { callback, preloadMfePlugin } from '../index.v0'
import { newHomeConfig, getPreviewUrl } from '../newHomeConfig'
import BrowserSDK from '@ekuaibao/sdk-bridge/sdk/browser'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import initLogger from '../../logger/initLogger'
import { initDatafluxRum } from '../dataflux-rum'

import { initFeatBit, anonymousUser, switchFeatBitUser } from '../../lib/featbit'
import { initializeI18n } from '../i18n-initialize'

/********注册平台类型*******/
window.__PLANTFORM__ = 'DEBUGGER'

initDatafluxRum();
initLogger()
function startup() {
  app.use({
    id: '@browser',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}

const init = async () => {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(BrowserSDK)

  registerHandler()
  preloadMfePlugin()

  try {
    await Promise.all([
      initPlatformInfo(),
      (app.sdk as BrowserSDK).initialize(),
      initFeatBit(anonymousUser()),
      initializeI18n(),
      getPreviewUrl()
    ])
    await switchFeatBitUser()

    initRoute()

    await newHomeConfig({ needInitFeatBit: false, needInitI18n: false, needInitPreviewUrl: false })
    systemLimitLogout()
    initializeTitle()
    registerAPI()
    setNetworkAdapter()
  } catch (e) {
    console.error('init error', e)
  }
}

let initPromise = null
export const appDataInitialize = async () => {
  initPromise = init()
  await initPromise
}

export async function bootstrap() {
  if (initPromise) {
    await initPromise
    initPromise = null
  }
  startup()
}
