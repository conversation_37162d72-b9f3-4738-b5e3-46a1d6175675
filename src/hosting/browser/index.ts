import { reportHomePageLoadedDuration, reportHomePagePaintDuration } from '../../logger/homePageLoad'

import '../polyfill'

import { FetchError } from '@ekuaibao/fetch'
import { fetchHandleError } from '../FetchConfig'
import { goto } from '../../lib/lib-util'

import { appDataInitialize, bootstrap } from './bootstrap'

// hook ajax response handler
function unAuthorizedAction(error: FetchError) {
  if (error.status === 401) {
    // 未登录
    goto('/login')
  }
}

fetchHandleError(unAuthorizedAction)

window.addEventListener('load', () => {
  reportHomePageLoadedDuration()
  reportHomePagePaintDuration()
})

appDataInitialize()

window.addEventListener('DOMContentLoaded', async () => {
  bootstrap()
})
