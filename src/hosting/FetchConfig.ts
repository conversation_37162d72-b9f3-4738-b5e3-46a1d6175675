/***************************************************
 * Created by nanyuantingfeng on 2020/3/12 19:17. *
 ***************************************************/
import { configure, Fetch, FetchError } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
import qs from 'qs'
import { getV, pageViewCacheKey } from '@ekuaibao/lib/lib/help'
import { localCacheSet } from '@ekuaibao/session-info'


configure({
  hideLoading: window.hideLoading,
  showLoading: window.showLoading as any,
  handleStatus431: () => {
    const n = new Date().getTime()

    const hash = getV(location, 'hash', '')
    const isAuthPage = hash.includes('/auth-check')
    if (!isAuthPage) {
      api.history.replace('/auth-check')
    }
  },
  networkErrorMessage: () => i18n.get('网络无法访问，请检查网络设置'),
  checkNetworkMessage: () => i18n.get('请检查网络'),
  getStaffId: () => api.getState('@common.userinfo.staff.id'),
  handleErrorCode500: () => {
    window.alertMessage(i18n.get('系统正在处理中，请稍后重试'))
  },
})

export function go2RedirectUrl(error: FetchError) {
  return new Promise(resolve => {
    const { beforePagePathKey, beforePageKeyAppId, beforePageKey } = pageViewCacheKey(Fetch.ekbCorpId)

    if (error.status === 401) {
      api.logger?.error?.('AuthError401', { error, corpId: Fetch.ekbCorpId, pathname: location.pathname })
      if (window.IS_SMG && window.location.href.indexOf('/thirdparty') >= 0) {
        alert('授权已失效，请通过10.27.143.43/smgface/single/login页面重新登录')
        resolve('')
      } else {
        const urlState = qs.parse(location.search.slice(1))
        if (urlState.overdueTokenRedirect) {
          const url = new URL(urlState.overdueTokenRedirect)
          const VALID_PROTOCOLS = ['http:', 'https:']
          if (VALID_PROTOCOLS.includes(url.protocol)) {
            location.href = urlState.overdueTokenRedirect
          } else {
            resolve('')
          }
        } else {
          resolve('')
        }
      }
    } else if (error.status === 700) {
      const hash = getV(location, 'hash', '')
      const isMCError = hash.includes('/mc-error')
      if (!isMCError) {
        localCacheSet(beforePageKey, '', true)
        localCacheSet(beforePagePathKey, '', true)
        localCacheSet(beforePageKeyAppId, '', true)
        api.history.replace('/mc-error')
      }
    } else if (error.status === 701) {
      const hash = getV(location, 'hash', '')
      const isSharedError = hash.includes('/shared-error') || hash.includes('/login')
      if (!isSharedError) {
        localCacheSet(beforePageKey, '', true)
        localCacheSet(beforePagePathKey, '', true)
        localCacheSet(beforePageKeyAppId, '', true)
        api.history.replace('/shared-error')
      }
    } else {
      resolve('')
    }
  })
}

// @ts-ignore
Fetch.go2RedirectUrl = go2RedirectUrl

export function fetchHandleError(action?: (error: FetchError) => void) {
  Fetch.handleError = function(err: FetchError) {
    go2RedirectUrl(err).then(() => {
      action && action(err)
    })
  }
}

export default Fetch

export * from '@ekuaibao/fetch'
