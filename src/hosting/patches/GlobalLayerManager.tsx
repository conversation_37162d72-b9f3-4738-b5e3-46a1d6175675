/***************************************************
 * Created by nany<PERSON>ingfeng on 2019/12/24 15:27. *
 ***************************************************/
import React from 'react'
import { app } from '@ekuaibao/whispered'
import { Point } from '@ekuaibao/whispered'
import { flatten } from 'lodash'
import { ILayerManagerProps, EnhanceLayerManager } from '@ekuaibao/enhance-layer-manager'
import { addRumAction } from '../dataflux-rum'

@EnhanceLayerManager(props => props.layers)
class LM extends React.Component<ILayerManagerProps & { layers: any }> {
  state: any = {}
  private static instance: LM | null = null
  private isMountedRef = { current: false } // 使用对象引用

  componentDidMount() {
    if (LM.instance) {
      console.warn('GlobalLayerManager已存在实例，跳过重复挂载')
      return
    }

    LM.instance = this
    this.isMountedRef.current = true
    app.watch('@@:open:layer', this.safeEventHandler('@@:open:layer', this.handleOpenLayer))
    app.watch('@@:close:layer', this.safeEventHandler('@@:close:layer', this.handleCloseLayer))
    app.watch(
      '@layout:support:select-member',
      this.safeEventHandler('@layout:support:select-member', this.handleSelectStaff)
    )
    addRumAction('GlobalLayerManager_componentDidMount', {})
  }

  componentWillUnmount() {
    if (LM.instance === this) {
      LM.instance = null
    }
    this.isMountedRef.current = false
    app.un('@@:open:layer', this.handleOpenLayer)
    app.un('@@:close:layer', this.handleCloseLayer)
    app.un('@layout:support:select-member', this.handleSelectStaff)
    console.log('GlobalLayerManager事件监听器已安全清理')
    addRumAction('GlobalLayerManager_componentWillUnmount', {})
  }

  /**
   * 安全的事件处理包装器
   */
  private safeEventHandler = (eventName: string, handler: Function) => {
    return (...args: any[]) => {
      if (!this.isMountedRef.current || LM.instance !== this) {
        console.warn(`GlobalLayerManager实例已失效，忽略事件: ${eventName}`)
        return
      }
      return handler.apply(this, args)
    }
  }

  /**
   * 处理关闭弹窗事件
   */
  handleCloseLayer = () => {
    const { layerManager } = this.props
    layerManager.close()
  }

  /**
   * 处理打开弹窗事件
   */
  handleOpenLayer = (key: string, props: any, isSingle?: boolean) => {
    if (!this.isMountedRef.current) {
      console.warn('GlobalLayerManager已卸载，忽略弹窗打开请求:', key)
      return Promise.reject('GlobalLayerManager已卸载')
    }

    const { layerManager } = this.props
    const result = isSingle ? layerManager.open(key, props) : layerManager.push(key, props)
    return result
  }

  /**
   * 处理选择员工事件
   */
  handleSelectStaff = args => {
    if (!this.isMountedRef.current) {
      console.warn('GlobalLayerManager已卸载，忽略选择员工请求')
      return Promise.resolve()
    }

    const checkedList = [
      { type: 'department-member', multiple: !!args.multiple, checkedKeys: args.checkedKeys || args.users || [] }
    ]

    return app
      .open('@layout:SelectStaffsModal', {
        checkedList,
        disabledKeys: args.closeable,
        showStaffsAll: args.showStaffsAll,
        whiteList: args.whiteList,
        isVisibilityStaffs: args.isVisibilityStaffs,
        showResignationStaff: args.showResignationStaff,
        isCrosscorp: args.isCrosscorp,
        crosscorpOwners: args.crosscorpOwners,
        isShowLeftCrossCorpNode: args.isShowLeftCrossCorpNode,
        isNewContactList: !!args?.isNewContactList,
        isMcTransfer: !!args.isMcTransfer,
        defaultDeparment: args.defaultDeparment,
        staffFilterList: args.staffFilterList
      })
      .then((data: any) => {
        const { checkedList } = data
        const { checkedData } = checkedList[0]
        if (!!args.isMcTransfer) {
          args.callback && args.callback(!!args.multiple ? checkedData : checkedData[0], data.selectedDept)
          return
        }
        args.callback && args.callback(!!args.multiple ? checkedData : checkedData[0])
      })
  }

  render() {
    return null
  }
}

export const GlobalLayerManager: React.FunctionComponent = () => {
  return (
    <Point point="@@layers" namespace={null}>
      {data => <LM layers={flatten(data)} />}
    </Point>
  )
}

export default GlobalLayerManager
