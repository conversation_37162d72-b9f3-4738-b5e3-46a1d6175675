import { reportHomePageLoadedDuration, reportHomePagePaintDuration } from '../../logger/homePageLoad'

import '../polyfill'

import { FetchError } from '@ekuaibao/fetch'
import { fetchHandleError } from '../FetchConfig'

import { appDataInitialize, bootstrap } from './bootstrap'

fetchHandleError((error: FetchError) => {
  if (error.status === 401) {
    let path = '/web/errorInfo.html?errorMessage=授权失败，请重新登录'
    if (location.href.includes(':9999')) {
      path = '/errorInfo.html?errorMessage=授权失败，请重新登录'
    }
    location.replace(path)
  }
})

window.addEventListener('load', () => {
  reportHomePageLoadedDuration()
  reportHomePagePaintDuration()
})

appDataInitialize()

window.addEventListener('DOMContentLoaded', async () => {
  bootstrap()
})
