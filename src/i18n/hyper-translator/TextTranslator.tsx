import React, { useEffect, useRef, useState } from 'react';
import styles from './TextTranslator.module.less';
import { FilledOtherTranslate, OutlinedDirectionRefresh, OutlinedEditCopy, OutlinedTipsClose } from '@hose/eui-icons'
import { Button, message, Spin } from '@hose/eui';
import { Fetch } from '@ekuaibao/fetch';
interface TextTranslatorProps {
  translateConfig?: any,
  isClose?: boolean
}


export const TextTranslator: React.FC<TextTranslatorProps> = ({ translateConfig, isClose }) => {
  const [selectedText, setSelectedText] = useState('');
  const [translationText, setTranslationText] = useState('');
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const translationContainerRef = useRef<HTMLDivElement>(null);
  const [translationStatus, setTranslationStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [showTranslatorStatus, setShowTranslatorStatus] = useState<'idle' | 'icon' | 'popover'>('idle');

  useEffect(() => {
    document.addEventListener('mouseup', handleMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);


  const handleMouseUp = async (e) => {
    e.preventDefault();
    const selection = window.getSelection();
    setShowTranslatorStatus('idle');

    if (!selection || selection.isCollapsed || localStorage.getItem(`${Fetch.ekbCorpId}_enableHyperTranslate`) === 'close') {
      return;
    }

    // 判断是否选中翻译本身
    if (translationContainerRef.current?.contains(e.target)) {
      return;
    }

    const text = selection.toString().trim();

    if (!text) {
      return;
    }

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    setPosition({
      x: rect.x + rect.width,
      y: rect.y + rect.height,
    });

    setSelectedText(text);
    setShowTranslatorStatus('icon');
  }

  const getTranslation = async () => {
    setShowTranslatorStatus('popover');
    try {
      setTranslationStatus('loading');
      const result = await translateConfig.translateText(selectedText)
      setTranslationText(result);
      setTranslationStatus('success');
    } catch (error) {
      setTranslationStatus('error');
      console.error('Translation failed:', error);
    } finally {
      const popoverHeight = translationContainerRef.current?.offsetHeight
      const popoverWidth = translationContainerRef.current?.offsetWidth
      if (position.y + popoverHeight > window.innerHeight || position.x + popoverWidth > window.innerWidth) {
        setPosition({
          x: position.x + popoverWidth > window.innerWidth ? position.x - popoverWidth : position.x,
          y: position.y + popoverHeight > window.innerHeight ? position.y - popoverHeight : position.y,
        })
      }
    }
  }

  const renderIcon = () => {
    return (
      <FilledOtherTranslate
        style={{ position: 'fixed', left: position.x, top: position.y, background: '#fff' }}
        fontSize={24} color="var(--eui-primary-pri-500)"
        onMouseEnter={getTranslation}
      />
    )
  };

  const renderPopover = () => {
    const content = (
      <div ref={translationContainerRef} className='translator-popover' style={{ position: 'fixed', left: position.x, top: position.y }}>
        <div className='popover-header'>
          <div className='popover-header-title'>
            <FilledOtherTranslate fontSize={32} color="var(--eui-primary-pri-500)" />
            <div>{i18n.get('翻译')}</div>
          </div>
          <div className='popover-header-close'>
            <OutlinedTipsClose
              fontSize={16}
              color="var(--eui-icon-n2)"
              onClick={() => {
                setSelectedText('')
                setShowTranslatorStatus('idle');
              }}
            />
          </div>
        </div>
        <div className='popover-content'>
          {translationStatus === 'loading' &&
            <div className='popover-content-loading'>
              <Spin text={i18n.get('加载中...')} direction="vertical" />
            </div>
          }
          {translationStatus === 'error' &&
            <div className='popover-content-error'>
              <div>{i18n.get('翻译失败, 请重试')}</div>
              <Button category="text" theme="highlight" icon={<OutlinedDirectionRefresh fontSize={20} />}>
                {i18n.get('刷新重试')}
              </Button>
            </div>
          }
          {translationStatus === 'success' &&
            <div className='popover-content-success'>
              <span>{translationText}</span>
              <OutlinedEditCopy
                fontSize={16}
                className='copy-icon'
                color="var(--eui-icon-n2)"
                onClick={() => {
                  navigator.clipboard.writeText(translationText)
                  message.success(i18n.get('复制成功'))
                }}
              />
            </div>
          }
        </div>
      </div>
    );
    return content;
  }

  return (
    <div className={styles['text-translator']} >
      {showTranslatorStatus === 'icon' && renderIcon()}
      {showTranslatorStatus === 'popover' && renderPopover()}
    </div>
  )
}