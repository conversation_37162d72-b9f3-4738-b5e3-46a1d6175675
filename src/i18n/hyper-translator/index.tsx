import React, { useState } from 'react';
import { TextTranslator } from './TextTranslator';
import { TranslatorBubble } from './TranslatorBubble'
import { getBoolVariation } from '../../lib/featbit'
import ReactDOM from 'react-dom'
import { Fetch } from '@ekuaibao/fetch'
import { getLanguageByLanguageCode } from '../utils'
const HyperTranslator = ({ translateConfig, target }) => {
    const [isClose, setClose] = useState(false);
    const onBubbleClose = () => {
        setClose(true)
    }
    if (isClose || !getBoolVariation('ao-20-hyper-translator') || window.localStorage.getItem(`${Fetch.ekbCorpId}_enableHyperTranslate`) === 'close') return null
    return (
        <>
            <TextTranslator translateConfig={translateConfig} />
            <TranslatorBubble translateConfig={translateConfig} onBubbleClose={onBubbleClose} target={target} />
        </>
    )
}

const languageMap = {
    cn: "chinese_simplified",
    "zh-CN": "chinese_simplified",
    en: "english",
    "en-US": "english",
    english: "english",
};

export function initTranslator(translateConfig, target) {
    translateConfig.translate.language.translateLocal = false
    const language = getLanguageByLanguageCode(target)
    const languageCode = languageMap[language] || language
    translateConfig.translate.language.setLocal(languageCode)
    if (['zh-CN', 'en-US'].includes(target)) {
        translateConfig.translate.language.setDefaultTo(languageCode)
    } else {
        translateConfig.translate.ignore.class.push('translate-ignore-class');
        translateConfig.setToLanguage(target)
    }
    translateConfig.initTranslate()

    const existingContainer = document.getElementById('hyber-translator-container');
    if (existingContainer) {
        return;
    }
    const container = document.createElement('div');
    container.id = 'hyber-translator-container'
    document.body.appendChild(container);
    ReactDOM.render(
        <HyperTranslator translateConfig={translateConfig} target={target} />,
        container
    );

}

