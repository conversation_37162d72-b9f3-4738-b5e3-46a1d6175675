# 使用nrm 快速管理多个 registry
● 全局安装nrm 
`Shellnpm i nrm -g `
● 添加 ekuaibao 私有源
`Shellnrm add ekb https://npm.ekuaibao.com/`
● 切换 ekuaibao 私有源
`Shellnrm use ekb`

# 项目代码解析

**简介**

@ekuaibao/entry-web 是[易快报产品 pc 端](https://app.ekuaibao.com/web/app.html)源代码。使用了 React、Mobx、Redux及基于webpack自主研发的@ekuaibao/whispered 构建工具等主流技术开发，支持多平台的微前端解决方案，本文档旨在将前端的项目大体流程稍作解释，帮助新加入的小伙伴有个整体上的认识，用于学习参考。

**项目概览**

以web端代码作为示例，applet 同理。

1. **前端项目结构**

环境参数：Node.js >= 12.0.0    mobx@4   react@16.13.1   antd@3.8.4  

|  JavaScript   \|-- whispered       \|-- ...       \|-- Dockerfile   // docker 部署配置       \|-- index.hbs   // 静态模板入口       \|-- package.json   // 包管理       \|-- webpack.basic.js  // 公用构建配置文件       \|-- webpack.build.js  // 生产构建配置文件       \|-- webpack.config.js  // 开发构建配置文件       \|-- webpack.config.plugin.build.js       \|-- webpack.config.plugin.dev.js         \|-- buildscript   // 构建脚本       \|-- configs   // 差异化配置       \|   \|-- opg.config.js       \|   \|-- opg.ico       \|-- scripts   // 构建配置       \|   \|-- build       \|   \|-- builds-dev       \|   \|-- builds-pro       \|   \|-- dev       \|   \|-- fix-jszip-issue.js       \|-- src   // 源码区           \|-- global.d.ts   // 全局ts 类型申明           \|-- res-codemod.ts   /;.,,,,,,,,,,,,,,,,,,,           \|-- res\_lib\_sync.ts           \|-- codemod-reports              \|   \|-- .codemod-reports-account.json           \|   \|-- ...           \|-- components   // 通用公共组件           \|   \|-- ...           \|-- dist   // 自动生成，无需管理           \|   \|-- res\_lib\_sync.js           \|-- ekb-components  // ekb 组件，该目录废弃           \|   \|-- index.js           \|   \|-- ...           \|-- elements   // 跨服务通用业务组件           \|   \|-- FeetypeRecommend.tsx           \|   \|-- ...           \|-- file   // 静态文件，会被粘贴到根目录           \|   \|-- iconfont.js           \|   \|-- ...           \|-- hosting    // 多平台入口           \|   \|-- app   // sass app 入口           \|   \|   \|-- \_plugins.ts           \|   \|   \|-- bootstrap.ts           \|   \|   \|-- index.ts           \|   \|-- dingtalk   // 钉钉入口           \|   \|   \|-- ...           \|   \|-- ...           \|-- i18n   // 多语言           \|   \|-- brand.ts           \|   \|-- locale           \|       \|-- zh-CN.json           \|       \|-- ...           \|-- images  // 图片及图标           \|-- \|--...           \|-- plugins   // 微前端模块           \|   \|-- bills           \|   \|-- ...           \|-- styles  // 全局样式           \|   \|-- antd-cover.less           \|   \|-- app.less           \|-- test   // 测试用例           \|   \|-- dynamic-form.js           \|-- track   // 埋点配置               \|-- index.js               \|-- shence.js  |
| --- |

2. **项目入口及代理配置**

*   **entry (webpack.config.js)**
    

|  JavaScript   // web/webpack.config.js      config.patch.entry({     app: './src/hosting/app/index.ts',     debugger: './src/hosting/browser/index.ts',     group: './src/hosting/group/index.ts',     ldap: './src/hosting/ldap/index.ts',     billentry: './src/hosting/billentry/index.ts',     nbbank: './src/hosting/nbbank/index.ts',     'nbbank-message': './src/hosting/nbbank/message.ts',     huawei: './src/hosting/huawei/index.ts',     dingtalk: './src/hosting/dingtalk/index.ts',     kdcloud: './src/hosting/kdcloud/index.ts',     shareexpinfo: './src/hosting/shareexpinfo/index.ts',     wx: './src/hosting/weixin/index.ts',     'wx-message': './src/hosting/weixin/message.ts',     payment: './src/hosting/payment/index.ts',     thirdparty: './src/hosting/thirdparty/index.ts',     feishu: './src/hosting/feishu/index.ts',     'fs-message': './src/hosting/feishu/message.ts'   })  |
| --- |

先找到 ./webpack.config.js 文件，entry 就是对应的项目的入口文件，整个项目会运行在不同的终端上面，比如app是原生app，dingtalk运行在钉钉的终端上。

进入到 app 的入口文件  ./src/hosting/app/index.ts，当初始HTML文档已完全加载和解析时，将触发 DOMContentLoaded 事件，然后主要的走了一个 startup 函数。

startup函数用到了"@ekuaibao/whispered"里面暴露出来的api.use()。我们的项目是用了一个plugin的思想，每个功能模块独立出来看成一个插件挂载到全局上。

|  JavaScript   // web/src/hosting/app/bootstrap.ts      function startup() {     app.use({       id: '@app',         onready() {         initSentry()  // 初始化sentry引擎，记录错误日志         addNcpcCode()       }     })     callback()   }      _export_ async function bootstrap() {     initColors()   // 初始化颜色变量     initRoute()   // 初始化路由配置     app.container.set('IDeviceType', 'DESKTOP')     app.container.set('ISMessageEntry', false)     app.sdk = app.container.get(eKuaibaoSDK)   // 初始化当前环境jsBridge     registerHandler()        startup()   // 主入口     registerAPI()        _try_ {       _await_ (app.sdk _as_ eKuaibaoSDK).initialize()       setNetworkAdapter()   // 设置网络adapter     } _catch_ (e) {       _await_ newHomeConfig()       setNetworkAdapter()       _return_     }     _await_ newHomeConfig()    // 首页配置        initializeTitle()   // 初始化页面标题   }  |
| --- |

|  JavaScript   // web/src/hosting/\_plugins.ts      _/\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*_   _\* 自动生成的文件, 请勿修改_   _\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*/_   _import_ { app } _from_ '@ekuaibao/whispered'   app.use(require('../res-codemod').default)   app.use(require('../res\_lib\_sync').default)   app.use(require('../plugins/bills').default)   app.use(require('../plugins/reports-adv').default)     app.use(require('../plugins/safeSetting').default)  |
| --- |

所以想用哪个模块，就 app.use 它。我们可以看一下 @ekuaibao/whispered 的源码，实际上是维护了一个\_plugin数组。

|  TypeScript   // whispered/src/core/Context.ts        use<T>(        ): this {       _if_ (isPromise(plugin)) {         _this_.\_asyncplugins.push(plugin _as_ Promise<Whispered.Plugin<T> \| Array<Whispered.Plugin<T>>>)         _return_ _this_       }          _if_ (!Array.isArray(plugin)) {         plugin = \[plugin _as_ Whispered.Plugin<T>\]       }          const plugins = (plugin _as_ Array<Whispered.Plugin<T>>).map(p => {         _if_ (!p.id) {           p.id = uuid(8, 16)         }            _if_ (p.id === p.point) {           _throw_ new Error(\` 'id' & 'point' must be different  >> ${p.id}\`)         }            _return_ p       })          _this_.plugins.push(...plugins)       _return_ _this_     }  |
| --- |

可以看一下web 下 @bills这个plugin下面的index.js,就是暴露出来给app.use捕获的数组。

|  JavaScript   // web/src/plugins/bills/index.js   _export_ _default_ \[     {           id: '@bills',   // 唯一标识           reducer: () => import('./bills.reducer'),  // 引入了对应的reducer（Reducer也是单独封装的，可以到对应的文件去看）代码           path: '/',  // 文件的路由           onload: () => import('./bill-entry'),           dependencies: \['@common', '@layout5'\]  // 这个plugin依赖的模块     },     {       point: '@@components',       namespace: '@bills',       onload: () => \[{ key: 'BillDetail', component: () => import('./layers/bill-info-popup/BillDetail') }\]     },     {       point: '@@layers',       prefix: '@bills',       onload: () => import('./layers')     }   \]  |
| --- |

![Drawing 0](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/pLdn5gg17yd7Oo83/img/a1b3368d-159a-4742-ac61-3e311aedb70d.png)

关于生命周期，可以看一下源码的定义，我们的项目就是经过这样一个个的plugin一个个装载起来的。

|  TypeScript   // whispered/src/core/Hooks.ts      _export_ _default_ class Hooks {     onready = new AsyncParallelHook()     onafter = new AsyncParallelHook()        onfirst = new AsyncSeriesHook()     onload = new SyncSeriesHook()     others = new SyncParallelHook()     onuse = new SyncSeriesHook()        reg<T>(name: string, fn: (...args: any) => T) {       _this_.get(name).add(fn)       _return_ _this_     }        private get(name: string): Hook {       _// @ts-ignore_       _return_ _this_\[name\] ? _this_\[name\] : _this_.others     }   }  |
| --- |

*    **代理环境**
    

设置 proxyURL 可以更改本地代理环境，方便调试各个环境问题。

|  JavaScript   // web/webpack.config.js      _// const proxyURL = '_[http://460mix.ekuaibao.net/'](http://460mix.ekuaibao.net/) _// 460mix_   _// const proxyURL = '_[http://460.ekuaibao.net/'](http://460.ekuaibao.net/) _// 460_   _// const proxyURL = 'http://t2.dev.ekuaibao.cn/' // T组_   _// const proxyURL = 'http://w1.dev.ekuaibao.cn/' // W组_   _// const proxyURL = '_[http://n.dev.ekuaibao.cn/'](http://n.dev.ekuaibao.cn/) _// N组_   _// const proxyURL = '_[_https://wx2.ekuaibao.com/'_](https://wx2.ekuaibao.com/) _// 微信_   const proxyURL = '[https://app.ekuaibao.com/'](https://app.ekuaibao.com/) _// 生产环境_  |
| --- |

3. **部分内部封装库的说明（建议阅读源码）**

**为控制打包体积禁止滥用第三方库/包。如需引用，请和各自组前端负责人沟通。**

3.1 **@ekuaibao/fetch** 

网络请求封装，https://git.ekuaibao.com/mistery/ekuaibao\_fetch

3.2 **@ekuaibao/template** 

动态表单，https://git.ekuaibao.com/mistery/ekuaibao\_template

3.3 **@ekuaibao/messagecenter**

组件通信bus方法，https://git.ekuaibao.com/mistery/ekuaibao\_messagecenter

3.4 **@ekuaibao/eui**

内部封装的一版组件库，目前里面有ButtonGroup和CityPicker两个模块，现在大多废弃不使用了，改用antd了，https://git.ekuaibao.com/mistery/EUI

3.5 **@ekuaibao/helpers** 

封装的一些常用的方法，暴露出来的api，详细内容请自行拉取源码查看，https://git.ekuaibao.com/mistery/helpers

3.6 **@ekuaibao/theme-variables** 

新版颜色变量生成方案，https://git.ekuaibao.com/torpedo/theme-variables

3.7 **@ekuaibao/sdk-bridge**

已对接的各平台 jsBridge 封装，https://git.ekuaibao.com/external-extension/ekuaibao\_sdk-bridge

3.8 **@ekuaibao/whispered**

4. **前端代码分支策略**

![Drawing 1](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/pLdn5gg17yd7Oo83/img/3d22b3eb-8c58-4d53-be93-efd75f5e61ca.png)

|  Shell   // Finished代码命令   git merge hotfix/2021xxxx-master --no-ff -m "Finished hotfix/2021xxxx-master"  |
| --- |

**代码片段解析**

1.  **api 和 bus 方法** 

    api 是全局方法， bus 是局部方法

*   **API 方法**
    

|  JavaScript   api.on // 事件监听，事件注册   api.emit // 调用注册的事件，没有回调，返回值   api.un // 清除事件   api.watch // 事件注册，有返回值   api.invoke // 调用注册的事件，有回调，返回值   api.invokeService // 调用接口，发送 dispatch 【用于跨模块之间调用，如果没有跨模块，可直接 dispatch action 里暴露的方法】   api.dataLoader().load() // 如果本地有数据就不会去网络请求，直接拿本地的数据   api.open // 打开弹窗，可以传递值，有回调 （在新的弹窗里，用 getResult 可以返回值给上一个弹窗，也可以在 handleOk 函数里，用 this.props.layer.emitOk 传值）  |
| --- |

*   **BUS 方法**
    

|  JavaScript   // bus 是局部方法，需要在组件里传递 bus ，才能调用 bus 上注册的方法。    this.bus = new MessageCenter()    bus.on // 事件监听，事件注册   bus.emit // 调用注册的事件，没有回调，返回值   bus.un // 清除事件   bus.watch // 事件注册，有返回值   bus.invoke // 调用注册的事件，有回调，返回值  |
| --- |

*   **模块：Dynamic 动态渲染页 ；两种状态（可编辑态、只读态）**
    

      动态表单的数据，少用 state ,可以在 this.props 来获取，在子组件里拿到后，做数据的展示，编辑，删除，然后用 onChange 方法把数据再传回去，父组件获得最新的数据，数据变化，在传入子组件里，这样就成了数据的变化。

![Drawing 2](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/pLdn5gg17yd7Oo83/img/f4764f50-98a3-4131-a34b-aeb2049a9c36.png)

    Dynamic 封装动态渲染(三个必填参数)：

|  **template**：是一个数组（数组中是对象，即表格中的每一项），每一个对象中都包含一个 type 类型 **element**：是一个数组，里面的每一项都是一个类，他们都有自己的 type ，template 里对象包含的，type 类型和 element 里的 type 进行匹配 **create** : T => Form.create()(T) 创建表单  |
| --- |

*   **charge 控制开通：**
    

1.  环境地址/charge   打开 charge 页面
    

2.  账号： admin  密码： admin123
    

3.  左侧导航列表 --> 企业列表 --> 右侧企业名称中搜索出当前所在企业，比如 “G团队” --> 开通功能
    

*   **EnhanceStackerManager 该方法一般和面包屑一起使用**
    

|  JavaScript   // 在 EnhanceStackerManager 里写入一个数组，   // 数组里是对象，包含 key (字符串，用于方法的 push ) ，   // getComponent (点击显示组件的地址) , title (面包屑显示的标题)   this.props.stackerManager.push // push 定义的 key   this.props.stackerManager.open // 打开需要展示的页面。只需要填对应的下标   this.props.stackerManager.value // push 进去的元素数据   this.props.stackerManager.clear // 清除 push 进去是数据  |
| --- |

*   **接口的 select 和 join （如果接口允许，一个接口既可以写 join ，也可以写 select）**
    

**select**

  select 类似于我们百度输入的查询，我们在接口里写入我们想要查询的数据 例如：let query = new QuerySelect().select(\`entityId(...),ownerId(...),platformId(...),...\`) ，通过 query.value() 来获取.

**join**

![Drawing 3](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/pLdn5gg17yd7Oo83/img/b8a52439-763e-4bda-aa2c-512c53d3d14f.png)

如上例：我们在这个接口里用 join ，3 就是我们要查询的接口，1 就是查询这个接口传的数据，2 就是查询回来的数据另起的名字